package androidx.core.util;

import androidx.exifinterface.media.ExifInterface;
import kotlin.Metadata;
import okhttp3.internal.p042io.InterfaceC7155;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.zu2;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a\u001c\u0010\u0003\u001a\b\u0012\u0004\u0012\u00028\u00000\u0002\"\u0004\b\u0000\u0010\u0000*\b\u0012\u0004\u0012\u00028\u00000\u0001¨\u0006\u0004"}, m4115d2 = {ExifInterface.GPS_DIRECTION_TRUE, "Lokhttp3/internal/io/ৡ;", "Landroidx/core/util/Consumer;", "asAndroidXConsumer", "core-ktx_release"}, m4116k = 2, m4117mv = {1, 6, 0})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class AndroidXConsumerKt {
    @zu2
    public static final <T> Consumer<T> asAndroidXConsumer(@zu2 InterfaceC7155<? super T> interfaceC7155) {
        fa1.m6826(interfaceC7155, "<this>");
        return new AndroidXContinuationConsumer(interfaceC7155);
    }
}
