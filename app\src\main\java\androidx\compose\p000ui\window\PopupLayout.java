package androidx.compose.p000ui.window;

import android.annotation.SuppressLint;
import android.graphics.Outline;
import android.graphics.Rect;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewOutlineProvider;
import android.view.WindowManager;
import androidx.annotation.VisibleForTesting;
import androidx.compose.p000ui.platform.AbstractComposeView;
import androidx.compose.runtime.ParcelableSnapshotMutableState;
import androidx.core.app.NotificationCompat;
import androidx.lifecycle.ViewTreeLifecycleOwner;
import com.stardust.autojs.engine.RhinoJavaScriptEngine;
import kotlin.Metadata;
import okhttp3.internal.p042io.AbstractC6779;
import okhttp3.internal.p042io.C3535h8;
import okhttp3.internal.p042io.C6699;
import okhttp3.internal.p042io.C7171;
import okhttp3.internal.p042io.InterfaceC6575;
import okhttp3.internal.p042io.InterfaceC6968;
import okhttp3.internal.p042io.InterfaceC7452;
import okhttp3.internal.p042io.aw5;
import okhttp3.internal.p042io.bt2;
import okhttp3.internal.p042io.bw1;
import okhttp3.internal.p042io.cw1;
import okhttp3.internal.p042io.di0;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.fi0;
import okhttp3.internal.p042io.g03;
import okhttp3.internal.p042io.g71;
import okhttp3.internal.p042io.hi4;
import okhttp3.internal.p042io.ho3;
import okhttp3.internal.p042io.hz3;
import okhttp3.internal.p042io.k71;
import okhttp3.internal.p042io.kg3;
import okhttp3.internal.p042io.lu4;
import okhttp3.internal.p042io.lv1;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.ly3;
import okhttp3.internal.p042io.m71;
import okhttp3.internal.p042io.mg3;
import okhttp3.internal.p042io.n71;
import okhttp3.internal.p042io.ng3;
import okhttp3.internal.p042io.nh0;
import okhttp3.internal.p042io.qb4;
import okhttp3.internal.p042io.rg0;
import okhttp3.internal.p042io.s86;
import okhttp3.internal.p042io.wv2;
import okhttp3.internal.p042io.ym2;
import okhttp3.internal.p042io.zu2;
import org.mozilla.javascript.optimizer.Codegen;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000\u009a\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\b\n\u0002\b\r\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u001b\n\u0002\u0018\u0002\n\u0002\b\f\n\u0002\u0018\u0002\n\u0002\b\r\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0001\u0018\u00002\u00020\u00012\u00020\u0002BT\u0012\u000e\u0010'\u001a\n\u0012\u0004\u0012\u00020\u0005\u0018\u00010\u000f\u0012\u0006\u0010)\u001a\u00020(\u0012\u0006\u0010+\u001a\u00020*\u0012\u0006\u0010y\u001a\u00020x\u0012\u0006\u0010{\u001a\u00020z\u0012\u0006\u0010|\u001a\u00020]\u0012\u0006\u0010~\u001a\u00020}\u0012\t\b\u0002\u0010\u0080\u0001\u001a\u00020\u007f¢\u0006\u0006\b\u0081\u0001\u0010\u0082\u0001J\u0010\u0010\u0006\u001a\u00020\u00052\u0006\u0010\u0004\u001a\u00020\u0003H\u0002J\u0010\u0010\t\u001a\u00020\u00052\u0006\u0010\b\u001a\u00020\u0007H\u0002J\u0010\u0010\u000b\u001a\u00020\u00052\u0006\u0010\n\u001a\u00020\u0003H\u0002J\u0006\u0010\f\u001a\u00020\u0005J(\u0010\u0012\u001a\u00020\u00052\u0006\u0010\u000e\u001a\u00020\r2\u0011\u0010\u0011\u001a\r\u0012\u0004\u0012\u00020\u00050\u000f¢\u0006\u0002\b\u0010¢\u0006\u0004\b\u0012\u0010\u0013J\u000f\u0010\u0014\u001a\u00020\u0005H\u0017¢\u0006\u0004\b\u0014\u0010\u0015J\u001f\u0010\u001b\u001a\u00020\u00052\u0006\u0010\u0017\u001a\u00020\u00162\u0006\u0010\u0018\u001a\u00020\u0016H\u0010¢\u0006\u0004\b\u0019\u0010\u001aJ7\u0010#\u001a\u00020\u00052\u0006\u0010\u001c\u001a\u00020\u00032\u0006\u0010\u001d\u001a\u00020\u00162\u0006\u0010\u001e\u001a\u00020\u00162\u0006\u0010\u001f\u001a\u00020\u00162\u0006\u0010 \u001a\u00020\u0016H\u0010¢\u0006\u0004\b!\u0010\"J\u0010\u0010&\u001a\u00020\u00032\u0006\u0010%\u001a\u00020$H\u0016J.\u0010.\u001a\u00020\u00052\u000e\u0010'\u001a\n\u0012\u0004\u0012\u00020\u0005\u0018\u00010\u000f2\u0006\u0010)\u001a\u00020(2\u0006\u0010+\u001a\u00020*2\u0006\u0010-\u001a\u00020,J\u000e\u00101\u001a\u00020\u00052\u0006\u00100\u001a\u00020/J\u0006\u00102\u001a\u00020\u0005J\u000f\u00105\u001a\u00020\u0005H\u0001¢\u0006\u0004\b3\u00104J\u0006\u00106\u001a\u00020\u0005J\u0006\u00107\u001a\u00020\u0005J\u0012\u00109\u001a\u00020\u00032\b\u0010%\u001a\u0004\u0018\u000108H\u0016J\u0010\u0010:\u001a\u00020\u00052\u0006\u0010-\u001a\u00020\u0016H\u0016R\"\u0010+\u001a\u00020*8\u0006@\u0006X\u0086\u000e¢\u0006\u0012\n\u0004\b;\u0010<\u001a\u0004\b=\u0010>\"\u0004\b?\u0010@R \u0010G\u001a\u00020A8\u0000X\u0081\u0004¢\u0006\u0012\n\u0004\bB\u0010C\u0012\u0004\bF\u00104\u001a\u0004\bD\u0010ER$\u0010M\u001a\u00020\u00032\u0006\u0010H\u001a\u00020\u00038\u0014@RX\u0094\u000e¢\u0006\f\n\u0004\bI\u0010J\u001a\u0004\bK\u0010LR/\u00100\u001a\u0004\u0018\u00010/2\b\u0010H\u001a\u0004\u0018\u00010/8B@BX\u0082\u008e\u0002¢\u0006\u0012\n\u0004\bN\u0010O\u001a\u0004\bP\u0010Q\"\u0004\bR\u0010SRA\u0010\u0011\u001a\r\u0012\u0004\u0012\u00020\u00050\u000f¢\u0006\u0002\b\u00102\u0011\u0010H\u001a\r\u0012\u0004\u0012\u00020\u00050\u000f¢\u0006\u0002\b\u00108B@BX\u0082\u008e\u0002¢\u0006\u0012\n\u0004\bT\u0010O\u001a\u0004\bU\u0010V\"\u0004\b\u0012\u0010WR\u0014\u0010Z\u001a\u00020\u00168BX\u0082\u0004¢\u0006\u0006\u001a\u0004\bX\u0010YR\u0014\u0010\\\u001a\u00020\u00168BX\u0082\u0004¢\u0006\u0006\u001a\u0004\b[\u0010YR\"\u0010^\u001a\u00020]8\u0006@\u0006X\u0086\u000e¢\u0006\u0012\n\u0004\b^\u0010_\u001a\u0004\b`\u0010a\"\u0004\bb\u0010cR\"\u0010d\u001a\u00020,8\u0006@\u0006X\u0086\u000e¢\u0006\u0012\n\u0004\bd\u0010e\u001a\u0004\bf\u0010g\"\u0004\bh\u0010iR8\u0010p\u001a\u0004\u0018\u00010j2\b\u0010H\u001a\u0004\u0018\u00010j8F@FX\u0086\u008e\u0002ø\u0001\u0000ø\u0001\u0001ø\u0001\u0002¢\u0006\u0012\n\u0004\bk\u0010O\u001a\u0004\bl\u0010m\"\u0004\bn\u0010oR\u001b\u0010t\u001a\u00020\u00038FX\u0086\u0084\u0002¢\u0006\f\n\u0004\bq\u0010r\u001a\u0004\bs\u0010LR\u0014\u0010w\u001a\u00020\u00018VX\u0096\u0004¢\u0006\u0006\u001a\u0004\bu\u0010v\u0082\u0002\u000f\n\u0002\b\u0019\n\u0005\b¡\u001e0\u0001\n\u0002\b!¨\u0006\u0083\u0001"}, m4115d2 = {"Landroidx/compose/ui/window/PopupLayout;", "Landroidx/compose/ui/platform/AbstractComposeView;", "Lokhttp3/internal/io/s86;", "", "isFocusable", "Lokhttp3/internal/io/lx5;", "setIsFocusable", "Lokhttp3/internal/io/hi4;", "securePolicy", "setSecurePolicy", "clippingEnabled", "setClippingEnabled", "show", "Lokhttp3/internal/io/ܔ;", "parent", "Lkotlin/Function0;", "Lokhttp3/internal/io/ಭ;", "content", "setContent", "(Lokhttp3/internal/io/ܔ;Lokhttp3/internal/io/di0;)V", "Content", "(Lokhttp3/internal/io/ࡊ;I)V", "", "widthMeasureSpec", "heightMeasureSpec", "internalOnMeasure$ui_release", "(II)V", "internalOnMeasure", "changed", "left", "top", "right", "bottom", "internalOnLayout$ui_release", "(ZIIII)V", "internalOnLayout", "Landroid/view/KeyEvent;", NotificationCompat.CATEGORY_EVENT, "dispatchKeyEvent", "onDismissRequest", "Lokhttp3/internal/io/ng3;", "properties", "", "testTag", "Lokhttp3/internal/io/cw1;", "layoutDirection", "updateParameters", "Lokhttp3/internal/io/bw1;", "parentLayoutCoordinates", "updateParentLayoutCoordinates", "pollForLocationOnScreenChange", "updateParentBounds$ui_release", Codegen.TEMPLATE_LITERAL_INIT_METHOD_SIGNATURE, "updateParentBounds", "updatePosition", "dismiss", "Landroid/view/MotionEvent;", "onTouchEvent", "setLayoutDirection", "ၹ", "Ljava/lang/String;", "getTestTag", "()Ljava/lang/String;", "setTestTag", "(Ljava/lang/String;)V", "Landroid/view/WindowManager$LayoutParams;", "ၽ", "Landroid/view/WindowManager$LayoutParams;", "getParams$ui_release", "()Landroid/view/WindowManager$LayoutParams;", "getParams$ui_release$annotations", "params", "<set-?>", "ჾ", "Z", "getShouldCreateCompositionOnAttachedToWindow", "()Z", "shouldCreateCompositionOnAttachedToWindow", "parentLayoutCoordinates$delegate", "Lokhttp3/internal/io/yn2;", "getParentLayoutCoordinates", "()Lokhttp3/internal/io/bw1;", "setParentLayoutCoordinates", "(Lokhttp3/internal/io/bw1;)V", "content$delegate", "getContent", "()Lokhttp3/internal/io/di0;", "(Lokhttp3/internal/io/di0;)V", "getDisplayWidth", "()I", "displayWidth", "getDisplayHeight", "displayHeight", "Lokhttp3/internal/io/mg3;", "positionProvider", "Lokhttp3/internal/io/mg3;", "getPositionProvider", "()Lokhttp3/internal/io/mg3;", "setPositionProvider", "(Lokhttp3/internal/io/mg3;)V", "parentLayoutDirection", "Lokhttp3/internal/io/cw1;", "getParentLayoutDirection", "()Lokhttp3/internal/io/cw1;", "setParentLayoutDirection", "(Lokhttp3/internal/io/cw1;)V", "Lokhttp3/internal/io/m71;", "popupContentSize$delegate", "getPopupContentSize-bOM6tXw", "()Lokhttp3/internal/io/m71;", "setPopupContentSize-fhxjrPA", "(Lokhttp3/internal/io/m71;)V", "popupContentSize", "canCalculatePosition$delegate", "Lokhttp3/internal/io/g05;", "getCanCalculatePosition", "canCalculatePosition", "getSubCompositionView", "()Landroidx/compose/ui/platform/AbstractComposeView;", "subCompositionView", "Landroid/view/View;", "composeView", "Lokhttp3/internal/io/u7;", "density", "initialPositionProvider", "Ljava/util/UUID;", "popupId", "Lokhttp3/internal/io/kg3;", "popupLayoutHelper", RhinoJavaScriptEngine.SOURCE_NAME_INIT, "(Lokhttp3/internal/io/nh0;Lokhttp3/internal/io/ng3;Ljava/lang/String;Landroid/view/View;Lokhttp3/internal/io/u7;Lokhttp3/internal/io/mg3;Ljava/util/UUID;Lokhttp3/internal/io/kg3;)V", "ui_release"}, m4116k = 1, m4117mv = {1, 7, 1})
@SuppressLint({"ViewConstructor"})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class PopupLayout extends AbstractComposeView implements s86 {

    /* renamed from: ၷ */
    @wv2
    public nh0<lx5> f264;

    /* renamed from: ၸ */
    @zu2
    public ng3 f265;

    /* renamed from: ၹ, reason: from kotlin metadata */
    @zu2
    public String testTag;

    /* renamed from: ၺ */
    @zu2
    public final View f267;

    /* renamed from: ၻ */
    @zu2
    public final kg3 f268;

    /* renamed from: ၼ */
    @zu2
    public final WindowManager f269;

    /* renamed from: ၽ, reason: from kotlin metadata */
    @zu2
    public final WindowManager.LayoutParams params;

    /* renamed from: ၾ */
    @zu2
    public mg3 f271;

    /* renamed from: ၿ */
    @zu2
    public cw1 f272;

    /* renamed from: ႀ */
    @zu2
    public final ParcelableSnapshotMutableState f273;

    /* renamed from: ႁ */
    @zu2
    public final ParcelableSnapshotMutableState f274;

    /* renamed from: ႎ */
    @wv2
    public k71 f275;

    /* renamed from: Ⴧ */
    @zu2
    public final C3535h8 f276;

    /* renamed from: Ⴭ */
    @zu2
    public final Rect f277;

    /* renamed from: ჽ */
    @zu2
    public final ParcelableSnapshotMutableState f278;

    /* renamed from: ჾ, reason: from kotlin metadata */
    public boolean shouldCreateCompositionOnAttachedToWindow;

    /* renamed from: ჿ */
    @zu2
    public final int[] f280;

    /* renamed from: androidx.compose.ui.window.PopupLayout$Ϳ */
    public static final class C0212 extends ViewOutlineProvider {
        @Override // android.view.ViewOutlineProvider
        public final void getOutline(@zu2 View view, @zu2 Outline outline) {
            fa1.m6826(view, "view");
            fa1.m6826(outline, "result");
            outline.setRect(0, 0, view.getWidth(), view.getHeight());
            outline.setAlpha(0.0f);
        }
    }

    /* renamed from: androidx.compose.ui.window.PopupLayout$Ԩ */
    public static final class C0213 extends lv1 implements di0<InterfaceC6968, Integer, lx5> {

        /* renamed from: ၦ */
        public final /* synthetic */ int f282;

        /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
        public C0213(int i) {
            super(2);
            this.f282 = i;
        }

        @Override // okhttp3.internal.p042io.di0
        /* renamed from: invoke */
        public final lx5 mo18338invoke(InterfaceC6968 interfaceC6968, Integer num) {
            num.intValue();
            PopupLayout.this.Content(interfaceC6968, this.f282 | 1);
            return lx5.f14876;
        }
    }

    /* renamed from: androidx.compose.ui.window.PopupLayout$Ԫ */
    public static final class C0214 extends lv1 implements nh0<Boolean> {
        public C0214() {
            super(0);
        }

        @Override // okhttp3.internal.p042io.nh0
        public final Boolean invoke() {
            return Boolean.valueOf((PopupLayout.this.getParentLayoutCoordinates() == null || PopupLayout.this.m18357getPopupContentSizebOM6tXw() == null) ? false : true);
        }
    }

    /* JADX WARN: Illegal instructions before constructor call */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public PopupLayout(@okhttp3.internal.p042io.wv2 okhttp3.internal.p042io.nh0<okhttp3.internal.p042io.lx5> r8, @okhttp3.internal.p042io.zu2 okhttp3.internal.p042io.ng3 r9, @okhttp3.internal.p042io.zu2 java.lang.String r10, @okhttp3.internal.p042io.zu2 android.view.View r11, @okhttp3.internal.p042io.zu2 okhttp3.internal.p042io.InterfaceC5282u7 r12, @okhttp3.internal.p042io.zu2 okhttp3.internal.p042io.mg3 r13, @okhttp3.internal.p042io.zu2 java.util.UUID r14, @okhttp3.internal.p042io.zu2 okhttp3.internal.p042io.kg3 r15) {
        /*
            Method dump skipped, instructions count: 271
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.compose.p000ui.window.PopupLayout.<init>(okhttp3.internal.io.nh0, okhttp3.internal.io.ng3, java.lang.String, android.view.View, okhttp3.internal.io.u7, okhttp3.internal.io.mg3, java.util.UUID, okhttp3.internal.io.kg3):void");
    }

    /* JADX WARN: Illegal instructions before constructor call */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public /* synthetic */ PopupLayout(okhttp3.internal.p042io.nh0 r11, okhttp3.internal.p042io.ng3 r12, java.lang.String r13, android.view.View r14, okhttp3.internal.p042io.InterfaceC5282u7 r15, okhttp3.internal.p042io.mg3 r16, java.util.UUID r17, okhttp3.internal.p042io.kg3 r18, int r19, okhttp3.internal.p042io.C2849b5 r20) {
        /*
            r10 = this;
            r0 = r19
            r0 = r0 & 128(0x80, float:1.8E-43)
            if (r0 == 0) goto L19
            int r0 = android.os.Build.VERSION.SDK_INT
            r1 = 29
            if (r0 < r1) goto L12
            okhttp3.internal.io.lg3 r0 = new okhttp3.internal.io.lg3
            r0.<init>()
            goto L17
        L12:
            okhttp3.internal.io.ല r0 = new okhttp3.internal.io.ല
            r0.<init>()
        L17:
            r9 = r0
            goto L1b
        L19:
            r9 = r18
        L1b:
            r1 = r10
            r2 = r11
            r3 = r12
            r4 = r13
            r5 = r14
            r6 = r15
            r7 = r16
            r8 = r17
            r1.<init>(r2, r3, r4, r5, r6, r7, r8, r9)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.compose.p000ui.window.PopupLayout.<init>(okhttp3.internal.io.nh0, okhttp3.internal.io.ng3, java.lang.String, android.view.View, okhttp3.internal.io.u7, okhttp3.internal.io.mg3, java.util.UUID, okhttp3.internal.io.kg3, int, okhttp3.internal.io.b5):void");
    }

    private final di0<InterfaceC6968, Integer, lx5> getContent() {
        return (di0) this.f278.getValue();
    }

    private final int getDisplayHeight() {
        return ly3.m9477(getContext().getResources().getConfiguration().screenHeightDp * getContext().getResources().getDisplayMetrics().density);
    }

    private final int getDisplayWidth() {
        return ly3.m9477(getContext().getResources().getConfiguration().screenWidthDp * getContext().getResources().getDisplayMetrics().density);
    }

    @VisibleForTesting(otherwise = 2)
    public static /* synthetic */ void getParams$ui_release$annotations() {
    }

    /* JADX INFO: Access modifiers changed from: private */
    public final bw1 getParentLayoutCoordinates() {
        return (bw1) this.f274.getValue();
    }

    private final void setClippingEnabled(boolean z) {
        m80(z ? this.params.flags & (-513) : this.params.flags | 512);
    }

    private final void setContent(di0<? super InterfaceC6968, ? super Integer, lx5> di0Var) {
        this.f278.setValue(di0Var);
    }

    private final void setIsFocusable(boolean z) {
        m80(!z ? this.params.flags | 8 : this.params.flags & (-9));
    }

    private final void setParentLayoutCoordinates(bw1 bw1Var) {
        this.f274.setValue(bw1Var);
    }

    private final void setSecurePolicy(hi4 hi4Var) {
        m80(ym2.m14324(hi4Var, C6699.m15838(this.f267)) ? this.params.flags | 8192 : this.params.flags & (-8193));
    }

    @Override // androidx.compose.p000ui.platform.AbstractComposeView
    @aw5
    @InterfaceC7452
    public void Content(@wv2 InterfaceC6968 interfaceC6968, int i) {
        InterfaceC6968 mo16280 = interfaceC6968.mo16280(-857613600);
        fi0<InterfaceC6575<?>, lu4, hz3, lx5> fi0Var = C7171.f31108;
        getContent().mo18338invoke(mo16280, 0);
        qb4 mo16286 = mo16280.mo16286();
        if (mo16286 == null) {
            return;
        }
        mo16286.mo5841(new C0213(i));
    }

    public final void dismiss() {
        ViewTreeLifecycleOwner.set(this, null);
        this.f269.removeViewImmediate(this);
    }

    @Override // android.view.ViewGroup, android.view.View
    public boolean dispatchKeyEvent(@zu2 KeyEvent event) {
        KeyEvent.DispatcherState keyDispatcherState;
        fa1.m6826(event, NotificationCompat.CATEGORY_EVENT);
        if (event.getKeyCode() == 4 && this.f265.f16020) {
            if (getKeyDispatcherState() == null) {
                return super.dispatchKeyEvent(event);
            }
            if (event.getAction() == 0 && event.getRepeatCount() == 0) {
                KeyEvent.DispatcherState keyDispatcherState2 = getKeyDispatcherState();
                if (keyDispatcherState2 != null) {
                    keyDispatcherState2.startTracking(event, this);
                }
                return true;
            }
            if (event.getAction() == 1 && (keyDispatcherState = getKeyDispatcherState()) != null && keyDispatcherState.isTracking(event) && !event.isCanceled()) {
                nh0<lx5> nh0Var = this.f264;
                if (nh0Var != null) {
                    nh0Var.invoke();
                }
                return true;
            }
        }
        return super.dispatchKeyEvent(event);
    }

    public final boolean getCanCalculatePosition() {
        return ((Boolean) this.f276.getValue()).booleanValue();
    }

    @zu2
    /* renamed from: getParams$ui_release, reason: from getter */
    public final WindowManager.LayoutParams getParams() {
        return this.params;
    }

    @zu2
    /* renamed from: getParentLayoutDirection, reason: from getter */
    public final cw1 getF272() {
        return this.f272;
    }

    /* JADX WARN: Multi-variable type inference failed */
    @wv2
    /* renamed from: getPopupContentSize-bOM6tXw, reason: not valid java name */
    public final m71 m18357getPopupContentSizebOM6tXw() {
        return (m71) this.f273.getValue();
    }

    @zu2
    /* renamed from: getPositionProvider, reason: from getter */
    public final mg3 getF271() {
        return this.f271;
    }

    @Override // androidx.compose.p000ui.platform.AbstractComposeView
    public boolean getShouldCreateCompositionOnAttachedToWindow() {
        return this.shouldCreateCompositionOnAttachedToWindow;
    }

    @zu2
    public AbstractComposeView getSubCompositionView() {
        return this;
    }

    @zu2
    public final String getTestTag() {
        return this.testTag;
    }

    @wv2
    public /* bridge */ /* synthetic */ View getViewRoot() {
        return null;
    }

    @Override // androidx.compose.p000ui.platform.AbstractComposeView
    public void internalOnLayout$ui_release(boolean changed, int left, int top, int right, int bottom) {
        super.internalOnLayout$ui_release(changed, left, top, right, bottom);
        View childAt = getChildAt(0);
        if (childAt == null) {
            return;
        }
        this.params.width = childAt.getMeasuredWidth();
        this.params.height = childAt.getMeasuredHeight();
        this.f268.mo8949(this.f269, this, this.params);
    }

    @Override // androidx.compose.p000ui.platform.AbstractComposeView
    public void internalOnMeasure$ui_release(int widthMeasureSpec, int heightMeasureSpec) {
        if (!this.f265.f16025) {
            widthMeasureSpec = View.MeasureSpec.makeMeasureSpec(getDisplayWidth(), Integer.MIN_VALUE);
            heightMeasureSpec = View.MeasureSpec.makeMeasureSpec(getDisplayHeight(), Integer.MIN_VALUE);
        }
        super.internalOnMeasure$ui_release(widthMeasureSpec, heightMeasureSpec);
    }

    @Override // android.view.View
    public boolean onTouchEvent(@wv2 MotionEvent event) {
        if (!this.f265.f16021) {
            return super.onTouchEvent(event);
        }
        boolean z = false;
        if ((event != null && event.getAction() == 0) && (event.getX() < 0.0f || event.getX() >= getWidth() || event.getY() < 0.0f || event.getY() >= getHeight())) {
            nh0<lx5> nh0Var = this.f264;
            if (nh0Var != null) {
                nh0Var.invoke();
            }
            return true;
        }
        if (event != null && event.getAction() == 4) {
            z = true;
        }
        if (!z) {
            return super.onTouchEvent(event);
        }
        nh0<lx5> nh0Var2 = this.f264;
        if (nh0Var2 != null) {
            nh0Var2.invoke();
        }
        return true;
    }

    public final void pollForLocationOnScreenChange() {
        int[] iArr = this.f280;
        int i = iArr[0];
        int i2 = iArr[1];
        this.f267.getLocationOnScreen(iArr);
        int[] iArr2 = this.f280;
        if (i == iArr2[0] && i2 == iArr2[1]) {
            return;
        }
        updateParentBounds$ui_release();
    }

    public final void setContent(@zu2 AbstractC6779 parent, @zu2 di0<? super InterfaceC6968, ? super Integer, lx5> content) {
        fa1.m6826(parent, "parent");
        fa1.m6826(content, "content");
        setParentCompositionContext(parent);
        setContent(content);
        this.shouldCreateCompositionOnAttachedToWindow = true;
    }

    @Override // android.view.View
    public void setLayoutDirection(int i) {
    }

    public final void setParentLayoutDirection(@zu2 cw1 cw1Var) {
        fa1.m6826(cw1Var, "<set-?>");
        this.f272 = cw1Var;
    }

    /* renamed from: setPopupContentSize-fhxjrPA, reason: not valid java name */
    public final void m18358setPopupContentSizefhxjrPA(@wv2 m71 m71Var) {
        this.f273.setValue(m71Var);
    }

    public final void setPositionProvider(@zu2 mg3 mg3Var) {
        fa1.m6826(mg3Var, "<set-?>");
        this.f271 = mg3Var;
    }

    public final void setTestTag(@zu2 String str) {
        fa1.m6826(str, "<set-?>");
        this.testTag = str;
    }

    public final void show() {
        this.f269.addView(this, this.params);
    }

    public final void updateParameters(@wv2 nh0<lx5> nh0Var, @zu2 ng3 ng3Var, @zu2 String str, @zu2 cw1 cw1Var) {
        fa1.m6826(ng3Var, "properties");
        fa1.m6826(str, "testTag");
        fa1.m6826(cw1Var, "layoutDirection");
        this.f264 = nh0Var;
        this.f265 = ng3Var;
        this.testTag = str;
        setIsFocusable(ng3Var.f16019);
        setSecurePolicy(ng3Var.f16022);
        setClippingEnabled(ng3Var.f16024);
        int ordinal = cw1Var.ordinal();
        int i = 1;
        if (ordinal == 0) {
            i = 0;
        } else if (ordinal != 1) {
            throw new bt2();
        }
        super.setLayoutDirection(i);
    }

    @VisibleForTesting(otherwise = 2)
    public final void updateParentBounds$ui_release() {
        bw1 parentLayoutCoordinates = getParentLayoutCoordinates();
        if (parentLayoutCoordinates == null) {
            return;
        }
        long mo5344 = parentLayoutCoordinates.mo5344();
        g03.C3393 c3393 = g03.f10466;
        long mo5346 = parentLayoutCoordinates.mo5346(g03.f10467);
        long m11587 = rg0.m11587(ly3.m9477(g03.m7139(mo5346)), ly3.m9477(g03.m7140(mo5346)));
        g71.C3412 c3412 = g71.f10637;
        int i = (int) (m11587 >> 32);
        k71 k71Var = new k71(i, g71.m7254(m11587), ((int) (mo5344 >> 32)) + i, m71.m9614(mo5344) + g71.m7254(m11587));
        if (fa1.m6818(k71Var, this.f275)) {
            return;
        }
        this.f275 = k71Var;
        updatePosition();
    }

    public final void updateParentLayoutCoordinates(@zu2 bw1 bw1Var) {
        fa1.m6826(bw1Var, "parentLayoutCoordinates");
        setParentLayoutCoordinates(bw1Var);
        updateParentBounds$ui_release();
    }

    public final void updatePosition() {
        m71 m18357getPopupContentSizebOM6tXw;
        k71 k71Var = this.f275;
        if (k71Var == null || (m18357getPopupContentSizebOM6tXw = m18357getPopupContentSizebOM6tXw()) == null) {
            return;
        }
        long j = m18357getPopupContentSizebOM6tXw.f15070;
        Rect rect = this.f277;
        this.f268.mo8951(this.f267, rect);
        ho3<String> ho3Var = C6699.f29797;
        long m9931 = n71.m9931(rect.right - rect.left, rect.bottom - rect.top);
        long mo7898 = this.f271.mo7898(k71Var, m9931, this.f272, j);
        WindowManager.LayoutParams layoutParams = this.params;
        g71.C3412 c3412 = g71.f10637;
        layoutParams.x = (int) (mo7898 >> 32);
        layoutParams.y = g71.m7254(mo7898);
        if (this.f265.f16023) {
            this.f268.mo8950(this, (int) (m9931 >> 32), m71.m9614(m9931));
        }
        this.f268.mo8949(this.f269, this, this.params);
    }

    /* renamed from: ԫ */
    public final void m80(int i) {
        WindowManager.LayoutParams layoutParams = this.params;
        layoutParams.flags = i;
        this.f268.mo8949(this.f269, this, layoutParams);
    }
}
