package okhttp3.internal.p042io;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.res.AssetManager;
import android.content.res.Resources;
import android.os.Build;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;

@SuppressLint({"DiscouragedPrivateApi", "PrivateApi"})
/* renamed from: okhttp3.internal.io.ul */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5335ul {

    /* renamed from: Ϳ */
    @zu2
    public final Context f23265;

    /* renamed from: Ԩ */
    @zu2
    public final AssetManager f23266;

    /* renamed from: ԩ */
    @zu2
    public final Resources f23267;

    /* renamed from: Ԫ */
    @zu2
    public final HashSet<String> f23268;

    public C5335ul(@zu2 Context context, @zu2 Resources resources, @zu2 AssetManager assetManager, @zu2 String str) {
        fa1.m6826(context, "context");
        fa1.m6826(str, "apkPath");
        this.f23265 = context;
        this.f23268 = new HashSet<>();
        ApplicationInfo applicationInfo = context.getApplicationInfo();
        Object newInstance = AssetManager.class.newInstance();
        fa1.m6824(newInstance, "null cannot be cast to non-null type android.content.res.AssetManager");
        AssetManager assetManager2 = (AssetManager) newInstance;
        this.f23266 = assetManager2;
        Method declaredMethod = AssetManager.class.getDeclaredMethod("addAssetPath", String.class);
        declaredMethod.invoke(assetManager2, str);
        declaredMethod.invoke(assetManager2, applicationInfo.sourceDir);
        m12900();
        this.f23267 = new Resources(assetManager2, resources.getDisplayMetrics(), resources.getConfiguration());
    }

    /* renamed from: Ϳ */
    public final void m12900() {
        ApplicationInfo applicationInfo = this.f23265.getApplicationInfo();
        if (Build.VERSION.SDK_INT >= 24) {
            String[] strArr = applicationInfo.sharedLibraryFiles;
            if (strArr == null) {
                strArr = new String[0];
            }
            AssetManager assetManager = this.f23266;
            ArrayList arrayList = new ArrayList();
            for (String str : strArr) {
                if (true ^ this.f23268.contains(str)) {
                    arrayList.add(str);
                }
            }
            ArrayList arrayList2 = new ArrayList();
            Iterator it = arrayList.iterator();
            while (it.hasNext()) {
                Object next = it.next();
                if (z45.m14446((String) next, ".apk")) {
                    arrayList2.add(next);
                }
            }
            if (arrayList2.isEmpty()) {
                return;
            }
            Method declaredMethod = AssetManager.class.getDeclaredMethod("addAssetPathAsSharedLibrary", String.class);
            declaredMethod.setAccessible(true);
            Iterator it2 = arrayList2.iterator();
            while (it2.hasNext()) {
                String str2 = (String) it2.next();
                declaredMethod.invoke(assetManager, str2);
                this.f23268.add(str2);
            }
        }
    }
}
