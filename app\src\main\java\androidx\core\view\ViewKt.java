package androidx.core.view;

import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import androidx.annotation.Px;
import androidx.annotation.RequiresApi;
import androidx.autofill.HintConstants;
import androidx.exifinterface.media.ExifInterface;
import java.util.Objects;
import kotlin.Metadata;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.ka1;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.nh0;
import okhttp3.internal.p042io.ph0;
import okhttp3.internal.p042io.pk4;
import okhttp3.internal.p042io.uk4;
import okhttp3.internal.p042io.yo1;
import okhttp3.internal.p042io.zu2;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\n\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0002\b\u0016\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\u001a5\u0010\u0007\u001a\u00020\u0005*\u00020\u00002#\b\u0004\u0010\u0006\u001a\u001d\u0012\u0013\u0012\u00110\u0000¢\u0006\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0004\u0012\u0004\u0012\u00020\u00050\u0001H\u0086\bø\u0001\u0000\u001a5\u0010\b\u001a\u00020\u0005*\u00020\u00002#\b\u0004\u0010\u0006\u001a\u001d\u0012\u0013\u0012\u00110\u0000¢\u0006\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0004\u0012\u0004\u0012\u00020\u00050\u0001H\u0086\bø\u0001\u0000\u001a5\u0010\n\u001a\u00020\t*\u00020\u00002#\b\u0004\u0010\u0006\u001a\u001d\u0012\u0013\u0012\u00110\u0000¢\u0006\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0004\u0012\u0004\u0012\u00020\u00050\u0001H\u0086\bø\u0001\u0000\u001a5\u0010\u000b\u001a\u00020\u0005*\u00020\u00002#\b\u0004\u0010\u0006\u001a\u001d\u0012\u0013\u0012\u00110\u0000¢\u0006\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0004\u0012\u0004\u0012\u00020\u00050\u0001H\u0086\bø\u0001\u0000\u001a5\u0010\f\u001a\u00020\u0005*\u00020\u00002#\b\u0004\u0010\u0006\u001a\u001d\u0012\u0013\u0012\u00110\u0000¢\u0006\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0004\u0012\u0004\u0012\u00020\u00050\u0001H\u0086\bø\u0001\u0000\u001a5\u0010\u0012\u001a\u00020\u0005*\u00020\u00002\b\b\u0003\u0010\u000e\u001a\u00020\r2\b\b\u0003\u0010\u000f\u001a\u00020\r2\b\b\u0003\u0010\u0010\u001a\u00020\r2\b\b\u0003\u0010\u0011\u001a\u00020\rH\u0087\b\u001a5\u0010\u0015\u001a\u00020\u0005*\u00020\u00002\b\b\u0003\u0010\u0013\u001a\u00020\r2\b\b\u0003\u0010\u000f\u001a\u00020\r2\b\b\u0003\u0010\u0014\u001a\u00020\r2\b\b\u0003\u0010\u0011\u001a\u00020\rH\u0086\b\u001a\u0017\u0010\u0017\u001a\u00020\u0005*\u00020\u00002\b\b\u0001\u0010\u0016\u001a\u00020\rH\u0086\b\u001a(\u0010\u001c\u001a\u00020\u001b*\u00020\u00002\u0006\u0010\u0019\u001a\u00020\u00182\u000e\b\u0004\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u001aH\u0086\bø\u0001\u0000\u001a(\u0010\u001d\u001a\u00020\u001b*\u00020\u00002\u0006\u0010\u0019\u001a\u00020\u00182\u000e\b\u0004\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u001aH\u0087\bø\u0001\u0000\u001a\u0014\u0010!\u001a\u00020 *\u00020\u00002\b\b\u0002\u0010\u001f\u001a\u00020\u001e\u001a)\u0010%\u001a\u00020\u0005*\u00020\u00002\u0017\u0010$\u001a\u0013\u0012\u0004\u0012\u00020\"\u0012\u0004\u0012\u00020\u00050\u0001¢\u0006\u0002\b#H\u0086\bø\u0001\u0000\u001a<\u0010%\u001a\u00020\u0005\"\n\b\u0000\u0010&\u0018\u0001*\u00020\"*\u00020\u00002\u0017\u0010$\u001a\u0013\u0012\u0004\u0012\u00028\u0000\u0012\u0004\u0012\u00020\u00050\u0001¢\u0006\u0002\b#H\u0087\bø\u0001\u0000¢\u0006\u0004\b'\u0010(\"*\u0010+\u001a\u00020)*\u00020\u00002\u0006\u0010*\u001a\u00020)8Æ\u0002@Æ\u0002X\u0086\u000e¢\u0006\f\u001a\u0004\b+\u0010,\"\u0004\b-\u0010.\"*\u0010/\u001a\u00020)*\u00020\u00002\u0006\u0010*\u001a\u00020)8Æ\u0002@Æ\u0002X\u0086\u000e¢\u0006\f\u001a\u0004\b/\u0010,\"\u0004\b0\u0010.\"*\u00101\u001a\u00020)*\u00020\u00002\u0006\u0010*\u001a\u00020)8Æ\u0002@Æ\u0002X\u0086\u000e¢\u0006\f\u001a\u0004\b1\u0010,\"\u0004\b2\u0010.\"\u0016\u00105\u001a\u00020\r*\u00020\u00008Æ\u0002¢\u0006\u0006\u001a\u0004\b3\u00104\"\u0016\u00107\u001a\u00020\r*\u00020\u00008Æ\u0002¢\u0006\u0006\u001a\u0004\b6\u00104\"\u0016\u00109\u001a\u00020\r*\u00020\u00008Æ\u0002¢\u0006\u0006\u001a\u0004\b8\u00104\"\u0016\u0010;\u001a\u00020\r*\u00020\u00008Æ\u0002¢\u0006\u0006\u001a\u0004\b:\u00104\"\u0016\u0010=\u001a\u00020\r*\u00020\u00008Æ\u0002¢\u0006\u0006\u001a\u0004\b<\u00104\"\u0016\u0010?\u001a\u00020\r*\u00020\u00008Æ\u0002¢\u0006\u0006\u001a\u0004\b>\u00104\"\u001b\u0010D\u001a\b\u0012\u0004\u0012\u00020A0@*\u00020\u00008F¢\u0006\u0006\u001a\u0004\bB\u0010C\"\u001b\u0010F\u001a\b\u0012\u0004\u0012\u00020\u00000@*\u00020\u00008F¢\u0006\u0006\u001a\u0004\bE\u0010C\u0082\u0002\u0007\n\u0005\b\u009920\u0001¨\u0006G"}, m4115d2 = {"Landroid/view/View;", "Lkotlin/Function1;", "Lokhttp3/internal/io/o73;", HintConstants.AUTOFILL_HINT_NAME, "view", "Lokhttp3/internal/io/lx5;", "action", "doOnNextLayout", "doOnLayout", "Landroidx/core/view/OneShotPreDrawListener;", "doOnPreDraw", "doOnAttach", "doOnDetach", "", "start", "top", "end", "bottom", "updatePaddingRelative", "left", "right", "updatePadding", "size", "setPadding", "", "delayInMillis", "Lkotlin/Function0;", "Ljava/lang/Runnable;", "postDelayed", "postOnAnimationDelayed", "Landroid/graphics/Bitmap$Config;", "config", "Landroid/graphics/Bitmap;", "drawToBitmap", "Landroid/view/ViewGroup$LayoutParams;", "Lokhttp3/internal/io/f00;", "block", "updateLayoutParams", ExifInterface.GPS_DIRECTION_TRUE, "updateLayoutParamsTyped", "(Landroid/view/View;Lokhttp3/internal/io/ph0;)V", "", "value", "isVisible", "(Landroid/view/View;)Z", "setVisible", "(Landroid/view/View;Z)V", "isInvisible", "setInvisible", "isGone", "setGone", "getMarginLeft", "(Landroid/view/View;)I", "marginLeft", "getMarginTop", "marginTop", "getMarginRight", "marginRight", "getMarginBottom", "marginBottom", "getMarginStart", "marginStart", "getMarginEnd", "marginEnd", "Lokhttp3/internal/io/pk4;", "Landroid/view/ViewParent;", "getAncestors", "(Landroid/view/View;)Lokhttp3/internal/io/pk4;", "ancestors", "getAllViews", "allViews", "core-ktx_release"}, m4116k = 2, m4117mv = {1, 6, 0})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class ViewKt {
    public static final void doOnAttach(@zu2 final View view, @zu2 final ph0<? super View, lx5> ph0Var) {
        fa1.m6826(view, "<this>");
        fa1.m6826(ph0Var, "action");
        if (ViewCompat.isAttachedToWindow(view)) {
            ph0Var.invoke(view);
        } else {
            view.addOnAttachStateChangeListener(new View.OnAttachStateChangeListener() { // from class: androidx.core.view.ViewKt$doOnAttach$1
                @Override // android.view.View.OnAttachStateChangeListener
                public void onViewAttachedToWindow(@zu2 View view2) {
                    fa1.m6826(view2, "view");
                    view.removeOnAttachStateChangeListener(this);
                    ph0Var.invoke(view2);
                }

                @Override // android.view.View.OnAttachStateChangeListener
                public void onViewDetachedFromWindow(@zu2 View view2) {
                    fa1.m6826(view2, "view");
                }
            });
        }
    }

    public static final void doOnDetach(@zu2 final View view, @zu2 final ph0<? super View, lx5> ph0Var) {
        fa1.m6826(view, "<this>");
        fa1.m6826(ph0Var, "action");
        if (ViewCompat.isAttachedToWindow(view)) {
            view.addOnAttachStateChangeListener(new View.OnAttachStateChangeListener() { // from class: androidx.core.view.ViewKt$doOnDetach$1
                @Override // android.view.View.OnAttachStateChangeListener
                public void onViewAttachedToWindow(@zu2 View view2) {
                    fa1.m6826(view2, "view");
                }

                @Override // android.view.View.OnAttachStateChangeListener
                public void onViewDetachedFromWindow(@zu2 View view2) {
                    fa1.m6826(view2, "view");
                    view.removeOnAttachStateChangeListener(this);
                    ph0Var.invoke(view2);
                }
            });
        } else {
            ph0Var.invoke(view);
        }
    }

    public static final void doOnLayout(@zu2 View view, @zu2 final ph0<? super View, lx5> ph0Var) {
        fa1.m6826(view, "<this>");
        fa1.m6826(ph0Var, "action");
        if (!ViewCompat.isLaidOut(view) || view.isLayoutRequested()) {
            view.addOnLayoutChangeListener(new View.OnLayoutChangeListener() { // from class: androidx.core.view.ViewKt$doOnLayout$$inlined$doOnNextLayout$1
                @Override // android.view.View.OnLayoutChangeListener
                public void onLayoutChange(@zu2 View view2, int i, int i2, int i3, int i4, int i5, int i6, int i7, int i8) {
                    fa1.m6826(view2, "view");
                    view2.removeOnLayoutChangeListener(this);
                    ph0.this.invoke(view2);
                }
            });
        } else {
            ph0Var.invoke(view);
        }
    }

    public static final void doOnNextLayout(@zu2 View view, @zu2 final ph0<? super View, lx5> ph0Var) {
        fa1.m6826(view, "<this>");
        fa1.m6826(ph0Var, "action");
        view.addOnLayoutChangeListener(new View.OnLayoutChangeListener() { // from class: androidx.core.view.ViewKt$doOnNextLayout$1
            @Override // android.view.View.OnLayoutChangeListener
            public void onLayoutChange(@zu2 View view2, int i, int i2, int i3, int i4, int i5, int i6, int i7, int i8) {
                fa1.m6826(view2, "view");
                view2.removeOnLayoutChangeListener(this);
                ph0Var.invoke(view2);
            }
        });
    }

    @zu2
    public static final OneShotPreDrawListener doOnPreDraw(@zu2 final View view, @zu2 final ph0<? super View, lx5> ph0Var) {
        fa1.m6826(view, "<this>");
        fa1.m6826(ph0Var, "action");
        OneShotPreDrawListener add = OneShotPreDrawListener.add(view, new Runnable() { // from class: androidx.core.view.ViewKt$doOnPreDraw$1
            @Override // java.lang.Runnable
            public final void run() {
                ph0Var.invoke(view);
            }
        });
        fa1.m6825(add, "View.doOnPreDraw(\n    cr…dd(this) { action(this) }");
        return add;
    }

    @zu2
    public static final Bitmap drawToBitmap(@zu2 View view, @zu2 Bitmap.Config config) {
        fa1.m6826(view, "<this>");
        fa1.m6826(config, "config");
        if (!ViewCompat.isLaidOut(view)) {
            throw new IllegalStateException("View needs to be laid out before calling drawToBitmap()");
        }
        Bitmap createBitmap = Bitmap.createBitmap(view.getWidth(), view.getHeight(), config);
        fa1.m6825(createBitmap, "createBitmap(width, height, config)");
        Canvas canvas = new Canvas(createBitmap);
        canvas.translate(-view.getScrollX(), -view.getScrollY());
        view.draw(canvas);
        return createBitmap;
    }

    public static /* synthetic */ Bitmap drawToBitmap$default(View view, Bitmap.Config config, int i, Object obj) {
        if ((i & 1) != 0) {
            config = Bitmap.Config.ARGB_8888;
        }
        return drawToBitmap(view, config);
    }

    @zu2
    public static final pk4<View> getAllViews(@zu2 View view) {
        fa1.m6826(view, "<this>");
        return ka1.m8870(new ViewKt$allViews$1(view, null));
    }

    @zu2
    public static final pk4<ViewParent> getAncestors(@zu2 View view) {
        fa1.m6826(view, "<this>");
        return uk4.m12898(view.getParent(), ViewKt$ancestors$1.INSTANCE);
    }

    public static final int getMarginBottom(@zu2 View view) {
        fa1.m6826(view, "<this>");
        ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
        ViewGroup.MarginLayoutParams marginLayoutParams = layoutParams instanceof ViewGroup.MarginLayoutParams ? (ViewGroup.MarginLayoutParams) layoutParams : null;
        if (marginLayoutParams != null) {
            return marginLayoutParams.bottomMargin;
        }
        return 0;
    }

    public static final int getMarginEnd(@zu2 View view) {
        fa1.m6826(view, "<this>");
        ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
        if (layoutParams instanceof ViewGroup.MarginLayoutParams) {
            return MarginLayoutParamsCompat.getMarginEnd((ViewGroup.MarginLayoutParams) layoutParams);
        }
        return 0;
    }

    public static final int getMarginLeft(@zu2 View view) {
        fa1.m6826(view, "<this>");
        ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
        ViewGroup.MarginLayoutParams marginLayoutParams = layoutParams instanceof ViewGroup.MarginLayoutParams ? (ViewGroup.MarginLayoutParams) layoutParams : null;
        if (marginLayoutParams != null) {
            return marginLayoutParams.leftMargin;
        }
        return 0;
    }

    public static final int getMarginRight(@zu2 View view) {
        fa1.m6826(view, "<this>");
        ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
        ViewGroup.MarginLayoutParams marginLayoutParams = layoutParams instanceof ViewGroup.MarginLayoutParams ? (ViewGroup.MarginLayoutParams) layoutParams : null;
        if (marginLayoutParams != null) {
            return marginLayoutParams.rightMargin;
        }
        return 0;
    }

    public static final int getMarginStart(@zu2 View view) {
        fa1.m6826(view, "<this>");
        ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
        if (layoutParams instanceof ViewGroup.MarginLayoutParams) {
            return MarginLayoutParamsCompat.getMarginStart((ViewGroup.MarginLayoutParams) layoutParams);
        }
        return 0;
    }

    public static final int getMarginTop(@zu2 View view) {
        fa1.m6826(view, "<this>");
        ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
        ViewGroup.MarginLayoutParams marginLayoutParams = layoutParams instanceof ViewGroup.MarginLayoutParams ? (ViewGroup.MarginLayoutParams) layoutParams : null;
        if (marginLayoutParams != null) {
            return marginLayoutParams.topMargin;
        }
        return 0;
    }

    public static final boolean isGone(@zu2 View view) {
        fa1.m6826(view, "<this>");
        return view.getVisibility() == 8;
    }

    public static final boolean isInvisible(@zu2 View view) {
        fa1.m6826(view, "<this>");
        return view.getVisibility() == 4;
    }

    public static final boolean isVisible(@zu2 View view) {
        fa1.m6826(view, "<this>");
        return view.getVisibility() == 0;
    }

    @zu2
    public static final Runnable postDelayed(@zu2 View view, long j, @zu2 final nh0<lx5> nh0Var) {
        fa1.m6826(view, "<this>");
        fa1.m6826(nh0Var, "action");
        Runnable runnable = new Runnable() { // from class: androidx.core.view.ViewKt$postDelayed$runnable$1
            @Override // java.lang.Runnable
            public final void run() {
                nh0Var.invoke();
            }
        };
        view.postDelayed(runnable, j);
        return runnable;
    }

    @RequiresApi(16)
    @zu2
    public static final Runnable postOnAnimationDelayed(@zu2 View view, long j, @zu2 final nh0<lx5> nh0Var) {
        fa1.m6826(view, "<this>");
        fa1.m6826(nh0Var, "action");
        Runnable runnable = new Runnable() { // from class: androidx.core.view.ViewKt$postOnAnimationDelayed$runnable$1
            @Override // java.lang.Runnable
            public final void run() {
                nh0Var.invoke();
            }
        };
        view.postOnAnimationDelayed(runnable, j);
        return runnable;
    }

    public static final void setGone(@zu2 View view, boolean z) {
        fa1.m6826(view, "<this>");
        view.setVisibility(z ? 8 : 0);
    }

    public static final void setInvisible(@zu2 View view, boolean z) {
        fa1.m6826(view, "<this>");
        view.setVisibility(z ? 4 : 0);
    }

    public static final void setPadding(@zu2 View view, @Px int i) {
        fa1.m6826(view, "<this>");
        view.setPadding(i, i, i, i);
    }

    public static final void setVisible(@zu2 View view, boolean z) {
        fa1.m6826(view, "<this>");
        view.setVisibility(z ? 0 : 8);
    }

    public static final void updateLayoutParams(@zu2 View view, @zu2 ph0<? super ViewGroup.LayoutParams, lx5> ph0Var) {
        fa1.m6826(view, "<this>");
        fa1.m6826(ph0Var, "block");
        ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
        Objects.requireNonNull(layoutParams, "null cannot be cast to non-null type android.view.ViewGroup.LayoutParams");
        ph0Var.invoke(layoutParams);
        view.setLayoutParams(layoutParams);
    }

    @yo1
    public static final /* synthetic */ <T extends ViewGroup.LayoutParams> void updateLayoutParamsTyped(View view, ph0<? super T, lx5> ph0Var) {
        fa1.m6826(view, "<this>");
        fa1.m6826(ph0Var, "block");
        view.getLayoutParams();
        fa1.m6844();
        throw null;
    }

    public static final void updatePadding(@zu2 View view, @Px int i, @Px int i2, @Px int i3, @Px int i4) {
        fa1.m6826(view, "<this>");
        view.setPadding(i, i2, i3, i4);
    }

    public static /* synthetic */ void updatePadding$default(View view, int i, int i2, int i3, int i4, int i5, Object obj) {
        if ((i5 & 1) != 0) {
            i = view.getPaddingLeft();
        }
        if ((i5 & 2) != 0) {
            i2 = view.getPaddingTop();
        }
        if ((i5 & 4) != 0) {
            i3 = view.getPaddingRight();
        }
        if ((i5 & 8) != 0) {
            i4 = view.getPaddingBottom();
        }
        fa1.m6826(view, "<this>");
        view.setPadding(i, i2, i3, i4);
    }

    @RequiresApi(17)
    public static final void updatePaddingRelative(@zu2 View view, @Px int i, @Px int i2, @Px int i3, @Px int i4) {
        fa1.m6826(view, "<this>");
        view.setPaddingRelative(i, i2, i3, i4);
    }

    public static /* synthetic */ void updatePaddingRelative$default(View view, int i, int i2, int i3, int i4, int i5, Object obj) {
        if ((i5 & 1) != 0) {
            i = view.getPaddingStart();
        }
        if ((i5 & 2) != 0) {
            i2 = view.getPaddingTop();
        }
        if ((i5 & 4) != 0) {
            i3 = view.getPaddingEnd();
        }
        if ((i5 & 8) != 0) {
            i4 = view.getPaddingBottom();
        }
        fa1.m6826(view, "<this>");
        view.setPaddingRelative(i, i2, i3, i4);
    }
}
