package androidx.core.view;

import android.view.View;
import androidx.annotation.NonNull;

/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public class ViewPropertyAnimatorListenerAdapter implements ViewPropertyAnimatorListener {
    @Override // androidx.core.view.ViewPropertyAnimatorListener
    public void onAnimationCancel(@NonNull View view) {
    }

    @Override // androidx.core.view.ViewPropertyAnimatorListener
    public void onAnimationEnd(@NonNull View view) {
    }

    @Override // androidx.core.view.ViewPropertyAnimatorListener
    public void onAnimationStart(@NonNull View view) {
    }
}
