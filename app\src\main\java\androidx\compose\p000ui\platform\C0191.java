package androidx.compose.p000ui.platform;

import android.view.View;
import java.util.Set;
import okhttp3.internal.p042io.C7171;
import okhttp3.internal.p042io.C7352;
import okhttp3.internal.p042io.C7771;
import okhttp3.internal.p042io.InterfaceC6575;
import okhttp3.internal.p042io.InterfaceC6968;
import okhttp3.internal.p042io.InterfaceC7030;
import okhttp3.internal.p042io.di0;
import okhttp3.internal.p042io.f51;
import okhttp3.internal.p042io.fi0;
import okhttp3.internal.p042io.hz3;
import okhttp3.internal.p042io.jo3;
import okhttp3.internal.p042io.lu4;
import okhttp3.internal.p042io.lv1;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.mr1;
import okhttp3.internal.p042io.ns2;
import okhttp3.internal.p042io.op3;
import okhttp3.internal.p042io.xq1;

/* renamed from: androidx.compose.ui.platform.ؠ */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class C0191 extends lv1 implements di0<InterfaceC6968, Integer, lx5> {

    /* renamed from: ၥ */
    public final /* synthetic */ WrappedComposition f192;

    /* renamed from: ၦ */
    public final /* synthetic */ di0<InterfaceC6968, Integer, lx5> f193;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    /* JADX WARN: Multi-variable type inference failed */
    public C0191(WrappedComposition wrappedComposition, di0<? super InterfaceC6968, ? super Integer, lx5> di0Var) {
        super(2);
        this.f192 = wrappedComposition;
        this.f193 = di0Var;
    }

    @Override // okhttp3.internal.p042io.di0
    /* renamed from: invoke */
    public final lx5 mo18338invoke(InterfaceC6968 interfaceC6968, Integer num) {
        InterfaceC6968 interfaceC69682 = interfaceC6968;
        if ((num.intValue() & 11) == 2 && interfaceC69682.mo16283()) {
            interfaceC69682.mo16290();
        } else {
            fi0<InterfaceC6575<?>, lu4, hz3, lx5> fi0Var = C7171.f31108;
            AndroidComposeView androidComposeView = this.f192.f175;
            int i = op3.inspection_slot_table_set;
            Object tag = androidComposeView.getTag(i);
            Set<InterfaceC7030> set = (tag instanceof Set) && (!(tag instanceof xq1) || (tag instanceof mr1)) ? (Set) tag : null;
            if (set == null) {
                Object parent = this.f192.f175.getParent();
                View view = parent instanceof View ? (View) parent : null;
                Object tag2 = view != null ? view.getTag(i) : null;
                set = (tag2 instanceof Set) && (!(tag2 instanceof xq1) || (tag2 instanceof mr1)) ? (Set) tag2 : null;
            }
            if (set != null) {
                set.add(interfaceC69682.mo16274());
                interfaceC69682.mo16264();
            }
            WrappedComposition wrappedComposition = this.f192;
            C7352.m16926(wrappedComposition.f175, new C0188(wrappedComposition, null), interfaceC69682);
            WrappedComposition wrappedComposition2 = this.f192;
            C7352.m16926(wrappedComposition2.f175, new C0189(wrappedComposition2, null), interfaceC69682);
            C7771.m17399(new jo3[]{f51.f9775.m7927(set)}, ns2.m10131(interfaceC69682, -1193460702, new C0190(this.f192, this.f193)), interfaceC69682, 56);
        }
        return lx5.f14876;
    }
}
