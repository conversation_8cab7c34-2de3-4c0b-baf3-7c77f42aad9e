package androidx.datastore.core;

import androidx.autofill.HintConstants;
import androidx.exifinterface.media.ExifInterface;
import com.stardust.autojs.engine.RhinoJavaScriptEngine;
import java.util.List;
import kotlin.Metadata;
import okhttp3.internal.p042io.C2849b5;
import okhttp3.internal.p042io.InterfaceC7155;
import okhttp3.internal.p042io.di0;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.zu2;
import org.mozilla.javascript.optimizer.Codegen;

@Metadata(m4114d1 = {"\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0003\b\u0000\u0018\u0000 \u0004*\u0004\b\u0000\u0010\u00012\u00020\u0002:\u0001\u0004B\u0005¢\u0006\u0002\u0010\u0003¨\u0006\u0005"}, m4115d2 = {"Landroidx/datastore/core/DataMigrationInitializer;", ExifInterface.GPS_DIRECTION_TRUE, "", Codegen.TEMPLATE_LITERAL_INIT_METHOD_SIGNATURE, "Companion", "datastore-core"}, m4116k = 1, m4117mv = {1, 5, 1}, m4119xi = 48)
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class DataMigrationInitializer<T> {

    /* renamed from: Companion, reason: from kotlin metadata */
    @zu2
    public static final Companion INSTANCE = new Companion(null);

    @Metadata(m4113bv = {}, m4114d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0011\u0010\u0012J;\u0010\t\u001a\u00020\b\"\u0004\b\u0001\u0010\u00022\u0012\u0010\u0005\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00028\u00010\u00040\u00032\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00028\u00010\u0006H\u0082@ø\u0001\u0000¢\u0006\u0004\b\t\u0010\nJ[\u0010\u000f\u001a3\b\u0001\u0012\u0019\u0012\u0017\u0012\u0004\u0012\u00028\u00010\u0006¢\u0006\f\b\f\u0012\b\b\r\u0012\u0004\b\b(\u0007\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u000e\u0012\u0006\u0012\u0004\u0018\u00010\u00010\u000b\"\u0004\b\u0001\u0010\u00022\u0012\u0010\u0005\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00028\u00010\u00040\u0003ø\u0001\u0000¢\u0006\u0004\b\u000f\u0010\u0010\u0082\u0002\u0004\n\u0002\b\u0019¨\u0006\u0013"}, m4115d2 = {"Landroidx/datastore/core/DataMigrationInitializer$Companion;", "", ExifInterface.GPS_DIRECTION_TRUE, "", "Landroidx/datastore/core/DataMigration;", "migrations", "Landroidx/datastore/core/InitializerApi;", "api", "Lokhttp3/internal/io/lx5;", "runMigrations", "(Ljava/util/List;Landroidx/datastore/core/InitializerApi;Lokhttp3/internal/io/ৡ;)Ljava/lang/Object;", "Lkotlin/Function2;", "Lokhttp3/internal/io/o73;", HintConstants.AUTOFILL_HINT_NAME, "Lokhttp3/internal/io/ৡ;", "getInitializer", "(Ljava/util/List;)Lokhttp3/internal/io/di0;", RhinoJavaScriptEngine.SOURCE_NAME_INIT, Codegen.TEMPLATE_LITERAL_INIT_METHOD_SIGNATURE, "datastore-core"}, m4116k = 1, m4117mv = {1, 5, 1})
    public static final class Companion {
        private Companion() {
        }

        public /* synthetic */ Companion(C2849b5 c2849b5) {
            this();
        }

        /* JADX INFO: Access modifiers changed from: private */
        /* JADX WARN: Removed duplicated region for block: B:16:0x006d  */
        /* JADX WARN: Removed duplicated region for block: B:27:0x0098  */
        /* JADX WARN: Removed duplicated region for block: B:29:0x009b  */
        /* JADX WARN: Removed duplicated region for block: B:39:0x0044  */
        /* JADX WARN: Removed duplicated region for block: B:8:0x0022  */
        /* JADX WARN: Type inference failed for: r9v3, types: [T, java.lang.Throwable] */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:29:0x0084 -> B:13:0x0067). Please report as a decompilation issue!!! */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:30:0x0087 -> B:13:0x0067). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct code enable 'Show inconsistent code' option in preferences
        */
        public final <T> java.lang.Object runMigrations(java.util.List<? extends androidx.datastore.core.DataMigration<T>> r7, androidx.datastore.core.InitializerApi<T> r8, okhttp3.internal.p042io.InterfaceC7155<? super okhttp3.internal.p042io.lx5> r9) {
            /*
                r6 = this;
                boolean r0 = r9 instanceof androidx.datastore.core.DataMigrationInitializer$Companion$runMigrations$1
                if (r0 == 0) goto L13
                r0 = r9
                androidx.datastore.core.DataMigrationInitializer$Companion$runMigrations$1 r0 = (androidx.datastore.core.DataMigrationInitializer$Companion$runMigrations$1) r0
                int r1 = r0.label
                r2 = -2147483648(0xffffffff80000000, float:-0.0)
                r3 = r1 & r2
                if (r3 == 0) goto L13
                int r1 = r1 - r2
                r0.label = r1
                goto L18
            L13:
                androidx.datastore.core.DataMigrationInitializer$Companion$runMigrations$1 r0 = new androidx.datastore.core.DataMigrationInitializer$Companion$runMigrations$1
                r0.<init>(r6, r9)
            L18:
                java.lang.Object r9 = r0.result
                okhttp3.internal.io.ஐ r1 = okhttp3.internal.p042io.EnumC7329.COROUTINE_SUSPENDED
                int r2 = r0.label
                r3 = 2
                r4 = 1
                if (r2 == 0) goto L44
                if (r2 == r4) goto L3c
                if (r2 != r3) goto L34
                java.lang.Object r7 = r0.L$1
                java.util.Iterator r7 = (java.util.Iterator) r7
                java.lang.Object r8 = r0.L$0
                okhttp3.internal.io.xv3 r8 = (okhttp3.internal.p042io.xv3) r8
                okhttp3.internal.p042io.C4350o9.m10270(r9)     // Catch: java.lang.Throwable -> L32
                goto L67
            L32:
                r9 = move-exception
                goto L80
            L34:
                java.lang.IllegalStateException r7 = new java.lang.IllegalStateException
                java.lang.String r8 = "call to 'resume' before 'invoke' with coroutine"
                r7.<init>(r8)
                throw r7
            L3c:
                java.lang.Object r7 = r0.L$0
                java.util.List r7 = (java.util.List) r7
                okhttp3.internal.p042io.C4350o9.m10270(r9)
                goto L5e
            L44:
                okhttp3.internal.p042io.C4350o9.m10270(r9)
                java.util.ArrayList r9 = new java.util.ArrayList
                r9.<init>()
                androidx.datastore.core.DataMigrationInitializer$Companion$runMigrations$2 r2 = new androidx.datastore.core.DataMigrationInitializer$Companion$runMigrations$2
                r5 = 0
                r2.<init>(r7, r9, r5)
                r0.L$0 = r9
                r0.label = r4
                java.lang.Object r7 = r8.updateData(r2, r0)
                if (r7 != r1) goto L5d
                return r1
            L5d:
                r7 = r9
            L5e:
                okhttp3.internal.io.xv3 r8 = new okhttp3.internal.io.xv3
                r8.<init>()
                java.util.Iterator r7 = r7.iterator()
            L67:
                boolean r9 = r7.hasNext()
                if (r9 == 0) goto L92
                java.lang.Object r9 = r7.next()
                okhttp3.internal.io.ph0 r9 = (okhttp3.internal.p042io.ph0) r9
                r0.L$0 = r8     // Catch: java.lang.Throwable -> L32
                r0.L$1 = r7     // Catch: java.lang.Throwable -> L32
                r0.label = r3     // Catch: java.lang.Throwable -> L32
                java.lang.Object r9 = r9.invoke(r0)     // Catch: java.lang.Throwable -> L32
                if (r9 != r1) goto L67
                return r1
            L80:
                T r2 = r8.f26199
                if (r2 != 0) goto L87
                r8.f26199 = r9
                goto L67
            L87:
                okhttp3.internal.p042io.fa1.m6823(r2)
                T r2 = r8.f26199
                java.lang.Throwable r2 = (java.lang.Throwable) r2
                okhttp3.internal.p042io.ly3.m9475(r2, r9)
                goto L67
            L92:
                T r7 = r8.f26199
                java.lang.Throwable r7 = (java.lang.Throwable) r7
                if (r7 != 0) goto L9b
                okhttp3.internal.io.lx5 r7 = okhttp3.internal.p042io.lx5.f14876
                return r7
            L9b:
                throw r7
            */
            throw new UnsupportedOperationException("Method not decompiled: androidx.datastore.core.DataMigrationInitializer.Companion.runMigrations(java.util.List, androidx.datastore.core.InitializerApi, okhttp3.internal.io.ৡ):java.lang.Object");
        }

        @zu2
        public final <T> di0<InitializerApi<T>, InterfaceC7155<? super lx5>, Object> getInitializer(@zu2 List<? extends DataMigration<T>> migrations) {
            fa1.m6826(migrations, "migrations");
            return new DataMigrationInitializer$Companion$getInitializer$1(migrations, null);
        }
    }
}
