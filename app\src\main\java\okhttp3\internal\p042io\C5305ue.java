package okhttp3.internal.p042io;

import com.google.common.collect.AbstractC1588;
import com.google.common.collect.AbstractC1592;
import com.google.common.collect.AbstractC1595;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import okhttp3.internal.p042io.AbstractC5189tf;
import okhttp3.internal.p042io.C7109;
import okhttp3.internal.p042io.dd1;

/* renamed from: okhttp3.internal.io.ue */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5305ue extends AbstractC5189tf<C6165, C6165, C6169, C6169, C6142, C7658, C7182, C6251, C6614, C7662, <PERSON>7505, <PERSON>7102, <PERSON><PERSON>27, C6796, C7663, <PERSON><PERSON>26, <PERSON>face<PERSON>6028, C6742, C6503, C6513, C7116, C6114, C7109, C7676, C7404, C6159, C7458, C7240, C6835, C6133> {

    /* renamed from: okhttp3.internal.io.ue$Ϳ, reason: contains not printable characters */
    public class C9403 extends AbstractC5189tf<C6165, C6165, C6169, C6169, C6142, C7658, C7182, C6251, C6614, C7662, C7505, C7102, C7027, C6796, C7663, C7026, InterfaceC6028, C6742, C6503, C6513, C7116, C6114, C7109, C7676, C7404, C6159, C7458, C7240, C6835, C6133>.AbstractC9356 {
        public C9403() {
        }

        @Override // okhttp3.internal.p042io.AbstractC5189tf.AbstractC9356
        @pu2
        /* renamed from: Ϳ */
        public final C7240 mo10725() {
            return new C7240(C5305ue.this);
        }

        @Override // okhttp3.internal.p042io.AbstractC5189tf.AbstractC9356
        @pu2
        /* renamed from: Ԩ */
        public final C6835 mo10726() {
            return new C6835(C5305ue.this);
        }

        @Override // okhttp3.internal.p042io.AbstractC5189tf.AbstractC9356
        @pu2
        /* renamed from: ԩ */
        public final C7404 mo10727() {
            return new C7404(C5305ue.this);
        }

        @Override // okhttp3.internal.p042io.AbstractC5189tf.AbstractC9356
        @pu2
        /* renamed from: Ԫ */
        public final C7676 mo10728() {
            return new C7676(C5305ue.this);
        }

        @Override // okhttp3.internal.p042io.AbstractC5189tf.AbstractC9356
        @pu2
        /* renamed from: ԫ */
        public final C6133 mo10729() {
            return new C6133(C5305ue.this);
        }

        @Override // okhttp3.internal.p042io.AbstractC5189tf.AbstractC9356
        @pu2
        /* renamed from: Ԭ */
        public final C6114 mo10730() {
            return new C6114(C5305ue.this);
        }

        @Override // okhttp3.internal.p042io.AbstractC5189tf.AbstractC9356
        @pu2
        /* renamed from: ԭ */
        public final C6159 mo10731() {
            return new C6159(C5305ue.this);
        }

        @Override // okhttp3.internal.p042io.AbstractC5189tf.AbstractC9356
        @pu2
        /* renamed from: Ԯ */
        public final C7109 mo10732() {
            return new C7109(C5305ue.this);
        }

        @Override // okhttp3.internal.p042io.AbstractC5189tf.AbstractC9356
        @pu2
        /* renamed from: ԯ */
        public final C7116 mo10733() {
            return new C7116(C5305ue.this);
        }

        @Override // okhttp3.internal.p042io.AbstractC5189tf.AbstractC9356
        @pu2
        /* renamed from: ֏ */
        public final C6503 mo10734() {
            return new C6503();
        }

        @Override // okhttp3.internal.p042io.AbstractC5189tf.AbstractC9356
        @pu2
        /* renamed from: ؠ */
        public final C7458 mo10735() {
            return new C7458(C5305ue.this);
        }

        @Override // okhttp3.internal.p042io.AbstractC5189tf.AbstractC9356
        @pu2
        /* renamed from: ހ */
        public final C6513 mo10736() {
            return new C6513(C5305ue.this);
        }
    }

    public C5305ue(@pu2 u13 u13Var) {
        super(u13Var);
    }

    @Override // okhttp3.internal.p042io.AbstractC5189tf
    @pu2
    /* renamed from: Ԩ */
    public final AbstractC5189tf<C6165, C6165, C6169, C6169, C6142, C7658, C7182, C6251, C6614, C7662, C7505, C7102, C7027, C6796, C7663, C7026, InterfaceC6028, C6742, C6503, C6513, C7116, C6114, C7109, C7676, C7404, C6159, C7458, C7240, C6835, C6133>.AbstractC9356 mo10722() {
        return new C9403();
    }

    @Override // okhttp3.internal.p042io.AbstractC5189tf
    /* renamed from: އ */
    public final void mo10723(@pu2 AbstractC5189tf.C9354 c9354, @pu2 InterfaceC6028 interfaceC6028) {
        InterfaceC6028 interfaceC60282 = interfaceC6028;
        int mo4973 = interfaceC60282.mo4973();
        if (mo4973 == 0) {
            c9354.f24405.m13562(0, ((InterfaceC6512) interfaceC60282).getValue());
            return;
        }
        if (mo4973 == 6) {
            c9354.m13330(((y82) interfaceC60282).getValue());
            return;
        }
        if (mo4973 == 2) {
            c9354.f24405.m13562(2, ((bp4) interfaceC60282).getValue());
            return;
        }
        if (mo4973 == 3) {
            c9354.f24405.m13563(3, ((InterfaceC6730) interfaceC60282).getValue());
            return;
        }
        if (mo4973 == 4) {
            c9354.f24405.m13562(4, ((a71) interfaceC60282).getValue());
            return;
        }
        if (mo4973 == 16) {
            c9354.m13329(((g70) interfaceC60282).getValue());
            return;
        }
        if (mo4973 == 17) {
            c9354.m13328(((InterfaceC4707qi) interfaceC60282).getValue());
            return;
        }
        switch (mo4973) {
            case 21:
                c9354.f24405.m13563(21, c9354.f24410.mo9913(((C6177) interfaceC60282).f28270));
                return;
            case 22:
                c9354.f24405.m13563(22, c9354.f24411.mo9913(((C6054) interfaceC60282).f27966));
                return;
            case 23:
                c9354.f24405.m13563(23, c9354.f24406.mo9913(((C6029) interfaceC60282).f27841));
                return;
            case 24:
                c9354.f24405.m13563(24, c9354.f24407.mo9913(((C7294) interfaceC60282).f31497));
                return;
            case 25:
                c9354.f24405.m13563(25, c9354.f24408.mo9913(((C6679) interfaceC60282).f29749));
                return;
            case 26:
                c9354.f24405.m13563(26, c9354.f24409.mo9913(((C6194) interfaceC60282).f28298));
                return;
            case 27:
                c9354.f24405.m13563(27, c9354.f24408.mo9913(((C7301) interfaceC60282).f31518));
                return;
            case 28:
                c9354.m13327(((C7026) interfaceC60282).f30713);
                return;
            case 29:
                C7851 c7851 = (C7851) interfaceC60282;
                c9354.m13326(c7851.f33130, c7851.f33131);
                return;
            case 30:
                c9354.f24405.write(30);
                return;
            case 31:
                c9354.f24405.m13564(31, ((InterfaceC7132) interfaceC60282).getValue() ? 1 : 0);
                return;
            default:
                throw new C3261ew(null, "Unrecognized value type: %d", Integer.valueOf(interfaceC60282.mo4973()));
        }
    }

    /* JADX WARN: Type inference failed for: r1v0, types: [j$.util.concurrent.ConcurrentHashMap, java.util.concurrent.ConcurrentMap<okhttp3.internal.io.ଢ, okhttp3.internal.io.צ>] */
    /* JADX WARN: Type inference failed for: r4v2, types: [j$.util.concurrent.ConcurrentHashMap, java.util.concurrent.ConcurrentMap<okhttp3.internal.io.ଢ, okhttp3.internal.io.צ>] */
    /* renamed from: ޔ */
    public final C6614 m12846(@pu2 InterfaceC7292 interfaceC7292) {
        C7404 c7404 = (C7404) this.f22163;
        C6614 c6614 = (C6614) c7404.f31778.get(interfaceC7292);
        if (c6614 != null) {
            return c6614;
        }
        C6614 c66142 = new C6614(interfaceC7292.getName(), ((C6133) c7404.f28131.f22168).m14893(new C7069(interfaceC7292)));
        C6614 c66143 = (C6614) c7404.f31778.putIfAbsent(c66142, c66142);
        return c66143 == null ? c66142 : c66143;
    }

    /* JADX WARN: Type inference failed for: r1v9, types: [j$.util.concurrent.ConcurrentHashMap, java.util.concurrent.ConcurrentMap<java.lang.String, okhttp3.internal.io.ʄ>] */
    @pu2
    /* renamed from: ޕ */
    public final C6251 m12847(@pu2 String str, int i, @vv2 String str2, @vv2 List<String> list, @vv2 String str3, @pu2 Set<? extends InterfaceC6280> set, @vv2 Iterable<? extends C6796> iterable, @vv2 Iterable<? extends C7663> iterable2) {
        List<String> list2 = list;
        if (list2 == null) {
            AbstractC7161 abstractC7161 = AbstractC1588.f2751;
            list2 = zy3.f27827;
        } else {
            HashSet hashSet = new HashSet(list2);
            Iterator<String> it = list.iterator();
            while (it.hasNext()) {
                String next = it.next();
                if (hashSet.contains(next)) {
                    hashSet.remove(next);
                } else {
                    it.remove();
                }
            }
        }
        AbstractC1595 m1541 = AbstractC1595.m1541(zc1.m14491(iterable, n20.f15723));
        AbstractC1595 m15412 = AbstractC1595.m1541(zc1.m14491(iterable, n20.f15724));
        InterfaceC6034 m13795 = x05.m13795(m1541);
        C7026 m14893 = m13795 != null ? ((C6133) this.f22168).m14893(m13795) : null;
        C7676 c7676 = (C7676) this.f22162;
        C6169 m15499 = ((C6513) this.f22158).m15499(str);
        C6513 c6513 = (C6513) this.f22158;
        Objects.requireNonNull(c6513);
        C6169 m154992 = str2 == null ? null : c6513.m15499(str2);
        C7027 m17052 = ((C7458) this.f22165).m17052(list2);
        C6503 c6503 = (C6503) this.f22157;
        Objects.requireNonNull(c6503);
        C6251 c6251 = new C6251(m15499, i, m154992, m17052, str3 == null ? null : c6503.m15478(str3), ((C6835) this.f22167).m16068(set), m1541, m15412, iterable2, m14893);
        if (((C6251) c7676.f32610.put(c6251.getType(), c6251)) == null) {
            return c6251;
        }
        throw new C3261ew(null, "Class %s has already been interned", c6251.getType());
    }

    @pu2
    /* renamed from: ޖ */
    public final InterfaceC6028 m12848(@pu2 InterfaceC4949rr interfaceC4949rr) {
        int mo4973 = interfaceC4949rr.mo4973();
        if (mo4973 == 0) {
            return new C7196(((InterfaceC6512) interfaceC4949rr).getValue());
        }
        if (mo4973 == 6) {
            return new C7344(((y82) interfaceC4949rr).getValue());
        }
        if (mo4973 == 2) {
            return new C6481(((bp4) interfaceC4949rr).getValue());
        }
        if (mo4973 == 3) {
            return new C6647(((InterfaceC6730) interfaceC4949rr).getValue());
        }
        if (mo4973 == 4) {
            return new C6272(((a71) interfaceC4949rr).getValue());
        }
        if (mo4973 == 16) {
            return new C6834(((g70) interfaceC4949rr).getValue());
        }
        if (mo4973 == 17) {
            return new C6635(((InterfaceC4707qi) interfaceC4949rr).getValue());
        }
        switch (mo4973) {
            case 21:
                return new C6177(((C7116) this.f22159).m16507(((bj2) interfaceC4949rr).getValue()));
            case 22:
                return new C6054(((C6159) this.f22164).m14934(((ei2) interfaceC4949rr).getValue()));
            case 23:
                return new C6029(((C6503) this.f22157).m15478(((y35) interfaceC4949rr).getValue()));
            case 24:
                return new C7294(((C6513) this.f22158).m15499(((xr5) interfaceC4949rr).getValue()));
            case 25:
                return new C6679(((C6114) this.f22160).m14858(((u10) interfaceC4949rr).getValue()));
            case 26:
                return new C6194(((C7109) this.f22161).m16478(((di2) interfaceC4949rr).getValue()));
            case 27:
                return new C7301(((C6114) this.f22160).m14858(((InterfaceC3771it) interfaceC4949rr).getValue()));
            case 28:
                return new C7026(AbstractC1588.m1508(new dd1.C3116(((InterfaceC6034) interfaceC4949rr).getValue().iterator(), new C5182te(this))));
            case 29:
                InterfaceC7239 interfaceC7239 = (InterfaceC7239) interfaceC4949rr;
                return new C7851(((C6513) this.f22158).m15499(interfaceC7239.getType()), AbstractC1592.m1532(new dd1.C3116(interfaceC7239.getElements().iterator(), new C5055se(this))));
            case 30:
                return C7423.f31822;
            case 31:
                return ((InterfaceC7132) interfaceC4949rr).getValue() ? C7455.f31908 : C7455.f31909;
            default:
                throw new C3261ew(null, "Unexpected encoded value type: %d", Integer.valueOf(interfaceC4949rr.mo4973()));
        }
    }

    @pu2
    /* renamed from: ޗ */
    public final C6796 m12849(@pu2 String str, @pu2 String str2, @pu2 String str3, int i, @vv2 InterfaceC4949rr interfaceC4949rr, @pu2 Set<? extends InterfaceC6280> set, @pu2 Set<yr0> set2) {
        C6114 c6114 = (C6114) this.f22160;
        Objects.requireNonNull(c6114);
        return new C6796(c6114.m14858(new a11(str, str2, str3)), i, interfaceC4949rr == null ? null : m12848(interfaceC4949rr), ((C6835) this.f22167).m16068(set), set2);
    }

    @pu2
    /* renamed from: ޘ */
    public final C7658 m12850(@pu2 j20 j20Var) {
        return ((C6114) this.f22160).m14858(j20Var);
    }

    @pu2
    /* renamed from: ޙ */
    public final C7663 m12851(@pu2 String str, @pu2 String str2, @vv2 List<? extends si2> list, @pu2 String str3, int i, @pu2 Set<? extends InterfaceC6280> set, @pu2 Set<yr0> set2, @vv2 mi2 mi2Var) {
        List<? extends si2> list2;
        AbstractC1588<Object> m1508;
        if (list == null) {
            AbstractC7161 abstractC7161 = AbstractC1588.f2751;
            list2 = zy3.f27827;
        } else {
            list2 = list;
        }
        C7109 c7109 = (C7109) this.f22161;
        Objects.requireNonNull(c7109);
        C7182 m16478 = c7109.m16478(new C7109.C10016(str, str2, list2, str3));
        if (list2 == null) {
            AbstractC7161 abstractC71612 = AbstractC1588.f2751;
            m1508 = zy3.f27827;
        } else {
            m1508 = AbstractC1588.m1508(new dd1.C3116(list2.iterator(), new C4912re(this)));
        }
        return new C7663(m16478, m1508, i, ((C6835) this.f22167).m16068(set), set2, mi2Var);
    }

    @pu2
    /* renamed from: ޚ */
    public final C6142 m12852(@pu2 wi2 wi2Var) {
        return ((C7116) this.f22159).m16507(wi2Var);
    }

    @pu2
    /* renamed from: ޛ */
    public final C7182 m12853(@pu2 xi2 xi2Var) {
        return ((C7109) this.f22161).m16478(xi2Var);
    }

    @vv2
    /* renamed from: ޜ */
    public final C6165 m12854(@vv2 String str) {
        if (str != null) {
            return m12856(str);
        }
        return null;
    }

    @pu2
    /* renamed from: ޝ */
    public final InterfaceC6449 m12855(@pu2 aw3 aw3Var) {
        if (aw3Var instanceof h45) {
            return m12856(((h45) aw3Var).getString());
        }
        if (aw3Var instanceof lt5) {
            return m12857(((lt5) aw3Var).getType());
        }
        if (aw3Var instanceof xi2) {
            return m12853((xi2) aw3Var);
        }
        if (aw3Var instanceof j20) {
            return m12850((j20) aw3Var);
        }
        if (aw3Var instanceof wi2) {
            return m12852((wi2) aw3Var);
        }
        if (aw3Var instanceof InterfaceC7292) {
            return m12846((InterfaceC7292) aw3Var);
        }
        if (!(aw3Var instanceof gi2)) {
            throw new IllegalArgumentException("Could not determine type of reference");
        }
        return ((C6159) this.f22164).m14934((gi2) aw3Var);
    }

    @pu2
    /* renamed from: ޞ */
    public final C6165 m12856(@pu2 String str) {
        return ((C6503) this.f22157).m15478(str);
    }

    @pu2
    /* renamed from: ޟ */
    public final C6169 m12857(@pu2 String str) {
        return ((C6513) this.f22158).m15499(str);
    }
}
