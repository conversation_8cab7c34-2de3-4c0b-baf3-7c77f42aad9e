package androidx.core.view;

import android.view.Menu;
import android.view.MenuItem;
import androidx.autofill.HintConstants;
import java.util.Iterator;
import kotlin.Metadata;
import okhttp3.internal.p042io.di0;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.ph0;
import okhttp3.internal.p042io.pk4;
import okhttp3.internal.p042io.zu2;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000B\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010)\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\u001a\u0015\u0010\u0004\u001a\u00020\u0003*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u0001H\u0086\n\u001a\u0015\u0010\u0007\u001a\u00020\u0006*\u00020\u00002\u0006\u0010\u0005\u001a\u00020\u0003H\u0086\u0002\u001a\u0015\u0010\t\u001a\u00020\b*\u00020\u00002\u0006\u0010\u0005\u001a\u00020\u0003H\u0086\n\u001a\r\u0010\n\u001a\u00020\u0006*\u00020\u0000H\u0086\b\u001a\r\u0010\u000b\u001a\u00020\u0006*\u00020\u0000H\u0086\b\u001a3\u0010\u0010\u001a\u00020\b*\u00020\u00002!\u0010\u000f\u001a\u001d\u0012\u0013\u0012\u00110\u0003¢\u0006\f\b\r\u0012\b\b\u000e\u0012\u0004\b\b(\u0005\u0012\u0004\u0012\u00020\b0\fH\u0086\bø\u0001\u0000\u001aH\u0010\u0012\u001a\u00020\b*\u00020\u000026\u0010\u000f\u001a2\u0012\u0013\u0012\u00110\u0001¢\u0006\f\b\r\u0012\b\b\u000e\u0012\u0004\b\b(\u0002\u0012\u0013\u0012\u00110\u0003¢\u0006\f\b\r\u0012\b\b\u000e\u0012\u0004\b\b(\u0005\u0012\u0004\u0012\u00020\b0\u0011H\u0086\bø\u0001\u0000\u001a\u0013\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00030\u0013*\u00020\u0000H\u0086\u0002\"\u0016\u0010\u0017\u001a\u00020\u0001*\u00020\u00008Æ\u0002¢\u0006\u0006\u001a\u0004\b\u0015\u0010\u0016\"\u001b\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u00030\u0018*\u00020\u00008F¢\u0006\u0006\u001a\u0004\b\u0019\u0010\u001a\u0082\u0002\u0007\n\u0005\b\u009920\u0001¨\u0006\u001c"}, m4115d2 = {"Landroid/view/Menu;", "", "index", "Landroid/view/MenuItem;", "get", "item", "", "contains", "Lokhttp3/internal/io/lx5;", "minusAssign", "isEmpty", "isNotEmpty", "Lkotlin/Function1;", "Lokhttp3/internal/io/o73;", HintConstants.AUTOFILL_HINT_NAME, "action", "forEach", "Lkotlin/Function2;", "forEachIndexed", "", "iterator", "getSize", "(Landroid/view/Menu;)I", "size", "Lokhttp3/internal/io/pk4;", "getChildren", "(Landroid/view/Menu;)Lokhttp3/internal/io/pk4;", "children", "core-ktx_release"}, m4116k = 2, m4117mv = {1, 6, 0})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class MenuKt {
    public static final boolean contains(@zu2 Menu menu, @zu2 MenuItem menuItem) {
        fa1.m6826(menu, "<this>");
        fa1.m6826(menuItem, "item");
        int size = menu.size();
        for (int i = 0; i < size; i++) {
            if (fa1.m6818(menu.getItem(i), menuItem)) {
                return true;
            }
        }
        return false;
    }

    public static final void forEach(@zu2 Menu menu, @zu2 ph0<? super MenuItem, lx5> ph0Var) {
        fa1.m6826(menu, "<this>");
        fa1.m6826(ph0Var, "action");
        int size = menu.size();
        for (int i = 0; i < size; i++) {
            MenuItem item = menu.getItem(i);
            fa1.m6825(item, "getItem(index)");
            ph0Var.invoke(item);
        }
    }

    public static final void forEachIndexed(@zu2 Menu menu, @zu2 di0<? super Integer, ? super MenuItem, lx5> di0Var) {
        fa1.m6826(menu, "<this>");
        fa1.m6826(di0Var, "action");
        int size = menu.size();
        for (int i = 0; i < size; i++) {
            Integer valueOf = Integer.valueOf(i);
            MenuItem item = menu.getItem(i);
            fa1.m6825(item, "getItem(index)");
            di0Var.mo18338invoke(valueOf, item);
        }
    }

    @zu2
    public static final MenuItem get(@zu2 Menu menu, int i) {
        fa1.m6826(menu, "<this>");
        MenuItem item = menu.getItem(i);
        fa1.m6825(item, "getItem(index)");
        return item;
    }

    @zu2
    public static final pk4<MenuItem> getChildren(@zu2 final Menu menu) {
        fa1.m6826(menu, "<this>");
        return new pk4<MenuItem>() { // from class: androidx.core.view.MenuKt$children$1
            @Override // okhttp3.internal.p042io.pk4
            @zu2
            public Iterator<MenuItem> iterator() {
                return MenuKt.iterator(menu);
            }
        };
    }

    public static final int getSize(@zu2 Menu menu) {
        fa1.m6826(menu, "<this>");
        return menu.size();
    }

    public static final boolean isEmpty(@zu2 Menu menu) {
        fa1.m6826(menu, "<this>");
        return menu.size() == 0;
    }

    public static final boolean isNotEmpty(@zu2 Menu menu) {
        fa1.m6826(menu, "<this>");
        return menu.size() != 0;
    }

    @zu2
    public static final Iterator<MenuItem> iterator(@zu2 Menu menu) {
        fa1.m6826(menu, "<this>");
        return new MenuKt$iterator$1(menu);
    }

    public static final void minusAssign(@zu2 Menu menu, @zu2 MenuItem menuItem) {
        fa1.m6826(menu, "<this>");
        fa1.m6826(menuItem, "item");
        menu.removeItem(menuItem.getItemId());
    }
}
