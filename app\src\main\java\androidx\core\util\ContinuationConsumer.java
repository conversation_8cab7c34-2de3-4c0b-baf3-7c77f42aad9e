package androidx.core.util;

import androidx.annotation.RequiresApi;
import androidx.exifinterface.media.ExifInterface;
import com.stardust.autojs.engine.RhinoJavaScriptEngine;
import com.stardust.autojs.project.ProjectConfig;
import java.util.concurrent.atomic.AtomicBoolean;
import kotlin.Metadata;
import okhttp3.internal.p042io.InterfaceC7155;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.lf2;
import okhttp3.internal.p042io.zu2;
import p041j$.util.function.Consumer;

@RequiresApi(24)
@Metadata(m4113bv = {}, m4114d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0003\u0018\u0000*\u0004\b\u0000\u0010\u00012\b\u0012\u0004\u0012\u00028\u00000\u00022\u00020\u0003B\u0015\u0012\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00028\u00000\n¢\u0006\u0004\b\f\u0010\rJ\u0017\u0010\u0006\u001a\u00020\u00052\u0006\u0010\u0004\u001a\u00028\u0000H\u0016¢\u0006\u0004\b\u0006\u0010\u0007J\b\u0010\t\u001a\u00020\bH\u0016¨\u0006\u000e"}, m4115d2 = {"Landroidx/core/util/ContinuationConsumer;", ExifInterface.GPS_DIRECTION_TRUE, "j$/util/function/Consumer", "Ljava/util/concurrent/atomic/AtomicBoolean;", "value", "Lokhttp3/internal/io/lx5;", "accept", "(Ljava/lang/Object;)V", "", "toString", "Lokhttp3/internal/io/ৡ;", ProjectConfig.FEATURE_CONTINUATION, RhinoJavaScriptEngine.SOURCE_NAME_INIT, "(Lokhttp3/internal/io/ৡ;)V", "core-ktx_release"}, m4116k = 1, m4117mv = {1, 6, 0})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
final class ContinuationConsumer<T> extends AtomicBoolean implements p041j$.util.function.Consumer<T> {

    @zu2
    private final InterfaceC7155<T> continuation;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    /* JADX WARN: Multi-variable type inference failed */
    public ContinuationConsumer(@zu2 InterfaceC7155<? super T> interfaceC7155) {
        super(false);
        fa1.m6826(interfaceC7155, ProjectConfig.FEATURE_CONTINUATION);
        this.continuation = interfaceC7155;
    }

    @Override // p041j$.util.function.Consumer
    /* renamed from: accept */
    public void m4009t(T value) {
        if (compareAndSet(false, true)) {
            this.continuation.resumeWith(value);
        }
    }

    @Override // p041j$.util.function.Consumer
    public final /* synthetic */ p041j$.util.function.Consumer andThen(p041j$.util.function.Consumer consumer) {
        return Consumer.CC.$default$andThen(this, consumer);
    }

    @Override // java.util.concurrent.atomic.AtomicBoolean
    @zu2
    public String toString() {
        StringBuilder m9240 = lf2.m9240("ContinuationConsumer(resultAccepted = ");
        m9240.append(get());
        m9240.append(')');
        return m9240.toString();
    }
}
