#!/usr/bin/env python3
"""
AutoJS9服务器启动脚本
支持配置文件和命令行参数
"""

import argparse
import json
import logging
import sys
from pathlib import Path
from autojs_server import AutoJSServer

def load_config(config_path: str) -> dict:
    """加载配置文件"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"配置文件不存在: {config_path}")
        return {}
    except json.JSONDecodeError as e:
        print(f"配置文件格式错误: {e}")
        return {}

def setup_logging(config: dict):
    """设置日志"""
    log_config = config.get("logging", {})
    level = getattr(logging, log_config.get("level", "INFO").upper())
    
    # 配置日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    
    # 文件处理器
    handlers = [console_handler]
    log_file = log_config.get("file")
    if log_file:
        from logging.handlers import RotatingFileHandler
        file_handler = RotatingFileHandler(
            log_file,
            maxBytes=log_config.get("max_size_mb", 10) * 1024 * 1024,
            backupCount=log_config.get("backup_count", 5)
        )
        file_handler.setFormatter(formatter)
        handlers.append(file_handler)
    
    # 配置根日志器
    logging.basicConfig(
        level=level,
        handlers=handlers,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="AutoJS9兼容服务器")
    parser.add_argument(
        "--config", "-c",
        default="config.json",
        help="配置文件路径 (默认: config.json)"
    )
    parser.add_argument(
        "--host",
        help="服务器主机地址"
    )
    parser.add_argument(
        "--port", "-p",
        type=int,
        help="服务器端口"
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="启用调试模式"
    )
    
    args = parser.parse_args()
    
    # 加载配置
    config = load_config(args.config)
    
    # 设置日志
    if args.debug:
        config.setdefault("logging", {})["level"] = "DEBUG"
    setup_logging(config)
    
    logger = logging.getLogger(__name__)
    
    # 获取服务器配置
    server_config = config.get("server", {})
    host = args.host or server_config.get("host", "0.0.0.0")
    port = args.port or server_config.get("port", 9317)
    
    logger.info("启动AutoJS9兼容服务器")
    logger.info(f"配置文件: {args.config}")
    logger.info(f"服务器地址: {host}:{port}")
    
    # 创建并启动服务器
    try:
        server = AutoJSServer(host=host, port=port)
        
        # 应用配置
        if "auth" in config:
            auth_config = config["auth"]
            if "database_path" in auth_config:
                server.token_manager.db_path = auth_config["database_path"]
        
        # 启动服务器
        import asyncio
        asyncio.run(server.start_server())
        
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭服务器...")
    except Exception as e:
        logger.error(f"服务器启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
