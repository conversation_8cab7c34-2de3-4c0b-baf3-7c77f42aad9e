package okhttp3.internal.p042io;

/* renamed from: okhttp3.internal.io.vc */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5459vc extends AbstractC5303ud implements v51 {
    public C5459vc(@pu2 C3318fc c3318fc, @pu2 s13 s13Var, int i) {
        super(c3318fc, s13Var, i);
    }

    @Override // okhttp3.internal.p042io.n13
    /* renamed from: ؠ */
    public final int mo4577() {
        return this.f23079.f9943.m11104(this.f23081 + 1);
    }

    @Override // okhttp3.internal.p042io.yp2
    /* renamed from: ރ */
    public final int mo5653() {
        return mo7056() << 16;
    }

    @Override // okhttp3.internal.p042io.fr0
    /* renamed from: ލ */
    public final short mo7056() {
        return (short) this.f23079.f9943.m11102(this.f23081 + 2);
    }
}
