package okhttp3.internal.p042io;

import java.io.File;

/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public class z40 extends y40 {
    @zu2
    /* renamed from: ֏ */
    public static final v40 m14442(@zu2 File file, @zu2 int i) {
        fa1.m6826(file, "<this>");
        rn5.m11782(i, "direction");
        return new v40(file, i);
    }
}
