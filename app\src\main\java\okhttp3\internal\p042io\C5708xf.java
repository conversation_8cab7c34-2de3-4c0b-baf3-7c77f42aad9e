package okhttp3.internal.p042io;

import android.content.Context;
import android.view.View;
import android.view.Window;
import androidx.compose.p000ui.platform.AbstractComposeView;
import androidx.compose.runtime.ParcelableSnapshotMutableState;

/* renamed from: okhttp3.internal.io.xf */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes3.dex */
public final class C5708xf extends AbstractComposeView implements InterfaceC3031cg {

    /* renamed from: ၷ */
    @zu2
    public final Window f25848;

    /* renamed from: ၸ */
    @zu2
    public final ParcelableSnapshotMutableState f25849;

    /* renamed from: ၹ */
    public boolean f25850;

    /* renamed from: ၺ */
    public boolean f25851;

    /* renamed from: okhttp3.internal.io.xf$Ϳ, reason: contains not printable characters */
    public static final class C9485 extends lv1 implements di0<InterfaceC6968, Integer, lx5> {

        /* renamed from: ၦ */
        public final /* synthetic */ int f25853;

        /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
        public C9485(int i) {
            super(2);
            this.f25853 = i;
        }

        @Override // okhttp3.internal.p042io.di0
        /* renamed from: invoke */
        public final lx5 mo18338invoke(InterfaceC6968 interfaceC6968, Integer num) {
            num.intValue();
            C5708xf.this.Content(interfaceC6968, this.f25853 | 1);
            return lx5.f14876;
        }
    }

    public C5708xf(@zu2 Context context, @zu2 Window window) {
        super(context, null, 0, 6, null);
        this.f25848 = window;
        C6664 c6664 = C6664.f29717;
        this.f25849 = (ParcelableSnapshotMutableState) ov4.m10507(C6664.f29718);
    }

    @Override // androidx.compose.p000ui.platform.AbstractComposeView
    @InterfaceC7452
    public final void Content(@wv2 InterfaceC6968 interfaceC6968, int i) {
        InterfaceC6968 mo16280 = interfaceC6968.mo16280(1735448596);
        fi0<InterfaceC6575<?>, lu4, hz3, lx5> fi0Var = C7171.f31108;
        ((di0) this.f25849.getValue()).mo18338invoke(mo16280, 0);
        qb4 mo16286 = mo16280.mo16286();
        if (mo16286 == null) {
            return;
        }
        mo16286.mo5841(new C9485(i));
    }

    @Override // androidx.compose.p000ui.platform.AbstractComposeView
    public final boolean getShouldCreateCompositionOnAttachedToWindow() {
        return this.f25851;
    }

    @Override // androidx.compose.p000ui.platform.AbstractComposeView
    public final void internalOnLayout$ui_release(boolean z, int i, int i2, int i3, int i4) {
        super.internalOnLayout$ui_release(z, i, i2, i3, i4);
        View childAt = getChildAt(0);
        if (childAt == null) {
            return;
        }
        this.f25848.setLayout(childAt.getMeasuredWidth(), childAt.getMeasuredHeight());
    }

    @Override // androidx.compose.p000ui.platform.AbstractComposeView
    public final void internalOnMeasure$ui_release(int i, int i2) {
        if (!this.f25850) {
            i = View.MeasureSpec.makeMeasureSpec(ly3.m9477(getContext().getResources().getConfiguration().screenWidthDp * getContext().getResources().getDisplayMetrics().density), Integer.MIN_VALUE);
            i2 = View.MeasureSpec.makeMeasureSpec(ly3.m9477(getContext().getResources().getConfiguration().screenHeightDp * getContext().getResources().getDisplayMetrics().density), Integer.MIN_VALUE);
        }
        super.internalOnMeasure$ui_release(i, i2);
    }
}
