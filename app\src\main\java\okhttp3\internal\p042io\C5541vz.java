package okhttp3.internal.p042io;

import java.security.cert.CertificateEncodingException;

/* renamed from: okhttp3.internal.io.vz */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5541vz extends CertificateEncodingException {

    /* renamed from: ၥ */
    public Throwable f24563;

    public C5541vz(String str, Throwable th) {
        super(str);
        this.f24563 = th;
    }

    @Override // java.lang.Throwable
    public final Throwable getCause() {
        return this.f24563;
    }
}
