package okhttp3.internal.p042io;

import com.stardust.autojs.project.ProjectConfig;
import com.stardust.pio.PFile;

/* renamed from: okhttp3.internal.io.xy */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5762xy extends C4971ry {

    /* renamed from: ၵ */
    public ProjectConfig f26259;

    public C5762xy(PFile pFile, InterfaceC5629wy interfaceC5629wy, ProjectConfig projectConfig) {
        super(pFile, interfaceC5629wy);
        this.f26259 = projectConfig;
    }

    @Override // okhttp3.internal.p042io.C4971ry, okhttp3.internal.p042io.C5131sy
    /* renamed from: ԯ */
    public final C5131sy mo11836(String str) {
        return new C5762xy(this.f21658.renameTo(str), this.f21659, this.f26259);
    }
}
