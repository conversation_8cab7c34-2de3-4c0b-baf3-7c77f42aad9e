package okhttp3.internal.p042io;

import com.stardust.autojs.core.inputevent.InputEventCodes;
import org.autojs.autojs.p047ui.edit.editor.InterfaceC8120;

@InterfaceC4988s2(m11868c = "org.autojs.autojs.ui.edit.EditorMenu$insertImportStatement$1", m11869f = "EditorMenu.kt", m11870l = {InputEventCodes.BTN_8}, m11871m = "invokeSuspend")
/* renamed from: okhttp3.internal.io.vo */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5501vo extends u75 implements di0<InterfaceC7881, InterfaceC7155<? super lx5>, Object> {

    /* renamed from: ၥ */
    public int f24359;

    /* renamed from: ၦ */
    public final /* synthetic */ C2798ap f24360;

    /* renamed from: ၮ */
    public final /* synthetic */ AbstractC7040 f24361;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public C5501vo(C2798ap c2798ap, AbstractC7040 abstractC7040, InterfaceC7155<? super C5501vo> interfaceC7155) {
        super(2, interfaceC7155);
        this.f24360 = c2798ap;
        this.f24361 = abstractC7040;
    }

    @Override // okhttp3.internal.p042io.AbstractC7853
    @zu2
    public final InterfaceC7155<lx5> create(@wv2 Object obj, @zu2 InterfaceC7155<?> interfaceC7155) {
        return new C5501vo(this.f24360, this.f24361, interfaceC7155);
    }

    @Override // okhttp3.internal.p042io.di0
    /* renamed from: invoke */
    public final Object mo18338invoke(InterfaceC7881 interfaceC7881, InterfaceC7155<? super lx5> interfaceC7155) {
        return ((C5501vo) create(interfaceC7881, interfaceC7155)).invokeSuspend(lx5.f14876);
    }

    @Override // okhttp3.internal.p042io.AbstractC7853
    @wv2
    public final Object invokeSuspend(@zu2 Object obj) {
        EnumC7329 enumC7329 = EnumC7329.COROUTINE_SUSPENDED;
        int i = this.f24359;
        if (i == 0) {
            C4350o9.m10270(obj);
            InterfaceC8120 interfaceC8120 = this.f24360.f6601;
            this.f24359 = 1;
            obj = interfaceC8120.getText(this);
            if (obj == enumC7329) {
                return enumC7329;
            }
        } else {
            if (i != 1) {
                throw new IllegalStateException("call to 'resume' before 'invoke' with coroutine");
            }
            C4350o9.m10270(obj);
        }
        if (z45.m14453((String) obj, "\"ui\";")) {
            this.f24360.f6601.insert(1, this.f24361.mo16397() + ";\n");
        } else {
            this.f24360.f6601.insert(0, this.f24361.mo16397() + ";\n");
        }
        return lx5.f14876;
    }
}
