package okhttp3.internal.p042io;

import okhttp3.internal.p042io.AbstractC6620;

@fz4
/* renamed from: okhttp3.internal.io.x3 */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes3.dex */
public final class C5655x3<T, V extends AbstractC6620> implements InterfaceC7633<T, V> {

    /* renamed from: Ϳ */
    @zu2
    public final g56<V> f25325;

    /* renamed from: Ԩ */
    @zu2
    public final jq5<T, V> f25326;

    /* renamed from: ԩ */
    public final T f25327;

    /* renamed from: Ԫ */
    @zu2
    public final V f25328;

    /* renamed from: ԫ */
    @zu2
    public final V f25329;

    /* renamed from: Ԭ */
    @zu2
    public final V f25330;

    /* renamed from: ԭ */
    public final T f25331;

    /* renamed from: Ԯ */
    public final long f25332;

    public C5655x3(@zu2 InterfaceC5779y3<T> interfaceC5779y3, @zu2 jq5<T, V> jq5Var, T t, @zu2 V v) {
        fa1.m6826(interfaceC5779y3, "animationSpec");
        fa1.m6826(jq5Var, "typeConverter");
        fa1.m6826(v, "initialVelocityVector");
        g56<V> mo14128 = interfaceC5779y3.mo14128(jq5Var);
        fa1.m6826(mo14128, "animationSpec");
        this.f25325 = mo14128;
        this.f25326 = jq5Var;
        this.f25327 = t;
        V invoke = jq5Var.mo8709().invoke(t);
        this.f25328 = invoke;
        this.f25329 = (V) C7720.m17347(v);
        this.f25331 = jq5Var.mo8710().invoke(mo14128.mo7242(invoke, v));
        long mo7243 = mo14128.mo7243(invoke, v);
        this.f25332 = mo7243;
        V v2 = (V) C7720.m17347(mo14128.mo7240(mo7243, invoke, v));
        this.f25330 = v2;
        int mo14835 = v2.mo14835();
        for (int i = 0; i < mo14835; i++) {
            V v3 = this.f25330;
            v3.mo14838(i, C6489.m15437(v3.mo14834(i), -this.f25325.mo7239(), this.f25325.mo7239()));
        }
    }

    @Override // okhttp3.internal.p042io.InterfaceC7633
    /* renamed from: Ϳ */
    public final boolean mo13498() {
        return false;
    }

    @Override // okhttp3.internal.p042io.InterfaceC7633
    /* renamed from: Ԩ */
    public final long mo13499() {
        return this.f25332;
    }

    @Override // okhttp3.internal.p042io.InterfaceC7633
    @zu2
    /* renamed from: ԩ */
    public final jq5<T, V> mo13500() {
        return this.f25326;
    }

    @Override // okhttp3.internal.p042io.InterfaceC7633
    @zu2
    /* renamed from: Ԫ */
    public final V mo13501(long j) {
        return !mo13502(j) ? this.f25325.mo7240(j, this.f25328, this.f25329) : this.f25330;
    }

    @Override // okhttp3.internal.p042io.InterfaceC7633
    /* renamed from: ԫ */
    public final boolean mo13502(long j) {
        return j >= mo13499();
    }

    @Override // okhttp3.internal.p042io.InterfaceC7633
    /* renamed from: Ԭ */
    public final T mo13503(long j) {
        return !mo13502(j) ? (T) this.f25326.mo8710().invoke(this.f25325.mo7241(j, this.f25328, this.f25329)) : this.f25331;
    }

    @Override // okhttp3.internal.p042io.InterfaceC7633
    /* renamed from: ԭ */
    public final T mo13504() {
        return this.f25331;
    }
}
