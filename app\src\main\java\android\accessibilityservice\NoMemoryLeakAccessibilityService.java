package android.accessibilityservice;

import android.accessibilityservice.AccessibilityService;
import android.os.Handler;
import android.util.SparseArray;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import java.lang.reflect.Field;

/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public abstract class NoMemoryLeakAccessibilityService extends AccessibilityService {
    private Field mGestureStatusCallbackInfos;
    private Field mGestureStatusCallbackSequence;
    private final Object mLock;

    public NoMemoryLeakAccessibilityService() {
        Object obj;
        try {
            Field declaredField = AccessibilityService.class.getDeclaredField("mGestureStatusCallbackSequence");
            this.mGestureStatusCallbackSequence = declaredField;
            declaredField.setAccessible(true);
            Field declaredField2 = AccessibilityService.class.getDeclaredField("mGestureStatusCallbackInfos");
            this.mGestureStatusCallbackInfos = declaredField2;
            declaredField2.setAccessible(true);
            Field declaredField3 = AccessibilityService.class.getDeclaredField("mLock");
            declaredField3.setAccessible(true);
            obj = declaredField3.get(this);
        } catch (Exception unused) {
            obj = null;
        }
        this.mLock = obj;
    }

    @RequiresApi(api = 24)
    public synchronized int dispatchGestureNoMemoryLeak(@NonNull Object obj, @Nullable Object obj2, @Nullable Handler handler) {
        int intValue;
        Object obj3 = this.mLock;
        if (obj3 == null) {
            dispatchGesture((GestureDescription) obj, (AccessibilityService.GestureResultCallback) obj2, handler);
            return -1;
        }
        synchronized (obj3) {
            dispatchGesture((GestureDescription) obj, (AccessibilityService.GestureResultCallback) obj2, handler);
            try {
                intValue = ((Integer) this.mGestureStatusCallbackSequence.get(this)).intValue();
            } catch (IllegalAccessException unused) {
                return -1;
            }
        }
        return intValue;
    }

    @RequiresApi(api = 24)
    public native synchronized int nativeDispatchGesture(@NonNull Object obj, @Nullable Object obj2, @Nullable Handler handler, long j);

    public synchronized void removeGestureCallbackInfo(int i) {
        if (i != -1) {
            Object obj = this.mLock;
            if (obj != null) {
                synchronized (obj) {
                    try {
                        SparseArray sparseArray = (SparseArray) this.mGestureStatusCallbackInfos.get(this);
                        if (sparseArray != null) {
                            sparseArray.remove(i);
                        }
                    } catch (IllegalAccessException unused) {
                    }
                }
            }
        }
    }
}
