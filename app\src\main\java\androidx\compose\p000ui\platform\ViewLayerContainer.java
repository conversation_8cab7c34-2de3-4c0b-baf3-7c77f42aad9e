package androidx.compose.p000ui.platform;

import android.content.Context;
import android.graphics.Canvas;
import com.stardust.autojs.engine.RhinoJavaScriptEngine;
import kotlin.Metadata;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.zu2;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0000\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0005\u001a\u00020\u0004¢\u0006\u0004\b\u0006\u0010\u0007J\b\u0010\u0003\u001a\u00020\u0002H\u0004¨\u0006\b"}, m4115d2 = {"Landroidx/compose/ui/platform/ViewLayerContainer;", "Landroidx/compose/ui/platform/DrawChildContainer;", "Lokhttp3/internal/io/lx5;", "dispatchGetDisplayList", "Landroid/content/Context;", "context", RhinoJavaScriptEngine.SOURCE_NAME_INIT, "(Landroid/content/Context;)V", "ui_release"}, m4116k = 1, m4117mv = {1, 7, 1})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class ViewLayerContainer extends DrawChildContainer {
    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public ViewLayerContainer(@zu2 Context context) {
        super(context);
        fa1.m6826(context, "context");
    }

    @Override // androidx.compose.p000ui.platform.DrawChildContainer, android.view.ViewGroup, android.view.View
    public final void dispatchDraw(@zu2 Canvas canvas) {
        fa1.m6826(canvas, "canvas");
    }

    public final void dispatchGetDisplayList() {
    }
}
