package okhttp3.internal.p042io;

import java.io.InputStream;

/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public abstract class z22 extends InputStream {

    /* renamed from: ၥ */
    public final InputStream f27057;

    /* renamed from: ၦ */
    public int f27058;

    public z22(InputStream inputStream, int i) {
        this.f27057 = inputStream;
        this.f27058 = i;
    }

    /* renamed from: Ϳ */
    public int mo5948() {
        return this.f27058;
    }

    /* renamed from: Ԩ */
    public final void m14423() {
        InputStream inputStream = this.f27057;
        if (inputStream instanceof m21) {
            m21 m21Var = (m21) inputStream;
            m21Var.f14945 = true;
            m21Var.m9523();
        }
    }
}
