package androidx.datastore.core;

import androidx.datastore.core.SingleProcessDataStore;
import androidx.exifinterface.media.ExifInterface;
import java.io.File;
import java.util.Set;
import kotlin.Metadata;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.lv1;
import okhttp3.internal.p042io.nh0;
import okhttp3.internal.p042io.zu2;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000\b\n\u0000\n\u0002\u0018\u0002\n\u0000\u0010\u0002\u001a\u00020\u0001\"\u0004\b\u0000\u0010\u0000H\n"}, m4115d2 = {ExifInterface.GPS_DIRECTION_TRUE, "Ljava/io/File;", "<anonymous>"}, m4116k = 3, m4117mv = {1, 5, 1})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class SingleProcessDataStore$file$2 extends lv1 implements nh0<File> {
    public final /* synthetic */ SingleProcessDataStore<T> this$0;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public SingleProcessDataStore$file$2(SingleProcessDataStore<T> singleProcessDataStore) {
        super(0);
        this.this$0 = singleProcessDataStore;
    }

    /* JADX WARN: Can't rename method to resolve collision */
    @Override // okhttp3.internal.p042io.nh0
    @zu2
    public final File invoke() {
        nh0 nh0Var;
        nh0Var = ((SingleProcessDataStore) this.this$0).produceFile;
        File file = (File) nh0Var.invoke();
        String absolutePath = file.getAbsolutePath();
        SingleProcessDataStore.Companion companion = SingleProcessDataStore.INSTANCE;
        synchronized (companion.getActiveFilesLock$datastore_core()) {
            if (!(!companion.getActiveFiles$datastore_core().contains(absolutePath))) {
                throw new IllegalStateException(("There are multiple DataStores active for the same file: " + file + ". You should either maintain your DataStore as a singleton or confirm that there is no two DataStore's active on the same file (by confirming that the scope is cancelled).").toString());
            }
            Set<String> activeFiles$datastore_core = companion.getActiveFiles$datastore_core();
            fa1.m6825(absolutePath, "it");
            activeFiles$datastore_core.add(absolutePath);
        }
        return file;
    }
}
