package android.view.compose;

import android.view.result.ActivityResultLauncher;
import android.view.result.contract.ActivityResultContract;
import androidx.core.app.ActivityOptionsCompat;
import com.stardust.autojs.engine.RhinoJavaScriptEngine;
import kotlin.Metadata;
import okhttp3.internal.p042io.InterfaceC5795y7;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.fz4;
import okhttp3.internal.p042io.g05;
import okhttp3.internal.p042io.wv2;
import okhttp3.internal.p042io.zu2;
import org.mozilla.javascript.optimizer.OptRuntime;

@fz4
@Metadata(m4113bv = {}, m4114d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0007\u0018\u0000*\u0004\b\u0000\u0010\u0001*\u0004\b\u0001\u0010\u00022\b\u0012\u0004\u0012\u00028\u00000\u0003B1\b\u0000\u0012\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00028\u00000\r\u0012\u0018\u0010\u0011\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00028\u0000\u0012\u0004\u0012\u00028\u00010\u000b0\u0010¢\u0006\u0004\b\u0012\u0010\u0013J\b\u0010\u0005\u001a\u00020\u0004H\u0017J!\u0010\t\u001a\u00020\u00042\u0006\u0010\u0006\u001a\u00028\u00002\b\u0010\b\u001a\u0004\u0018\u00010\u0007H\u0016¢\u0006\u0004\b\t\u0010\nJ\u0012\u0010\f\u001a\f\u0012\u0004\u0012\u00028\u0000\u0012\u0002\b\u00030\u000bH\u0016R\u001a\u0010\u000e\u001a\b\u0012\u0004\u0012\u00028\u00000\r8\u0002X\u0082\u0004¢\u0006\u0006\n\u0004\b\u000e\u0010\u000f¨\u0006\u0014"}, m4115d2 = {"Landroidx/activity/compose/ManagedActivityResultLauncher;", OptRuntime.GeneratorState.resumptionPoint_TYPE, "O", "Landroidx/activity/result/ActivityResultLauncher;", "Lokhttp3/internal/io/lx5;", "unregister", "input", "Landroidx/core/app/ActivityOptionsCompat;", "options", "launch", "(Ljava/lang/Object;Landroidx/core/app/ActivityOptionsCompat;)V", "Landroidx/activity/result/contract/ActivityResultContract;", "getContract", "Landroidx/activity/compose/ActivityResultLauncherHolder;", "launcher", "Landroidx/activity/compose/ActivityResultLauncherHolder;", "Lokhttp3/internal/io/g05;", "contract", RhinoJavaScriptEngine.SOURCE_NAME_INIT, "(Landroidx/activity/compose/ActivityResultLauncherHolder;Lokhttp3/internal/io/g05;)V", "activity-compose_release"}, m4116k = 1, m4117mv = {1, 6, 0})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class ManagedActivityResultLauncher<I, O> extends ActivityResultLauncher<I> {
    public static final int $stable = 8;

    @zu2
    private final g05<ActivityResultContract<I, O>> contract;

    @zu2
    private final ActivityResultLauncherHolder<I> launcher;

    /* JADX WARN: Multi-variable type inference failed */
    public ManagedActivityResultLauncher(@zu2 ActivityResultLauncherHolder<I> activityResultLauncherHolder, @zu2 g05<? extends ActivityResultContract<I, O>> g05Var) {
        fa1.m6826(activityResultLauncherHolder, "launcher");
        fa1.m6826(g05Var, "contract");
        this.launcher = activityResultLauncherHolder;
        this.contract = g05Var;
    }

    @Override // android.view.result.ActivityResultLauncher
    @zu2
    public ActivityResultContract<I, ?> getContract() {
        return this.contract.getValue();
    }

    @Override // android.view.result.ActivityResultLauncher
    public void launch(I input, @wv2 ActivityOptionsCompat options) {
        this.launcher.launch(input, options);
    }

    @Override // android.view.result.ActivityResultLauncher
    @InterfaceC5795y7
    public void unregister() {
        throw new UnsupportedOperationException("Registration is automatically handled by rememberLauncherForActivityResult");
    }
}
