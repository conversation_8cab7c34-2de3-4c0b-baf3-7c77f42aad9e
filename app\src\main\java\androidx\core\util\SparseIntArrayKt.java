package androidx.core.util;

import android.util.SparseIntArray;
import androidx.autofill.HintConstants;
import kotlin.Metadata;
import okhttp3.internal.p042io.c71;
import okhttp3.internal.p042io.di0;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.nh0;
import okhttp3.internal.p042io.zu2;

@Metadata(m4113bv = {}, m4114d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0006\u001a\u0015\u0010\u0004\u001a\u00020\u0003*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u0001H\u0086\n\u001a\u001d\u0010\u0007\u001a\u00020\u0006*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0005\u001a\u00020\u0001H\u0086\n\u001a\u0015\u0010\t\u001a\u00020\u0000*\u00020\u00002\u0006\u0010\b\u001a\u00020\u0000H\u0086\u0002\u001a\u0015\u0010\n\u001a\u00020\u0003*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u0001H\u0086\b\u001a\u0015\u0010\u000b\u001a\u00020\u0003*\u00020\u00002\u0006\u0010\u0005\u001a\u00020\u0001H\u0086\b\u001a\u001d\u0010\r\u001a\u00020\u0001*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\f\u001a\u00020\u0001H\u0086\b\u001a&\u0010\u000f\u001a\u00020\u0001*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u00012\f\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00010\u000eH\u0086\bø\u0001\u0000\u001a\r\u0010\u0010\u001a\u00020\u0003*\u00020\u0000H\u0086\b\u001a\r\u0010\u0011\u001a\u00020\u0003*\u00020\u0000H\u0086\b\u001a\u001a\u0010\u0012\u001a\u00020\u0003*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0005\u001a\u00020\u0001\u001a\u0012\u0010\u0013\u001a\u00020\u0006*\u00020\u00002\u0006\u0010\b\u001a\u00020\u0000\u001aH\u0010\u0018\u001a\u00020\u0006*\u00020\u000026\u0010\u0017\u001a2\u0012\u0013\u0012\u00110\u0001¢\u0006\f\b\u0015\u0012\b\b\u0016\u0012\u0004\b\b(\u0002\u0012\u0013\u0012\u00110\u0001¢\u0006\f\b\u0015\u0012\b\b\u0016\u0012\u0004\b\b(\u0005\u0012\u0004\u0012\u00020\u00060\u0014H\u0086\bø\u0001\u0000\u001a\n\u0010\u001a\u001a\u00020\u0019*\u00020\u0000\u001a\n\u0010\u001b\u001a\u00020\u0019*\u00020\u0000\"\u0016\u0010\u001e\u001a\u00020\u0001*\u00020\u00008Æ\u0002¢\u0006\u0006\u001a\u0004\b\u001c\u0010\u001d\u0082\u0002\u0007\n\u0005\b\u009920\u0001¨\u0006\u001f"}, m4115d2 = {"Landroid/util/SparseIntArray;", "", "key", "", "contains", "value", "Lokhttp3/internal/io/lx5;", "set", "other", "plus", "containsKey", "containsValue", "defaultValue", "getOrDefault", "Lkotlin/Function0;", "getOrElse", "isEmpty", "isNotEmpty", "remove", "putAll", "Lkotlin/Function2;", "Lokhttp3/internal/io/o73;", HintConstants.AUTOFILL_HINT_NAME, "action", "forEach", "Lokhttp3/internal/io/c71;", "keyIterator", "valueIterator", "getSize", "(Landroid/util/SparseIntArray;)I", "size", "core-ktx_release"}, m4116k = 2, m4117mv = {1, 6, 0})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class SparseIntArrayKt {
    public static final boolean contains(@zu2 SparseIntArray sparseIntArray, int i) {
        fa1.m6826(sparseIntArray, "<this>");
        return sparseIntArray.indexOfKey(i) >= 0;
    }

    public static final boolean containsKey(@zu2 SparseIntArray sparseIntArray, int i) {
        fa1.m6826(sparseIntArray, "<this>");
        return sparseIntArray.indexOfKey(i) >= 0;
    }

    public static final boolean containsValue(@zu2 SparseIntArray sparseIntArray, int i) {
        fa1.m6826(sparseIntArray, "<this>");
        return sparseIntArray.indexOfValue(i) >= 0;
    }

    public static final void forEach(@zu2 SparseIntArray sparseIntArray, @zu2 di0<? super Integer, ? super Integer, lx5> di0Var) {
        fa1.m6826(sparseIntArray, "<this>");
        fa1.m6826(di0Var, "action");
        int size = sparseIntArray.size();
        for (int i = 0; i < size; i++) {
            di0Var.mo18338invoke(Integer.valueOf(sparseIntArray.keyAt(i)), Integer.valueOf(sparseIntArray.valueAt(i)));
        }
    }

    public static final int getOrDefault(@zu2 SparseIntArray sparseIntArray, int i, int i2) {
        fa1.m6826(sparseIntArray, "<this>");
        return sparseIntArray.get(i, i2);
    }

    public static final int getOrElse(@zu2 SparseIntArray sparseIntArray, int i, @zu2 nh0<Integer> nh0Var) {
        fa1.m6826(sparseIntArray, "<this>");
        fa1.m6826(nh0Var, "defaultValue");
        int indexOfKey = sparseIntArray.indexOfKey(i);
        return indexOfKey >= 0 ? sparseIntArray.valueAt(indexOfKey) : nh0Var.invoke().intValue();
    }

    public static final int getSize(@zu2 SparseIntArray sparseIntArray) {
        fa1.m6826(sparseIntArray, "<this>");
        return sparseIntArray.size();
    }

    public static final boolean isEmpty(@zu2 SparseIntArray sparseIntArray) {
        fa1.m6826(sparseIntArray, "<this>");
        return sparseIntArray.size() == 0;
    }

    public static final boolean isNotEmpty(@zu2 SparseIntArray sparseIntArray) {
        fa1.m6826(sparseIntArray, "<this>");
        return sparseIntArray.size() != 0;
    }

    @zu2
    public static final c71 keyIterator(@zu2 final SparseIntArray sparseIntArray) {
        fa1.m6826(sparseIntArray, "<this>");
        return new c71() { // from class: androidx.core.util.SparseIntArrayKt$keyIterator$1
            private int index;

            public final int getIndex() {
                return this.index;
            }

            @Override // java.util.Iterator, p041j$.util.Iterator
            /* renamed from: hasNext */
            public boolean getHasMore() {
                return this.index < sparseIntArray.size();
            }

            @Override // okhttp3.internal.p042io.c71
            public int nextInt() {
                SparseIntArray sparseIntArray2 = sparseIntArray;
                int i = this.index;
                this.index = i + 1;
                return sparseIntArray2.keyAt(i);
            }

            public final void setIndex(int i) {
                this.index = i;
            }
        };
    }

    @zu2
    public static final SparseIntArray plus(@zu2 SparseIntArray sparseIntArray, @zu2 SparseIntArray sparseIntArray2) {
        fa1.m6826(sparseIntArray, "<this>");
        fa1.m6826(sparseIntArray2, "other");
        SparseIntArray sparseIntArray3 = new SparseIntArray(sparseIntArray2.size() + sparseIntArray.size());
        putAll(sparseIntArray3, sparseIntArray);
        putAll(sparseIntArray3, sparseIntArray2);
        return sparseIntArray3;
    }

    public static final void putAll(@zu2 SparseIntArray sparseIntArray, @zu2 SparseIntArray sparseIntArray2) {
        fa1.m6826(sparseIntArray, "<this>");
        fa1.m6826(sparseIntArray2, "other");
        int size = sparseIntArray2.size();
        for (int i = 0; i < size; i++) {
            sparseIntArray.put(sparseIntArray2.keyAt(i), sparseIntArray2.valueAt(i));
        }
    }

    public static final boolean remove(@zu2 SparseIntArray sparseIntArray, int i, int i2) {
        fa1.m6826(sparseIntArray, "<this>");
        int indexOfKey = sparseIntArray.indexOfKey(i);
        if (indexOfKey < 0 || i2 != sparseIntArray.valueAt(indexOfKey)) {
            return false;
        }
        sparseIntArray.removeAt(indexOfKey);
        return true;
    }

    public static final void set(@zu2 SparseIntArray sparseIntArray, int i, int i2) {
        fa1.m6826(sparseIntArray, "<this>");
        sparseIntArray.put(i, i2);
    }

    @zu2
    public static final c71 valueIterator(@zu2 final SparseIntArray sparseIntArray) {
        fa1.m6826(sparseIntArray, "<this>");
        return new c71() { // from class: androidx.core.util.SparseIntArrayKt$valueIterator$1
            private int index;

            public final int getIndex() {
                return this.index;
            }

            @Override // java.util.Iterator, p041j$.util.Iterator
            /* renamed from: hasNext */
            public boolean getHasMore() {
                return this.index < sparseIntArray.size();
            }

            @Override // okhttp3.internal.p042io.c71
            public int nextInt() {
                SparseIntArray sparseIntArray2 = sparseIntArray;
                int i = this.index;
                this.index = i + 1;
                return sparseIntArray2.valueAt(i);
            }

            public final void setIndex(int i) {
                this.index = i;
            }
        };
    }
}
