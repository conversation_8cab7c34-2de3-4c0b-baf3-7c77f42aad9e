package androidx.core.animation;

import android.animation.Animator;
import androidx.annotation.RequiresApi;
import androidx.autofill.HintConstants;
import kotlin.Metadata;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.ph0;
import okhttp3.internal.p042io.zu2;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u000b\u001a5\u0010\b\u001a\u00020\u0007*\u00020\u00002#\b\u0004\u0010\u0006\u001a\u001d\u0012\u0013\u0012\u00110\u0000¢\u0006\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0004\u0012\u0004\u0012\u00020\u00050\u0001H\u0086\bø\u0001\u0000\u001a5\u0010\t\u001a\u00020\u0007*\u00020\u00002#\b\u0004\u0010\u0006\u001a\u001d\u0012\u0013\u0012\u00110\u0000¢\u0006\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0004\u0012\u0004\u0012\u00020\u00050\u0001H\u0086\bø\u0001\u0000\u001a5\u0010\n\u001a\u00020\u0007*\u00020\u00002#\b\u0004\u0010\u0006\u001a\u001d\u0012\u0013\u0012\u00110\u0000¢\u0006\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0004\u0012\u0004\u0012\u00020\u00050\u0001H\u0086\bø\u0001\u0000\u001a5\u0010\u000b\u001a\u00020\u0007*\u00020\u00002#\b\u0004\u0010\u0006\u001a\u001d\u0012\u0013\u0012\u00110\u0000¢\u0006\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0004\u0012\u0004\u0012\u00020\u00050\u0001H\u0086\bø\u0001\u0000\u001a5\u0010\r\u001a\u00020\f*\u00020\u00002#\b\u0004\u0010\u0006\u001a\u001d\u0012\u0013\u0012\u00110\u0000¢\u0006\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0004\u0012\u0004\u0012\u00020\u00050\u0001H\u0087\bø\u0001\u0000\u001a5\u0010\u000e\u001a\u00020\f*\u00020\u00002#\b\u0004\u0010\u0006\u001a\u001d\u0012\u0013\u0012\u00110\u0000¢\u0006\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0004\u0012\u0004\u0012\u00020\u00050\u0001H\u0087\bø\u0001\u0000\u001a¤\u0001\u0010\u0013\u001a\u00020\u0007*\u00020\u00002#\b\u0006\u0010\u000f\u001a\u001d\u0012\u0013\u0012\u00110\u0000¢\u0006\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0004\u0012\u0004\u0012\u00020\u00050\u00012#\b\u0006\u0010\u0010\u001a\u001d\u0012\u0013\u0012\u00110\u0000¢\u0006\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0004\u0012\u0004\u0012\u00020\u00050\u00012#\b\u0006\u0010\u0011\u001a\u001d\u0012\u0013\u0012\u00110\u0000¢\u0006\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0004\u0012\u0004\u0012\u00020\u00050\u00012#\b\u0006\u0010\u0012\u001a\u001d\u0012\u0013\u0012\u00110\u0000¢\u0006\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0004\u0012\u0004\u0012\u00020\u00050\u0001H\u0086\bø\u0001\u0000\u001aZ\u0010\u0016\u001a\u00020\f*\u00020\u00002#\b\u0006\u0010\u0014\u001a\u001d\u0012\u0013\u0012\u00110\u0000¢\u0006\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0004\u0012\u0004\u0012\u00020\u00050\u00012#\b\u0006\u0010\u0015\u001a\u001d\u0012\u0013\u0012\u00110\u0000¢\u0006\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0004\u0012\u0004\u0012\u00020\u00050\u0001H\u0087\bø\u0001\u0000\u0082\u0002\u0007\n\u0005\b\u009920\u0001¨\u0006\u0017"}, m4115d2 = {"Landroid/animation/Animator;", "Lkotlin/Function1;", "Lokhttp3/internal/io/o73;", HintConstants.AUTOFILL_HINT_NAME, "animator", "Lokhttp3/internal/io/lx5;", "action", "Landroid/animation/Animator$AnimatorListener;", "doOnEnd", "doOnStart", "doOnCancel", "doOnRepeat", "Landroid/animation/Animator$AnimatorPauseListener;", "doOnResume", "doOnPause", "onEnd", "onStart", "onCancel", "onRepeat", "addListener", "onResume", "onPause", "addPauseListener", "core-ktx_release"}, m4116k = 2, m4117mv = {1, 6, 0})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class AnimatorKt {
    @zu2
    public static final Animator.AnimatorListener addListener(@zu2 Animator animator, @zu2 ph0<? super Animator, lx5> ph0Var, @zu2 ph0<? super Animator, lx5> ph0Var2, @zu2 ph0<? super Animator, lx5> ph0Var3, @zu2 ph0<? super Animator, lx5> ph0Var4) {
        fa1.m6826(animator, "<this>");
        fa1.m6826(ph0Var, "onEnd");
        fa1.m6826(ph0Var2, "onStart");
        fa1.m6826(ph0Var3, "onCancel");
        fa1.m6826(ph0Var4, "onRepeat");
        AnimatorKt$addListener$listener$1 animatorKt$addListener$listener$1 = new AnimatorKt$addListener$listener$1(ph0Var4, ph0Var, ph0Var3, ph0Var2);
        animator.addListener(animatorKt$addListener$listener$1);
        return animatorKt$addListener$listener$1;
    }

    public static /* synthetic */ Animator.AnimatorListener addListener$default(Animator animator, ph0 ph0Var, ph0 ph0Var2, ph0 ph0Var3, ph0 ph0Var4, int i, Object obj) {
        if ((i & 1) != 0) {
            ph0Var = AnimatorKt$addListener$1.INSTANCE;
        }
        if ((i & 2) != 0) {
            ph0Var2 = AnimatorKt$addListener$2.INSTANCE;
        }
        if ((i & 4) != 0) {
            ph0Var3 = AnimatorKt$addListener$3.INSTANCE;
        }
        if ((i & 8) != 0) {
            ph0Var4 = AnimatorKt$addListener$4.INSTANCE;
        }
        fa1.m6826(animator, "<this>");
        fa1.m6826(ph0Var, "onEnd");
        fa1.m6826(ph0Var2, "onStart");
        fa1.m6826(ph0Var3, "onCancel");
        fa1.m6826(ph0Var4, "onRepeat");
        AnimatorKt$addListener$listener$1 animatorKt$addListener$listener$1 = new AnimatorKt$addListener$listener$1(ph0Var4, ph0Var, ph0Var3, ph0Var2);
        animator.addListener(animatorKt$addListener$listener$1);
        return animatorKt$addListener$listener$1;
    }

    @RequiresApi(19)
    @zu2
    public static final Animator.AnimatorPauseListener addPauseListener(@zu2 Animator animator, @zu2 ph0<? super Animator, lx5> ph0Var, @zu2 ph0<? super Animator, lx5> ph0Var2) {
        fa1.m6826(animator, "<this>");
        fa1.m6826(ph0Var, "onResume");
        fa1.m6826(ph0Var2, "onPause");
        AnimatorKt$addPauseListener$listener$1 animatorKt$addPauseListener$listener$1 = new AnimatorKt$addPauseListener$listener$1(ph0Var2, ph0Var);
        animator.addPauseListener(animatorKt$addPauseListener$listener$1);
        return animatorKt$addPauseListener$listener$1;
    }

    public static /* synthetic */ Animator.AnimatorPauseListener addPauseListener$default(Animator animator, ph0 ph0Var, ph0 ph0Var2, int i, Object obj) {
        if ((i & 1) != 0) {
            ph0Var = AnimatorKt$addPauseListener$1.INSTANCE;
        }
        if ((i & 2) != 0) {
            ph0Var2 = AnimatorKt$addPauseListener$2.INSTANCE;
        }
        fa1.m6826(animator, "<this>");
        fa1.m6826(ph0Var, "onResume");
        fa1.m6826(ph0Var2, "onPause");
        AnimatorKt$addPauseListener$listener$1 animatorKt$addPauseListener$listener$1 = new AnimatorKt$addPauseListener$listener$1(ph0Var2, ph0Var);
        animator.addPauseListener(animatorKt$addPauseListener$listener$1);
        return animatorKt$addPauseListener$listener$1;
    }

    @zu2
    public static final Animator.AnimatorListener doOnCancel(@zu2 Animator animator, @zu2 final ph0<? super Animator, lx5> ph0Var) {
        fa1.m6826(animator, "<this>");
        fa1.m6826(ph0Var, "action");
        Animator.AnimatorListener animatorListener = new Animator.AnimatorListener() { // from class: androidx.core.animation.AnimatorKt$doOnCancel$$inlined$addListener$default$1
            @Override // android.animation.Animator.AnimatorListener
            public void onAnimationCancel(@zu2 Animator animator2) {
                fa1.m6826(animator2, "animator");
                ph0.this.invoke(animator2);
            }

            @Override // android.animation.Animator.AnimatorListener
            public void onAnimationEnd(@zu2 Animator animator2) {
                fa1.m6826(animator2, "animator");
            }

            @Override // android.animation.Animator.AnimatorListener
            public void onAnimationRepeat(@zu2 Animator animator2) {
                fa1.m6826(animator2, "animator");
            }

            @Override // android.animation.Animator.AnimatorListener
            public void onAnimationStart(@zu2 Animator animator2) {
                fa1.m6826(animator2, "animator");
            }
        };
        animator.addListener(animatorListener);
        return animatorListener;
    }

    @zu2
    public static final Animator.AnimatorListener doOnEnd(@zu2 Animator animator, @zu2 final ph0<? super Animator, lx5> ph0Var) {
        fa1.m6826(animator, "<this>");
        fa1.m6826(ph0Var, "action");
        Animator.AnimatorListener animatorListener = new Animator.AnimatorListener() { // from class: androidx.core.animation.AnimatorKt$doOnEnd$$inlined$addListener$default$1
            @Override // android.animation.Animator.AnimatorListener
            public void onAnimationCancel(@zu2 Animator animator2) {
                fa1.m6826(animator2, "animator");
            }

            @Override // android.animation.Animator.AnimatorListener
            public void onAnimationEnd(@zu2 Animator animator2) {
                fa1.m6826(animator2, "animator");
                ph0.this.invoke(animator2);
            }

            @Override // android.animation.Animator.AnimatorListener
            public void onAnimationRepeat(@zu2 Animator animator2) {
                fa1.m6826(animator2, "animator");
            }

            @Override // android.animation.Animator.AnimatorListener
            public void onAnimationStart(@zu2 Animator animator2) {
                fa1.m6826(animator2, "animator");
            }
        };
        animator.addListener(animatorListener);
        return animatorListener;
    }

    @RequiresApi(19)
    @zu2
    public static final Animator.AnimatorPauseListener doOnPause(@zu2 Animator animator, @zu2 final ph0<? super Animator, lx5> ph0Var) {
        fa1.m6826(animator, "<this>");
        fa1.m6826(ph0Var, "action");
        Animator.AnimatorPauseListener animatorPauseListener = new Animator.AnimatorPauseListener() { // from class: androidx.core.animation.AnimatorKt$doOnPause$$inlined$addPauseListener$default$1
            @Override // android.animation.Animator.AnimatorPauseListener
            public void onAnimationPause(@zu2 Animator animator2) {
                fa1.m6826(animator2, "animator");
                ph0.this.invoke(animator2);
            }

            @Override // android.animation.Animator.AnimatorPauseListener
            public void onAnimationResume(@zu2 Animator animator2) {
                fa1.m6826(animator2, "animator");
            }
        };
        animator.addPauseListener(animatorPauseListener);
        return animatorPauseListener;
    }

    @zu2
    public static final Animator.AnimatorListener doOnRepeat(@zu2 Animator animator, @zu2 final ph0<? super Animator, lx5> ph0Var) {
        fa1.m6826(animator, "<this>");
        fa1.m6826(ph0Var, "action");
        Animator.AnimatorListener animatorListener = new Animator.AnimatorListener() { // from class: androidx.core.animation.AnimatorKt$doOnRepeat$$inlined$addListener$default$1
            @Override // android.animation.Animator.AnimatorListener
            public void onAnimationCancel(@zu2 Animator animator2) {
                fa1.m6826(animator2, "animator");
            }

            @Override // android.animation.Animator.AnimatorListener
            public void onAnimationEnd(@zu2 Animator animator2) {
                fa1.m6826(animator2, "animator");
            }

            @Override // android.animation.Animator.AnimatorListener
            public void onAnimationRepeat(@zu2 Animator animator2) {
                fa1.m6826(animator2, "animator");
                ph0.this.invoke(animator2);
            }

            @Override // android.animation.Animator.AnimatorListener
            public void onAnimationStart(@zu2 Animator animator2) {
                fa1.m6826(animator2, "animator");
            }
        };
        animator.addListener(animatorListener);
        return animatorListener;
    }

    @RequiresApi(19)
    @zu2
    public static final Animator.AnimatorPauseListener doOnResume(@zu2 Animator animator, @zu2 final ph0<? super Animator, lx5> ph0Var) {
        fa1.m6826(animator, "<this>");
        fa1.m6826(ph0Var, "action");
        Animator.AnimatorPauseListener animatorPauseListener = new Animator.AnimatorPauseListener() { // from class: androidx.core.animation.AnimatorKt$doOnResume$$inlined$addPauseListener$default$1
            @Override // android.animation.Animator.AnimatorPauseListener
            public void onAnimationPause(@zu2 Animator animator2) {
                fa1.m6826(animator2, "animator");
            }

            @Override // android.animation.Animator.AnimatorPauseListener
            public void onAnimationResume(@zu2 Animator animator2) {
                fa1.m6826(animator2, "animator");
                ph0.this.invoke(animator2);
            }
        };
        animator.addPauseListener(animatorPauseListener);
        return animatorPauseListener;
    }

    @zu2
    public static final Animator.AnimatorListener doOnStart(@zu2 Animator animator, @zu2 final ph0<? super Animator, lx5> ph0Var) {
        fa1.m6826(animator, "<this>");
        fa1.m6826(ph0Var, "action");
        Animator.AnimatorListener animatorListener = new Animator.AnimatorListener() { // from class: androidx.core.animation.AnimatorKt$doOnStart$$inlined$addListener$default$1
            @Override // android.animation.Animator.AnimatorListener
            public void onAnimationCancel(@zu2 Animator animator2) {
                fa1.m6826(animator2, "animator");
            }

            @Override // android.animation.Animator.AnimatorListener
            public void onAnimationEnd(@zu2 Animator animator2) {
                fa1.m6826(animator2, "animator");
            }

            @Override // android.animation.Animator.AnimatorListener
            public void onAnimationRepeat(@zu2 Animator animator2) {
                fa1.m6826(animator2, "animator");
            }

            @Override // android.animation.Animator.AnimatorListener
            public void onAnimationStart(@zu2 Animator animator2) {
                fa1.m6826(animator2, "animator");
                ph0.this.invoke(animator2);
            }
        };
        animator.addListener(animatorListener);
        return animatorListener;
    }
}
