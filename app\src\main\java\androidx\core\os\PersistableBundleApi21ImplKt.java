package androidx.core.os;

import android.os.Build;
import android.os.PersistableBundle;
import androidx.annotation.DoNotInline;
import androidx.annotation.RequiresApi;
import com.stardust.autojs.engine.RhinoJavaScriptEngine;
import kotlin.Metadata;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.pp1;
import okhttp3.internal.p042io.wv2;
import okhttp3.internal.p042io.zu2;
import org.mozilla.javascript.optimizer.Codegen;

@RequiresApi(21)
@Metadata(m4113bv = {}, m4114d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\bÃ\u0002\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\f\u0010\rJ\u0010\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H\u0007J$\u0010\u000b\u001a\u00020\n2\u0006\u0010\u0006\u001a\u00020\u00042\b\u0010\b\u001a\u0004\u0018\u00010\u00072\b\u0010\t\u001a\u0004\u0018\u00010\u0001H\u0007¨\u0006\u000e"}, m4115d2 = {"Landroidx/core/os/PersistableBundleApi21ImplKt;", "", "", "capacity", "Landroid/os/PersistableBundle;", "createPersistableBundle", "persistableBundle", "", "key", "value", "Lokhttp3/internal/io/lx5;", "putValue", RhinoJavaScriptEngine.SOURCE_NAME_INIT, Codegen.TEMPLATE_LITERAL_INIT_METHOD_SIGNATURE, "core-ktx_release"}, m4116k = 1, m4117mv = {1, 6, 0})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
final class PersistableBundleApi21ImplKt {

    @zu2
    public static final PersistableBundleApi21ImplKt INSTANCE = new PersistableBundleApi21ImplKt();

    private PersistableBundleApi21ImplKt() {
    }

    @DoNotInline
    @pp1
    @zu2
    public static final PersistableBundle createPersistableBundle(int capacity) {
        return new PersistableBundle(capacity);
    }

    @DoNotInline
    @pp1
    public static final void putValue(@zu2 PersistableBundle persistableBundle, @wv2 String str, @wv2 Object obj) {
        String str2;
        fa1.m6826(persistableBundle, "persistableBundle");
        if (obj == null) {
            str2 = null;
        } else {
            if (obj instanceof Boolean) {
                if (Build.VERSION.SDK_INT >= 22) {
                    PersistableBundleApi22ImplKt.putBoolean(persistableBundle, str, ((Boolean) obj).booleanValue());
                    return;
                }
                throw new IllegalArgumentException("Illegal value type boolean for key \"" + str + '\"');
            }
            if (obj instanceof Double) {
                persistableBundle.putDouble(str, ((Number) obj).doubleValue());
                return;
            }
            if (obj instanceof Integer) {
                persistableBundle.putInt(str, ((Number) obj).intValue());
                return;
            }
            if (obj instanceof Long) {
                persistableBundle.putLong(str, ((Number) obj).longValue());
                return;
            }
            if (!(obj instanceof String)) {
                if (obj instanceof boolean[]) {
                    if (Build.VERSION.SDK_INT >= 22) {
                        PersistableBundleApi22ImplKt.putBooleanArray(persistableBundle, str, (boolean[]) obj);
                        return;
                    }
                    throw new IllegalArgumentException("Illegal value type boolean[] for key \"" + str + '\"');
                }
                if (obj instanceof double[]) {
                    persistableBundle.putDoubleArray(str, (double[]) obj);
                    return;
                }
                if (obj instanceof int[]) {
                    persistableBundle.putIntArray(str, (int[]) obj);
                    return;
                }
                if (obj instanceof long[]) {
                    persistableBundle.putLongArray(str, (long[]) obj);
                    return;
                }
                if (!(obj instanceof Object[])) {
                    throw new IllegalArgumentException("Illegal value type " + obj.getClass().getCanonicalName() + " for key \"" + str + '\"');
                }
                Class<?> componentType = obj.getClass().getComponentType();
                fa1.m6823(componentType);
                if (String.class.isAssignableFrom(componentType)) {
                    persistableBundle.putStringArray(str, (String[]) obj);
                    return;
                }
                throw new IllegalArgumentException("Illegal value array type " + componentType.getCanonicalName() + " for key \"" + str + '\"');
            }
            str2 = (String) obj;
        }
        persistableBundle.putString(str, str2);
    }
}
