package okhttp3.internal.p042io;

/* renamed from: okhttp3.internal.io.uc */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5296uc extends AbstractC5303ud implements u51 {
    public C5296uc(@pu2 C3318fc c3318fc, @pu2 s13 s13Var, int i) {
        super(c3318fc, s13Var, i);
    }

    @Override // okhttp3.internal.p042io.dw3
    @pu2
    /* renamed from: Ԫ */
    public final aw3 mo4575() {
        C3318fc c3318fc = this.f23079;
        return AbstractC3434ge.m7318(c3318fc, this.f23080.f20800, c3318fc.f9943.m11105(this.f23081 + 2));
    }

    @Override // okhttp3.internal.p042io.n13
    /* renamed from: ؠ */
    public final int mo4577() {
        return this.f23079.f9943.m11104(this.f23081 + 1);
    }

    @Override // okhttp3.internal.p042io.dw3
    /* renamed from: ދ */
    public final int mo4578() {
        return this.f23080.f20800;
    }
}
