package androidx.compose.p000ui.platform;

import okhttp3.internal.p042io.C4350o9;
import okhttp3.internal.p042io.EnumC7329;
import okhttp3.internal.p042io.InterfaceC4988s2;
import okhttp3.internal.p042io.InterfaceC7155;
import okhttp3.internal.p042io.InterfaceC7881;
import okhttp3.internal.p042io.di0;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.u75;
import okhttp3.internal.p042io.wv2;
import okhttp3.internal.p042io.zu2;

@InterfaceC4988s2(m11868c = "androidx.compose.ui.platform.WrappedComposition$setContent$1$1$1", m11869f = "Wrapper.android.kt", m11870l = {153}, m11871m = "invokeSuspend")
/* renamed from: androidx.compose.ui.platform.Ԭ */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class C0188 extends u75 implements di0<InterfaceC7881, InterfaceC7155<? super lx5>, Object> {

    /* renamed from: ၥ */
    public int f186;

    /* renamed from: ၦ */
    public final /* synthetic */ WrappedComposition f187;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public C0188(WrappedComposition wrappedComposition, InterfaceC7155<? super C0188> interfaceC7155) {
        super(2, interfaceC7155);
        this.f187 = wrappedComposition;
    }

    @Override // okhttp3.internal.p042io.AbstractC7853
    @zu2
    public final InterfaceC7155<lx5> create(@wv2 Object obj, @zu2 InterfaceC7155<?> interfaceC7155) {
        return new C0188(this.f187, interfaceC7155);
    }

    @Override // okhttp3.internal.p042io.di0
    /* renamed from: invoke */
    public final Object mo18338invoke(InterfaceC7881 interfaceC7881, InterfaceC7155<? super lx5> interfaceC7155) {
        return ((C0188) create(interfaceC7881, interfaceC7155)).invokeSuspend(lx5.f14876);
    }

    @Override // okhttp3.internal.p042io.AbstractC7853
    @wv2
    public final Object invokeSuspend(@zu2 Object obj) {
        EnumC7329 enumC7329 = EnumC7329.COROUTINE_SUSPENDED;
        int i = this.f186;
        if (i == 0) {
            C4350o9.m10270(obj);
            AndroidComposeView androidComposeView = this.f187.f175;
            this.f186 = 1;
            if (androidComposeView.keyboardVisibilityEventLoop(this) == enumC7329) {
                return enumC7329;
            }
        } else {
            if (i != 1) {
                throw new IllegalStateException("call to 'resume' before 'invoke' with coroutine");
            }
            C4350o9.m10270(obj);
        }
        return lx5.f14876;
    }
}
