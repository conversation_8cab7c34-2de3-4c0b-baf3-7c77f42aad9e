package androidx.core.location;

import android.location.Location;
import kotlin.Metadata;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.zu2;

@Metadata(m4114d1 = {"\u0000\u000e\n\u0000\n\u0002\u0010\u0006\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a\r\u0010\u0000\u001a\u00020\u0001*\u00020\u0002H\u0086\n\u001a\r\u0010\u0003\u001a\u00020\u0001*\u00020\u0002H\u0086\n¨\u0006\u0004"}, m4115d2 = {"component1", "", "Landroid/location/Location;", "component2", "core-ktx_release"}, m4116k = 2, m4117mv = {1, 6, 0}, m4119xi = 48)
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class LocationKt {
    public static final double component1(@zu2 Location location) {
        fa1.m6826(location, "<this>");
        return location.getLatitude();
    }

    public static final double component2(@zu2 Location location) {
        fa1.m6826(location, "<this>");
        return location.getLongitude();
    }
}
