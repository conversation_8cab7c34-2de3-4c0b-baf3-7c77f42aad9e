package okhttp3.internal.p042io;

import java.util.Objects;
import okhttp3.internal.p042io.vn5;

/* renamed from: okhttp3.internal.io.vs */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes3.dex */
public final class C5513vs extends lv1 implements fi0<vn5.InterfaceC5494<EnumC4542ps>, InterfaceC6968, Integer, u50<Float>> {

    /* renamed from: ၥ */
    public final /* synthetic */ AbstractC3067ct f24437;

    /* renamed from: ၦ */
    public final /* synthetic */ AbstractC3493gx f24438;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public C5513vs(AbstractC3067ct abstractC3067ct, AbstractC3493gx abstractC3493gx) {
        super(3);
        this.f24437 = abstractC3067ct;
        this.f24438 = abstractC3493gx;
    }

    @Override // okhttp3.internal.p042io.fi0
    public final u50<Float> invoke(vn5.InterfaceC5494<EnumC4542ps> interfaceC5494, InterfaceC6968 interfaceC6968, Integer num) {
        xn5 mo7492;
        vn5.InterfaceC5494<EnumC4542ps> interfaceC54942 = interfaceC5494;
        InterfaceC6968 interfaceC69682 = interfaceC6968;
        num.intValue();
        fa1.m6826(interfaceC54942, "$this$animateFloat");
        interfaceC69682.mo16268(-9519413);
        EnumC4542ps enumC4542ps = EnumC4542ps.PreEnter;
        EnumC4542ps enumC4542ps2 = EnumC4542ps.Visible;
        if (!interfaceC54942.mo13292(enumC4542ps, enumC4542ps2)) {
            if (interfaceC54942.mo13292(enumC4542ps2, EnumC4542ps.PostExit)) {
                mo7492 = this.f24438.mo7492();
            }
            ay4<Float> ay4Var = C4838qs.f19486;
            interfaceC69682.mo16300();
            return ay4Var;
        }
        mo7492 = this.f24437.mo5806();
        Objects.requireNonNull(mo7492);
        ay4<Float> ay4Var2 = C4838qs.f19486;
        interfaceC69682.mo16300();
        return ay4Var2;
    }
}
