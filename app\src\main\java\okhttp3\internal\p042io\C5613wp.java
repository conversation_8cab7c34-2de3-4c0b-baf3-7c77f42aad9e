package okhttp3.internal.p042io;

import java.math.BigInteger;
import java.security.spec.AlgorithmParameterSpec;

/* renamed from: okhttp3.internal.io.wp */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5613wp implements AlgorithmParameterSpec {

    /* renamed from: ၥ */
    public BigInteger f25131;

    /* renamed from: ၦ */
    public BigInteger f25132;

    public C5613wp(BigInteger bigInteger, BigInteger bigInteger2) {
        this.f25131 = bigInteger;
        this.f25132 = bigInteger2;
    }
}
