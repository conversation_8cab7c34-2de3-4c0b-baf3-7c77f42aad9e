package androidx.core.os;

import androidx.exifinterface.media.ExifInterface;
import kotlin.Metadata;
import okhttp3.internal.p042io.InterfaceC5795y7;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.nh0;
import okhttp3.internal.p042io.zu2;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000\u0010\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\u001a/\u0010\u0005\u001a\u00028\u0000\"\u0004\b\u0000\u0010\u00002\u0006\u0010\u0002\u001a\u00020\u00012\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00028\u00000\u0003H\u0087\bø\u0001\u0000¢\u0006\u0004\b\u0005\u0010\u0006\u0082\u0002\u0007\n\u0005\b\u009920\u0001¨\u0006\u0007"}, m4115d2 = {ExifInterface.GPS_DIRECTION_TRUE, "", "sectionName", "Lkotlin/Function0;", "block", "trace", "(Ljava/lang/String;Lokhttp3/internal/io/nh0;)Ljava/lang/Object;", "core-ktx_release"}, m4116k = 2, m4117mv = {1, 6, 0})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class TraceKt {
    @InterfaceC5795y7
    public static final <T> T trace(@zu2 String str, @zu2 nh0<? extends T> nh0Var) {
        fa1.m6826(str, "sectionName");
        fa1.m6826(nh0Var, "block");
        TraceCompat.beginSection(str);
        try {
            return nh0Var.invoke();
        } finally {
            TraceCompat.endSection();
        }
    }
}
