package androidx.datastore.preferences.core;

import androidx.datastore.core.DataStore;
import kotlin.Metadata;
import okhttp3.internal.p042io.InterfaceC7155;
import okhttp3.internal.p042io.di0;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.wv2;
import okhttp3.internal.p042io.zu2;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0004\u001aA\u0010\b\u001a\u00020\u0001*\b\u0012\u0004\u0012\u00020\u00010\u00002\"\u0010\u0007\u001a\u001e\b\u0001\u0012\u0004\u0012\u00020\u0003\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u0004\u0012\u0006\u0012\u0004\u0018\u00010\u00060\u0002H\u0086@ø\u0001\u0000¢\u0006\u0004\b\b\u0010\t\u0082\u0002\u0004\n\u0002\b\u0019¨\u0006\n"}, m4115d2 = {"Landroidx/datastore/core/DataStore;", "Landroidx/datastore/preferences/core/Preferences;", "Lkotlin/Function2;", "Landroidx/datastore/preferences/core/MutablePreferences;", "Lokhttp3/internal/io/ৡ;", "Lokhttp3/internal/io/lx5;", "", "transform", "edit", "(Landroidx/datastore/core/DataStore;Lokhttp3/internal/io/di0;Lokhttp3/internal/io/ৡ;)Ljava/lang/Object;", "datastore-preferences-core"}, m4116k = 2, m4117mv = {1, 5, 1})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class PreferencesKt {
    @wv2
    public static final Object edit(@zu2 DataStore<Preferences> dataStore, @zu2 di0<? super MutablePreferences, ? super InterfaceC7155<? super lx5>, ? extends Object> di0Var, @zu2 InterfaceC7155<? super Preferences> interfaceC7155) {
        return dataStore.updateData(new PreferencesKt$edit$2(di0Var, null), interfaceC7155);
    }
}
