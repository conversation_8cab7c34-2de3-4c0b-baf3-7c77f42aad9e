package okhttp3.internal.p042io;

import com.stardust.autojs.core.inputevent.InputEventCodes;
import java.util.ArrayList;

@InterfaceC4988s2(m11868c = "org.autojs.hrapps.visual.lib.main.fragment.DesignFragment", m11869f = "DesignFragment.kt", m11870l = {InputEventCodes.KEY_RFKILL, 281}, m11871m = "addWidget")
/* renamed from: okhttp3.internal.io.wa */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5566wa extends AbstractC6644 {

    /* renamed from: ၥ */
    public C5452va f24736;

    /* renamed from: ၦ */
    public ArrayList f24737;

    /* renamed from: ၮ */
    public bb6 f24738;

    /* renamed from: ၯ */
    public int f24739;

    /* renamed from: ၰ */
    public /* synthetic */ Object f24740;

    /* renamed from: ၵ */
    public final /* synthetic */ C5452va f24741;

    /* renamed from: ၶ */
    public int f24742;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public C5566wa(C5452va c5452va, InterfaceC7155<? super C5566wa> interfaceC7155) {
        super(interfaceC7155);
        this.f24741 = c5452va;
    }

    @Override // okhttp3.internal.p042io.AbstractC7853
    @wv2
    public final Object invokeSuspend(@zu2 Object obj) {
        this.f24740 = obj;
        this.f24742 |= Integer.MIN_VALUE;
        return C5452va.m13155(this.f24741, 0, null, null, this);
    }
}
