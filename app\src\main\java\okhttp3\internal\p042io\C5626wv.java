package okhttp3.internal.p042io;

/* renamed from: okhttp3.internal.io.wv */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5626wv implements a54<InterfaceC5361uv> {

    /* renamed from: Ϳ */
    @pu2
    public final g54 f25179;

    /* renamed from: okhttp3.internal.io.wv$Ϳ, reason: contains not printable characters */
    public class C9473 extends AbstractC7602 {

        /* renamed from: ၥ */
        @pu2
        public InterfaceC5361uv f25180;

        public C9473(@pu2 InterfaceC5361uv interfaceC5361uv) {
            this.f25180 = interfaceC5361uv;
        }

        @Override // okhttp3.internal.p042io.InterfaceC5361uv
        @vv2
        /* renamed from: ށ */
        public final String mo10331() {
            return (String) f54.m6759(C5626wv.this.f25179.mo7232(), this.f25180.mo10331());
        }

        @Override // okhttp3.internal.p042io.InterfaceC5361uv
        /* renamed from: ޒ */
        public final int mo10332() {
            return this.f25180.mo10332();
        }
    }

    public C5626wv(@pu2 g54 g54Var) {
        this.f25179 = g54Var;
    }

    @Override // okhttp3.internal.p042io.a54
    @pu2
    /* renamed from: Ϳ */
    public final InterfaceC5361uv mo4402(@pu2 InterfaceC5361uv interfaceC5361uv) {
        return new C9473(interfaceC5361uv);
    }
}
