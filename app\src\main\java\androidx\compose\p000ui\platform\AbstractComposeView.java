package androidx.compose.p000ui.platform;

import android.content.Context;
import android.os.IBinder;
import android.util.AttributeSet;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import androidx.customview.poolingcontainer.PoolingContainer;
import com.stardust.autojs.engine.RhinoJavaScriptEngine;
import java.lang.ref.WeakReference;
import kotlin.Metadata;
import okhttp3.internal.p042io.AbstractC6779;
import okhttp3.internal.p042io.C2849b5;
import okhttp3.internal.p042io.C7171;
import okhttp3.internal.p042io.InterfaceC6575;
import okhttp3.internal.p042io.InterfaceC6968;
import okhttp3.internal.p042io.InterfaceC7452;
import okhttp3.internal.p042io.InterfaceC7474;
import okhttp3.internal.p042io.a86;
import okhttp3.internal.p042io.aw5;
import okhttp3.internal.p042io.bp1;
import okhttp3.internal.p042io.di0;
import okhttp3.internal.p042io.dv3;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.fi0;
import okhttp3.internal.p042io.fz4;
import okhttp3.internal.p042io.hz3;
import okhttp3.internal.p042io.lf2;
import okhttp3.internal.p042io.lu4;
import okhttp3.internal.p042io.lv1;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.nh0;
import okhttp3.internal.p042io.ns2;
import okhttp3.internal.p042io.s81;
import okhttp3.internal.p042io.wv2;
import okhttp3.internal.p042io.x76;
import okhttp3.internal.p042io.y33;
import okhttp3.internal.p042io.y76;
import okhttp3.internal.p042io.z76;
import okhttp3.internal.p042io.zu2;
import org.mozilla.javascript.optimizer.Codegen;

@fz4
@Metadata(m4113bv = {}, m4114d1 = {"\u0000R\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\b\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0002\b\n\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0017\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\b'\u0018\u00002\u00020\u0001B'\b\u0007\u0012\u0006\u0010@\u001a\u00020?\u0012\n\b\u0002\u0010B\u001a\u0004\u0018\u00010A\u0012\b\b\u0002\u0010C\u001a\u00020\r¢\u0006\u0004\bD\u0010EJ\u0010\u0010\u0005\u001a\u00020\u00042\b\u0010\u0003\u001a\u0004\u0018\u00010\u0002J\u000e\u0010\b\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\u0006J\u000f\u0010\t\u001a\u00020\u0004H'¢\u0006\u0004\b\t\u0010\nJ\u0006\u0010\u000b\u001a\u00020\u0004J\u0006\u0010\f\u001a\u00020\u0004J\u001f\u0010\u0012\u001a\u00020\u00042\u0006\u0010\u000e\u001a\u00020\r2\u0006\u0010\u000f\u001a\u00020\rH\u0010¢\u0006\u0004\b\u0010\u0010\u0011J7\u0010\u001b\u001a\u00020\u00042\u0006\u0010\u0014\u001a\u00020\u00132\u0006\u0010\u0015\u001a\u00020\r2\u0006\u0010\u0016\u001a\u00020\r2\u0006\u0010\u0017\u001a\u00020\r2\u0006\u0010\u0018\u001a\u00020\rH\u0010¢\u0006\u0004\b\u0019\u0010\u001aJ\u0010\u0010\u001d\u001a\u00020\u00042\u0006\u0010\u001c\u001a\u00020\rH\u0016J\u0012\u0010 \u001a\u00020\u00042\b\u0010\u001f\u001a\u0004\u0018\u00010\u001eH\u0016J\u001a\u0010 \u001a\u00020\u00042\b\u0010\u001f\u001a\u0004\u0018\u00010\u001e2\u0006\u0010!\u001a\u00020\rH\u0016J\"\u0010 \u001a\u00020\u00042\b\u0010\u001f\u001a\u0004\u0018\u00010\u001e2\u0006\u0010\"\u001a\u00020\r2\u0006\u0010#\u001a\u00020\rH\u0016J\u001c\u0010 \u001a\u00020\u00042\b\u0010\u001f\u001a\u0004\u0018\u00010\u001e2\b\u0010%\u001a\u0004\u0018\u00010$H\u0016J$\u0010 \u001a\u00020\u00042\b\u0010\u001f\u001a\u0004\u0018\u00010\u001e2\u0006\u0010!\u001a\u00020\r2\b\u0010%\u001a\u0004\u0018\u00010$H\u0016J\b\u0010&\u001a\u00020\u0013H\u0016R(\u0010-\u001a\u0004\u0018\u00010'2\b\u0010(\u001a\u0004\u0018\u00010'8\u0002@BX\u0082\u000e¢\u0006\f\n\u0004\b)\u0010*\"\u0004\b+\u0010,R0\u00106\u001a\u00020\u00132\u0006\u0010(\u001a\u00020\u00138F@FX\u0087\u000e¢\u0006\u0018\n\u0004\b.\u0010/\u0012\u0004\b4\u00105\u001a\u0004\b0\u00101\"\u0004\b2\u00103R(\u00107\u001a\u0004\u0018\u00010\u00022\b\u0010(\u001a\u0004\u0018\u00010\u00028\u0002@BX\u0082\u000e¢\u0006\f\n\u0004\b7\u00108\"\u0004\b9\u0010:R\u0014\u0010<\u001a\u00020\u00138TX\u0094\u0004¢\u0006\u0006\u001a\u0004\b;\u00101R\u0011\u0010>\u001a\u00020\u00138F¢\u0006\u0006\u001a\u0004\b=\u00101¨\u0006F"}, m4115d2 = {"Landroidx/compose/ui/platform/AbstractComposeView;", "Landroid/view/ViewGroup;", "Lokhttp3/internal/io/ܔ;", "parent", "Lokhttp3/internal/io/lx5;", "setParentCompositionContext", "Lokhttp3/internal/io/a86;", "strategy", "setViewCompositionStrategy", "Content", "(Lokhttp3/internal/io/ࡊ;I)V", "createComposition", "disposeComposition", "", "widthMeasureSpec", "heightMeasureSpec", "internalOnMeasure$ui_release", "(II)V", "internalOnMeasure", "", "changed", "left", "top", "right", "bottom", "internalOnLayout$ui_release", "(ZIIII)V", "internalOnLayout", "layoutDirection", "onRtlPropertiesChanged", "Landroid/view/View;", "child", "addView", "index", "width", "height", "Landroid/view/ViewGroup$LayoutParams;", "params", "shouldDelayChildPressedState", "Landroid/os/IBinder;", "value", "ၦ", "Landroid/os/IBinder;", "setPreviousAttachedWindowToken", "(Landroid/os/IBinder;)V", "previousAttachedWindowToken", "ၵ", "Z", "getShowLayoutBounds", "()Z", "setShowLayoutBounds", "(Z)V", "getShowLayoutBounds$annotations", Codegen.TEMPLATE_LITERAL_INIT_METHOD_SIGNATURE, "showLayoutBounds", "parentContext", "Lokhttp3/internal/io/ܔ;", "setParentContext", "(Lokhttp3/internal/io/ܔ;)V", "getShouldCreateCompositionOnAttachedToWindow", "shouldCreateCompositionOnAttachedToWindow", "getHasComposition", "hasComposition", "Landroid/content/Context;", "context", "Landroid/util/AttributeSet;", "attrs", "defStyleAttr", RhinoJavaScriptEngine.SOURCE_NAME_INIT, "(Landroid/content/Context;Landroid/util/AttributeSet;I)V", "ui_release"}, m4116k = 1, m4117mv = {1, 7, 1})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public abstract class AbstractComposeView extends ViewGroup {

    /* renamed from: ၥ */
    @wv2
    public WeakReference<AbstractC6779> f37;

    /* renamed from: ၦ, reason: from kotlin metadata */
    @wv2
    public IBinder previousAttachedWindowToken;

    /* renamed from: ၮ */
    @wv2
    public InterfaceC7474 f39;

    /* renamed from: ၯ */
    @wv2
    public AbstractC6779 f40;

    /* renamed from: ၰ */
    @wv2
    public nh0<lx5> f41;

    /* renamed from: ၵ, reason: from kotlin metadata */
    public boolean showLayoutBounds;

    /* renamed from: ၶ */
    public boolean f43;

    /* renamed from: androidx.compose.ui.platform.AbstractComposeView$Ϳ */
    public static final class C0164 extends lv1 implements di0<InterfaceC6968, Integer, lx5> {
        public C0164() {
            super(2);
        }

        @Override // okhttp3.internal.p042io.di0
        /* renamed from: invoke */
        public final lx5 mo18338invoke(InterfaceC6968 interfaceC6968, Integer num) {
            InterfaceC6968 interfaceC69682 = interfaceC6968;
            if ((num.intValue() & 11) == 2 && interfaceC69682.mo16283()) {
                interfaceC69682.mo16290();
            } else {
                fi0<InterfaceC6575<?>, lu4, hz3, lx5> fi0Var = C7171.f31108;
                AbstractComposeView.this.Content(interfaceC69682, 8);
            }
            return lx5.f14876;
        }
    }

    /* JADX WARN: 'this' call moved to the top of the method (can break code semantics) */
    @bp1
    public AbstractComposeView(@zu2 Context context) {
        this(context, null, 0, 6, null);
        fa1.m6826(context, "context");
    }

    /* JADX WARN: 'this' call moved to the top of the method (can break code semantics) */
    @bp1
    public AbstractComposeView(@zu2 Context context, @wv2 AttributeSet attributeSet) {
        this(context, attributeSet, 0, 4, null);
        fa1.m6826(context, "context");
    }

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    @bp1
    public AbstractComposeView(@zu2 Context context, @wv2 AttributeSet attributeSet, int i) {
        super(context, attributeSet, i);
        fa1.m6826(context, "context");
        setClipChildren(false);
        setClipToPadding(false);
        int i2 = a86.f6185;
        y76 y76Var = new y76(this);
        addOnAttachStateChangeListener(y76Var);
        z76 z76Var = new z76(this);
        PoolingContainer.addPoolingContainerListener(this, z76Var);
        this.f41 = new x76(this, y76Var, z76Var);
    }

    public /* synthetic */ AbstractComposeView(Context context, AttributeSet attributeSet, int i, int i2, C2849b5 c2849b5) {
        this(context, (i2 & 2) != 0 ? null : attributeSet, (i2 & 4) != 0 ? 0 : i);
    }

    private static /* synthetic */ void getDisposeViewCompositionStrategy$annotations() {
    }

    @s81
    public static /* synthetic */ void getShowLayoutBounds$annotations() {
    }

    private final void setParentContext(AbstractC6779 abstractC6779) {
        if (this.f40 != abstractC6779) {
            this.f40 = abstractC6779;
            if (abstractC6779 != null) {
                this.f37 = null;
            }
            InterfaceC7474 interfaceC7474 = this.f39;
            if (interfaceC7474 != null) {
                interfaceC7474.dispose();
                this.f39 = null;
                if (isAttachedToWindow()) {
                    m28();
                }
            }
        }
    }

    private final void setPreviousAttachedWindowToken(IBinder iBinder) {
        if (this.previousAttachedWindowToken != iBinder) {
            this.previousAttachedWindowToken = iBinder;
            this.f37 = null;
        }
    }

    @aw5
    @InterfaceC7452
    public abstract void Content(@wv2 InterfaceC6968 interfaceC6968, int i);

    @Override // android.view.ViewGroup
    public void addView(@wv2 View view) {
        m27();
        super.addView(view);
    }

    @Override // android.view.ViewGroup
    public void addView(@wv2 View view, int i) {
        m27();
        super.addView(view, i);
    }

    @Override // android.view.ViewGroup
    public void addView(@wv2 View view, int i, int i2) {
        m27();
        super.addView(view, i, i2);
    }

    @Override // android.view.ViewGroup
    public void addView(@wv2 View view, int i, @wv2 ViewGroup.LayoutParams layoutParams) {
        m27();
        super.addView(view, i, layoutParams);
    }

    @Override // android.view.ViewGroup, android.view.ViewManager
    public void addView(@wv2 View view, @wv2 ViewGroup.LayoutParams layoutParams) {
        m27();
        super.addView(view, layoutParams);
    }

    @Override // android.view.ViewGroup
    public final boolean addViewInLayout(@wv2 View view, int i, @wv2 ViewGroup.LayoutParams layoutParams) {
        m27();
        return super.addViewInLayout(view, i, layoutParams);
    }

    @Override // android.view.ViewGroup
    public final boolean addViewInLayout(@wv2 View view, int i, @wv2 ViewGroup.LayoutParams layoutParams, boolean z) {
        m27();
        return super.addViewInLayout(view, i, layoutParams, z);
    }

    public final void createComposition() {
        if (!(this.f40 != null || isAttachedToWindow())) {
            throw new IllegalStateException("createComposition requires either a parent reference or the View to be attachedto a window. Attach the View or call setParentCompositionReference.".toString());
        }
        m28();
    }

    public final void disposeComposition() {
        InterfaceC7474 interfaceC7474 = this.f39;
        if (interfaceC7474 != null) {
            interfaceC7474.dispose();
        }
        this.f39 = null;
        requestLayout();
    }

    public final boolean getHasComposition() {
        return this.f39 != null;
    }

    public boolean getShouldCreateCompositionOnAttachedToWindow() {
        return true;
    }

    public final boolean getShowLayoutBounds() {
        return this.showLayoutBounds;
    }

    public void internalOnLayout$ui_release(boolean changed, int left, int top, int right, int bottom) {
        View childAt = getChildAt(0);
        if (childAt != null) {
            childAt.layout(getPaddingLeft(), getPaddingTop(), (right - left) - getPaddingRight(), (bottom - top) - getPaddingBottom());
        }
    }

    public void internalOnMeasure$ui_release(int widthMeasureSpec, int heightMeasureSpec) {
        View childAt = getChildAt(0);
        if (childAt == null) {
            super.onMeasure(widthMeasureSpec, heightMeasureSpec);
            return;
        }
        childAt.measure(View.MeasureSpec.makeMeasureSpec(Math.max(0, (View.MeasureSpec.getSize(widthMeasureSpec) - getPaddingLeft()) - getPaddingRight()), View.MeasureSpec.getMode(widthMeasureSpec)), View.MeasureSpec.makeMeasureSpec(Math.max(0, (View.MeasureSpec.getSize(heightMeasureSpec) - getPaddingTop()) - getPaddingBottom()), View.MeasureSpec.getMode(heightMeasureSpec)));
        setMeasuredDimension(getPaddingRight() + getPaddingLeft() + childAt.getMeasuredWidth(), getPaddingBottom() + getPaddingTop() + childAt.getMeasuredHeight());
    }

    @Override // android.view.ViewGroup, android.view.View
    public final void onAttachedToWindow() {
        super.onAttachedToWindow();
        setPreviousAttachedWindowToken(getWindowToken());
        if (getShouldCreateCompositionOnAttachedToWindow()) {
            m28();
        }
    }

    @Override // android.view.ViewGroup, android.view.View
    public final void onLayout(boolean z, int i, int i2, int i3, int i4) {
        internalOnLayout$ui_release(z, i, i2, i3, i4);
    }

    @Override // android.view.View
    public final void onMeasure(int i, int i2) {
        m28();
        internalOnMeasure$ui_release(i, i2);
    }

    @Override // android.view.View
    public void onRtlPropertiesChanged(int i) {
        View childAt = getChildAt(0);
        if (childAt == null) {
            return;
        }
        childAt.setLayoutDirection(i);
    }

    public final void setParentCompositionContext(@wv2 AbstractC6779 abstractC6779) {
        setParentContext(abstractC6779);
    }

    public final void setShowLayoutBounds(boolean z) {
        this.showLayoutBounds = z;
        KeyEvent.Callback childAt = getChildAt(0);
        if (childAt != null) {
            ((y33) childAt).setShowLayoutBounds(z);
        }
    }

    public final void setViewCompositionStrategy(@zu2 a86 a86Var) {
        fa1.m6826(a86Var, "strategy");
        nh0<lx5> nh0Var = this.f41;
        if (nh0Var != null) {
            nh0Var.invoke();
        }
        this.f41 = a86Var.m4479(this);
    }

    @Override // android.view.ViewGroup
    public boolean shouldDelayChildPressedState() {
        return false;
    }

    /* renamed from: Ϳ */
    public final void m27() {
        if (this.f43) {
            return;
        }
        StringBuilder m9240 = lf2.m9240("Cannot add views to ");
        m9240.append(getClass().getSimpleName());
        m9240.append("; only Compose content is supported");
        throw new UnsupportedOperationException(m9240.toString());
    }

    /* renamed from: Ԩ */
    public final void m28() {
        if (this.f39 == null) {
            try {
                this.f43 = true;
                this.f39 = C0192.m59(this, m30(), ns2.m10132(-656146368, true, new C0164()));
            } finally {
                this.f43 = false;
            }
        }
    }

    /* renamed from: ԩ */
    public final boolean m29(AbstractC6779 abstractC6779) {
        return !(abstractC6779 instanceof dv3) || ((dv3) abstractC6779).f8879.getValue().compareTo(dv3.EnumC3171.ShuttingDown) > 0;
    }

    /* JADX WARN: Code restructure failed: missing block: B:25:0x004c, code lost:
    
        if (r2 != false) goto L30;
     */
    /* renamed from: Ԫ */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final okhttp3.internal.p042io.AbstractC6779 m30() {
        /*
            Method dump skipped, instructions count: 250
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.compose.p000ui.platform.AbstractComposeView.m30():okhttp3.internal.io.ܔ");
    }
}
