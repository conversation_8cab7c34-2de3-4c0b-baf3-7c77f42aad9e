package androidx.compose.p000ui.text.android;

import android.graphics.Paint;
import android.text.Layout;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.fz4;
import okhttp3.internal.p042io.g32;
import okhttp3.internal.p042io.kw1;
import okhttp3.internal.p042io.vb5;
import okhttp3.internal.p042io.wv2;
import okhttp3.internal.p042io.wx1;
import okhttp3.internal.p042io.z81;
import okhttp3.internal.p042io.zu2;

@z81
@fz4
/* renamed from: androidx.compose.ui.text.android.Ԯ */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class C0197 {

    /* renamed from: Ϳ */
    public final boolean f199;

    /* renamed from: Ԩ */
    public final boolean f200;

    /* renamed from: ԩ */
    public final boolean f201;

    /* renamed from: Ԫ */
    @zu2
    public final Layout f202;

    /* renamed from: ԫ */
    public final int f203;

    /* renamed from: Ԭ */
    public final int f204;

    /* renamed from: ԭ */
    public final int f205;

    /* renamed from: Ԯ */
    public final float f206;

    /* renamed from: ԯ */
    public final float f207;

    /* renamed from: ֏ */
    public final boolean f208;

    /* renamed from: ؠ */
    @wv2
    public final Paint.FontMetricsInt f209;

    /* renamed from: ހ */
    public final int f210;

    /* renamed from: ށ */
    @zu2
    public final g32[] f211;

    /* renamed from: ނ */
    @zu2
    public final vb5 f212;

    /* renamed from: ރ */
    @zu2
    public final wx1 f213;

    /* JADX WARN: Code restructure failed: missing block: B:55:0x01c7, code lost:
    
        if ((r2.length == 0) != false) goto L76;
     */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:25:0x00ef  */
    /* JADX WARN: Removed duplicated region for block: B:32:0x0109  */
    /* JADX WARN: Removed duplicated region for block: B:52:0x01a6  */
    /* JADX WARN: Removed duplicated region for block: B:58:0x01d3  */
    /* JADX WARN: Removed duplicated region for block: B:69:0x01f2 A[ADDED_TO_REGION] */
    /* JADX WARN: Removed duplicated region for block: B:73:0x0244  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public C0197(java.lang.CharSequence r47, float r48, android.text.TextPaint r49, int r50, android.text.TextUtils.TruncateAt r51, int r52, boolean r53, int r54, int r55, int r56, int r57, int r58, int r59, okhttp3.internal.p042io.uw1 r60) {
        /*
            Method dump skipped, instructions count: 840
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.compose.p000ui.text.android.C0197.<init>(java.lang.CharSequence, float, android.text.TextPaint, int, android.text.TextUtils$TruncateAt, int, boolean, int, int, int, int, int, int, okhttp3.internal.io.uw1):void");
    }

    /* renamed from: Ϳ */
    public final int m63() {
        return (this.f201 ? this.f202.getLineBottom(this.f203 - 1) : this.f202.getHeight()) + this.f204 + this.f205 + this.f210;
    }

    /* renamed from: Ԩ */
    public final float m64(int i) {
        if (i == this.f203 - 1) {
            return this.f206 + this.f207;
        }
        return 0.0f;
    }

    /* renamed from: ԩ */
    public final float m65(int i) {
        return this.f204 + ((i != this.f203 + (-1) || this.f209 == null) ? this.f202.getLineBaseline(i) : m69(i) - this.f209.ascent);
    }

    /* renamed from: Ԫ */
    public final float m66(int i) {
        if (i != this.f203 - 1 || this.f209 == null) {
            return this.f204 + this.f202.getLineBottom(i) + (i == this.f203 + (-1) ? this.f205 : 0);
        }
        return this.f202.getLineBottom(i - 1) + this.f209.bottom;
    }

    /* renamed from: ԫ */
    public final int m67(int i) {
        return this.f202.getEllipsisStart(i) == 0 ? this.f202.getLineEnd(i) : this.f202.getText().length();
    }

    /* renamed from: Ԭ */
    public final int m68(int i) {
        return this.f202.getLineForOffset(i);
    }

    /* renamed from: ԭ */
    public final float m69(int i) {
        return this.f202.getLineTop(i) + (i == 0 ? 0 : this.f204);
    }

    /* renamed from: Ԯ */
    public final float m70(int i, boolean z) {
        return m64(m68(i)) + ((kw1) this.f213.getValue()).m9105(i, true, z);
    }

    /* renamed from: ԯ */
    public final float m71(int i, boolean z) {
        return m64(m68(i)) + ((kw1) this.f213.getValue()).m9105(i, false, z);
    }

    @zu2
    /* renamed from: ֏ */
    public final CharSequence m72() {
        CharSequence text = this.f202.getText();
        fa1.m6825(text, "layout.text");
        return text;
    }
}
