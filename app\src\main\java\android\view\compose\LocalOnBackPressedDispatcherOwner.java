package android.view.compose;

import android.content.Context;
import android.content.ContextWrapper;
import android.view.OnBackPressedDispatcherOwner;
import android.view.View;
import com.stardust.autojs.engine.RhinoJavaScriptEngine;
import kotlin.Metadata;
import okhttp3.internal.p042io.C7313;
import okhttp3.internal.p042io.C7771;
import okhttp3.internal.p042io.InterfaceC6968;
import okhttp3.internal.p042io.InterfaceC7452;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.fz4;
import okhttp3.internal.p042io.ho3;
import okhttp3.internal.p042io.jo3;
import okhttp3.internal.p042io.k55;
import okhttp3.internal.p042io.wv2;
import okhttp3.internal.p042io.yo1;
import okhttp3.internal.p042io.zu2;
import org.mozilla.javascript.optimizer.Codegen;

@fz4
@Metadata(m4113bv = {}, m4114d1 = {"\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\bÇ\u0002\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\t\u0010\nJ\u0019\u0010\u0005\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H\u0086\u0004R\u0013\u0010\b\u001a\u0004\u0018\u00010\u00028G¢\u0006\u0006\u001a\u0004\b\u0006\u0010\u0007¨\u0006\u000b"}, m4115d2 = {"Landroidx/activity/compose/LocalOnBackPressedDispatcherOwner;", "", "Landroidx/activity/OnBackPressedDispatcherOwner;", "dispatcherOwner", "Lokhttp3/internal/io/jo3;", "provides", "getCurrent", "(Lokhttp3/internal/io/ࡊ;I)Landroidx/activity/OnBackPressedDispatcherOwner;", "current", RhinoJavaScriptEngine.SOURCE_NAME_INIT, Codegen.TEMPLATE_LITERAL_INIT_METHOD_SIGNATURE, "activity-compose_release"}, m4116k = 1, m4117mv = {1, 6, 0})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class LocalOnBackPressedDispatcherOwner {
    public static final int $stable = 0;

    @zu2
    public static final LocalOnBackPressedDispatcherOwner INSTANCE = new LocalOnBackPressedDispatcherOwner();

    @zu2
    private static final ho3<OnBackPressedDispatcherOwner> LocalOnBackPressedDispatcherOwner;

    static {
        ho3<OnBackPressedDispatcherOwner> m17400;
        m17400 = C7771.m17400(k55.f13609, C0043xbb3fc51f.INSTANCE);
        LocalOnBackPressedDispatcherOwner = m17400;
    }

    private LocalOnBackPressedDispatcherOwner() {
    }

    @yo1
    @wv2
    @InterfaceC7452
    public final OnBackPressedDispatcherOwner getCurrent(@wv2 InterfaceC6968 interfaceC6968, int i) {
        interfaceC6968.mo16268(-2068013981);
        OnBackPressedDispatcherOwner onBackPressedDispatcherOwner = (OnBackPressedDispatcherOwner) interfaceC6968.mo16276(LocalOnBackPressedDispatcherOwner);
        interfaceC6968.mo16268(1680121597);
        if (onBackPressedDispatcherOwner == null) {
            onBackPressedDispatcherOwner = View.get((android.view.View) interfaceC6968.mo16276(C7313.f31551));
        }
        interfaceC6968.mo16300();
        if (onBackPressedDispatcherOwner == null) {
            Object obj = (Context) interfaceC6968.mo16276(C7313.f31547);
            while (true) {
                if (!(obj instanceof ContextWrapper)) {
                    obj = null;
                    break;
                }
                if (obj instanceof OnBackPressedDispatcherOwner) {
                    break;
                }
                obj = ((ContextWrapper) obj).getBaseContext();
                fa1.m6825(obj, "innerContext.baseContext");
            }
            onBackPressedDispatcherOwner = (OnBackPressedDispatcherOwner) obj;
        }
        interfaceC6968.mo16300();
        return onBackPressedDispatcherOwner;
    }

    @zu2
    public final jo3<OnBackPressedDispatcherOwner> provides(@zu2 OnBackPressedDispatcherOwner dispatcherOwner) {
        fa1.m6826(dispatcherOwner, "dispatcherOwner");
        return LocalOnBackPressedDispatcherOwner.m7927(dispatcherOwner);
    }
}
