package androidx.compose.foundation.layout;

import okhttp3.internal.p042io.C7409;
import okhttp3.internal.p042io.bf2;
import okhttp3.internal.p042io.di0;
import okhttp3.internal.p042io.ea1;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.m91;
import okhttp3.internal.p042io.ph0;
import okhttp3.internal.p042io.qk2;
import okhttp3.internal.p042io.rk2;
import okhttp3.internal.p042io.sk2;
import okhttp3.internal.p042io.ue2;
import okhttp3.internal.p042io.ze2;
import okhttp3.internal.p042io.zu2;

/* renamed from: androidx.compose.foundation.layout.Ԫ */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class C0159 implements InterfaceC0158 {

    /* renamed from: ၥ */
    @zu2
    public static final C0159 f23 = new C0159();

    @Override // okhttp3.internal.p042io.ax1
    /* renamed from: ԫ */
    public final int mo16(ea1 ea1Var, m91 m91Var, int i) {
        fa1.m6826(ea1Var, "<this>");
        return m91Var.mo6257(i);
    }

    @Override // okhttp3.internal.p042io.ax1
    /* renamed from: ހ */
    public final int mo17(ea1 ea1Var, m91 m91Var, int i) {
        fa1.m6826(ea1Var, "<this>");
        return m91Var.mo6259(i);
    }

    @Override // okhttp3.internal.p042io.ax1
    /* renamed from: ބ */
    public final int mo18(ea1 ea1Var, m91 m91Var, int i) {
        fa1.m6826(ea1Var, "<this>");
        return m91Var.mo6255(i);
    }

    @Override // okhttp3.internal.p042io.ax1
    /* renamed from: މ */
    public final /* synthetic */ ze2 mo19(bf2 bf2Var, ue2 ue2Var, long j) {
        return C0157.m13(this, bf2Var, ue2Var, j);
    }

    @Override // okhttp3.internal.p042io.ax1
    /* renamed from: ގ */
    public final int mo20(@zu2 ea1 ea1Var, @zu2 m91 m91Var, int i) {
        fa1.m6826(ea1Var, "<this>");
        return m91Var.mo6257(i);
    }

    @Override // androidx.compose.foundation.layout.InterfaceC0158
    /* renamed from: ޕ */
    public final long mo14(@zu2 bf2 bf2Var, @zu2 ue2 ue2Var, long j) {
        fa1.m6826(bf2Var, "$this$calculateContentConstraints");
        return C7409.f31780.m17010(ue2Var.mo6257(C7409.m17002(j)));
    }

    @Override // okhttp3.internal.p042io.rk2
    /* renamed from: ޘ */
    public final /* synthetic */ boolean mo21(ph0 ph0Var) {
        return sk2.m12063(this, ph0Var);
    }

    @Override // okhttp3.internal.p042io.rk2
    /* renamed from: ޙ */
    public final Object mo22(Object obj, di0 di0Var) {
        fa1.m6826(di0Var, "operation");
        return di0Var.mo18338invoke(obj, this);
    }

    @Override // okhttp3.internal.p042io.rk2
    /* renamed from: ޡ */
    public final /* synthetic */ rk2 mo23(rk2 rk2Var) {
        return qk2.m11347(this, rk2Var);
    }

    @Override // androidx.compose.foundation.layout.InterfaceC0158
    /* renamed from: ࢥ */
    public final /* synthetic */ void mo15() {
    }
}
