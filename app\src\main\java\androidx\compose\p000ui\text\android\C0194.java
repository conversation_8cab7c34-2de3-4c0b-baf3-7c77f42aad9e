package androidx.compose.p000ui.text.android;

import android.os.Build;
import android.text.Layout;
import android.text.StaticLayout;
import android.text.TextDirectionHeuristic;
import android.text.TextPaint;
import android.text.TextUtils;
import androidx.annotation.FloatRange;
import androidx.annotation.IntRange;
import androidx.core.app.NotificationCompat;
import okhttp3.internal.p042io.f15;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.wv2;
import okhttp3.internal.p042io.zu2;

/* renamed from: androidx.compose.ui.text.android.Ԩ */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class C0194 {

    /* renamed from: Ϳ */
    @zu2
    public static final C0194 f195 = new C0194();

    /* renamed from: Ԩ */
    @zu2
    public static final InterfaceC0196 f196;

    static {
        f196 = Build.VERSION.SDK_INT >= 23 ? new C0193() : new C0195();
    }

    @zu2
    /* renamed from: Ϳ */
    public final StaticLayout m62(@zu2 CharSequence charSequence, int i, int i2, @zu2 TextPaint textPaint, int i3, @zu2 TextDirectionHeuristic textDirectionHeuristic, @zu2 Layout.Alignment alignment, @IntRange(from = 0) int i4, @wv2 TextUtils.TruncateAt truncateAt, @IntRange(from = 0) int i5, @FloatRange(from = 0.0d) float f, float f2, int i6, boolean z, boolean z2, int i7, int i8, int i9, int i10, @wv2 int[] iArr, @wv2 int[] iArr2) {
        fa1.m6826(charSequence, NotificationCompat.MessagingStyle.Message.KEY_TEXT);
        fa1.m6826(textPaint, "paint");
        fa1.m6826(textDirectionHeuristic, "textDir");
        fa1.m6826(alignment, "alignment");
        return f196.mo60(new f15(charSequence, i, i2, textPaint, i3, textDirectionHeuristic, alignment, i4, truncateAt, i5, f, f2, i6, z, z2, i7, i8, i9, i10, iArr, iArr2));
    }
}
