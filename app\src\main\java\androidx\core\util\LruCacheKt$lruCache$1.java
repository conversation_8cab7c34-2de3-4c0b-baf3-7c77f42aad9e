package androidx.core.util;

import androidx.exifinterface.media.ExifInterface;
import com.stardust.autojs.core.inputevent.InputEventCodes;
import kotlin.Metadata;
import okhttp3.internal.p042io.di0;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.lv1;
import okhttp3.internal.p042io.zu2;

@Metadata(m4114d1 = {"\u0000\u0010\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0005\u0010\u0000\u001a\u00020\u0001\"\b\b\u0000\u0010\u0002*\u00020\u0003\"\b\b\u0001\u0010\u0004*\u00020\u00032\u0006\u0010\u0005\u001a\u0002H\u00022\u0006\u0010\u0006\u001a\u0002H\u0004H\n¢\u0006\u0004\b\u0007\u0010\b"}, m4115d2 = {"<anonymous>", "", "K", "", ExifInterface.GPS_MEASUREMENT_INTERRUPTED, "<anonymous parameter 0>", "<anonymous parameter 1>", "invoke", "(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Integer;"}, m4116k = 3, m4117mv = {1, 6, 0}, m4119xi = InputEventCodes.KEY_EDIT)
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class LruCacheKt$lruCache$1 extends lv1 implements di0 {
    public static final LruCacheKt$lruCache$1 INSTANCE = new LruCacheKt$lruCache$1();

    public LruCacheKt$lruCache$1() {
        super(2);
    }

    @Override // okhttp3.internal.p042io.di0
    @zu2
    /* renamed from: invoke */
    public final Integer mo18338invoke(@zu2 Object obj, @zu2 Object obj2) {
        fa1.m6826(obj, "<anonymous parameter 0>");
        fa1.m6826(obj2, "<anonymous parameter 1>");
        return 1;
    }
}
