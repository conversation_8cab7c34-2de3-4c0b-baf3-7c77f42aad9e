package android.view;

import androidx.annotation.MainThread;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProvider;
import androidx.lifecycle.viewmodel.CreationExtras;
import kotlin.Metadata;
import okhttp3.internal.p042io.InterfaceC5795y7;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.nh0;
import okhttp3.internal.p042io.wx1;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a4\u0010\u0007\u001a\b\u0012\u0004\u0012\u00028\u00000\u0006\"\n\b\u0000\u0010\u0001\u0018\u0001*\u00020\u0000*\u00020\u00022\u0010\b\n\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003H\u0087\bø\u0001\u0000\u001aF\u0010\u0007\u001a\b\u0012\u0004\u0012\u00028\u00000\u0006\"\n\b\u0000\u0010\u0001\u0018\u0001*\u00020\u0000*\u00020\u00022\u0010\b\n\u0010\t\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u00032\u0010\b\n\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003H\u0087\bø\u0001\u0000\u0082\u0002\u0007\n\u0005\b\u009920\u0001¨\u0006\n"}, m4115d2 = {"Landroidx/lifecycle/ViewModel;", "VM", "Landroidx/activity/ComponentActivity;", "Lkotlin/Function0;", "Landroidx/lifecycle/ViewModelProvider$Factory;", "factoryProducer", "Lokhttp3/internal/io/wx1;", "viewModels", "Landroidx/lifecycle/viewmodel/CreationExtras;", "extrasProducer", "activity-ktx_release"}, m4116k = 2, m4117mv = {1, 6, 0})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class ActivityViewModelLazyKt {
    @InterfaceC5795y7
    @MainThread
    public static final /* synthetic */ <VM extends ViewModel> wx1<VM> viewModels(ComponentActivity componentActivity, nh0<? extends ViewModelProvider.Factory> nh0Var) {
        fa1.m6826(componentActivity, "<this>");
        if (nh0Var == null) {
            new ActivityViewModelLazyKt$viewModels$factoryPromise$1(componentActivity);
        }
        fa1.m6844();
        throw null;
    }

    @MainThread
    public static final /* synthetic */ <VM extends ViewModel> wx1<VM> viewModels(ComponentActivity componentActivity, nh0<? extends CreationExtras> nh0Var, nh0<? extends ViewModelProvider.Factory> nh0Var2) {
        fa1.m6826(componentActivity, "<this>");
        if (nh0Var2 == null) {
            new ActivityViewModelLazyKt$viewModels$factoryPromise$2(componentActivity);
        }
        fa1.m6844();
        throw null;
    }

    public static /* synthetic */ wx1 viewModels$default(ComponentActivity componentActivity, nh0 nh0Var, int i, Object obj) {
        if ((i & 1) != 0) {
            nh0Var = null;
        }
        fa1.m6826(componentActivity, "<this>");
        if (nh0Var == null) {
            new ActivityViewModelLazyKt$viewModels$factoryPromise$1(componentActivity);
        }
        fa1.m6844();
        throw null;
    }

    public static /* synthetic */ wx1 viewModels$default(ComponentActivity componentActivity, nh0 nh0Var, nh0 nh0Var2, int i, Object obj) {
        if ((i & 2) != 0) {
            nh0Var2 = null;
        }
        fa1.m6826(componentActivity, "<this>");
        if (nh0Var2 == null) {
            new ActivityViewModelLazyKt$viewModels$factoryPromise$2(componentActivity);
        }
        fa1.m6844();
        throw null;
    }
}
