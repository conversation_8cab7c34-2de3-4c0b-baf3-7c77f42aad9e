package okhttp3.internal.p042io;

import java.lang.annotation.ElementType;
import java.lang.annotation.Target;

/* JADX WARN: Method from annotation default annotation not found: allowedOnPath */
/* JADX WARN: Method from annotation default annotation not found: checkerName */
/* JADX WARN: Method from annotation default annotation not found: whitelistAnnotations */
/* JADX WARN: Method from annotation default annotation not found: whitelistWithWarningAnnotations */
@Target({ElementType.CONSTRUCTOR, ElementType.METHOD})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public @interface z34 {
}
