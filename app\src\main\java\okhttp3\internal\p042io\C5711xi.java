package okhttp3.internal.p042io;

@f01
@uo1
/* renamed from: okhttp3.internal.io.xi */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes3.dex */
public final class C5711xi implements Comparable<C5711xi> {

    /* renamed from: ၥ */
    public final float f25951;

    public /* synthetic */ C5711xi(float f) {
        this.f25951 = f;
    }

    /* renamed from: ԩ */
    public static final boolean m13976(float f, float f2) {
        return fa1.m6818(Float.valueOf(f), Float.valueOf(f2));
    }

    @gz4
    @zu2
    /* renamed from: ؠ */
    public static String m13977(float f) {
        if (Float.isNaN(f)) {
            return "Dp.Unspecified";
        }
        return f + ".dp";
    }

    @Override // java.lang.Comparable
    public final int compareTo(C5711xi c5711xi) {
        return Float.compare(this.f25951, c5711xi.f25951);
    }

    public final boolean equals(Object obj) {
        float f = this.f25951;
        if (obj instanceof C5711xi) {
            return fa1.m6818(Float.valueOf(f), Float.valueOf(((C5711xi) obj).f25951));
        }
        return false;
    }

    public final int hashCode() {
        return Float.floatToIntBits(this.f25951);
    }

    @gz4
    @zu2
    public final String toString() {
        return m13977(this.f25951);
    }
}
