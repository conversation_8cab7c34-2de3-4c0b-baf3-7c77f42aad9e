package androidx.core.graphics;

import android.graphics.Point;
import android.graphics.Rect;
import android.graphics.Region;
import android.graphics.RegionIterator;
import androidx.autofill.HintConstants;
import java.util.Iterator;
import kotlin.Metadata;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.ph0;
import okhttp3.internal.p042io.zu2;

@Metadata(m4113bv = {}, m4114d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010(\n\u0002\b\u0002\u001a\u0015\u0010\u0004\u001a\u00020\u0003*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u0001H\u0086\n\u001a\u0015\u0010\u0007\u001a\u00020\u0000*\u00020\u00002\u0006\u0010\u0006\u001a\u00020\u0005H\u0086\n\u001a\u0015\u0010\u0007\u001a\u00020\u0000*\u00020\u00002\u0006\u0010\u0006\u001a\u00020\u0000H\u0086\n\u001a\u0015\u0010\b\u001a\u00020\u0000*\u00020\u00002\u0006\u0010\u0006\u001a\u00020\u0005H\u0086\n\u001a\u0015\u0010\b\u001a\u00020\u0000*\u00020\u00002\u0006\u0010\u0006\u001a\u00020\u0000H\u0086\n\u001a\r\u0010\t\u001a\u00020\u0000*\u00020\u0000H\u0086\n\u001a\r\u0010\n\u001a\u00020\u0000*\u00020\u0000H\u0086\n\u001a\u0015\u0010\u000b\u001a\u00020\u0000*\u00020\u00002\u0006\u0010\u0006\u001a\u00020\u0005H\u0086\f\u001a\u0015\u0010\u000b\u001a\u00020\u0000*\u00020\u00002\u0006\u0010\u0006\u001a\u00020\u0000H\u0086\f\u001a\u0015\u0010\f\u001a\u00020\u0000*\u00020\u00002\u0006\u0010\u0006\u001a\u00020\u0005H\u0086\f\u001a\u0015\u0010\f\u001a\u00020\u0000*\u00020\u00002\u0006\u0010\u0006\u001a\u00020\u0000H\u0086\f\u001a\u0015\u0010\r\u001a\u00020\u0000*\u00020\u00002\u0006\u0010\u0006\u001a\u00020\u0005H\u0086\f\u001a\u0015\u0010\r\u001a\u00020\u0000*\u00020\u00002\u0006\u0010\u0006\u001a\u00020\u0000H\u0086\f\u001a3\u0010\u0014\u001a\u00020\u0012*\u00020\u00002!\u0010\u0013\u001a\u001d\u0012\u0013\u0012\u00110\u0005¢\u0006\f\b\u000f\u0012\b\b\u0010\u0012\u0004\b\b(\u0011\u0012\u0004\u0012\u00020\u00120\u000eH\u0086\bø\u0001\u0000\u001a\u0013\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00050\u0015*\u00020\u0000H\u0086\u0002\u0082\u0002\u0007\n\u0005\b\u009920\u0001¨\u0006\u0017"}, m4115d2 = {"Landroid/graphics/Region;", "Landroid/graphics/Point;", "p", "", "contains", "Landroid/graphics/Rect;", "r", "plus", "minus", "unaryMinus", "not", "or", "and", "xor", "Lkotlin/Function1;", "Lokhttp3/internal/io/o73;", HintConstants.AUTOFILL_HINT_NAME, "rect", "Lokhttp3/internal/io/lx5;", "action", "forEach", "", "iterator", "core-ktx_release"}, m4116k = 2, m4117mv = {1, 6, 0})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class RegionKt {
    @zu2
    public static final Region and(@zu2 Region region, @zu2 Rect rect) {
        fa1.m6826(region, "<this>");
        fa1.m6826(rect, "r");
        Region region2 = new Region(region);
        region2.op(rect, Region.Op.INTERSECT);
        return region2;
    }

    @zu2
    public static final Region and(@zu2 Region region, @zu2 Region region2) {
        fa1.m6826(region, "<this>");
        fa1.m6826(region2, "r");
        Region region3 = new Region(region);
        region3.op(region2, Region.Op.INTERSECT);
        return region3;
    }

    public static final boolean contains(@zu2 Region region, @zu2 Point point) {
        fa1.m6826(region, "<this>");
        fa1.m6826(point, "p");
        return region.contains(point.x, point.y);
    }

    public static final void forEach(@zu2 Region region, @zu2 ph0<? super Rect, lx5> ph0Var) {
        fa1.m6826(region, "<this>");
        fa1.m6826(ph0Var, "action");
        RegionIterator regionIterator = new RegionIterator(region);
        while (true) {
            Rect rect = new Rect();
            if (!regionIterator.next(rect)) {
                return;
            } else {
                ph0Var.invoke(rect);
            }
        }
    }

    @zu2
    public static final Iterator<Rect> iterator(@zu2 Region region) {
        fa1.m6826(region, "<this>");
        return new RegionKt$iterator$1(region);
    }

    @zu2
    public static final Region minus(@zu2 Region region, @zu2 Rect rect) {
        fa1.m6826(region, "<this>");
        fa1.m6826(rect, "r");
        Region region2 = new Region(region);
        region2.op(rect, Region.Op.DIFFERENCE);
        return region2;
    }

    @zu2
    public static final Region minus(@zu2 Region region, @zu2 Region region2) {
        fa1.m6826(region, "<this>");
        fa1.m6826(region2, "r");
        Region region3 = new Region(region);
        region3.op(region2, Region.Op.DIFFERENCE);
        return region3;
    }

    @zu2
    public static final Region not(@zu2 Region region) {
        fa1.m6826(region, "<this>");
        Region region2 = new Region(region.getBounds());
        region2.op(region, Region.Op.DIFFERENCE);
        return region2;
    }

    @zu2
    /* renamed from: or */
    public static final Region m102or(@zu2 Region region, @zu2 Rect rect) {
        fa1.m6826(region, "<this>");
        fa1.m6826(rect, "r");
        Region region2 = new Region(region);
        region2.union(rect);
        return region2;
    }

    @zu2
    /* renamed from: or */
    public static final Region m103or(@zu2 Region region, @zu2 Region region2) {
        fa1.m6826(region, "<this>");
        fa1.m6826(region2, "r");
        Region region3 = new Region(region);
        region3.op(region2, Region.Op.UNION);
        return region3;
    }

    @zu2
    public static final Region plus(@zu2 Region region, @zu2 Rect rect) {
        fa1.m6826(region, "<this>");
        fa1.m6826(rect, "r");
        Region region2 = new Region(region);
        region2.union(rect);
        return region2;
    }

    @zu2
    public static final Region plus(@zu2 Region region, @zu2 Region region2) {
        fa1.m6826(region, "<this>");
        fa1.m6826(region2, "r");
        Region region3 = new Region(region);
        region3.op(region2, Region.Op.UNION);
        return region3;
    }

    @zu2
    public static final Region unaryMinus(@zu2 Region region) {
        fa1.m6826(region, "<this>");
        Region region2 = new Region(region.getBounds());
        region2.op(region, Region.Op.DIFFERENCE);
        return region2;
    }

    @zu2
    public static final Region xor(@zu2 Region region, @zu2 Rect rect) {
        fa1.m6826(region, "<this>");
        fa1.m6826(rect, "r");
        Region region2 = new Region(region);
        region2.op(rect, Region.Op.XOR);
        return region2;
    }

    @zu2
    public static final Region xor(@zu2 Region region, @zu2 Region region2) {
        fa1.m6826(region, "<this>");
        fa1.m6826(region2, "r");
        Region region3 = new Region(region);
        region3.op(region2, Region.Op.XOR);
        return region3;
    }
}
