package okhttp3.internal.p042io;

import java.util.Objects;
import okhttp3.internal.p042io.C5625wu;
import okhttp3.internal.p042io.ty4;
import okhttp3.internal.p042io.vy4;

/* renamed from: okhttp3.internal.io.vu */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes3.dex */
public final class C5518vu implements ty4.InterfaceC5259 {

    /* renamed from: Ϳ */
    public final /* synthetic */ C5625wu f24456;

    /* renamed from: okhttp3.internal.io.vu$Ϳ, reason: contains not printable characters */
    public class C9454 implements vy4.InterfaceC5540 {
        public C9454() {
        }

        @Override // okhttp3.internal.p042io.vy4.InterfaceC5540
        /* renamed from: Ϳ */
        public final void mo7909(ub3 ub3Var) {
        }

        @Override // okhttp3.internal.p042io.vy4.InterfaceC5540
        /* renamed from: Ԩ */
        public final void mo7910(su2 su2Var) {
            C5625wu.C9472 c9472;
            C5625wu c5625wu = C5518vu.this.f24456;
            Objects.requireNonNull(c5625wu);
            int i = su2Var.f21526.f6113.f15777;
            ty3 ty3Var = su2Var.f24561;
            if (i == 56 && ty3Var.f22641.mo9369() == 9) {
                ty3 ty3Var2 = su2Var.f24561;
                vy4 m13720 = c5625wu.m13720(su2Var);
                int i2 = m13720.mo12142().f15777;
                if (i2 != 5) {
                    if (i2 != 38 && i2 != 45) {
                        if (i2 != 46) {
                            switch (i2) {
                                case 40:
                                    break;
                                case 41:
                                case 42:
                                    if (!m13720.mo12144().m13019(0).f22641.mo9370()) {
                                        c9472 = new C5625wu.C9472(ty3Var2.f22640, c5625wu.f25171, 5);
                                        break;
                                    } else {
                                        c9472 = new C5625wu.C9472(ty3Var2.f22640, c5625wu.f25171, 2);
                                        c9472.f25177 = true;
                                        break;
                                    }
                                case 43:
                                    break;
                                default:
                                    c9472 = null;
                                    break;
                            }
                        } else {
                            c9472 = new C5625wu.C9472(ty3Var2.f22640, c5625wu.f25171, 5);
                        }
                        c5625wu.f25172.add(c9472);
                    }
                    ty3 m13019 = m13720.mo12144().m13019(0);
                    int m13719 = c5625wu.m13719(m13019);
                    if (m13719 != c5625wu.f25172.size()) {
                        c9472 = c5625wu.f25172.get(m13719);
                        c9472.f25173.set(ty3Var2.f22640);
                    } else {
                        c9472 = m13019.getType() == mq5.f15504 ? new C5625wu.C9472(ty3Var2.f22640, c5625wu.f25171, 2) : new C5625wu.C9472(ty3Var2.f22640, c5625wu.f25171, 5);
                        c5625wu.f25172.add(c9472);
                    }
                }
                c9472 = new C5625wu.C9472(ty3Var2.f22640, c5625wu.f25171, 2);
                c5625wu.f25172.add(c9472);
            } else {
                if (i == 3 && ty3Var.f22641.mo9369() == 9) {
                    c9472 = new C5625wu.C9472(ty3Var.f22640, c5625wu.f25171, 2);
                } else if (i != 55 || ty3Var.f22641.mo9369() != 9) {
                    return;
                } else {
                    c9472 = new C5625wu.C9472(ty3Var.f22640, c5625wu.f25171, 2);
                }
                c5625wu.f25172.add(c9472);
            }
            c5625wu.m13725(ty3Var, c9472);
        }

        @Override // okhttp3.internal.p042io.vy4.InterfaceC5540
        /* renamed from: ԩ */
        public final void mo7911(su2 su2Var) {
        }
    }

    public C5518vu(C5625wu c5625wu) {
        this.f24456 = c5625wu;
    }

    @Override // okhttp3.internal.p042io.ty4.InterfaceC5259
    /* renamed from: Ϳ */
    public final void mo8957(ty4 ty4Var, ty4 ty4Var2) {
        ty4Var.m12640(new C9454());
    }
}
