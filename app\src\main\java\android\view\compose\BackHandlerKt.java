package android.view.compose;

import android.view.OnBackPressedCallback;
import android.view.OnBackPressedDispatcher;
import android.view.OnBackPressedDispatcherOwner;
import androidx.lifecycle.LifecycleOwner;
import com.stardust.autojs.project.Features;
import kotlin.Metadata;
import okhttp3.internal.p042io.C7313;
import okhttp3.internal.p042io.C7352;
import okhttp3.internal.p042io.InterfaceC6968;
import okhttp3.internal.p042io.InterfaceC7452;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.g05;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.nh0;
import okhttp3.internal.p042io.ov4;
import okhttp3.internal.p042io.qb4;
import okhttp3.internal.p042io.wv2;
import okhttp3.internal.p042io.zu2;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000\u0012\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\u001a'\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0001\u001a\u00020\u00002\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00030\u0002H\u0007¢\u0006\u0004\b\u0005\u0010\u0006¨\u0006\u0007"}, m4115d2 = {"", Features.NODEJS_ENABLED, "Lkotlin/Function0;", "Lokhttp3/internal/io/lx5;", "onBack", "BackHandler", "(ZLokhttp3/internal/io/nh0;Lokhttp3/internal/io/ࡊ;II)V", "activity-compose_release"}, m4116k = 2, m4117mv = {1, 6, 0})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class BackHandlerKt {
    @InterfaceC7452
    public static final void BackHandler(final boolean z, @zu2 nh0<lx5> nh0Var, @wv2 InterfaceC6968 interfaceC6968, int i, int i2) {
        int i3;
        fa1.m6826(nh0Var, "onBack");
        InterfaceC6968 mo16280 = interfaceC6968.mo16280(-361453782);
        int i4 = i2 & 1;
        if (i4 != 0) {
            i3 = i | 6;
        } else if ((i & 14) == 0) {
            i3 = (mo16280.mo16266(z) ? 4 : 2) | i;
        } else {
            i3 = i;
        }
        if ((i2 & 2) != 0) {
            i3 |= 48;
        } else if ((i & 112) == 0) {
            i3 |= mo16280.mo16303(nh0Var) ? 32 : 16;
        }
        if ((i3 & 91) == 18 && mo16280.mo16283()) {
            mo16280.mo16290();
        } else {
            if (i4 != 0) {
                z = true;
            }
            final g05 m10511 = ov4.m10511(nh0Var, mo16280);
            mo16280.mo16268(-3687241);
            Object mo16269 = mo16280.mo16269();
            InterfaceC6968.C9945.C9946 c9946 = InterfaceC6968.C9945.f30561;
            if (mo16269 == c9946) {
                mo16269 = new OnBackPressedCallback(z) { // from class: androidx.activity.compose.BackHandlerKt$BackHandler$backCallback$1$1
                    @Override // android.view.OnBackPressedCallback
                    public void handleOnBackPressed() {
                        nh0 m18333BackHandler$lambda0;
                        m18333BackHandler$lambda0 = BackHandlerKt.m18333BackHandler$lambda0(m10511);
                        m18333BackHandler$lambda0.invoke();
                    }
                };
                mo16280.mo16296(mo16269);
            }
            mo16280.mo16300();
            BackHandlerKt$BackHandler$backCallback$1$1 backHandlerKt$BackHandler$backCallback$1$1 = (BackHandlerKt$BackHandler$backCallback$1$1) mo16269;
            Boolean valueOf = Boolean.valueOf(z);
            mo16280.mo16268(-3686552);
            boolean mo16303 = mo16280.mo16303(valueOf) | mo16280.mo16303(backHandlerKt$BackHandler$backCallback$1$1);
            Object mo162692 = mo16280.mo16269();
            if (mo16303 || mo162692 == c9946) {
                mo162692 = new BackHandlerKt$BackHandler$1$1(backHandlerKt$BackHandler$backCallback$1$1, z);
                mo16280.mo16296(mo162692);
            }
            mo16280.mo16300();
            C7352.m16928((nh0) mo162692, mo16280);
            OnBackPressedDispatcherOwner current = LocalOnBackPressedDispatcherOwner.INSTANCE.getCurrent(mo16280, 6);
            if (current == null) {
                throw new IllegalStateException("No OnBackPressedDispatcherOwner was provided via LocalOnBackPressedDispatcherOwner".toString());
            }
            OnBackPressedDispatcher onBackPressedDispatcher = current.getOnBackPressedDispatcher();
            fa1.m6825(onBackPressedDispatcher, "checkNotNull(LocalOnBack…}.onBackPressedDispatcher");
            LifecycleOwner lifecycleOwner = (LifecycleOwner) mo16280.mo16276(C7313.f31549);
            C7352.m16922(lifecycleOwner, onBackPressedDispatcher, new BackHandlerKt$BackHandler$2(onBackPressedDispatcher, lifecycleOwner, backHandlerKt$BackHandler$backCallback$1$1), mo16280);
        }
        qb4 mo16286 = mo16280.mo16286();
        if (mo16286 == null) {
            return;
        }
        mo16286.mo5841(new BackHandlerKt$BackHandler$3(z, nh0Var, i, i2));
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: BackHandler$lambda-0, reason: not valid java name */
    public static final nh0<lx5> m18333BackHandler$lambda0(g05<? extends nh0<lx5>> g05Var) {
        return g05Var.getValue();
    }
}
