package android.view.result;

import android.content.Context;
import android.content.Intent;
import android.view.result.contract.ActivityResultContract;
import kotlin.Metadata;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.lv1;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.nh0;
import okhttp3.internal.p042io.wv2;
import okhttp3.internal.p042io.zu2;
import org.mozilla.javascript.optimizer.OptRuntime;

@Metadata(m4114d1 = {"\u0000\t\n\u0000\n\u0002\b\u0005*\u0001\u0001\u0010\u0000\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H\u00030\u0001\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010\u0003H\n¢\u0006\u0004\b\u0004\u0010\u0005"}, m4115d2 = {"<anonymous>", "androidx/activity/result/ActivityResultCallerLauncher$resultContract$2$1", OptRuntime.GeneratorState.resumptionPoint_TYPE, "O", "invoke", "()Landroidx/activity/result/ActivityResultCallerLauncher$resultContract$2$1;"}, m4116k = 3, m4117mv = {1, 6, 0}, m4119xi = 48)
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class ActivityResultCallerLauncher$resultContract$2 extends lv1 implements nh0<C00481> {
    public final /* synthetic */ ActivityResultCallerLauncher<I, O> this$0;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public ActivityResultCallerLauncher$resultContract$2(ActivityResultCallerLauncher<I, O> activityResultCallerLauncher) {
        super(0);
        this.this$0 = activityResultCallerLauncher;
    }

    /* JADX WARN: Can't rename method to resolve collision */
    /* JADX WARN: Type inference failed for: r0v0, types: [androidx.activity.result.ActivityResultCallerLauncher$resultContract$2$1] */
    @Override // okhttp3.internal.p042io.nh0
    @zu2
    public final C00481 invoke() {
        final ActivityResultCallerLauncher<I, O> activityResultCallerLauncher = this.this$0;
        return new ActivityResultContract<lx5, O>() { // from class: androidx.activity.result.ActivityResultCallerLauncher$resultContract$2.1
            @Override // android.view.result.contract.ActivityResultContract
            @zu2
            public Intent createIntent(@zu2 Context context, @zu2 lx5 input) {
                fa1.m6826(context, "context");
                fa1.m6826(input, "input");
                return activityResultCallerLauncher.getCallerContract().createIntent(context, activityResultCallerLauncher.getCallerInput());
            }

            @Override // android.view.result.contract.ActivityResultContract
            public O parseResult(int resultCode, @wv2 Intent intent) {
                return (O) activityResultCallerLauncher.getCallerContract().parseResult(resultCode, intent);
            }
        };
    }
}
