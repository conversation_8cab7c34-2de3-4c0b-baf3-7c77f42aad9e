package okhttp3.internal.p042io;

import java.util.AbstractList;
import java.util.List;

/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class c54 extends AbstractList<Object> {

    /* renamed from: ၥ */
    public final /* synthetic */ a54 f7601;

    /* renamed from: ၦ */
    public final /* synthetic */ List f7602;

    public c54(a54 a54Var, List list) {
        this.f7601 = a54Var;
        this.f7602 = list;
    }

    @Override // java.util.AbstractList, java.util.List
    public final Object get(int i) {
        return f54.m6759(this.f7601, this.f7602.get(i));
    }

    @Override // java.util.AbstractCollection, java.util.Collection, java.util.List
    public final int size() {
        return this.f7602.size();
    }
}
