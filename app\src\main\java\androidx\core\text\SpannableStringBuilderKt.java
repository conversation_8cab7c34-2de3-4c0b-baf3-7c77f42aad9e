package androidx.core.text;

import android.text.SpannableStringBuilder;
import android.text.SpannedString;
import android.text.style.BackgroundColorSpan;
import android.text.style.ForegroundColorSpan;
import android.text.style.RelativeSizeSpan;
import android.text.style.StrikethroughSpan;
import android.text.style.StyleSpan;
import android.text.style.SubscriptSpan;
import android.text.style.SuperscriptSpan;
import android.text.style.UnderlineSpan;
import androidx.annotation.ColorInt;
import androidx.constraintlayout.core.motion.utils.TypedValues;
import kotlin.Metadata;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.ph0;
import okhttp3.internal.p042io.zu2;

@Metadata(m4113bv = {}, m4114d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0011\n\u0002\u0010\u0000\n\u0002\b\u0007\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u0007\n\u0002\b\u0005\u001a%\u0010\u0006\u001a\u00020\u00052\u0017\u0010\u0004\u001a\u0013\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u00020\u0000¢\u0006\u0002\b\u0003H\u0086\bø\u0001\u0000\u001aD\u0010\n\u001a\u00020\u0001*\u00020\u00012\u0012\u0010\t\u001a\n\u0012\u0006\b\u0001\u0012\u00020\b0\u0007\"\u00020\b2\u0017\u0010\u0004\u001a\u0013\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u00020\u0000¢\u0006\u0002\b\u0003H\u0086\bø\u0001\u0000¢\u0006\u0004\b\n\u0010\u000b\u001a1\u0010\n\u001a\u00020\u0001*\u00020\u00012\u0006\u0010\f\u001a\u00020\b2\u0017\u0010\u0004\u001a\u0013\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u00020\u0000¢\u0006\u0002\b\u0003H\u0086\bø\u0001\u0000\u001a)\u0010\r\u001a\u00020\u0001*\u00020\u00012\u0017\u0010\u0004\u001a\u0013\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u00020\u0000¢\u0006\u0002\b\u0003H\u0086\bø\u0001\u0000\u001a)\u0010\u000e\u001a\u00020\u0001*\u00020\u00012\u0017\u0010\u0004\u001a\u0013\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u00020\u0000¢\u0006\u0002\b\u0003H\u0086\bø\u0001\u0000\u001a)\u0010\u000f\u001a\u00020\u0001*\u00020\u00012\u0017\u0010\u0004\u001a\u0013\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u00020\u0000¢\u0006\u0002\b\u0003H\u0086\bø\u0001\u0000\u001a3\u0010\u0011\u001a\u00020\u0001*\u00020\u00012\b\b\u0001\u0010\u0011\u001a\u00020\u00102\u0017\u0010\u0004\u001a\u0013\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u00020\u0000¢\u0006\u0002\b\u0003H\u0086\bø\u0001\u0000\u001a3\u0010\u0012\u001a\u00020\u0001*\u00020\u00012\b\b\u0001\u0010\u0011\u001a\u00020\u00102\u0017\u0010\u0004\u001a\u0013\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u00020\u0000¢\u0006\u0002\b\u0003H\u0086\bø\u0001\u0000\u001a)\u0010\u0013\u001a\u00020\u0001*\u00020\u00012\u0017\u0010\u0004\u001a\u0013\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u00020\u0000¢\u0006\u0002\b\u0003H\u0086\bø\u0001\u0000\u001a1\u0010\u0016\u001a\u00020\u0001*\u00020\u00012\u0006\u0010\u0015\u001a\u00020\u00142\u0017\u0010\u0004\u001a\u0013\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u00020\u0000¢\u0006\u0002\b\u0003H\u0086\bø\u0001\u0000\u001a)\u0010\u0017\u001a\u00020\u0001*\u00020\u00012\u0017\u0010\u0004\u001a\u0013\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u00020\u0000¢\u0006\u0002\b\u0003H\u0086\bø\u0001\u0000\u001a)\u0010\u0018\u001a\u00020\u0001*\u00020\u00012\u0017\u0010\u0004\u001a\u0013\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u00020\u0000¢\u0006\u0002\b\u0003H\u0086\bø\u0001\u0000\u0082\u0002\u0007\n\u0005\b\u009920\u0001¨\u0006\u0019"}, m4115d2 = {"Lkotlin/Function1;", "Landroid/text/SpannableStringBuilder;", "Lokhttp3/internal/io/lx5;", "Lokhttp3/internal/io/f00;", "builderAction", "Landroid/text/SpannedString;", "buildSpannedString", "", "", "spans", "inSpans", "(Landroid/text/SpannableStringBuilder;[Ljava/lang/Object;Lokhttp3/internal/io/ph0;)Landroid/text/SpannableStringBuilder;", "span", "bold", "italic", "underline", "", TypedValues.Custom.S_COLOR, "backgroundColor", "strikeThrough", "", "proportion", "scale", "superscript", "subscript", "core-ktx_release"}, m4116k = 2, m4117mv = {1, 6, 0})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class SpannableStringBuilderKt {
    @zu2
    public static final SpannableStringBuilder backgroundColor(@zu2 SpannableStringBuilder spannableStringBuilder, @ColorInt int i, @zu2 ph0<? super SpannableStringBuilder, lx5> ph0Var) {
        fa1.m6826(spannableStringBuilder, "<this>");
        fa1.m6826(ph0Var, "builderAction");
        BackgroundColorSpan backgroundColorSpan = new BackgroundColorSpan(i);
        int length = spannableStringBuilder.length();
        ph0Var.invoke(spannableStringBuilder);
        spannableStringBuilder.setSpan(backgroundColorSpan, length, spannableStringBuilder.length(), 17);
        return spannableStringBuilder;
    }

    @zu2
    public static final SpannableStringBuilder bold(@zu2 SpannableStringBuilder spannableStringBuilder, @zu2 ph0<? super SpannableStringBuilder, lx5> ph0Var) {
        fa1.m6826(spannableStringBuilder, "<this>");
        fa1.m6826(ph0Var, "builderAction");
        StyleSpan styleSpan = new StyleSpan(1);
        int length = spannableStringBuilder.length();
        ph0Var.invoke(spannableStringBuilder);
        spannableStringBuilder.setSpan(styleSpan, length, spannableStringBuilder.length(), 17);
        return spannableStringBuilder;
    }

    @zu2
    public static final SpannedString buildSpannedString(@zu2 ph0<? super SpannableStringBuilder, lx5> ph0Var) {
        fa1.m6826(ph0Var, "builderAction");
        SpannableStringBuilder spannableStringBuilder = new SpannableStringBuilder();
        ph0Var.invoke(spannableStringBuilder);
        return new SpannedString(spannableStringBuilder);
    }

    @zu2
    public static final SpannableStringBuilder color(@zu2 SpannableStringBuilder spannableStringBuilder, @ColorInt int i, @zu2 ph0<? super SpannableStringBuilder, lx5> ph0Var) {
        fa1.m6826(spannableStringBuilder, "<this>");
        fa1.m6826(ph0Var, "builderAction");
        ForegroundColorSpan foregroundColorSpan = new ForegroundColorSpan(i);
        int length = spannableStringBuilder.length();
        ph0Var.invoke(spannableStringBuilder);
        spannableStringBuilder.setSpan(foregroundColorSpan, length, spannableStringBuilder.length(), 17);
        return spannableStringBuilder;
    }

    @zu2
    public static final SpannableStringBuilder inSpans(@zu2 SpannableStringBuilder spannableStringBuilder, @zu2 Object obj, @zu2 ph0<? super SpannableStringBuilder, lx5> ph0Var) {
        fa1.m6826(spannableStringBuilder, "<this>");
        fa1.m6826(obj, "span");
        fa1.m6826(ph0Var, "builderAction");
        int length = spannableStringBuilder.length();
        ph0Var.invoke(spannableStringBuilder);
        spannableStringBuilder.setSpan(obj, length, spannableStringBuilder.length(), 17);
        return spannableStringBuilder;
    }

    @zu2
    public static final SpannableStringBuilder inSpans(@zu2 SpannableStringBuilder spannableStringBuilder, @zu2 Object[] objArr, @zu2 ph0<? super SpannableStringBuilder, lx5> ph0Var) {
        fa1.m6826(spannableStringBuilder, "<this>");
        fa1.m6826(objArr, "spans");
        fa1.m6826(ph0Var, "builderAction");
        int length = spannableStringBuilder.length();
        ph0Var.invoke(spannableStringBuilder);
        for (Object obj : objArr) {
            spannableStringBuilder.setSpan(obj, length, spannableStringBuilder.length(), 17);
        }
        return spannableStringBuilder;
    }

    @zu2
    public static final SpannableStringBuilder italic(@zu2 SpannableStringBuilder spannableStringBuilder, @zu2 ph0<? super SpannableStringBuilder, lx5> ph0Var) {
        fa1.m6826(spannableStringBuilder, "<this>");
        fa1.m6826(ph0Var, "builderAction");
        StyleSpan styleSpan = new StyleSpan(2);
        int length = spannableStringBuilder.length();
        ph0Var.invoke(spannableStringBuilder);
        spannableStringBuilder.setSpan(styleSpan, length, spannableStringBuilder.length(), 17);
        return spannableStringBuilder;
    }

    @zu2
    public static final SpannableStringBuilder scale(@zu2 SpannableStringBuilder spannableStringBuilder, float f, @zu2 ph0<? super SpannableStringBuilder, lx5> ph0Var) {
        fa1.m6826(spannableStringBuilder, "<this>");
        fa1.m6826(ph0Var, "builderAction");
        RelativeSizeSpan relativeSizeSpan = new RelativeSizeSpan(f);
        int length = spannableStringBuilder.length();
        ph0Var.invoke(spannableStringBuilder);
        spannableStringBuilder.setSpan(relativeSizeSpan, length, spannableStringBuilder.length(), 17);
        return spannableStringBuilder;
    }

    @zu2
    public static final SpannableStringBuilder strikeThrough(@zu2 SpannableStringBuilder spannableStringBuilder, @zu2 ph0<? super SpannableStringBuilder, lx5> ph0Var) {
        fa1.m6826(spannableStringBuilder, "<this>");
        fa1.m6826(ph0Var, "builderAction");
        StrikethroughSpan strikethroughSpan = new StrikethroughSpan();
        int length = spannableStringBuilder.length();
        ph0Var.invoke(spannableStringBuilder);
        spannableStringBuilder.setSpan(strikethroughSpan, length, spannableStringBuilder.length(), 17);
        return spannableStringBuilder;
    }

    @zu2
    public static final SpannableStringBuilder subscript(@zu2 SpannableStringBuilder spannableStringBuilder, @zu2 ph0<? super SpannableStringBuilder, lx5> ph0Var) {
        fa1.m6826(spannableStringBuilder, "<this>");
        fa1.m6826(ph0Var, "builderAction");
        SubscriptSpan subscriptSpan = new SubscriptSpan();
        int length = spannableStringBuilder.length();
        ph0Var.invoke(spannableStringBuilder);
        spannableStringBuilder.setSpan(subscriptSpan, length, spannableStringBuilder.length(), 17);
        return spannableStringBuilder;
    }

    @zu2
    public static final SpannableStringBuilder superscript(@zu2 SpannableStringBuilder spannableStringBuilder, @zu2 ph0<? super SpannableStringBuilder, lx5> ph0Var) {
        fa1.m6826(spannableStringBuilder, "<this>");
        fa1.m6826(ph0Var, "builderAction");
        SuperscriptSpan superscriptSpan = new SuperscriptSpan();
        int length = spannableStringBuilder.length();
        ph0Var.invoke(spannableStringBuilder);
        spannableStringBuilder.setSpan(superscriptSpan, length, spannableStringBuilder.length(), 17);
        return spannableStringBuilder;
    }

    @zu2
    public static final SpannableStringBuilder underline(@zu2 SpannableStringBuilder spannableStringBuilder, @zu2 ph0<? super SpannableStringBuilder, lx5> ph0Var) {
        fa1.m6826(spannableStringBuilder, "<this>");
        fa1.m6826(ph0Var, "builderAction");
        UnderlineSpan underlineSpan = new UnderlineSpan();
        int length = spannableStringBuilder.length();
        ph0Var.invoke(spannableStringBuilder);
        spannableStringBuilder.setSpan(underlineSpan, length, spannableStringBuilder.length(), 17);
        return spannableStringBuilder;
    }
}
