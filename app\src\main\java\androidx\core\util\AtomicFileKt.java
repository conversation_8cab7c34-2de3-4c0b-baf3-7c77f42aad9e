package androidx.core.util;

import android.annotation.SuppressLint;
import androidx.annotation.RequiresApi;
import androidx.autofill.HintConstants;
import androidx.core.app.NotificationCompat;
import java.io.FileOutputStream;
import java.nio.charset.Charset;
import kotlin.Metadata;
import okhttp3.internal.p042io.C7449;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.ph0;
import okhttp3.internal.p042io.zu2;

@Metadata(m4113bv = {}, m4114d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0012\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\u001a3\u0010\b\u001a\u00020\u0006*\u00020\u00002!\u0010\u0007\u001a\u001d\u0012\u0013\u0012\u00110\u0002¢\u0006\f\b\u0003\u0012\b\b\u0004\u0012\u0004\b\b(\u0005\u0012\u0004\u0012\u00020\u00060\u0001H\u0087\bø\u0001\u0000\u001a\u0014\u0010\u000b\u001a\u00020\u0006*\u00020\u00002\u0006\u0010\n\u001a\u00020\tH\u0007\u001a\u001e\u0010\u0010\u001a\u00020\u0006*\u00020\u00002\u0006\u0010\r\u001a\u00020\f2\b\b\u0002\u0010\u000f\u001a\u00020\u000eH\u0007\u001a\r\u0010\u0011\u001a\u00020\t*\u00020\u0000H\u0087\b\u001a\u0016\u0010\u0012\u001a\u00020\f*\u00020\u00002\b\b\u0002\u0010\u000f\u001a\u00020\u000eH\u0007\u0082\u0002\u0007\n\u0005\b\u009920\u0001¨\u0006\u0013"}, m4115d2 = {"Landroid/util/AtomicFile;", "Lkotlin/Function1;", "Ljava/io/FileOutputStream;", "Lokhttp3/internal/io/o73;", HintConstants.AUTOFILL_HINT_NAME, "out", "Lokhttp3/internal/io/lx5;", "block", "tryWrite", "", "array", "writeBytes", "", NotificationCompat.MessagingStyle.Message.KEY_TEXT, "Ljava/nio/charset/Charset;", "charset", "writeText", "readBytes", "readText", "core-ktx_release"}, m4116k = 2, m4117mv = {1, 6, 0})
@SuppressLint({"ClassVerificationFailure"})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class AtomicFileKt {
    @RequiresApi(17)
    @zu2
    public static final byte[] readBytes(@zu2 android.util.AtomicFile atomicFile) {
        fa1.m6826(atomicFile, "<this>");
        byte[] readFully = atomicFile.readFully();
        fa1.m6825(readFully, "readFully()");
        return readFully;
    }

    @RequiresApi(17)
    @zu2
    public static final String readText(@zu2 android.util.AtomicFile atomicFile, @zu2 Charset charset) {
        fa1.m6826(atomicFile, "<this>");
        fa1.m6826(charset, "charset");
        byte[] readFully = atomicFile.readFully();
        fa1.m6825(readFully, "readFully()");
        return new String(readFully, charset);
    }

    public static /* synthetic */ String readText$default(android.util.AtomicFile atomicFile, Charset charset, int i, Object obj) {
        if ((i & 1) != 0) {
            charset = C7449.f31902;
        }
        return readText(atomicFile, charset);
    }

    @RequiresApi(17)
    public static final void tryWrite(@zu2 android.util.AtomicFile atomicFile, @zu2 ph0<? super FileOutputStream, lx5> ph0Var) {
        fa1.m6826(atomicFile, "<this>");
        fa1.m6826(ph0Var, "block");
        FileOutputStream startWrite = atomicFile.startWrite();
        try {
            fa1.m6825(startWrite, "stream");
            ph0Var.invoke(startWrite);
            atomicFile.finishWrite(startWrite);
        } catch (Throwable th) {
            atomicFile.failWrite(startWrite);
            throw th;
        }
    }

    @RequiresApi(17)
    public static final void writeBytes(@zu2 android.util.AtomicFile atomicFile, @zu2 byte[] bArr) {
        fa1.m6826(atomicFile, "<this>");
        fa1.m6826(bArr, "array");
        FileOutputStream startWrite = atomicFile.startWrite();
        try {
            fa1.m6825(startWrite, "stream");
            startWrite.write(bArr);
            atomicFile.finishWrite(startWrite);
        } catch (Throwable th) {
            atomicFile.failWrite(startWrite);
            throw th;
        }
    }

    @RequiresApi(17)
    public static final void writeText(@zu2 android.util.AtomicFile atomicFile, @zu2 String str, @zu2 Charset charset) {
        fa1.m6826(atomicFile, "<this>");
        fa1.m6826(str, NotificationCompat.MessagingStyle.Message.KEY_TEXT);
        fa1.m6826(charset, "charset");
        byte[] bytes = str.getBytes(charset);
        fa1.m6825(bytes, "this as java.lang.String).getBytes(charset)");
        writeBytes(atomicFile, bytes);
    }

    public static /* synthetic */ void writeText$default(android.util.AtomicFile atomicFile, String str, Charset charset, int i, Object obj) {
        if ((i & 2) != 0) {
            charset = C7449.f31902;
        }
        writeText(atomicFile, str, charset);
    }
}
