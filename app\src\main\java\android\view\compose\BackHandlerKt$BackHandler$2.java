package android.view.compose;

import android.view.OnBackPressedDispatcher;
import androidx.lifecycle.LifecycleOwner;
import kotlin.Metadata;
import okhttp3.internal.p042io.C4389oh;
import okhttp3.internal.p042io.InterfaceC4249nh;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.lv1;
import okhttp3.internal.p042io.ph0;
import okhttp3.internal.p042io.zu2;

@Metadata(m4116k = 3, m4117mv = {1, 6, 0}, m4119xi = 48)
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class BackHandlerKt$BackHandler$2 extends lv1 implements ph0<C4389oh, InterfaceC4249nh> {
    public final /* synthetic */ BackHandlerKt$BackHandler$backCallback$1$1 $backCallback;
    public final /* synthetic */ OnBackPressedDispatcher $backDispatcher;
    public final /* synthetic */ LifecycleOwner $lifecycleOwner;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public BackHandlerKt$BackHandler$2(OnBackPressedDispatcher onBackPressedDispatcher, LifecycleOwner lifecycleOwner, BackHandlerKt$BackHandler$backCallback$1$1 backHandlerKt$BackHandler$backCallback$1$1) {
        super(1);
        this.$backDispatcher = onBackPressedDispatcher;
        this.$lifecycleOwner = lifecycleOwner;
        this.$backCallback = backHandlerKt$BackHandler$backCallback$1$1;
    }

    @Override // okhttp3.internal.p042io.ph0
    @zu2
    public final InterfaceC4249nh invoke(@zu2 C4389oh c4389oh) {
        fa1.m6826(c4389oh, "$this$DisposableEffect");
        this.$backDispatcher.addCallback(this.$lifecycleOwner, this.$backCallback);
        final BackHandlerKt$BackHandler$backCallback$1$1 backHandlerKt$BackHandler$backCallback$1$1 = this.$backCallback;
        return new InterfaceC4249nh() { // from class: androidx.activity.compose.BackHandlerKt$BackHandler$2$invoke$$inlined$onDispose$1
            @Override // okhttp3.internal.p042io.InterfaceC4249nh
            public void dispose() {
                remove();
            }
        };
    }
}
