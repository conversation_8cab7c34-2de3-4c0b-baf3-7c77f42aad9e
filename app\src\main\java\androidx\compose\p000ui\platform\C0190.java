package androidx.compose.p000ui.platform;

import okhttp3.internal.p042io.C7171;
import okhttp3.internal.p042io.C7313;
import okhttp3.internal.p042io.InterfaceC6575;
import okhttp3.internal.p042io.InterfaceC6968;
import okhttp3.internal.p042io.di0;
import okhttp3.internal.p042io.fi0;
import okhttp3.internal.p042io.hz3;
import okhttp3.internal.p042io.lu4;
import okhttp3.internal.p042io.lv1;
import okhttp3.internal.p042io.lx5;

/* renamed from: androidx.compose.ui.platform.֏ */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class C0190 extends lv1 implements di0<InterfaceC6968, Integer, lx5> {

    /* renamed from: ၥ */
    public final /* synthetic */ WrappedComposition f190;

    /* renamed from: ၦ */
    public final /* synthetic */ di0<InterfaceC6968, Integer, lx5> f191;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    /* JADX WARN: Multi-variable type inference failed */
    public C0190(WrappedComposition wrappedComposition, di0<? super InterfaceC6968, ? super Integer, lx5> di0Var) {
        super(2);
        this.f190 = wrappedComposition;
        this.f191 = di0Var;
    }

    @Override // okhttp3.internal.p042io.di0
    /* renamed from: invoke */
    public final lx5 mo18338invoke(InterfaceC6968 interfaceC6968, Integer num) {
        InterfaceC6968 interfaceC69682 = interfaceC6968;
        if ((num.intValue() & 11) == 2 && interfaceC69682.mo16283()) {
            interfaceC69682.mo16290();
        } else {
            fi0<InterfaceC6575<?>, lu4, hz3, lx5> fi0Var = C7171.f31108;
            C7313.m16884(this.f190.f175, this.f191, interfaceC69682, 8);
        }
        return lx5.f14876;
    }
}
