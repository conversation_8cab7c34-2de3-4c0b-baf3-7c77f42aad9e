package okhttp3.internal.p042io;

import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import p041j$.util.concurrent.ConcurrentHashMap;

/* renamed from: okhttp3.internal.io.w5 */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5557w5 implements rl4 {

    /* renamed from: Ϳ */
    public Date f24629 = new Date();

    /* renamed from: Ԩ */
    public AtomicInteger f24630 = new AtomicInteger(0);

    /* renamed from: ԩ */
    public AtomicInteger f24631 = new AtomicInteger(0);

    /* renamed from: Ԫ */
    public AtomicInteger f24632 = new AtomicInteger(0);

    /* renamed from: ԫ */
    public AtomicInteger f24633 = new AtomicInteger(0);

    /* renamed from: Ԭ */
    public AtomicInteger f24634 = new AtomicInteger(0);

    /* renamed from: ԭ */
    public AtomicInteger f24635 = new AtomicInteger(0);

    /* renamed from: Ԯ */
    public AtomicInteger f24636 = new AtomicInteger(0);

    /* renamed from: ԯ */
    public AtomicInteger f24637 = new AtomicInteger(0);

    /* renamed from: ֏ */
    public AtomicInteger f24638 = new AtomicInteger(0);

    /* renamed from: ؠ */
    public AtomicInteger f24639 = new AtomicInteger(0);

    /* renamed from: ހ */
    public AtomicInteger f24640 = new AtomicInteger(0);

    /* renamed from: ށ */
    public AtomicInteger f24641 = new AtomicInteger(0);

    /* renamed from: ނ */
    public AtomicLong f24642 = new AtomicLong(0);

    /* renamed from: ރ */
    public AtomicLong f24643 = new AtomicLong(0);

    /* renamed from: ބ */
    public Map<String, C9460> f24644 = new ConcurrentHashMap();

    /* renamed from: okhttp3.internal.io.w5$Ϳ, reason: contains not printable characters */
    public static class C9460 {

        /* renamed from: Ϳ */
        public Map<InetAddress, AtomicInteger> f24645 = new ConcurrentHashMap();

        /* renamed from: Ԩ */
        public AtomicInteger f24646 = new AtomicInteger(1);

        /* JADX WARN: Type inference failed for: r0v2, types: [j$.util.concurrent.ConcurrentHashMap, java.util.Map<java.net.InetAddress, java.util.concurrent.atomic.AtomicInteger>] */
        public C9460(InetAddress inetAddress) {
            this.f24645.put(inetAddress, new AtomicInteger(1));
        }

        /* JADX WARN: Type inference failed for: r0v0, types: [j$.util.concurrent.ConcurrentHashMap, java.util.Map<java.net.InetAddress, java.util.concurrent.atomic.AtomicInteger>] */
        /* JADX WARN: Type inference failed for: r1v1, types: [j$.util.concurrent.ConcurrentHashMap, java.util.Map<java.net.InetAddress, java.util.concurrent.atomic.AtomicInteger>] */
        /* renamed from: Ϳ */
        public final AtomicInteger m13475(InetAddress inetAddress) {
            AtomicInteger atomicInteger = (AtomicInteger) this.f24645.get(inetAddress);
            if (atomicInteger != null) {
                return atomicInteger;
            }
            AtomicInteger atomicInteger2 = new AtomicInteger(0);
            this.f24645.put(inetAddress, atomicInteger2);
            return atomicInteger2;
        }
    }

    @Override // okhttp3.internal.p042io.hh0
    /* renamed from: Ϳ */
    public final int mo7809() {
        return this.f24633.get();
    }

    @Override // okhttp3.internal.p042io.rl4
    /* renamed from: Ԩ */
    public final synchronized void mo11756() {
        this.f24634.incrementAndGet();
    }

    @Override // okhttp3.internal.p042io.hh0
    /* renamed from: ԩ */
    public final int mo7810() {
        return this.f24631.get();
    }

    @Override // okhttp3.internal.p042io.hh0
    /* renamed from: Ԫ */
    public final int mo7811() {
        return this.f24630.get();
    }

    @Override // okhttp3.internal.p042io.hh0
    /* renamed from: ԫ */
    public final long mo7812() {
        return this.f24642.get();
    }

    /* JADX WARN: Type inference failed for: r1v2, types: [j$.util.concurrent.ConcurrentHashMap, java.util.Map<java.lang.String, okhttp3.internal.io.w5$Ϳ>] */
    /* JADX WARN: Type inference failed for: r1v7, types: [j$.util.concurrent.ConcurrentHashMap, java.util.Map<java.lang.String, okhttp3.internal.io.w5$Ϳ>] */
    @Override // okhttp3.internal.p042io.rl4
    /* renamed from: Ԭ */
    public final synchronized void mo11757(xg0 xg0Var) {
        this.f24635.incrementAndGet();
        this.f24636.incrementAndGet();
        lz5 m13957 = xg0Var.m13957();
        if ("anonymous".equals(m13957.getName())) {
            this.f24638.incrementAndGet();
            this.f24639.incrementAndGet();
        }
        synchronized (m13957) {
            C9460 c9460 = (C9460) this.f24644.get(m13957.getName());
            if (c9460 == null) {
                this.f24644.put(m13957.getName(), new C9460(xg0Var.mo7760() instanceof InetSocketAddress ? ((InetSocketAddress) xg0Var.mo7760()).getAddress() : null));
            } else {
                c9460.f24646.incrementAndGet();
                if (xg0Var.mo7760() instanceof InetSocketAddress) {
                    c9460.m13475(((InetSocketAddress) xg0Var.mo7760()).getAddress()).incrementAndGet();
                }
            }
        }
    }

    @Override // okhttp3.internal.p042io.hh0
    /* renamed from: ԭ */
    public final int mo7813() {
        return this.f24641.get();
    }

    @Override // okhttp3.internal.p042io.hh0
    /* renamed from: Ԯ */
    public final Date mo7814() {
        Date date = this.f24629;
        if (date != null) {
            return (Date) date.clone();
        }
        return null;
    }

    @Override // okhttp3.internal.p042io.rl4
    /* renamed from: ԯ */
    public final synchronized void mo11758() {
        if (this.f24640.get() > 0) {
            this.f24640.decrementAndGet();
        }
    }

    /* JADX WARN: Type inference failed for: r0v0, types: [j$.util.concurrent.ConcurrentHashMap, java.util.Map<java.lang.String, okhttp3.internal.io.w5$Ϳ>] */
    @Override // okhttp3.internal.p042io.hh0
    /* renamed from: ֏ */
    public final synchronized int mo7815(lz5 lz5Var) {
        C9460 c9460 = (C9460) this.f24644.get(lz5Var.getName());
        if (c9460 == null) {
            return 0;
        }
        return c9460.f24646.get();
    }

    @Override // okhttp3.internal.p042io.hh0
    /* renamed from: ؠ */
    public final int mo7816() {
        return this.f24636.get();
    }

    @Override // okhttp3.internal.p042io.hh0
    /* renamed from: ހ */
    public final int mo7817() {
        return this.f24640.get();
    }

    @Override // okhttp3.internal.p042io.hh0
    /* renamed from: ށ */
    public final int mo7818() {
        return this.f24638.get();
    }

    @Override // okhttp3.internal.p042io.hh0
    /* renamed from: ނ */
    public final int mo7819() {
        return this.f24635.get();
    }

    /* JADX WARN: Type inference failed for: r1v3, types: [j$.util.concurrent.ConcurrentHashMap, java.util.Map<java.lang.String, okhttp3.internal.io.w5$Ϳ>] */
    @Override // okhttp3.internal.p042io.rl4
    /* renamed from: ރ */
    public final synchronized void mo11759(xg0 xg0Var) {
        lz5 m13957 = xg0Var.m13957();
        if (m13957 == null) {
            return;
        }
        this.f24635.decrementAndGet();
        if ("anonymous".equals(m13957.getName())) {
            this.f24638.decrementAndGet();
        }
        synchronized (m13957) {
            C9460 c9460 = (C9460) this.f24644.get(m13957.getName());
            if (c9460 != null) {
                c9460.f24646.decrementAndGet();
                if (xg0Var.mo7760() instanceof InetSocketAddress) {
                    c9460.m13475(((InetSocketAddress) xg0Var.mo7760()).getAddress()).decrementAndGet();
                }
            }
        }
    }

    @Override // okhttp3.internal.p042io.rl4
    /* renamed from: ބ */
    public final synchronized void mo11760(long j) {
        this.f24630.incrementAndGet();
        this.f24642.addAndGet(j);
    }

    @Override // okhttp3.internal.p042io.hh0
    /* renamed from: ޅ */
    public final int mo7820() {
        return this.f24634.get();
    }

    @Override // okhttp3.internal.p042io.hh0
    /* renamed from: ކ */
    public final int mo7821() {
        return this.f24632.get();
    }

    @Override // okhttp3.internal.p042io.rl4
    /* renamed from: އ */
    public final synchronized void mo11761() {
        this.f24633.incrementAndGet();
    }

    @Override // okhttp3.internal.p042io.rl4
    /* renamed from: ވ */
    public final synchronized void mo11762() {
        this.f24640.incrementAndGet();
        this.f24641.incrementAndGet();
    }

    @Override // okhttp3.internal.p042io.hh0
    /* renamed from: މ */
    public final int mo7822() {
        return this.f24639.get();
    }

    @Override // okhttp3.internal.p042io.rl4
    /* renamed from: ފ */
    public final synchronized void mo11763(long j) {
        this.f24631.incrementAndGet();
        this.f24643.addAndGet(j);
    }

    /* JADX WARN: Type inference failed for: r0v0, types: [j$.util.concurrent.ConcurrentHashMap, java.util.Map<java.lang.String, okhttp3.internal.io.w5$Ϳ>] */
    @Override // okhttp3.internal.p042io.hh0
    /* renamed from: ދ */
    public final synchronized int mo7823(lz5 lz5Var, InetAddress inetAddress) {
        C9460 c9460 = (C9460) this.f24644.get(lz5Var.getName());
        if (c9460 == null) {
            return 0;
        }
        return c9460.m13475(inetAddress).get();
    }

    @Override // okhttp3.internal.p042io.rl4
    /* renamed from: ތ */
    public final synchronized void mo11764() {
        this.f24632.incrementAndGet();
    }

    @Override // okhttp3.internal.p042io.rl4
    /* renamed from: ލ */
    public final synchronized void mo11765() {
        this.f24637.incrementAndGet();
    }

    @Override // okhttp3.internal.p042io.hh0
    /* renamed from: ގ */
    public final long mo7824() {
        return this.f24643.get();
    }
}
