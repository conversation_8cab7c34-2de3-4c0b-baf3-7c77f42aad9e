package android.view;

import android.graphics.Rect;
import android.view.View;
import android.view.ViewTreeObserver;
import kotlin.Metadata;
import okhttp3.internal.p042io.C4350o9;
import okhttp3.internal.p042io.EnumC7329;
import okhttp3.internal.p042io.InterfaceC4988s2;
import okhttp3.internal.p042io.InterfaceC7155;
import okhttp3.internal.p042io.di0;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.lj3;
import okhttp3.internal.p042io.lv1;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.nh0;
import okhttp3.internal.p042io.pj3;
import okhttp3.internal.p042io.u75;
import okhttp3.internal.p042io.wv2;
import okhttp3.internal.p042io.zu2;
import org.mozilla.javascript.optimizer.Codegen;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000\u000e\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\u0010\u0003\u001a\u00020\u0002*\b\u0012\u0004\u0012\u00020\u00010\u0000H\u008a@"}, m4115d2 = {"Lokhttp3/internal/io/pj3;", "Landroid/graphics/Rect;", "Lokhttp3/internal/io/lx5;", "<anonymous>"}, m4116k = 3, m4117mv = {1, 6, 0})
@InterfaceC4988s2(m11868c = "androidx.activity.PipHintTrackerKt$trackPipAnimationHintView$flow$1", m11869f = "PipHintTracker.kt", m11870l = {87}, m11871m = "invokeSuspend")
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class PipHintTrackerKt$trackPipAnimationHintView$flow$1 extends u75 implements di0<pj3<? super Rect>, InterfaceC7155<? super lx5>, Object> {
    public final /* synthetic */ View $view;
    private /* synthetic */ Object L$0;
    public int label;

    @Metadata(m4113bv = {}, m4114d1 = {"\u0000\b\n\u0002\u0018\u0002\n\u0002\b\u0003\u0010\u0003\u001a\u00020\u0000H\n¢\u0006\u0004\b\u0001\u0010\u0002"}, m4115d2 = {"Lokhttp3/internal/io/lx5;", "invoke", Codegen.TEMPLATE_LITERAL_INIT_METHOD_SIGNATURE, "<anonymous>"}, m4116k = 3, m4117mv = {1, 6, 0})
    /* renamed from: androidx.activity.PipHintTrackerKt$trackPipAnimationHintView$flow$1$1 */
    public static final class C00371 extends lv1 implements nh0<lx5> {
        public final /* synthetic */ ViewOnAttachStateChangeListenerC0038x7c5261f5 $attachStateChangeListener;
        public final /* synthetic */ View.OnLayoutChangeListener $layoutChangeListener;
        public final /* synthetic */ ViewTreeObserver.OnScrollChangedListener $scrollChangeListener;
        public final /* synthetic */ View $view;

        /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
        public C00371(View view, ViewTreeObserver.OnScrollChangedListener onScrollChangedListener, View.OnLayoutChangeListener onLayoutChangeListener, ViewOnAttachStateChangeListenerC0038x7c5261f5 viewOnAttachStateChangeListenerC0038x7c5261f5) {
            super(0);
            this.$view = view;
            this.$scrollChangeListener = onScrollChangedListener;
            this.$layoutChangeListener = onLayoutChangeListener;
            this.$attachStateChangeListener = viewOnAttachStateChangeListenerC0038x7c5261f5;
        }

        @Override // okhttp3.internal.p042io.nh0
        public /* bridge */ /* synthetic */ lx5 invoke() {
            invoke2();
            return lx5.f14876;
        }

        /* renamed from: invoke, reason: avoid collision after fix types in other method */
        public final void invoke2() {
            this.$view.getViewTreeObserver().removeOnScrollChangedListener(this.$scrollChangeListener);
            this.$view.removeOnLayoutChangeListener(this.$layoutChangeListener);
            this.$view.removeOnAttachStateChangeListener(this.$attachStateChangeListener);
        }
    }

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public PipHintTrackerKt$trackPipAnimationHintView$flow$1(View view, InterfaceC7155<? super PipHintTrackerKt$trackPipAnimationHintView$flow$1> interfaceC7155) {
        super(2, interfaceC7155);
        this.$view = view;
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: invokeSuspend$lambda-0, reason: not valid java name */
    public static final void m18330invokeSuspend$lambda0(pj3 pj3Var, View view, int i, int i2, int i3, int i4, int i5, int i6, int i7, int i8) {
        Rect trackPipAnimationHintView$positionInWindow;
        if (i == i5 && i3 == i7 && i2 == i6 && i4 == i8) {
            return;
        }
        fa1.m6825(view, "v");
        trackPipAnimationHintView$positionInWindow = Activity.trackPipAnimationHintView$positionInWindow(view);
        pj3Var.mo8968(trackPipAnimationHintView$positionInWindow);
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: invokeSuspend$lambda-1, reason: not valid java name */
    public static final void m18331invokeSuspend$lambda1(pj3 pj3Var, View view) {
        Rect trackPipAnimationHintView$positionInWindow;
        trackPipAnimationHintView$positionInWindow = Activity.trackPipAnimationHintView$positionInWindow(view);
        pj3Var.mo8968(trackPipAnimationHintView$positionInWindow);
    }

    @Override // okhttp3.internal.p042io.AbstractC7853
    @zu2
    public final InterfaceC7155<lx5> create(@wv2 Object obj, @zu2 InterfaceC7155<?> interfaceC7155) {
        PipHintTrackerKt$trackPipAnimationHintView$flow$1 pipHintTrackerKt$trackPipAnimationHintView$flow$1 = new PipHintTrackerKt$trackPipAnimationHintView$flow$1(this.$view, interfaceC7155);
        pipHintTrackerKt$trackPipAnimationHintView$flow$1.L$0 = obj;
        return pipHintTrackerKt$trackPipAnimationHintView$flow$1;
    }

    @Override // okhttp3.internal.p042io.di0
    @wv2
    /* renamed from: invoke, reason: avoid collision after fix types in other method and merged with bridge method [inline-methods] */
    public final Object mo18338invoke(@zu2 pj3<? super Rect> pj3Var, @wv2 InterfaceC7155<? super lx5> interfaceC7155) {
        return ((PipHintTrackerKt$trackPipAnimationHintView$flow$1) create(pj3Var, interfaceC7155)).invokeSuspend(lx5.f14876);
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r5v0, types: [android.view.View$OnAttachStateChangeListener, androidx.activity.PipHintTrackerKt$trackPipAnimationHintView$flow$1$attachStateChangeListener$1] */
    @Override // okhttp3.internal.p042io.AbstractC7853
    @wv2
    public final Object invokeSuspend(@zu2 Object obj) {
        Rect trackPipAnimationHintView$positionInWindow;
        EnumC7329 enumC7329 = EnumC7329.COROUTINE_SUSPENDED;
        int i = this.label;
        if (i == 0) {
            C4350o9.m10270(obj);
            final pj3 pj3Var = (pj3) this.L$0;
            final View.OnLayoutChangeListener onLayoutChangeListener = new View.OnLayoutChangeListener() { // from class: androidx.activity.Ϳ
                @Override // android.view.View.OnLayoutChangeListener
                public final void onLayoutChange(View view, int i2, int i3, int i4, int i5, int i6, int i7, int i8, int i9) {
                    PipHintTrackerKt$trackPipAnimationHintView$flow$1.m18330invokeSuspend$lambda0(pj3.this, view, i2, i3, i4, i5, i6, i7, i8, i9);
                }
            };
            final View view = this.$view;
            final ViewTreeObserver.OnScrollChangedListener onScrollChangedListener = new ViewTreeObserver.OnScrollChangedListener() { // from class: androidx.activity.Ԩ
                @Override // android.view.ViewTreeObserver.OnScrollChangedListener
                public final void onScrollChanged() {
                    PipHintTrackerKt$trackPipAnimationHintView$flow$1.m18331invokeSuspend$lambda1(pj3.this, view);
                }
            };
            ?? r5 = new View.OnAttachStateChangeListener() { // from class: androidx.activity.PipHintTrackerKt$trackPipAnimationHintView$flow$1$attachStateChangeListener$1
                @Override // android.view.View.OnAttachStateChangeListener
                public void onViewAttachedToWindow(@zu2 View view2) {
                    Rect trackPipAnimationHintView$positionInWindow2;
                    fa1.m6826(view2, "v");
                    pj3<Rect> pj3Var2 = pj3Var;
                    trackPipAnimationHintView$positionInWindow2 = Activity.trackPipAnimationHintView$positionInWindow(view);
                    pj3Var2.mo8968(trackPipAnimationHintView$positionInWindow2);
                    view.getViewTreeObserver().addOnScrollChangedListener(onScrollChangedListener);
                    view.addOnLayoutChangeListener(onLayoutChangeListener);
                }

                @Override // android.view.View.OnAttachStateChangeListener
                public void onViewDetachedFromWindow(@zu2 View view2) {
                    fa1.m6826(view2, "v");
                    view2.getViewTreeObserver().removeOnScrollChangedListener(onScrollChangedListener);
                    view2.removeOnLayoutChangeListener(onLayoutChangeListener);
                }
            };
            if (Api19Impl.INSTANCE.isAttachedToWindow(this.$view)) {
                trackPipAnimationHintView$positionInWindow = Activity.trackPipAnimationHintView$positionInWindow(this.$view);
                pj3Var.mo8968(trackPipAnimationHintView$positionInWindow);
                this.$view.getViewTreeObserver().addOnScrollChangedListener(onScrollChangedListener);
                this.$view.addOnLayoutChangeListener(onLayoutChangeListener);
            }
            this.$view.addOnAttachStateChangeListener(r5);
            C00371 c00371 = new C00371(this.$view, onScrollChangedListener, onLayoutChangeListener, r5);
            this.label = 1;
            if (lj3.m9281(pj3Var, c00371, this) == enumC7329) {
                return enumC7329;
            }
        } else {
            if (i != 1) {
                throw new IllegalStateException("call to 'resume' before 'invoke' with coroutine");
            }
            C4350o9.m10270(obj);
        }
        return lx5.f14876;
    }
}
