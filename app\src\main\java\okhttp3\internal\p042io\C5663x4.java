package okhttp3.internal.p042io;

/* renamed from: okhttp3.internal.io.x4 */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5663x4 extends C2981c6 implements InterfaceC6466 {
    public C5663x4(hc1 hc1Var) {
        super(hc1Var);
    }

    @Override // okhttp3.internal.p042io.InterfaceC6466
    /* renamed from: ԫ */
    public final boolean mo13844() {
        boolean z;
        synchronized (this.f7620) {
            z = this.f7624;
        }
        if (z) {
            return ((Boolean) m5571()).booleanValue();
        }
        return false;
    }

    @Override // okhttp3.internal.p042io.InterfaceC6466
    /* renamed from: Ԭ */
    public final InterfaceC6466 mo13845(vb1<?> vb1Var) {
        m5567(vb1Var);
        return this;
    }

    /* renamed from: ԯ */
    public final void m13846() {
        m5573(Boolean.TRUE);
    }

    @Override // okhttp3.internal.p042io.C2981c6
    /* renamed from: ހ */
    public final ub1 mo5569() {
        super.mo5569();
        return this;
    }
}
