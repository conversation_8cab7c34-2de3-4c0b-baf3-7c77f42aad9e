package okhttp3.internal.p042io;

import com.stardust.autojs.core.p027ui.inflater.DynamicLayoutInflater;

/* renamed from: okhttp3.internal.io.xl */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final /* synthetic */ class C5717xl implements DynamicLayoutInflater.ClassLoader {

    /* renamed from: ၥ */
    public static final /* synthetic */ C5717xl f26002 = new C5717xl();

    /* renamed from: Ϳ */
    public static StringBuilder m14009(String str, String str2) {
        StringBuilder sb = new StringBuilder();
        sb.append(str);
        sb.append(str2);
        return sb;
    }

    @Override // com.stardust.autojs.core.ui.inflater.DynamicLayoutInflater.ClassLoader
    public Class loadClass(String str) {
        Class m18453classLoader$lambda0;
        m18453classLoader$lambda0 = DynamicLayoutInflater.m18453classLoader$lambda0(str);
        return m18453classLoader$lambda0;
    }
}
