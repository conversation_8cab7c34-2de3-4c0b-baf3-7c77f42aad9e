package androidx.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import kotlin.Metadata;
import okhttp3.internal.p042io.dn2;
import okhttp3.internal.p042io.l44;
import okhttp3.internal.p042io.u95;

@Target({ElementType.FIELD, ElementType.METHOD, ElementType.PARAMETER, ElementType.LOCAL_VARIABLE})
@dn2
@u95
@Retention(RetentionPolicy.CLASS)
@Metadata(m4114d1 = {"\u0000\n\n\u0002\u0018\u0002\n\u0002\u0010\u001b\n\u0000\b\u0087\u0002\u0018\u00002\u00020\u0001B\u0000¨\u0006\u0002"}, m4115d2 = {"Landroidx/annotation/RawRes;", "", "annotation"}, m4116k = 1, m4117mv = {1, 7, 1}, m4119xi = 48)
@l44
@Documented
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public @interface RawRes {
}
