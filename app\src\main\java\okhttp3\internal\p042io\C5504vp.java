package okhttp3.internal.p042io;

import java.math.BigInteger;
import java.util.Enumeration;

/* renamed from: okhttp3.internal.io.vp */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5504vp extends AbstractC6888 {

    /* renamed from: ၥ */
    public C6886 f24371;

    /* renamed from: ၦ */
    public C6886 f24372;

    public C5504vp(BigInteger bigInteger, BigInteger bigInteger2) {
        this.f24371 = new C6886(bigInteger);
        this.f24372 = new C6886(bigInteger2);
    }

    public C5504vp(AbstractC6895 abstractC6895) {
        Enumeration mo5869 = abstractC6895.mo5869();
        this.f24371 = (C6886) mo5869.nextElement();
        this.f24372 = (C6886) mo5869.nextElement();
    }

    /* renamed from: Ԯ */
    public static C5504vp m13305(Object obj) {
        if (obj instanceof C5504vp) {
            return (C5504vp) obj;
        }
        if (obj != null) {
            return new C5504vp(AbstractC6895.m16160(obj));
        }
        return null;
    }

    @Override // okhttp3.internal.p042io.AbstractC6888, okhttp3.internal.p042io.InterfaceC6879
    /* renamed from: Ԩ */
    public final AbstractC6894 mo4894() {
        C6880 c6880 = new C6880();
        c6880.m16115(this.f24371);
        c6880.m16115(this.f24372);
        return new C7380(c6880);
    }

    /* renamed from: ԭ */
    public final BigInteger m13306() {
        return this.f24372.m16134();
    }

    /* renamed from: ԯ */
    public final BigInteger m13307() {
        return this.f24371.m16134();
    }
}
