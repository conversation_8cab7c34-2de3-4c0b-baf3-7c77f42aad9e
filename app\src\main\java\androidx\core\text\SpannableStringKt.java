package androidx.core.text;

import android.annotation.SuppressLint;
import android.text.Spannable;
import android.text.SpannableString;
import kotlin.Metadata;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.j71;
import okhttp3.internal.p042io.zu2;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000(\n\u0002\u0010\r\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a\r\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\u0086\b\u001a\r\u0010\u0004\u001a\u00020\u0003*\u00020\u0001H\u0087\b\u001a%\u0010\n\u001a\u00020\u0003*\u00020\u00012\u0006\u0010\u0006\u001a\u00020\u00052\u0006\u0010\u0007\u001a\u00020\u00052\u0006\u0010\t\u001a\u00020\bH\u0086\n\u001a\u001d\u0010\n\u001a\u00020\u0003*\u00020\u00012\u0006\u0010\f\u001a\u00020\u000b2\u0006\u0010\t\u001a\u00020\bH\u0086\n¨\u0006\r"}, m4115d2 = {"", "Landroid/text/Spannable;", "toSpannable", "Lokhttp3/internal/io/lx5;", "clearSpans", "", "start", "end", "", "span", "set", "Lokhttp3/internal/io/j71;", "range", "core-ktx_release"}, m4116k = 2, m4117mv = {1, 6, 0})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class SpannableStringKt {
    @SuppressLint({"SyntheticAccessor"})
    public static final void clearSpans(@zu2 Spannable spannable) {
        fa1.m6826(spannable, "<this>");
        Object[] spans = spannable.getSpans(0, spannable.length(), Object.class);
        fa1.m6825(spans, "getSpans(start, end, T::class.java)");
        for (Object obj : spans) {
            spannable.removeSpan(obj);
        }
    }

    public static final void set(@zu2 Spannable spannable, int i, int i2, @zu2 Object obj) {
        fa1.m6826(spannable, "<this>");
        fa1.m6826(obj, "span");
        spannable.setSpan(obj, i, i2, 17);
    }

    public static final void set(@zu2 Spannable spannable, @zu2 j71 j71Var, @zu2 Object obj) {
        fa1.m6826(spannable, "<this>");
        fa1.m6826(j71Var, "range");
        fa1.m6826(obj, "span");
        spannable.setSpan(obj, j71Var.getStart().intValue(), j71Var.getEndInclusive().intValue(), 17);
    }

    @zu2
    public static final Spannable toSpannable(@zu2 CharSequence charSequence) {
        fa1.m6826(charSequence, "<this>");
        SpannableString valueOf = SpannableString.valueOf(charSequence);
        fa1.m6825(valueOf, "valueOf(this)");
        return valueOf;
    }
}
