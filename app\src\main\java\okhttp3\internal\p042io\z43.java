package okhttp3.internal.p042io;

/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class z43 extends f43 {

    /* renamed from: Ԫ */
    public InterfaceC3578hg f27108;

    /* renamed from: ԫ */
    public int f27109;

    /* renamed from: Ԭ */
    public int f27110;

    public z43(InterfaceC3578hg interfaceC3578hg) {
        this.f27108 = interfaceC3578hg;
        this.f27109 = interfaceC3578hg.mo5241();
        this.f27110 = ((InterfaceC5769xz) interfaceC3578hg).mo4805();
    }

    @Override // okhttp3.internal.p042io.f43
    /* renamed from: Ԩ */
    public final InterfaceC6218 mo5904(int i) {
        int i2 = i / 8;
        return new ot1(m14443(3, i2), 0, i2);
    }

    @Override // okhttp3.internal.p042io.f43
    /* renamed from: ԩ */
    public final InterfaceC6218 mo5905(int i) {
        int i2 = i / 8;
        return new ot1(m14443(1, i2), 0, i2);
    }

    @Override // okhttp3.internal.p042io.f43
    /* renamed from: Ԫ */
    public final InterfaceC6218 mo5906(int i, int i2) {
        int i3 = i / 8;
        int i4 = i2 / 8;
        byte[] m14443 = m14443(1, i3);
        return new u73(new ot1(m14443, 0, i3), m14443(2, i4), 0, i4);
    }

    /* renamed from: Ԭ */
    public final byte[] m14443(int i, int i2) {
        byte[] bArr;
        byte[] bArr2;
        byte[] bArr3;
        int i3 = this.f27110;
        byte[] bArr4 = new byte[i3];
        byte[] bArr5 = new byte[i2];
        int i4 = 0;
        for (int i5 = 0; i5 != i3; i5++) {
            bArr4[i5] = (byte) i;
        }
        byte[] bArr6 = this.f9762;
        if (bArr6 == null || bArr6.length == 0) {
            bArr = new byte[0];
        } else {
            int i6 = this.f27110;
            int length = (((bArr6.length + i6) - 1) / i6) * i6;
            bArr = new byte[length];
            for (int i7 = 0; i7 != length; i7++) {
                byte[] bArr7 = this.f9762;
                bArr[i7] = bArr7[i7 % bArr7.length];
            }
        }
        byte[] bArr8 = this.f9761;
        if (bArr8 == null || bArr8.length == 0) {
            bArr2 = new byte[0];
        } else {
            int i8 = this.f27110;
            int length2 = (((bArr8.length + i8) - 1) / i8) * i8;
            bArr2 = new byte[length2];
            for (int i9 = 0; i9 != length2; i9++) {
                byte[] bArr9 = this.f9761;
                bArr2[i9] = bArr9[i9 % bArr9.length];
            }
        }
        int length3 = bArr.length + bArr2.length;
        byte[] bArr10 = new byte[length3];
        System.arraycopy(bArr, 0, bArr10, 0, bArr.length);
        System.arraycopy(bArr2, 0, bArr10, bArr.length, bArr2.length);
        int i10 = this.f27110;
        byte[] bArr11 = new byte[i10];
        int i11 = this.f27109;
        int i12 = ((i2 + i11) - 1) / i11;
        byte[] bArr12 = new byte[i11];
        int i13 = 1;
        while (i13 <= i12) {
            this.f27108.mo4801(bArr4, i4, i3);
            this.f27108.mo4801(bArr10, i4, length3);
            this.f27108.mo4803(bArr12, i4);
            for (int i14 = 1; i14 < this.f9763; i14++) {
                this.f27108.mo4801(bArr12, i4, i11);
                this.f27108.mo4803(bArr12, i4);
            }
            for (int i15 = 0; i15 != i10; i15++) {
                bArr11[i15] = bArr12[i15 % i11];
            }
            int i16 = 0;
            while (true) {
                int i17 = this.f27110;
                if (i16 == length3 / i17) {
                    break;
                }
                int i18 = i17 * i16;
                int i19 = (i10 + i18) - 1;
                int i20 = i3;
                int i21 = (bArr11[i10 - 1] & 255) + (bArr10[i19] & 255) + 1;
                bArr10[i19] = (byte) i21;
                int i22 = i21 >>> 8;
                int i23 = i10 - 2;
                while (true) {
                    bArr3 = bArr4;
                    if (i23 >= 0) {
                        int i24 = i18 + i23;
                        int i25 = (bArr11[i23] & 255) + (bArr10[i24] & 255) + i22;
                        bArr10[i24] = (byte) i25;
                        i22 = i25 >>> 8;
                        i23--;
                        bArr4 = bArr3;
                        length3 = length3;
                    }
                }
                i16++;
                bArr4 = bArr3;
                i3 = i20;
                length3 = length3;
            }
            int i26 = i3;
            byte[] bArr13 = bArr4;
            int i27 = length3;
            if (i13 == i12) {
                int i28 = i13 - 1;
                int i29 = this.f27109;
                System.arraycopy(bArr12, 0, bArr5, i28 * i29, i2 - (i28 * i29));
            } else {
                System.arraycopy(bArr12, 0, bArr5, (i13 - 1) * this.f27109, i11);
            }
            i13++;
            bArr4 = bArr13;
            i3 = i26;
            length3 = i27;
            i4 = 0;
        }
        return bArr5;
    }
}
