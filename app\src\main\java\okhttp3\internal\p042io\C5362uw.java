package okhttp3.internal.p042io;

/* renamed from: okhttp3.internal.io.uw */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes3.dex */
public final class C5362uw extends en2 {

    /* renamed from: ၦ */
    public final lr5[] f23532;

    /* renamed from: ၮ */
    public final boolean[] f23533;

    /* renamed from: ၯ */
    public int f23534;

    public C5362uw(int i) {
        super(i != 0);
        this.f23532 = new lr5[i];
        this.f23533 = new boolean[i];
        this.f23534 = 0;
    }

    /* renamed from: ޤ */
    public static String m12997(lr5 lr5Var) {
        return lr5Var == null ? "<invalid>" : lr5Var.toString();
    }

    /* renamed from: ࡠ */
    public static lr5 m12998(String str) {
        throw new ar4(C4118lz.m9496("stack: ", str));
    }

    /* renamed from: ޙ */
    public final void m12999(C3382fw c3382fw) {
        int i = this.f23534 - 1;
        int i2 = 0;
        while (i2 <= i) {
            StringBuilder m14894 = C6134.m14894("stack[", i2 == i ? "top0" : lg5.m9265(i - i2), "]: ");
            m14894.append(m12997(this.f23532[i2]));
            c3382fw.m7107(m14894.toString());
            i2++;
        }
    }

    /* renamed from: ޚ */
    public final void m13000(int i, lr5 lr5Var) {
        m6526();
        try {
            lr5 mo9372 = lr5Var.mo9372();
            int i2 = (this.f23534 - i) - 1;
            lr5 lr5Var2 = this.f23532[i2];
            if (lr5Var2 != null && lr5Var2.getType().m9769() == mo9372.getType().m9769()) {
                this.f23532[i2] = mo9372;
                return;
            }
            StringBuilder m9240 = lf2.m9240("incompatible substitution: ");
            m9240.append(m12997(lr5Var2));
            m9240.append(" -> ");
            m9240.append(m12997(mo9372));
            m12998(m9240.toString());
            throw null;
        } catch (NullPointerException unused) {
            throw new NullPointerException("type == null");
        }
    }

    /* renamed from: ޜ */
    public final C5362uw m13001() {
        C5362uw c5362uw = new C5362uw(this.f23532.length);
        lr5[] lr5VarArr = this.f23532;
        System.arraycopy(lr5VarArr, 0, c5362uw.f23532, 0, lr5VarArr.length);
        boolean[] zArr = this.f23533;
        System.arraycopy(zArr, 0, c5362uw.f23533, 0, zArr.length);
        c5362uw.f23534 = this.f23534;
        return c5362uw;
    }

    /* renamed from: ޟ */
    public final C5362uw m13002(C5362uw c5362uw) {
        try {
            return qm5.m11374(this, c5362uw);
        } catch (ar4 e) {
            e.m7107("underlay stack:");
            m12999(e);
            e.m7107("overlay stack:");
            c5362uw.m12999(e);
            throw e;
        }
    }

    /* renamed from: ޠ */
    public final lr5 m13003(int i) {
        if (i < 0) {
            throw new IllegalArgumentException("n < 0");
        }
        if (i < this.f23534) {
            return this.f23532[(r0 - i) - 1];
        }
        m12998("underflow");
        throw null;
    }

    /* renamed from: ޡ */
    public final mq5 m13004(int i) {
        return m13003(i).getType();
    }

    /* renamed from: ޣ */
    public final void m13005(lr5 lr5Var) {
        m6526();
        try {
            lr5 mo9372 = lr5Var.mo9372();
            int m9769 = mo9372.getType().m9769();
            int i = this.f23534;
            int i2 = i + m9769;
            lr5[] lr5VarArr = this.f23532;
            if (i2 > lr5VarArr.length) {
                m12998("overflow");
                throw null;
            }
            if (m9769 == 2) {
                lr5VarArr[i] = null;
                this.f23534 = i + 1;
            }
            int i3 = this.f23534;
            lr5VarArr[i3] = mo9372;
            this.f23534 = i3 + 1;
        } catch (NullPointerException unused) {
            throw new NullPointerException("type == null");
        }
    }
}
