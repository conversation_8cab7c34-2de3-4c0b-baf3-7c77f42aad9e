package okhttp3.internal.p042io;

import android.os.Bundle;
import android.view.SavedStateRegistry;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/* renamed from: okhttp3.internal.io.uh */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes3.dex */
public final class C5326uh implements SavedStateRegistry.SavedStateProvider {

    /* renamed from: Ϳ */
    public final /* synthetic */ ia4 f23229;

    public C5326uh(ia4 ia4Var) {
        this.f23229 = ia4Var;
    }

    @Override // androidx.savedstate.SavedStateRegistry.SavedStateProvider
    @zu2
    public final Bundle saveState() {
        Map<String, List<Object>> mo8148 = this.f23229.mo8148();
        Bundle bundle = new Bundle();
        for (Map.Entry<String, List<Object>> entry : mo8148.entrySet()) {
            String key = entry.getKey();
            List<Object> value = entry.getValue();
            bundle.putParcelableArrayList(key, value instanceof ArrayList ? (ArrayList) value : new ArrayList<>(value));
        }
        return bundle;
    }
}
