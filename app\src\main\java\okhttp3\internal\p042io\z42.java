package okhttp3.internal.p042io;

import androidx.collection.ArrayMap;
import com.bumptech.glide.load.engine.C0941;
import com.bumptech.glide.load.engine.C0946;
import java.util.Collections;
import java.util.concurrent.atomic.AtomicReference;

/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes3.dex */
public final class z42 {

    /* renamed from: ԩ */
    public static final C0946<?, ?, ?> f27105 = new C0946<>(Object.class, Object.class, Object.class, Collections.singletonList(new C0941(Object.class, Object.class, Object.class, Collections.emptyList(), new C7073(), null)), null);

    /* renamed from: Ϳ */
    public final ArrayMap<lm2, C0946<?, ?, ?>> f27106 = new ArrayMap<>();

    /* renamed from: Ԩ */
    public final AtomicReference<lm2> f27107 = new AtomicReference<>();
}
