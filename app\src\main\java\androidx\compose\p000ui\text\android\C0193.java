package androidx.compose.p000ui.text.android;

import android.os.Build;
import android.text.StaticLayout;
import androidx.annotation.DoNotInline;
import androidx.annotation.OptIn;
import androidx.annotation.RequiresApi;
import androidx.core.os.BuildCompat;
import okhttp3.internal.p042io.c15;
import okhttp3.internal.p042io.d15;
import okhttp3.internal.p042io.e15;
import okhttp3.internal.p042io.f15;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.zu2;

@RequiresApi(23)
/* renamed from: androidx.compose.ui.text.android.Ϳ */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class C0193 implements InterfaceC0196 {
    @Override // androidx.compose.p000ui.text.android.InterfaceC0196
    @DoNotInline
    @zu2
    /* renamed from: Ϳ */
    public StaticLayout mo60(@zu2 f15 f15Var) {
        fa1.m6826(f15Var, "params");
        StaticLayout.Builder obtain = StaticLayout.Builder.obtain(f15Var.f9674, f15Var.f9675, f15Var.f9676, f15Var.f9677, f15Var.f9678);
        obtain.setTextDirection(f15Var.f9679);
        obtain.setAlignment(f15Var.f9680);
        obtain.setMaxLines(f15Var.f9681);
        obtain.setEllipsize(f15Var.f9682);
        obtain.setEllipsizedWidth(f15Var.f9683);
        obtain.setLineSpacing(f15Var.f9685, f15Var.f9684);
        obtain.setIncludePad(f15Var.f9687);
        obtain.setBreakStrategy(f15Var.f9689);
        obtain.setHyphenationFrequency(f15Var.f9692);
        obtain.setIndents(f15Var.f9693, f15Var.f9694);
        int i = Build.VERSION.SDK_INT;
        if (i >= 26) {
            c15.m5504(obtain, f15Var.f9686);
        }
        if (i >= 28) {
            d15.m5899(obtain, f15Var.f9688);
        }
        if (i >= 33) {
            e15.m6297(obtain, f15Var.f9690, f15Var.f9691);
        }
        StaticLayout build = obtain.build();
        fa1.m6825(build, "obtain(params.text, para…  }\n            }.build()");
        return build;
    }

    @Override // androidx.compose.p000ui.text.android.InterfaceC0196
    @OptIn(markerClass = {BuildCompat.PrereleaseSdkCheck.class})
    /* renamed from: Ԩ */
    public final boolean mo61(@zu2 StaticLayout staticLayout, boolean z) {
        if (BuildCompat.isAtLeastT()) {
            return e15.m6296(staticLayout);
        }
        if (Build.VERSION.SDK_INT >= 28) {
            return z;
        }
        return false;
    }
}
