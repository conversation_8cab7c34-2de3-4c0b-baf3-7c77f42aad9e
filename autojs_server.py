#!/usr/bin/env python3
"""
AutoJS9 兼容的调试服务器实现
支持WebSocket连接、token认证和RPC调用
"""

import asyncio
import json
import hashlib
import time
import logging
from typing import Dict, Set, Optional, Any
from dataclasses import dataclass
from pathlib import Path
import websockets
from websockets.server import WebSocketServerProtocol
import sqlite3
import uuid

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class AuthorizedDevice:
    """授权设备信息"""
    device_id: str
    token: str
    authorized_at: float
    last_access: float
    device_name: Optional[str] = None

class TokenManager:
    """Token管理器"""
    
    def __init__(self, db_path: str = "auth.db"):
        self.db_path = db_path
        self.authorized_tokens: Set[str] = set()
        self.pending_authorizations: Dict[str, asyncio.Future] = {}
        self._init_db()
        self._load_authorized_tokens()
    
    def _init_db(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        conn.execute("""
            CREATE TABLE IF NOT EXISTS authorized_devices (
                device_id TEXT PRIMARY KEY,
                token TEXT UNIQUE NOT NULL,
                authorized_at REAL NOT NULL,
                last_access REAL NOT NULL,
                device_name TEXT
            )
        """)
        conn.commit()
        conn.close()
    
    def _load_authorized_tokens(self):
        """从数据库加载已授权的token"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.execute("SELECT token FROM authorized_devices")
        self.authorized_tokens = {row[0] for row in cursor.fetchall()}
        conn.close()
        logger.info(f"加载了 {len(self.authorized_tokens)} 个已授权token")
    
    def generate_token(self, device_id: str) -> str:
        """为设备生成token"""
        # 使用设备ID和时间戳生成token
        data = f"{device_id}:{time.time()}:{uuid.uuid4()}"
        return hashlib.md5(data.encode()).hexdigest()
    
    def is_authorized(self, token: str) -> bool:
        """检查token是否已授权"""
        return token in self.authorized_tokens
    
    async def request_authorization(self, device_id: str, device_name: str = None) -> bool:
        """请求用户授权设备"""
        token = self.generate_token(device_id)
        
        # 创建等待授权的Future
        auth_future = asyncio.Future()
        self.pending_authorizations[token] = auth_future
        
        # 显示授权请求（实际应用中可能是GUI界面）
        print(f"\n=== 设备授权请求 ===")
        print(f"设备ID: {device_id}")
        print(f"设备名称: {device_name or '未知设备'}")
        print(f"Token: {token}")
        print(f"是否授权此设备？(y/n): ", end="", flush=True)
        
        # 在实际应用中，这里应该是异步等待用户在GUI中的操作
        # 这里简化为控制台输入
        asyncio.create_task(self._wait_for_console_input(token))
        
        try:
            # 等待授权结果，超时30秒
            result = await asyncio.wait_for(auth_future, timeout=30.0)
            
            if result:
                # 授权成功，保存到数据库
                self._save_authorized_device(device_id, token, device_name)
                self.authorized_tokens.add(token)
                logger.info(f"设备 {device_id} 授权成功")
            else:
                logger.info(f"设备 {device_id} 授权被拒绝")
            
            return result
            
        except asyncio.TimeoutError:
            logger.warning(f"设备 {device_id} 授权请求超时")
            return False
        finally:
            self.pending_authorizations.pop(token, None)
    
    async def _wait_for_console_input(self, token: str):
        """等待控制台输入（简化实现）"""
        # 在实际应用中，这应该是GUI事件处理
        loop = asyncio.get_event_loop()
        
        def get_input():
            try:
                return input().strip().lower()
            except:
                return 'n'
        
        try:
            response = await loop.run_in_executor(None, get_input)
            future = self.pending_authorizations.get(token)
            if future and not future.done():
                future.set_result(response == 'y')
        except Exception as e:
            logger.error(f"获取用户输入失败: {e}")
            future = self.pending_authorizations.get(token)
            if future and not future.done():
                future.set_result(False)
    
    def _save_authorized_device(self, device_id: str, token: str, device_name: str = None):
        """保存授权设备到数据库"""
        conn = sqlite3.connect(self.db_path)
        now = time.time()
        conn.execute("""
            INSERT OR REPLACE INTO authorized_devices 
            (device_id, token, authorized_at, last_access, device_name)
            VALUES (?, ?, ?, ?, ?)
        """, (device_id, token, now, now, device_name))
        conn.commit()
        conn.close()
    
    def update_last_access(self, token: str):
        """更新token的最后访问时间"""
        conn = sqlite3.connect(self.db_path)
        conn.execute("""
            UPDATE authorized_devices 
            SET last_access = ? 
            WHERE token = ?
        """, (time.time(), token))
        conn.commit()
        conn.close()

class AutoJSServer:
    """AutoJS9兼容的调试服务器"""
    
    def __init__(self, host: str = "0.0.0.0", port: int = 9317):
        self.host = host
        self.port = port
        self.token_manager = TokenManager()
        self.connected_clients: Dict[WebSocketServerProtocol, str] = {}
    
    async def handle_client(self, websocket: WebSocketServerProtocol, path: str):
        """处理客户端连接"""
        client_addr = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
        logger.info(f"新客户端连接: {client_addr}")
        
        try:
            async for message in websocket:
                await self.handle_message(websocket, message)
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"客户端断开连接: {client_addr}")
        except Exception as e:
            logger.error(f"处理客户端 {client_addr} 时发生错误: {e}")
        finally:
            self.connected_clients.pop(websocket, None)
    
    async def handle_message(self, websocket: WebSocketServerProtocol, message: str):
        """处理客户端消息"""
        try:
            data = json.loads(message)
            
            # 检查是否是RPC调用
            if "method" in data:
                await self.handle_rpc_call(websocket, data)
            else:
                logger.warning(f"未知消息格式: {data}")
                
        except json.JSONDecodeError:
            logger.error(f"无效的JSON消息: {message}")
            await self.send_error(websocket, -32700, "Parse error")
    
    async def handle_rpc_call(self, websocket: WebSocketServerProtocol, data: Dict[str, Any]):
        """处理RPC调用"""
        method = data.get("method")
        params = data.get("params", {})
        rpc_id = data.get("id")
        
        logger.info(f"RPC调用: {method}")
        
        # 特殊处理授权方法
        if method == "debug.authorize":
            await self.handle_authorize(websocket, params, rpc_id)
            return
        
        # 其他方法需要token验证
        token = params.get("token")
        if not token:
            await self.send_error(websocket, -32602, "no token field or token is not a string", rpc_id)
            return
        
        if not self.token_manager.is_authorized(token):
            await self.send_error(websocket, -32603, "Forbidden", rpc_id)
            return
        
        # 更新最后访问时间
        self.token_manager.update_last_access(token)
        
        # 处理具体的RPC方法
        await self.handle_authorized_rpc(websocket, method, params, rpc_id)
    
    async def handle_authorize(self, websocket: WebSocketServerProtocol, params: Dict[str, Any], rpc_id: Any):
        """处理授权请求"""
        device_id = params.get("device_id", "unknown")
        device_name = params.get("device_name")
        
        # 请求用户授权
        authorized = await self.token_manager.request_authorization(device_id, device_name)
        
        if authorized:
            # 生成新token并返回
            token = self.token_manager.generate_token(device_id)
            self.connected_clients[websocket] = token
            
            response = {
                "jsonrpc": "2.0",
                "result": {
                    "authorized": True,
                    "token": token
                },
                "id": rpc_id
            }
        else:
            response = {
                "jsonrpc": "2.0",
                "result": {
                    "authorized": False
                },
                "id": rpc_id
            }
        
        await websocket.send(json.dumps(response))
    
    async def handle_authorized_rpc(self, websocket: WebSocketServerProtocol, method: str, params: Dict[str, Any], rpc_id: Any):
        """处理已授权的RPC调用"""
        # 这里实现具体的RPC方法
        if method == "ping":
            result = {"pong": time.time()}
        elif method == "getDeviceInfo":
            result = {
                "model": "AutoJS Server",
                "version": "1.0.0",
                "api_level": 30,
                "brand": "AutoJS",
                "manufacturer": "AutoJS Team"
            }
        elif method == "execDebugCommand":
            # 执行调试命令
            cmd_type = params.get("type", "")
            cmd_data = params.get("data", "")
            result = await self.handle_debug_command(cmd_type, cmd_data)
        elif method == "enableVfs":
            # 启用虚拟文件系统
            result = {
                "enabled": True,
                "root_path": "/sdcard/Scripts",
                "script_path": "/sdcard/Scripts"
            }
        elif method == "enableFtpServer":
            # 启用FTP服务器
            result = {
                "enabled": True,
                "port": 2121,
                "username": "autojs",
                "password": "autojs123"
            }
        elif method == "listFiles":
            # 列出文件
            path = params.get("path", "/")
            result = await self.list_files(path)
        elif method == "readFile":
            # 读取文件
            file_path = params.get("path", "")
            result = await self.read_file(file_path)
        elif method == "writeFile":
            # 写入文件
            file_path = params.get("path", "")
            content = params.get("content", "")
            result = await self.write_file(file_path, content)
        else:
            await self.send_error(websocket, -32601, f"Method not found: {method}", rpc_id)
            return

        response = {
            "jsonrpc": "2.0",
            "result": result,
            "id": rpc_id
        }

        await websocket.send(json.dumps(response))

    async def handle_debug_command(self, cmd_type: str, cmd_data: str) -> Dict[str, Any]:
        """处理调试命令"""
        logger.info(f"执行调试命令: {cmd_type}")

        if cmd_type == "eval":
            # 执行JavaScript代码（这里只是模拟）
            return {
                "success": True,
                "result": f"执行结果: {cmd_data}",
                "type": "string"
            }
        elif cmd_type == "log":
            # 输出日志
            logger.info(f"客户端日志: {cmd_data}")
            return {"success": True}
        else:
            return {
                "success": False,
                "error": f"未知命令类型: {cmd_type}"
            }

    async def list_files(self, path: str) -> Dict[str, Any]:
        """列出文件（模拟实现）"""
        # 在实际实现中，这里应该列出真实的文件系统
        return {
            "path": path,
            "files": [
                {"name": "main.js", "type": "file", "size": 1024},
                {"name": "utils.js", "type": "file", "size": 512},
                {"name": "modules", "type": "directory", "size": 0}
            ]
        }

    async def read_file(self, file_path: str) -> Dict[str, Any]:
        """读取文件（模拟实现）"""
        # 在实际实现中，这里应该读取真实文件
        return {
            "path": file_path,
            "content": f"// 文件内容: {file_path}\nconsole.log('Hello AutoJS');",
            "encoding": "utf-8"
        }

    async def write_file(self, file_path: str, content: str) -> Dict[str, Any]:
        """写入文件（模拟实现）"""
        # 在实际实现中，这里应该写入真实文件
        logger.info(f"写入文件: {file_path} ({len(content)} 字符)")
        return {
            "path": file_path,
            "success": True,
            "bytes_written": len(content.encode('utf-8'))
        }
    
    async def send_error(self, websocket: WebSocketServerProtocol, code: int, message: str, rpc_id: Any = None):
        """发送错误响应"""
        response = {
            "jsonrpc": "2.0",
            "error": {
                "code": code,
                "message": message
            },
            "id": rpc_id
        }
        
        await websocket.send(json.dumps(response))
    
    async def start_server(self):
        """启动服务器"""
        logger.info(f"启动AutoJS服务器: {self.host}:{self.port}")
        
        async with websockets.serve(self.handle_client, self.host, self.port):
            logger.info("服务器启动成功，等待连接...")
            await asyncio.Future()  # 永远运行

if __name__ == "__main__":
    server = AutoJSServer()
    asyncio.run(server.start_server())
