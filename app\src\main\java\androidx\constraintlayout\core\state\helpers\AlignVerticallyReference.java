package androidx.constraintlayout.core.state.helpers;

import androidx.constraintlayout.core.state.HelperReference;
import androidx.constraintlayout.core.state.State;

/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public class AlignVerticallyReference extends HelperReference {
    private float mBias;

    public AlignVerticallyReference(State state) {
        super(state, State.Helper.ALIGN_VERTICALLY);
        this.mBias = 0.5f;
    }

    /* JADX WARN: Removed duplicated region for block: B:12:0x0046 A[SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:16:0x0006 A[SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:17:0x0034  */
    /* JADX WARN: Removed duplicated region for block: B:9:0x0030  */
    @Override // androidx.constraintlayout.core.state.HelperReference, androidx.constraintlayout.core.state.ConstraintReference, androidx.constraintlayout.core.state.Reference
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public void apply() {
        /*
            r4 = this;
            java.util.ArrayList<java.lang.Object> r0 = r4.mReferences
            java.util.Iterator r0 = r0.iterator()
        L6:
            boolean r1 = r0.hasNext()
            if (r1 == 0) goto L4a
            java.lang.Object r1 = r0.next()
            androidx.constraintlayout.core.state.State r2 = r4.mState
            androidx.constraintlayout.core.state.ConstraintReference r1 = r2.constraints(r1)
            r1.clearVertical()
            java.lang.Object r2 = r4.mTopToTop
            if (r2 == 0) goto L21
        L1d:
            r1.topToTop(r2)
            goto L2c
        L21:
            java.lang.Object r2 = r4.mTopToBottom
            if (r2 == 0) goto L29
            r1.topToBottom(r2)
            goto L2c
        L29:
            java.lang.Integer r2 = androidx.constraintlayout.core.state.State.PARENT
            goto L1d
        L2c:
            java.lang.Object r2 = r4.mBottomToTop
            if (r2 == 0) goto L34
            r1.bottomToTop(r2)
            goto L3e
        L34:
            java.lang.Object r2 = r4.mBottomToBottom
            if (r2 == 0) goto L39
            goto L3b
        L39:
            java.lang.Integer r2 = androidx.constraintlayout.core.state.State.PARENT
        L3b:
            r1.bottomToBottom(r2)
        L3e:
            float r2 = r4.mBias
            r3 = 1056964608(0x3f000000, float:0.5)
            int r3 = (r2 > r3 ? 1 : (r2 == r3 ? 0 : -1))
            if (r3 == 0) goto L6
            r1.verticalBias(r2)
            goto L6
        L4a:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.constraintlayout.core.state.helpers.AlignVerticallyReference.apply():void");
    }
}
