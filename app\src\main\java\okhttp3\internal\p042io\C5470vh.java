package okhttp3.internal.p042io;

/* renamed from: okhttp3.internal.io.vh */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes3.dex */
public final class C5470vh extends lv1 implements ph0<Object, Boolean> {

    /* renamed from: ၥ */
    public static final C5470vh f24183 = new C5470vh();

    public C5470vh() {
        super(1);
    }

    @Override // okhttp3.internal.p042io.ph0
    public final Boolean invoke(Object obj) {
        fa1.m6826(obj, "it");
        return Boolean.valueOf(C5591wh.m13575(obj));
    }
}
