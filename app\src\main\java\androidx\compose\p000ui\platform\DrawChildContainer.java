package androidx.compose.p000ui.platform;

import android.content.Context;
import android.graphics.Canvas;
import android.view.View;
import android.view.ViewGroup;
import com.stardust.autojs.engine.RhinoJavaScriptEngine;
import kotlin.Metadata;
import okhttp3.internal.p042io.C6052;
import okhttp3.internal.p042io.C6354;
import okhttp3.internal.p042io.InterfaceC7598;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.op3;
import okhttp3.internal.p042io.zu2;

@Metadata(m4113bv = {}, m4114d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0010\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u000f\u001a\u00020\u000e¢\u0006\u0004\b\u0010\u0010\u0011J\b\u0010\u0003\u001a\u00020\u0002H\u0016J'\u0010\r\u001a\u00020\n2\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\bH\u0000¢\u0006\u0004\b\u000b\u0010\f¨\u0006\u0012"}, m4115d2 = {"Landroidx/compose/ui/platform/DrawChildContainer;", "Landroid/view/ViewGroup;", "", "getChildCount", "Lokhttp3/internal/io/ค;", "canvas", "Landroid/view/View;", "view", "", "drawingTime", "Lokhttp3/internal/io/lx5;", "drawChild$ui_release", "(Lokhttp3/internal/io/ค;Landroid/view/View;J)V", "drawChild", "Landroid/content/Context;", "context", RhinoJavaScriptEngine.SOURCE_NAME_INIT, "(Landroid/content/Context;)V", "ui_release"}, m4116k = 1, m4117mv = {1, 7, 1})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public class DrawChildContainer extends ViewGroup {

    /* renamed from: ၥ */
    public boolean f137;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public DrawChildContainer(@zu2 Context context) {
        super(context);
        fa1.m6826(context, "context");
        setClipChildren(false);
        setTag(op3.hide_in_inspector_tag, Boolean.TRUE);
    }

    @Override // android.view.ViewGroup, android.view.View
    public void dispatchDraw(@zu2 Canvas canvas) {
        boolean z;
        fa1.m6826(canvas, "canvas");
        int childCount = super.getChildCount();
        int i = 0;
        while (true) {
            if (i >= childCount) {
                z = false;
                break;
            }
            View childAt = getChildAt(i);
            fa1.m6824(childAt, "null cannot be cast to non-null type androidx.compose.ui.platform.ViewLayer");
            if (((ViewLayer) childAt).getIsInvalidated()) {
                z = true;
                break;
            }
            i++;
        }
        if (z) {
            this.f137 = true;
            try {
                super.dispatchDraw(canvas);
            } finally {
                this.f137 = false;
            }
        }
    }

    public final void drawChild$ui_release(@zu2 InterfaceC7598 canvas, @zu2 View view, long drawingTime) {
        fa1.m6826(canvas, "canvas");
        fa1.m6826(view, "view");
        Canvas canvas2 = C6354.f28717;
        super.drawChild(((C6052) canvas).f27963, view, drawingTime);
    }

    @Override // android.view.ViewGroup
    public int getChildCount() {
        if (this.f137) {
            return super.getChildCount();
        }
        return 0;
    }

    @Override // android.view.ViewGroup, android.view.View
    public final void onLayout(boolean z, int i, int i2, int i3, int i4) {
    }

    @Override // android.view.View
    public final void onMeasure(int i, int i2) {
        setMeasuredDimension(0, 0);
    }
}
