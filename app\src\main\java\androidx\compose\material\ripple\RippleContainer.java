package androidx.compose.material.ripple;

import android.content.Context;
import android.view.ViewGroup;
import com.stardust.autojs.engine.RhinoJavaScriptEngine;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import kotlin.Metadata;
import okhttp3.internal.p042io.C7733;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.op3;
import okhttp3.internal.p042io.x54;
import okhttp3.internal.p042io.zu2;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0000\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\b\u001a\u00020\u0007¢\u0006\u0004\b\t\u0010\nJ\n\u0010\u0004\u001a\u00020\u0003*\u00020\u0002J\n\u0010\u0006\u001a\u00020\u0005*\u00020\u0002¨\u0006\u000b"}, m4115d2 = {"Landroidx/compose/material/ripple/RippleContainer;", "Landroid/view/ViewGroup;", "Lokhttp3/internal/io/ས;", "Landroidx/compose/material/ripple/RippleHostView;", "getRippleHostView", "Lokhttp3/internal/io/lx5;", "disposeRippleIfNeeded", "Landroid/content/Context;", "context", RhinoJavaScriptEngine.SOURCE_NAME_INIT, "(Landroid/content/Context;)V", "material-ripple_release"}, m4116k = 1, m4117mv = {1, 7, 1})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class RippleContainer extends ViewGroup {

    /* renamed from: ၥ */
    public final int f25;

    /* renamed from: ၦ */
    @zu2
    public final List<RippleHostView> f26;

    /* renamed from: ၮ */
    @zu2
    public final List<RippleHostView> f27;

    /* renamed from: ၯ */
    @zu2
    public final x54 f28;

    /* renamed from: ၰ */
    public int f29;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public RippleContainer(@zu2 Context context) {
        super(context);
        fa1.m6826(context, "context");
        this.f25 = 5;
        ArrayList arrayList = new ArrayList();
        this.f26 = arrayList;
        ArrayList arrayList2 = new ArrayList();
        this.f27 = arrayList2;
        this.f28 = new x54();
        setClipChildren(false);
        RippleHostView rippleHostView = new RippleHostView(context);
        addView(rippleHostView);
        arrayList.add(rippleHostView);
        arrayList2.add(rippleHostView);
        this.f29 = 1;
        setTag(op3.hide_in_inspector_tag, Boolean.TRUE);
    }

    /* JADX WARN: Type inference failed for: r0v3, types: [java.util.LinkedHashMap, java.util.Map<okhttp3.internal.io.ས, androidx.compose.material.ripple.RippleHostView>] */
    /* JADX WARN: Type inference failed for: r3v1, types: [java.util.ArrayList, java.util.List<androidx.compose.material.ripple.RippleHostView>] */
    public final void disposeRippleIfNeeded(@zu2 C7733 c7733) {
        fa1.m6826(c7733, "<this>");
        c7733.f32789.setValue(null);
        x54 x54Var = this.f28;
        Objects.requireNonNull(x54Var);
        RippleHostView rippleHostView = (RippleHostView) x54Var.f25393.get(c7733);
        if (rippleHostView != null) {
            rippleHostView.disposeRipple();
            this.f28.m13864(c7733);
            this.f27.add(rippleHostView);
        }
    }

    /* JADX WARN: Type inference failed for: r0v7, types: [java.util.ArrayList, java.util.List<androidx.compose.material.ripple.RippleHostView>] */
    /* JADX WARN: Type inference failed for: r1v1, types: [java.util.LinkedHashMap, java.util.Map<okhttp3.internal.io.ས, androidx.compose.material.ripple.RippleHostView>] */
    /* JADX WARN: Type inference failed for: r1v11, types: [java.util.LinkedHashMap, java.util.Map<androidx.compose.material.ripple.RippleHostView, okhttp3.internal.io.ས>] */
    /* JADX WARN: Type inference failed for: r1v17, types: [java.util.ArrayList, java.util.List<androidx.compose.material.ripple.RippleHostView>] */
    /* JADX WARN: Type inference failed for: r1v4, types: [java.lang.Object, java.util.ArrayList, java.util.List<androidx.compose.material.ripple.RippleHostView>] */
    @zu2
    public final RippleHostView getRippleHostView(@zu2 C7733 c7733) {
        fa1.m6826(c7733, "<this>");
        x54 x54Var = this.f28;
        Objects.requireNonNull(x54Var);
        RippleHostView rippleHostView = (RippleHostView) x54Var.f25393.get(c7733);
        if (rippleHostView != null) {
            return rippleHostView;
        }
        ?? r1 = this.f27;
        fa1.m6826(r1, "<this>");
        RippleHostView rippleHostView2 = (RippleHostView) (r1.isEmpty() ? null : r1.remove(0));
        if (rippleHostView2 == null) {
            if (this.f29 > fa1.m6834(this.f26)) {
                Context context = getContext();
                fa1.m6825(context, "context");
                rippleHostView2 = new RippleHostView(context);
                addView(rippleHostView2);
                this.f26.add(rippleHostView2);
            } else {
                rippleHostView2 = (RippleHostView) this.f26.get(this.f29);
                x54 x54Var2 = this.f28;
                Objects.requireNonNull(x54Var2);
                fa1.m6826(rippleHostView2, "rippleHostView");
                C7733 c77332 = (C7733) x54Var2.f25394.get(rippleHostView2);
                if (c77332 != null) {
                    c77332.f32789.setValue(null);
                    this.f28.m13864(c77332);
                    rippleHostView2.disposeRipple();
                }
            }
            int i = this.f29;
            if (i < this.f25 - 1) {
                this.f29 = i + 1;
            } else {
                this.f29 = 0;
            }
        }
        x54 x54Var3 = this.f28;
        Objects.requireNonNull(x54Var3);
        x54Var3.f25393.put(c7733, rippleHostView2);
        x54Var3.f25394.put(rippleHostView2, c7733);
        return rippleHostView2;
    }

    @Override // android.view.ViewGroup, android.view.View
    public final void onLayout(boolean z, int i, int i2, int i3, int i4) {
    }

    @Override // android.view.View
    public final void onMeasure(int i, int i2) {
        setMeasuredDimension(0, 0);
    }
}
