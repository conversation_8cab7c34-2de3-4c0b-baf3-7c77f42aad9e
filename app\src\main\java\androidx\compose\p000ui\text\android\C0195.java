package androidx.compose.p000ui.text.android;

import android.text.Layout;
import android.text.StaticLayout;
import android.text.TextDirectionHeuristic;
import android.text.TextPaint;
import android.text.TextUtils;
import android.util.Log;
import androidx.annotation.DoNotInline;
import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import okhttp3.internal.p042io.f15;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.wv2;
import okhttp3.internal.p042io.zu2;

/* renamed from: androidx.compose.ui.text.android.Ԫ */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class C0195 implements InterfaceC0196 {

    /* renamed from: Ϳ */
    public static boolean f197;

    /* renamed from: Ԩ */
    @wv2
    public static Constructor<StaticLayout> f198;

    @Override // androidx.compose.p000ui.text.android.InterfaceC0196
    @DoNotInline
    @zu2
    /* renamed from: Ϳ */
    public StaticLayout mo60(@zu2 f15 f15Var) {
        fa1.m6826(f15Var, "params");
        StaticLayout staticLayout = null;
        if (!f197) {
            f197 = true;
            try {
                Class cls = Integer.TYPE;
                Class cls2 = Float.TYPE;
                f198 = StaticLayout.class.getConstructor(CharSequence.class, cls, cls, TextPaint.class, cls, Layout.Alignment.class, TextDirectionHeuristic.class, cls2, cls2, Boolean.TYPE, TextUtils.TruncateAt.class, cls, cls);
            } catch (NoSuchMethodException unused) {
                f198 = null;
                Log.e("StaticLayoutFactory", "unable to collect necessary constructor.");
            }
        }
        Constructor<StaticLayout> constructor = f198;
        if (constructor != null) {
            try {
                staticLayout = constructor.newInstance(f15Var.f9674, Integer.valueOf(f15Var.f9675), Integer.valueOf(f15Var.f9676), f15Var.f9677, Integer.valueOf(f15Var.f9678), f15Var.f9680, f15Var.f9679, Float.valueOf(f15Var.f9684), Float.valueOf(f15Var.f9685), Boolean.valueOf(f15Var.f9687), f15Var.f9682, Integer.valueOf(f15Var.f9683), Integer.valueOf(f15Var.f9681));
            } catch (IllegalAccessException | InstantiationException | InvocationTargetException unused2) {
                f198 = null;
                Log.e("StaticLayoutFactory", "unable to call constructor");
            }
        }
        return staticLayout != null ? staticLayout : new StaticLayout(f15Var.f9674, f15Var.f9675, f15Var.f9676, f15Var.f9677, f15Var.f9678, f15Var.f9680, f15Var.f9684, f15Var.f9685, f15Var.f9687, f15Var.f9682, f15Var.f9683);
    }

    @Override // androidx.compose.p000ui.text.android.InterfaceC0196
    /* renamed from: Ԩ */
    public final boolean mo61(@zu2 StaticLayout staticLayout, boolean z) {
        return false;
    }
}
