package okhttp3.internal.p042io;

import okhttp3.internal.p042io.C5265u2;

/* renamed from: okhttp3.internal.io.v2 */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5386v2 extends lv1 implements nh0<C5265u2.C9370> {

    /* renamed from: ၥ */
    public final /* synthetic */ InterfaceC7881 f23658;

    /* renamed from: ၦ */
    public final /* synthetic */ cm1 f23659;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public C5386v2(InterfaceC7881 interfaceC7881, cm1 cm1Var) {
        super(0);
        this.f23658 = interfaceC7881;
        this.f23659 = cm1Var;
    }

    @Override // okhttp3.internal.p042io.nh0
    public final C5265u2.C9370 invoke() {
        return new C5265u2.C9370(this.f23658, this.f23659);
    }
}
