package androidx.core.transition;

import android.transition.Transition;
import kotlin.Metadata;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.lv1;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.ph0;
import okhttp3.internal.p042io.zu2;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\u0010\u0005\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n¢\u0006\u0004\b\u0003\u0010\u0004"}, m4115d2 = {"Landroid/transition/Transition;", "it", "Lokhttp3/internal/io/lx5;", "invoke", "(Landroid/transition/Transition;)V", "<anonymous>"}, m4116k = 3, m4117mv = {1, 6, 0})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class TransitionKt$addListener$3 extends lv1 implements ph0<Transition, lx5> {
    public static final TransitionKt$addListener$3 INSTANCE = new TransitionKt$addListener$3();

    public TransitionKt$addListener$3() {
        super(1);
    }

    @Override // okhttp3.internal.p042io.ph0
    public /* bridge */ /* synthetic */ lx5 invoke(Transition transition) {
        invoke2(transition);
        return lx5.f14876;
    }

    /* renamed from: invoke, reason: avoid collision after fix types in other method */
    public final void invoke2(@zu2 Transition transition) {
        fa1.m6826(transition, "it");
    }
}
