package androidx.compose.p000ui.platform;

import android.annotation.SuppressLint;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.graphics.Outline;
import android.graphics.Rect;
import android.os.Build;
import android.view.View;
import android.view.ViewOutlineProvider;
import androidx.annotation.DoNotInline;
import androidx.annotation.RequiresApi;
import com.stardust.autojs.engine.RhinoJavaScriptEngine;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Objects;
import kotlin.Metadata;
import okhttp3.internal.p042io.C4350o9;
import okhttp3.internal.p042io.C6052;
import okhttp3.internal.p042io.C7459;
import okhttp3.internal.p042io.InterfaceC5282u7;
import okhttp3.internal.p042io.InterfaceC7598;
import okhttp3.internal.p042io.a93;
import okhttp3.internal.p042io.co4;
import okhttp3.internal.p042io.cw1;
import okhttp3.internal.p042io.di0;
import okhttp3.internal.p042io.dn5;
import okhttp3.internal.p042io.du1;
import okhttp3.internal.p042io.e04;
import okhttp3.internal.p042io.f86;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.ft4;
import okhttp3.internal.p042io.g03;
import okhttp3.internal.p042io.g71;
import okhttp3.internal.p042io.g86;
import okhttp3.internal.p042io.he2;
import okhttp3.internal.p042io.lv1;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.m71;
import okhttp3.internal.p042io.nh0;
import okhttp3.internal.p042io.ph0;
import okhttp3.internal.p042io.pp1;
import okhttp3.internal.p042io.qv3;
import okhttp3.internal.p042io.ro0;
import okhttp3.internal.p042io.un2;
import okhttp3.internal.p042io.vv1;
import okhttp3.internal.p042io.w33;
import okhttp3.internal.p042io.wv2;
import okhttp3.internal.p042io.y23;
import okhttp3.internal.p042io.zu2;
import sun.security.x509.IssuingDistributionPointExtension;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000ª\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0007\n\u0002\b\n\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u000b\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\f\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\t\n\u0002\b\u000f\b\u0000\u0018\u00002\u00020\u00012\u00020\u00022\u00020\u0003:\u0002noB9\u0012\u0006\u0010O\u001a\u00020J\u0012\u0006\u0010U\u001a\u00020P\u0012\u0012\u0010?\u001a\u000e\u0012\u0004\u0012\u00020/\u0012\u0004\u0012\u00020\u001e0>\u0012\f\u0010A\u001a\b\u0012\u0004\u0012\u00020\u001e0@¢\u0006\u0004\bl\u0010mJ§\u0001\u0010!\u001a\u00020\u001e2\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0006\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\u00042\u0006\u0010\b\u001a\u00020\u00042\u0006\u0010\t\u001a\u00020\u00042\u0006\u0010\n\u001a\u00020\u00042\u0006\u0010\u000b\u001a\u00020\u00042\u0006\u0010\f\u001a\u00020\u00042\u0006\u0010\r\u001a\u00020\u00042\u0006\u0010\u000e\u001a\u00020\u00042\u0006\u0010\u0010\u001a\u00020\u000f2\u0006\u0010\u0012\u001a\u00020\u00112\u0006\u0010\u0014\u001a\u00020\u00132\b\u0010\u0016\u001a\u0004\u0018\u00010\u00152\u0006\u0010\u0018\u001a\u00020\u00172\u0006\u0010\u0019\u001a\u00020\u00172\u0006\u0010\u001b\u001a\u00020\u001a2\u0006\u0010\u001d\u001a\u00020\u001cH\u0016ø\u0001\u0000ø\u0001\u0001¢\u0006\u0004\b\u001f\u0010 J\u001d\u0010&\u001a\u00020\u00132\u0006\u0010#\u001a\u00020\"H\u0016ø\u0001\u0000ø\u0001\u0001¢\u0006\u0004\b$\u0010%J\u001d\u0010+\u001a\u00020\u001e2\u0006\u0010(\u001a\u00020'H\u0016ø\u0001\u0000ø\u0001\u0001¢\u0006\u0004\b)\u0010*J\u001d\u0010.\u001a\u00020\u001e2\u0006\u0010#\u001a\u00020,H\u0016ø\u0001\u0000ø\u0001\u0001¢\u0006\u0004\b-\u0010*J\u0010\u00101\u001a\u00020\u001e2\u0006\u00100\u001a\u00020/H\u0016J\b\u00102\u001a\u00020\u001eH\u0016J\b\u00103\u001a\u00020\u001eH\u0016J\b\u00104\u001a\u00020\u001eH\u0016J\b\u00105\u001a\u00020\u001eH\u0016J%\u0010:\u001a\u00020\"2\u0006\u00106\u001a\u00020\"2\u0006\u00107\u001a\u00020\u0013H\u0016ø\u0001\u0000ø\u0001\u0001¢\u0006\u0004\b8\u00109J\u0018\u0010=\u001a\u00020\u001e2\u0006\u0010<\u001a\u00020;2\u0006\u00107\u001a\u00020\u0013H\u0016J*\u0010B\u001a\u00020\u001e2\u0012\u0010?\u001a\u000e\u0012\u0004\u0012\u00020/\u0012\u0004\u0012\u00020\u001e0>2\f\u0010A\u001a\b\u0012\u0004\u0012\u00020\u001e0@H\u0016J\u001d\u0010G\u001a\u00020\u001e2\u0006\u0010D\u001a\u00020CH\u0016ø\u0001\u0000ø\u0001\u0001¢\u0006\u0004\bE\u0010FJ\u001d\u0010I\u001a\u00020\u001e2\u0006\u0010D\u001a\u00020CH\u0016ø\u0001\u0000ø\u0001\u0001¢\u0006\u0004\bH\u0010FR\u0017\u0010O\u001a\u00020J8\u0006¢\u0006\f\n\u0004\bK\u0010L\u001a\u0004\bM\u0010NR\u0017\u0010U\u001a\u00020P8\u0006¢\u0006\f\n\u0004\bQ\u0010R\u001a\u0004\bS\u0010TR*\u0010Y\u001a\u00020\u00132\u0006\u0010V\u001a\u00020\u00138\u0006@BX\u0086\u000e¢\u0006\u0012\n\u0004\bW\u0010X\u001a\u0004\bY\u0010Z\"\u0004\b[\u0010\\R\u0016\u0010`\u001a\u0004\u0018\u00010]8BX\u0082\u0004¢\u0006\u0006\u001a\u0004\b^\u0010_R\u0014\u0010d\u001a\u00020a8VX\u0096\u0004¢\u0006\u0006\u001a\u0004\bb\u0010cR\u0014\u0010f\u001a\u00020a8VX\u0096\u0004¢\u0006\u0006\u001a\u0004\be\u0010cR$\u0010k\u001a\u00020\u00042\u0006\u0010V\u001a\u00020\u00048F@FX\u0086\u000e¢\u0006\f\u001a\u0004\bg\u0010h\"\u0004\bi\u0010j\u0082\u0002\u000b\n\u0005\b¡\u001e0\u0001\n\u0002\b\u0019¨\u0006p"}, m4115d2 = {"Landroidx/compose/ui/platform/ViewLayer;", "Landroid/view/View;", "Lokhttp3/internal/io/w33;", "Lokhttp3/internal/io/ro0;", "", "scaleX", "scaleY", "alpha", "translationX", "translationY", "shadowElevation", "rotationX", "rotationY", "rotationZ", "cameraDistance", "Lokhttp3/internal/io/dn5;", "transformOrigin", "Lokhttp3/internal/io/co4;", "shape", "", "clip", "Lokhttp3/internal/io/e04;", "renderEffect", "Lokhttp3/internal/io/ਅ;", "ambientShadowColor", "spotShadowColor", "Lokhttp3/internal/io/cw1;", "layoutDirection", "Lokhttp3/internal/io/u7;", "density", "Lokhttp3/internal/io/lx5;", "updateLayerProperties-NHXXZp8", "(FFFFFFFFFFJLokhttp3/internal/io/co4;ZLokhttp3/internal/io/e04;JJLokhttp3/internal/io/cw1;Lokhttp3/internal/io/u7;)V", "updateLayerProperties", "Lokhttp3/internal/io/g03;", "position", "isInLayer-k-4lQ0M", "(J)Z", "isInLayer", "Lokhttp3/internal/io/m71;", "size", "resize-ozmzZPI", "(J)V", "resize", "Lokhttp3/internal/io/g71;", "move--gyyYBs", "move", "Lokhttp3/internal/io/ค;", "canvas", "drawLayer", "invalidate", "destroy", "updateDisplayList", "forceLayout", IssuingDistributionPointExtension.POINT, "inverse", "mapOffset-8S9VItk", "(JZ)J", "mapOffset", "Lokhttp3/internal/io/un2;", "rect", "mapBounds", "Lkotlin/Function1;", "drawBlock", "Lkotlin/Function0;", "invalidateParentLayer", "reuseLayer", "Lokhttp3/internal/io/he2;", "matrix", "transform-58bKbWc", "([F)V", "transform", "inverseTransform-58bKbWc", "inverseTransform", "Landroidx/compose/ui/platform/AndroidComposeView;", "ၥ", "Landroidx/compose/ui/platform/AndroidComposeView;", "getOwnerView", "()Landroidx/compose/ui/platform/AndroidComposeView;", "ownerView", "Landroidx/compose/ui/platform/DrawChildContainer;", "ၦ", "Landroidx/compose/ui/platform/DrawChildContainer;", "getContainer", "()Landroidx/compose/ui/platform/DrawChildContainer;", "container", "value", "ၷ", "Z", "isInvalidated", "()Z", "setInvalidated", "(Z)V", "Lokhttp3/internal/io/a93;", "getManualClipPath", "()Lokhttp3/internal/io/a93;", "manualClipPath", "", "getLayerId", "()J", "layerId", "getOwnerViewId", "ownerViewId", "getCameraDistancePx", "()F", "setCameraDistancePx", "(F)V", "cameraDistancePx", RhinoJavaScriptEngine.SOURCE_NAME_INIT, "(Landroidx/compose/ui/platform/AndroidComposeView;Landroidx/compose/ui/platform/DrawChildContainer;Lokhttp3/internal/io/ph0;Lokhttp3/internal/io/nh0;)V", "Ԫ", "Ԭ", "ui_release"}, m4116k = 1, m4117mv = {1, 7, 1})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class ViewLayer extends View implements w33, ro0 {

    /* renamed from: ၼ */
    @zu2
    public static final C0181 f138 = new C0181();

    /* renamed from: ၽ */
    @zu2
    public static final di0<View, Matrix, lx5> f139 = C0180.f157;

    /* renamed from: ၾ */
    @zu2
    public static final C0179 f140 = new C0179();

    /* renamed from: ၿ */
    @wv2
    public static Method f141;

    /* renamed from: ႀ */
    @wv2
    public static Field f142;

    /* renamed from: ႁ */
    public static boolean f143;

    /* renamed from: ႎ */
    public static boolean f144;

    /* renamed from: ၥ, reason: from kotlin metadata */
    @zu2
    public final AndroidComposeView ownerView;

    /* renamed from: ၦ, reason: from kotlin metadata */
    @zu2
    public final DrawChildContainer container;

    /* renamed from: ၮ */
    @wv2
    public ph0<? super InterfaceC7598, lx5> f147;

    /* renamed from: ၯ */
    @wv2
    public nh0<lx5> f148;

    /* renamed from: ၰ */
    @zu2
    public final y23 f149;

    /* renamed from: ၵ */
    public boolean f150;

    /* renamed from: ၶ */
    @wv2
    public Rect f151;

    /* renamed from: ၷ, reason: from kotlin metadata */
    public boolean isInvalidated;

    /* renamed from: ၸ */
    public boolean f153;

    /* renamed from: ၹ */
    @zu2
    public final C7459 f154;

    /* renamed from: ၺ */
    @zu2
    public final vv1<View> f155;

    /* renamed from: ၻ */
    public long f156;

    /* renamed from: androidx.compose.ui.platform.ViewLayer$Ϳ */
    public static final class C0179 extends ViewOutlineProvider {
        @Override // android.view.ViewOutlineProvider
        public final void getOutline(@zu2 View view, @zu2 Outline outline) {
            fa1.m6826(view, "view");
            fa1.m6826(outline, "outline");
            Outline m14123 = ((ViewLayer) view).f149.m14123();
            fa1.m6823(m14123);
            outline.set(m14123);
        }
    }

    /* renamed from: androidx.compose.ui.platform.ViewLayer$Ԩ */
    public static final class C0180 extends lv1 implements di0<View, Matrix, lx5> {

        /* renamed from: ၥ */
        public static final C0180 f157 = new C0180();

        public C0180() {
            super(2);
        }

        @Override // okhttp3.internal.p042io.di0
        /* renamed from: invoke */
        public final lx5 mo18338invoke(View view, Matrix matrix) {
            View view2 = view;
            Matrix matrix2 = matrix;
            fa1.m6826(view2, "view");
            fa1.m6826(matrix2, "matrix");
            matrix2.set(view2.getMatrix());
            return lx5.f14876;
        }
    }

    /* renamed from: androidx.compose.ui.platform.ViewLayer$Ԫ */
    public static final class C0181 {
        @SuppressLint({"BanUncheckedReflection"})
        /* renamed from: Ϳ */
        public final void m51(@zu2 View view) {
            Field field;
            fa1.m6826(view, "view");
            try {
                if (!ViewLayer.f143) {
                    ViewLayer.f143 = true;
                    if (Build.VERSION.SDK_INT < 28) {
                        ViewLayer.f141 = View.class.getDeclaredMethod("updateDisplayListIfDirty", new Class[0]);
                        field = View.class.getDeclaredField("mRecreateDisplayList");
                    } else {
                        ViewLayer.f141 = (Method) Class.class.getDeclaredMethod("getDeclaredMethod", String.class, new Class[0].getClass()).invoke(View.class, "updateDisplayListIfDirty", new Class[0]);
                        field = (Field) Class.class.getDeclaredMethod("getDeclaredField", String.class).invoke(View.class, "mRecreateDisplayList");
                    }
                    ViewLayer.f142 = field;
                    Method method = ViewLayer.f141;
                    if (method != null) {
                        method.setAccessible(true);
                    }
                    Field field2 = ViewLayer.f142;
                    if (field2 != null) {
                        field2.setAccessible(true);
                    }
                }
                Field field3 = ViewLayer.f142;
                if (field3 != null) {
                    field3.setBoolean(view, true);
                }
                Method method2 = ViewLayer.f141;
                if (method2 != null) {
                    method2.invoke(view, new Object[0]);
                }
            } catch (Throwable unused) {
                ViewLayer.f144 = true;
            }
        }
    }

    @RequiresApi(29)
    /* renamed from: androidx.compose.ui.platform.ViewLayer$Ԭ */
    public static final class C0182 {
        @DoNotInline
        @pp1
        /* renamed from: Ϳ */
        public static final long m52(@zu2 View view) {
            fa1.m6826(view, "view");
            return view.getUniqueDrawingId();
        }
    }

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public ViewLayer(@zu2 AndroidComposeView androidComposeView, @zu2 DrawChildContainer drawChildContainer, @zu2 ph0<? super InterfaceC7598, lx5> ph0Var, @zu2 nh0<lx5> nh0Var) {
        super(androidComposeView.getContext());
        fa1.m6826(androidComposeView, "ownerView");
        fa1.m6826(drawChildContainer, "container");
        fa1.m6826(ph0Var, "drawBlock");
        fa1.m6826(nh0Var, "invalidateParentLayer");
        this.ownerView = androidComposeView;
        this.container = drawChildContainer;
        this.f147 = ph0Var;
        this.f148 = nh0Var;
        this.f149 = new y23(androidComposeView.getDensity());
        this.f154 = new C7459();
        this.f155 = new vv1<>(f139);
        dn5.C3149 c3149 = dn5.f8700;
        this.f156 = dn5.f8701;
        setWillNotDraw(false);
        setId(View.generateViewId());
        drawChildContainer.addView(this);
    }

    private final a93 getManualClipPath() {
        if (getClipToOutline()) {
            y23 y23Var = this.f149;
            if (!(!y23Var.f26358)) {
                y23Var.m14126();
                return y23Var.f26356;
            }
        }
        return null;
    }

    private final void setInvalidated(boolean z) {
        if (z != this.isInvalidated) {
            this.isInvalidated = z;
            this.ownerView.notifyLayerIsDirty$ui_release(this, z);
        }
    }

    @Override // okhttp3.internal.p042io.w33
    public void destroy() {
        setInvalidated(false);
        this.ownerView.requestClearInvalidObservations();
        this.f147 = null;
        this.f148 = null;
        boolean recycle$ui_release = this.ownerView.recycle$ui_release(this);
        if (Build.VERSION.SDK_INT >= 23 || f144 || !recycle$ui_release) {
            this.container.removeViewInLayout(this);
        } else {
            setVisibility(8);
        }
    }

    @Override // android.view.View
    public final void dispatchDraw(@zu2 Canvas canvas) {
        fa1.m6826(canvas, "canvas");
        boolean z = false;
        setInvalidated(false);
        C7459 c7459 = this.f154;
        C6052 c6052 = c7459.f31914;
        Canvas canvas2 = c6052.f27963;
        Objects.requireNonNull(c6052);
        c6052.f27963 = canvas;
        C6052 c60522 = c7459.f31914;
        if (getManualClipPath() != null || !canvas.isHardwareAccelerated()) {
            z = true;
            c60522.mo11792();
            this.f149.m14122(c60522);
        }
        ph0<? super InterfaceC7598, lx5> ph0Var = this.f147;
        if (ph0Var != null) {
            ph0Var.invoke(c60522);
        }
        if (z) {
            c60522.mo11801();
        }
        c7459.f31914.m14765(canvas2);
    }

    @Override // okhttp3.internal.p042io.w33
    public void drawLayer(@zu2 InterfaceC7598 interfaceC7598) {
        fa1.m6826(interfaceC7598, "canvas");
        boolean z = getElevation() > 0.0f;
        this.f153 = z;
        if (z) {
            interfaceC7598.mo11804();
        }
        this.container.drawChild$ui_release(interfaceC7598, this, getDrawingTime());
        if (this.f153) {
            interfaceC7598.mo11793();
        }
    }

    @Override // android.view.View
    public void forceLayout() {
    }

    public final float getCameraDistancePx() {
        return getCameraDistance() / getResources().getDisplayMetrics().densityDpi;
    }

    @zu2
    public final DrawChildContainer getContainer() {
        return this.container;
    }

    public long getLayerId() {
        return getId();
    }

    @zu2
    public final AndroidComposeView getOwnerView() {
        return this.ownerView;
    }

    public long getOwnerViewId() {
        if (Build.VERSION.SDK_INT >= 29) {
            return C0182.m52(this.ownerView);
        }
        return -1L;
    }

    @Override // android.view.View, okhttp3.internal.p042io.w33
    public void invalidate() {
        if (this.isInvalidated) {
            return;
        }
        setInvalidated(true);
        super.invalidate();
        this.ownerView.invalidate();
    }

    /* renamed from: inverseTransform-58bKbWc, reason: not valid java name */
    public void m18350inverseTransform58bKbWc(@zu2 float[] matrix) {
        fa1.m6826(matrix, "matrix");
        float[] m13360 = this.f155.m13360(this);
        if (m13360 != null) {
            he2.m7787(matrix, m13360);
        }
    }

    @Override // okhttp3.internal.p042io.w33
    /* renamed from: isInLayer-k-4lQ0M, reason: not valid java name */
    public boolean mo18351isInLayerk4lQ0M(long position) {
        float m7139 = g03.m7139(position);
        float m7140 = g03.m7140(position);
        if (this.f150) {
            return 0.0f <= m7139 && m7139 < ((float) getWidth()) && 0.0f <= m7140 && m7140 < ((float) getHeight());
        }
        if (getClipToOutline()) {
            return this.f149.m14124(position);
        }
        return true;
    }

    /* renamed from: isInvalidated, reason: from getter */
    public final boolean getIsInvalidated() {
        return this.isInvalidated;
    }

    @Override // okhttp3.internal.p042io.w33
    public void mapBounds(@zu2 un2 un2Var, boolean z) {
        fa1.m6826(un2Var, "rect");
        if (!z) {
            he2.m7784(this.f155.m13361(this), un2Var);
            return;
        }
        float[] m13360 = this.f155.m13360(this);
        if (m13360 != null) {
            he2.m7784(m13360, un2Var);
            return;
        }
        un2Var.f23361 = 0.0f;
        un2Var.f23362 = 0.0f;
        un2Var.f23363 = 0.0f;
        un2Var.f23364 = 0.0f;
    }

    @Override // okhttp3.internal.p042io.w33
    /* renamed from: mapOffset-8S9VItk, reason: not valid java name */
    public long mo18352mapOffset8S9VItk(long point, boolean inverse) {
        if (!inverse) {
            return he2.m7783(this.f155.m13361(this), point);
        }
        float[] m13360 = this.f155.m13360(this);
        if (m13360 != null) {
            return he2.m7783(m13360, point);
        }
        g03.C3393 c3393 = g03.f10466;
        return g03.f10468;
    }

    @Override // okhttp3.internal.p042io.w33
    /* renamed from: move--gyyYBs, reason: not valid java name */
    public void mo18353movegyyYBs(long position) {
        g71.C3412 c3412 = g71.f10637;
        int i = (int) (position >> 32);
        if (i != getLeft()) {
            offsetLeftAndRight(i - getLeft());
            this.f155.m13362();
        }
        int m7254 = g71.m7254(position);
        if (m7254 != getTop()) {
            offsetTopAndBottom(m7254 - getTop());
            this.f155.m13362();
        }
    }

    @Override // android.view.View
    public final void onLayout(boolean z, int i, int i2, int i3, int i4) {
    }

    @Override // okhttp3.internal.p042io.w33
    /* renamed from: resize-ozmzZPI, reason: not valid java name */
    public void mo18354resizeozmzZPI(long size) {
        int i = (int) (size >> 32);
        int m9614 = m71.m9614(size);
        if (i == getWidth() && m9614 == getHeight()) {
            return;
        }
        float f = i;
        setPivotX(dn5.m6129(this.f156) * f);
        float f2 = m9614;
        setPivotY(dn5.m6130(this.f156) * f2);
        y23 y23Var = this.f149;
        long m10266 = C4350o9.m10266(f, f2);
        if (!ft4.m7081(y23Var.f26353, m10266)) {
            y23Var.f26353 = m10266;
            y23Var.f26357 = true;
        }
        setOutlineProvider(this.f149.m14123() != null ? f140 : null);
        layout(getLeft(), getTop(), getLeft() + i, getTop() + m9614);
        m50();
        this.f155.m13362();
    }

    @Override // okhttp3.internal.p042io.w33
    public void reuseLayer(@zu2 ph0<? super InterfaceC7598, lx5> ph0Var, @zu2 nh0<lx5> nh0Var) {
        fa1.m6826(ph0Var, "drawBlock");
        fa1.m6826(nh0Var, "invalidateParentLayer");
        if (Build.VERSION.SDK_INT >= 23 || f144) {
            this.container.addView(this);
        } else {
            setVisibility(0);
        }
        this.f150 = false;
        this.f153 = false;
        dn5.C3149 c3149 = dn5.f8700;
        this.f156 = dn5.f8701;
        this.f147 = ph0Var;
        this.f148 = nh0Var;
    }

    public final void setCameraDistancePx(float f) {
        setCameraDistance(f * getResources().getDisplayMetrics().densityDpi);
    }

    /* renamed from: transform-58bKbWc, reason: not valid java name */
    public void m18355transform58bKbWc(@zu2 float[] matrix) {
        fa1.m6826(matrix, "matrix");
        he2.m7787(matrix, this.f155.m13361(this));
    }

    @Override // okhttp3.internal.p042io.w33
    public void updateDisplayList() {
        if (!this.isInvalidated || f144) {
            return;
        }
        setInvalidated(false);
        f138.m51(this);
    }

    @Override // okhttp3.internal.p042io.w33
    /* renamed from: updateLayerProperties-NHXXZp8, reason: not valid java name */
    public void mo18356updateLayerPropertiesNHXXZp8(float scaleX, float scaleY, float alpha, float translationX, float translationY, float shadowElevation, float rotationX, float rotationY, float rotationZ, float cameraDistance, long transformOrigin, @zu2 co4 shape, boolean clip, @wv2 e04 renderEffect, long ambientShadowColor, long spotShadowColor, @zu2 cw1 layoutDirection, @zu2 InterfaceC5282u7 density) {
        nh0<lx5> nh0Var;
        fa1.m6826(shape, "shape");
        fa1.m6826(layoutDirection, "layoutDirection");
        fa1.m6826(density, "density");
        this.f156 = transformOrigin;
        setScaleX(scaleX);
        setScaleY(scaleY);
        setAlpha(alpha);
        setTranslationX(translationX);
        setTranslationY(translationY);
        setElevation(shadowElevation);
        setRotation(rotationZ);
        setRotationX(rotationX);
        setRotationY(rotationY);
        setPivotX(dn5.m6129(this.f156) * getWidth());
        setPivotY(dn5.m6130(this.f156) * getHeight());
        setCameraDistancePx(cameraDistance);
        this.f150 = clip && shape == qv3.f19552;
        m50();
        boolean z = getManualClipPath() != null;
        setClipToOutline(clip && shape != qv3.f19552);
        boolean m14125 = this.f149.m14125(shape, getAlpha(), getClipToOutline(), getElevation(), layoutDirection, density);
        setOutlineProvider(this.f149.m14123() != null ? f140 : null);
        boolean z2 = getManualClipPath() != null;
        if (z != z2 || (z2 && m14125)) {
            invalidate();
        }
        if (!this.f153 && getElevation() > 0.0f && (nh0Var = this.f148) != null) {
            nh0Var.invoke();
        }
        this.f155.m13362();
        int i = Build.VERSION.SDK_INT;
        if (i >= 28) {
            f86 f86Var = f86.f9861;
            f86Var.m6789(this, du1.m6210(ambientShadowColor));
            f86Var.m6790(this, du1.m6210(spotShadowColor));
        }
        if (i >= 31) {
            g86.f10702.m7261(this, null);
        }
    }

    /* renamed from: Ϳ */
    public final void m50() {
        Rect rect;
        if (this.f150) {
            Rect rect2 = this.f151;
            if (rect2 == null) {
                this.f151 = new Rect(0, 0, getWidth(), getHeight());
            } else {
                fa1.m6823(rect2);
                rect2.set(0, 0, getWidth(), getHeight());
            }
            rect = this.f151;
        } else {
            rect = null;
        }
        setClipBounds(rect);
    }
}
