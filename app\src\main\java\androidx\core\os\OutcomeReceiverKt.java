package androidx.core.os;

import android.os.OutcomeReceiver;
import androidx.annotation.RequiresApi;
import androidx.exifinterface.media.ExifInterface;
import kotlin.Metadata;
import okhttp3.internal.p042io.InterfaceC7155;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.zu2;

@RequiresApi(31)
@Metadata(m4113bv = {}, m4114d1 = {"\u0000\u0014\n\u0000\n\u0002\u0010\u0003\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a.\u0010\u0005\u001a\u000e\u0012\u0004\u0012\u00028\u0000\u0012\u0004\u0012\u00028\u00010\u0004\"\u0004\b\u0000\u0010\u0000\"\b\b\u0001\u0010\u0002*\u00020\u0001*\b\u0012\u0004\u0012\u00028\u00000\u0003H\u0007¨\u0006\u0006"}, m4115d2 = {"R", "", ExifInterface.LONGITUDE_EAST, "Lokhttp3/internal/io/ৡ;", "Landroid/os/OutcomeReceiver;", "asOutcomeReceiver", "core-ktx_release"}, m4116k = 2, m4117mv = {1, 6, 0})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class OutcomeReceiverKt {
    @RequiresApi(31)
    @zu2
    public static final <R, E extends Throwable> OutcomeReceiver<R, E> asOutcomeReceiver(@zu2 InterfaceC7155<? super R> interfaceC7155) {
        fa1.m6826(interfaceC7155, "<this>");
        return new ContinuationOutcomeReceiver(interfaceC7155);
    }
}
