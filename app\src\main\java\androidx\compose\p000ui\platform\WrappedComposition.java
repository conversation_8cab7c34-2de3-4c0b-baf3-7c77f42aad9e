package androidx.compose.p000ui.platform;

import androidx.compose.p000ui.platform.AndroidComposeView;
import androidx.core.app.NotificationCompat;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleEventObserver;
import androidx.lifecycle.LifecycleOwner;
import com.stardust.autojs.engine.ScriptEngine;
import kotlin.Metadata;
import okhttp3.internal.p042io.C7430;
import okhttp3.internal.p042io.InterfaceC6721;
import okhttp3.internal.p042io.InterfaceC6968;
import okhttp3.internal.p042io.InterfaceC7474;
import okhttp3.internal.p042io.di0;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.lv1;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.ns2;
import okhttp3.internal.p042io.op3;
import okhttp3.internal.p042io.ph0;
import okhttp3.internal.p042io.wv2;
import okhttp3.internal.p042io.zu2;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000\u000e\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b\u0002\u0018\u00002\u00020\u00012\u00020\u0002¨\u0006\u0003"}, m4115d2 = {"Landroidx/compose/ui/platform/WrappedComposition;", "Lokhttp3/internal/io/ഉ;", "Landroidx/lifecycle/LifecycleEventObserver;", "ui_release"}, m4116k = 1, m4117mv = {1, 7, 1})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
final class WrappedComposition implements InterfaceC7474, LifecycleEventObserver {

    /* renamed from: ၥ */
    @zu2
    public final AndroidComposeView f175;

    /* renamed from: ၦ */
    @zu2
    public final InterfaceC7474 f176;

    /* renamed from: ၮ */
    public boolean f177;

    /* renamed from: ၯ */
    @wv2
    public Lifecycle f178;

    /* renamed from: ၰ */
    @zu2
    public di0<? super InterfaceC6968, ? super Integer, lx5> f179;

    /* renamed from: androidx.compose.ui.platform.WrappedComposition$Ϳ */
    public static final class C0184 extends lv1 implements ph0<AndroidComposeView.C0166, lx5> {

        /* renamed from: ၦ */
        public final /* synthetic */ di0<InterfaceC6968, Integer, lx5> f181;

        /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
        /* JADX WARN: Multi-variable type inference failed */
        public C0184(di0<? super InterfaceC6968, ? super Integer, lx5> di0Var) {
            super(1);
            this.f181 = di0Var;
        }

        @Override // okhttp3.internal.p042io.ph0
        public final lx5 invoke(AndroidComposeView.C0166 c0166) {
            AndroidComposeView.C0166 c01662 = c0166;
            fa1.m6826(c01662, "it");
            if (!WrappedComposition.this.f177) {
                Lifecycle lifecycle = c01662.f115.getLifecycle();
                fa1.m6825(lifecycle, "it.lifecycleOwner.lifecycle");
                WrappedComposition wrappedComposition = WrappedComposition.this;
                wrappedComposition.f179 = this.f181;
                if (wrappedComposition.f178 == null) {
                    wrappedComposition.f178 = lifecycle;
                    lifecycle.addObserver(wrappedComposition);
                } else if (lifecycle.getCurrentState().isAtLeast(Lifecycle.State.CREATED)) {
                    WrappedComposition wrappedComposition2 = WrappedComposition.this;
                    wrappedComposition2.f176.mo55(ns2.m10132(-2000640158, true, new C0191(wrappedComposition2, this.f181)));
                }
            }
            return lx5.f14876;
        }
    }

    public WrappedComposition(@zu2 AndroidComposeView androidComposeView, @zu2 InterfaceC7474 interfaceC7474) {
        this.f175 = androidComposeView;
        this.f176 = interfaceC7474;
        C7430 c7430 = C7430.f31859;
        this.f179 = C7430.f31860;
    }

    @Override // okhttp3.internal.p042io.InterfaceC7474
    public final void dispose() {
        if (!this.f177) {
            this.f177 = true;
            this.f175.getView().setTag(op3.wrapped_composition_tag, null);
            Lifecycle lifecycle = this.f178;
            if (lifecycle != null) {
                lifecycle.removeObserver(this);
            }
        }
        this.f176.dispose();
    }

    @Override // androidx.lifecycle.LifecycleEventObserver
    public final void onStateChanged(@zu2 LifecycleOwner lifecycleOwner, @zu2 Lifecycle.Event event) {
        fa1.m6826(lifecycleOwner, ScriptEngine.TAG_SOURCE);
        fa1.m6826(event, NotificationCompat.CATEGORY_EVENT);
        if (event == Lifecycle.Event.ON_DESTROY) {
            dispose();
        } else {
            if (event != Lifecycle.Event.ON_CREATE || this.f177) {
                return;
            }
            mo55(this.f179);
        }
    }

    @Override // okhttp3.internal.p042io.InterfaceC7474
    /* renamed from: ԫ */
    public final boolean mo53() {
        return this.f176.mo53();
    }

    @Override // okhttp3.internal.p042io.InterfaceC7474
    /* renamed from: ށ */
    public final boolean mo54() {
        return this.f176.mo54();
    }

    @Override // okhttp3.internal.p042io.InterfaceC7474
    @InterfaceC6721
    /* renamed from: ނ */
    public final void mo55(@zu2 di0<? super InterfaceC6968, ? super Integer, lx5> di0Var) {
        fa1.m6826(di0Var, "content");
        this.f175.setOnViewTreeOwnersAvailable(new C0184(di0Var));
    }
}
