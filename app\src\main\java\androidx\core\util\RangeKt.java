package androidx.core.util;

import android.annotation.SuppressLint;
import android.util.Range;
import androidx.annotation.RequiresApi;
import androidx.exifinterface.media.ExifInterface;
import kotlin.Metadata;
import okhttp3.internal.p042io.InterfaceC6234;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.zu2;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000\u0018\n\u0002\u0010\u000f\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0003\u001a2\u0010\u0004\u001a\b\u0012\u0004\u0012\u00028\u00000\u0003\"\u000e\b\u0000\u0010\u0001*\b\u0012\u0004\u0012\u00028\u00000\u0000*\u00028\u00002\u0006\u0010\u0002\u001a\u00028\u0000H\u0087\f¢\u0006\u0004\b\u0004\u0010\u0005\u001a8\u0010\u0007\u001a\b\u0012\u0004\u0012\u00028\u00000\u0003\"\u000e\b\u0000\u0010\u0001*\b\u0012\u0004\u0012\u00028\u00000\u0000*\b\u0012\u0004\u0012\u00028\u00000\u00032\u0006\u0010\u0006\u001a\u00028\u0000H\u0087\n¢\u0006\u0004\b\u0007\u0010\b\u001a7\u0010\u0007\u001a\b\u0012\u0004\u0012\u00028\u00000\u0003\"\u000e\b\u0000\u0010\u0001*\b\u0012\u0004\u0012\u00028\u00000\u0000*\b\u0012\u0004\u0012\u00028\u00000\u00032\f\u0010\t\u001a\b\u0012\u0004\u0012\u00028\u00000\u0003H\u0087\n\u001a7\u0010\n\u001a\b\u0012\u0004\u0012\u00028\u00000\u0003\"\u000e\b\u0000\u0010\u0001*\b\u0012\u0004\u0012\u00028\u00000\u0000*\b\u0012\u0004\u0012\u00028\u00000\u00032\f\u0010\t\u001a\b\u0012\u0004\u0012\u00028\u00000\u0003H\u0087\f\u001a(\u0010\f\u001a\b\u0012\u0004\u0012\u00028\u00000\u000b\"\u000e\b\u0000\u0010\u0001*\b\u0012\u0004\u0012\u00028\u00000\u0000*\b\u0012\u0004\u0012\u00028\u00000\u0003H\u0007\u001a(\u0010\r\u001a\b\u0012\u0004\u0012\u00028\u00000\u0003\"\u000e\b\u0000\u0010\u0001*\b\u0012\u0004\u0012\u00028\u00000\u0000*\b\u0012\u0004\u0012\u00028\u00000\u000bH\u0007¨\u0006\u000e"}, m4115d2 = {"", ExifInterface.GPS_DIRECTION_TRUE, "that", "Landroid/util/Range;", "rangeTo", "(Ljava/lang/Comparable;Ljava/lang/Comparable;)Landroid/util/Range;", "value", "plus", "(Landroid/util/Range;Ljava/lang/Comparable;)Landroid/util/Range;", "other", "and", "Lokhttp3/internal/io/ɳ;", "toClosedRange", "toRange", "core-ktx_release"}, m4116k = 2, m4117mv = {1, 6, 0})
@SuppressLint({"ClassVerificationFailure"})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class RangeKt {
    @RequiresApi(21)
    @zu2
    public static final <T extends Comparable<? super T>> Range<T> and(@zu2 Range<T> range, @zu2 Range<T> range2) {
        fa1.m6826(range, "<this>");
        fa1.m6826(range2, "other");
        Range<T> intersect = range.intersect(range2);
        fa1.m6825(intersect, "intersect(other)");
        return intersect;
    }

    @RequiresApi(21)
    @zu2
    public static final <T extends Comparable<? super T>> Range<T> plus(@zu2 Range<T> range, @zu2 Range<T> range2) {
        fa1.m6826(range, "<this>");
        fa1.m6826(range2, "other");
        Range<T> extend = range.extend(range2);
        fa1.m6825(extend, "extend(other)");
        return extend;
    }

    @RequiresApi(21)
    @zu2
    public static final <T extends Comparable<? super T>> Range<T> plus(@zu2 Range<T> range, @zu2 T t) {
        fa1.m6826(range, "<this>");
        fa1.m6826(t, "value");
        Range<T> extend = range.extend((Range<T>) t);
        fa1.m6825(extend, "extend(value)");
        return extend;
    }

    @RequiresApi(21)
    @zu2
    public static final <T extends Comparable<? super T>> Range<T> rangeTo(@zu2 T t, @zu2 T t2) {
        fa1.m6826(t, "<this>");
        fa1.m6826(t2, "that");
        return new Range<>(t, t2);
    }

    @RequiresApi(21)
    @zu2
    public static final <T extends Comparable<? super T>> InterfaceC6234<T> toClosedRange(@zu2 final Range<T> range) {
        fa1.m6826(range, "<this>");
        return (InterfaceC6234<T>) new InterfaceC6234<T>() { // from class: androidx.core.util.RangeKt$toClosedRange$1
            /* JADX WARN: Incorrect types in method signature: (TT;)Z */
            public boolean contains(@zu2 Comparable comparable) {
                fa1.m6826(comparable, "value");
                return comparable.compareTo(getStart()) >= 0 && comparable.compareTo(getEndInclusive()) <= 0;
            }

            /* JADX WARN: Incorrect return type in method signature: ()TT; */
            @Override // okhttp3.internal.p042io.InterfaceC6234
            public Comparable getEndInclusive() {
                return range.getUpper();
            }

            /* JADX WARN: Incorrect return type in method signature: ()TT; */
            @Override // okhttp3.internal.p042io.InterfaceC6234
            public Comparable getStart() {
                return range.getLower();
            }

            public boolean isEmpty() {
                return getStart().compareTo(getEndInclusive()) > 0;
            }
        };
    }

    @RequiresApi(21)
    @zu2
    public static final <T extends Comparable<? super T>> Range<T> toRange(@zu2 InterfaceC6234<T> interfaceC6234) {
        fa1.m6826(interfaceC6234, "<this>");
        return new Range<>(interfaceC6234.getStart(), interfaceC6234.getEndInclusive());
    }
}
