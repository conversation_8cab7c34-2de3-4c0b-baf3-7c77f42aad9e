package androidx.core.graphics;

import android.graphics.Canvas;
import android.graphics.Matrix;
import android.graphics.Path;
import android.graphics.Rect;
import android.graphics.RectF;
import kotlin.Metadata;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.ph0;
import okhttp3.internal.p042io.zu2;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000@\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a)\u0010\u0005\u001a\u00020\u0002*\u00020\u00002\u0017\u0010\u0004\u001a\u0013\u0012\u0004\u0012\u00020\u0000\u0012\u0004\u0012\u00020\u00020\u0001¢\u0006\u0002\b\u0003H\u0086\bø\u0001\u0000\u001a=\u0010\t\u001a\u00020\u0002*\u00020\u00002\b\b\u0002\u0010\u0007\u001a\u00020\u00062\b\b\u0002\u0010\b\u001a\u00020\u00062\u0017\u0010\u0004\u001a\u0013\u0012\u0004\u0012\u00020\u0000\u0012\u0004\u0012\u00020\u00020\u0001¢\u0006\u0002\b\u0003H\u0086\bø\u0001\u0000\u001aG\u0010\r\u001a\u00020\u0002*\u00020\u00002\b\b\u0002\u0010\n\u001a\u00020\u00062\b\b\u0002\u0010\u000b\u001a\u00020\u00062\b\b\u0002\u0010\f\u001a\u00020\u00062\u0017\u0010\u0004\u001a\u0013\u0012\u0004\u0012\u00020\u0000\u0012\u0004\u0012\u00020\u00020\u0001¢\u0006\u0002\b\u0003H\u0086\bø\u0001\u0000\u001aQ\u0010\u000e\u001a\u00020\u0002*\u00020\u00002\b\b\u0002\u0010\u0007\u001a\u00020\u00062\b\b\u0002\u0010\b\u001a\u00020\u00062\b\b\u0002\u0010\u000b\u001a\u00020\u00062\b\b\u0002\u0010\f\u001a\u00020\u00062\u0017\u0010\u0004\u001a\u0013\u0012\u0004\u0012\u00020\u0000\u0012\u0004\u0012\u00020\u00020\u0001¢\u0006\u0002\b\u0003H\u0086\bø\u0001\u0000\u001a=\u0010\u000f\u001a\u00020\u0002*\u00020\u00002\b\b\u0002\u0010\u0007\u001a\u00020\u00062\b\b\u0002\u0010\b\u001a\u00020\u00062\u0017\u0010\u0004\u001a\u0013\u0012\u0004\u0012\u00020\u0000\u0012\u0004\u0012\u00020\u00020\u0001¢\u0006\u0002\b\u0003H\u0086\bø\u0001\u0000\u001a3\u0010\u0012\u001a\u00020\u0002*\u00020\u00002\b\b\u0002\u0010\u0011\u001a\u00020\u00102\u0017\u0010\u0004\u001a\u0013\u0012\u0004\u0012\u00020\u0000\u0012\u0004\u0012\u00020\u00020\u0001¢\u0006\u0002\b\u0003H\u0086\bø\u0001\u0000\u001a1\u0010\u0015\u001a\u00020\u0002*\u00020\u00002\u0006\u0010\u0014\u001a\u00020\u00132\u0017\u0010\u0004\u001a\u0013\u0012\u0004\u0012\u00020\u0000\u0012\u0004\u0012\u00020\u00020\u0001¢\u0006\u0002\b\u0003H\u0086\bø\u0001\u0000\u001a1\u0010\u0015\u001a\u00020\u0002*\u00020\u00002\u0006\u0010\u0014\u001a\u00020\u00162\u0017\u0010\u0004\u001a\u0013\u0012\u0004\u0012\u00020\u0000\u0012\u0004\u0012\u00020\u00020\u0001¢\u0006\u0002\b\u0003H\u0086\bø\u0001\u0000\u001aI\u0010\u0015\u001a\u00020\u0002*\u00020\u00002\u0006\u0010\u0018\u001a\u00020\u00172\u0006\u0010\u0019\u001a\u00020\u00172\u0006\u0010\u001a\u001a\u00020\u00172\u0006\u0010\u001b\u001a\u00020\u00172\u0017\u0010\u0004\u001a\u0013\u0012\u0004\u0012\u00020\u0000\u0012\u0004\u0012\u00020\u00020\u0001¢\u0006\u0002\b\u0003H\u0086\bø\u0001\u0000\u001aI\u0010\u0015\u001a\u00020\u0002*\u00020\u00002\u0006\u0010\u0018\u001a\u00020\u00062\u0006\u0010\u0019\u001a\u00020\u00062\u0006\u0010\u001a\u001a\u00020\u00062\u0006\u0010\u001b\u001a\u00020\u00062\u0017\u0010\u0004\u001a\u0013\u0012\u0004\u0012\u00020\u0000\u0012\u0004\u0012\u00020\u00020\u0001¢\u0006\u0002\b\u0003H\u0086\bø\u0001\u0000\u001a1\u0010\u0015\u001a\u00020\u0002*\u00020\u00002\u0006\u0010\u001d\u001a\u00020\u001c2\u0017\u0010\u0004\u001a\u0013\u0012\u0004\u0012\u00020\u0000\u0012\u0004\u0012\u00020\u00020\u0001¢\u0006\u0002\b\u0003H\u0086\bø\u0001\u0000\u0082\u0002\u0007\n\u0005\b\u009920\u0001¨\u0006\u001e"}, m4115d2 = {"Landroid/graphics/Canvas;", "Lkotlin/Function1;", "Lokhttp3/internal/io/lx5;", "Lokhttp3/internal/io/f00;", "block", "withSave", "", "x", "y", "withTranslation", "degrees", "pivotX", "pivotY", "withRotation", "withScale", "withSkew", "Landroid/graphics/Matrix;", "matrix", "withMatrix", "Landroid/graphics/Rect;", "clipRect", "withClip", "Landroid/graphics/RectF;", "", "left", "top", "right", "bottom", "Landroid/graphics/Path;", "clipPath", "core-ktx_release"}, m4116k = 2, m4117mv = {1, 6, 0})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class CanvasKt {
    public static final void withClip(@zu2 Canvas canvas, float f, float f2, float f3, float f4, @zu2 ph0<? super Canvas, lx5> ph0Var) {
        fa1.m6826(canvas, "<this>");
        fa1.m6826(ph0Var, "block");
        int save = canvas.save();
        canvas.clipRect(f, f2, f3, f4);
        try {
            ph0Var.invoke(canvas);
        } finally {
            canvas.restoreToCount(save);
        }
    }

    public static final void withClip(@zu2 Canvas canvas, int i, int i2, int i3, int i4, @zu2 ph0<? super Canvas, lx5> ph0Var) {
        fa1.m6826(canvas, "<this>");
        fa1.m6826(ph0Var, "block");
        int save = canvas.save();
        canvas.clipRect(i, i2, i3, i4);
        try {
            ph0Var.invoke(canvas);
        } finally {
            canvas.restoreToCount(save);
        }
    }

    public static final void withClip(@zu2 Canvas canvas, @zu2 Path path, @zu2 ph0<? super Canvas, lx5> ph0Var) {
        fa1.m6826(canvas, "<this>");
        fa1.m6826(path, "clipPath");
        fa1.m6826(ph0Var, "block");
        int save = canvas.save();
        canvas.clipPath(path);
        try {
            ph0Var.invoke(canvas);
        } finally {
            canvas.restoreToCount(save);
        }
    }

    public static final void withClip(@zu2 Canvas canvas, @zu2 Rect rect, @zu2 ph0<? super Canvas, lx5> ph0Var) {
        fa1.m6826(canvas, "<this>");
        fa1.m6826(rect, "clipRect");
        fa1.m6826(ph0Var, "block");
        int save = canvas.save();
        canvas.clipRect(rect);
        try {
            ph0Var.invoke(canvas);
        } finally {
            canvas.restoreToCount(save);
        }
    }

    public static final void withClip(@zu2 Canvas canvas, @zu2 RectF rectF, @zu2 ph0<? super Canvas, lx5> ph0Var) {
        fa1.m6826(canvas, "<this>");
        fa1.m6826(rectF, "clipRect");
        fa1.m6826(ph0Var, "block");
        int save = canvas.save();
        canvas.clipRect(rectF);
        try {
            ph0Var.invoke(canvas);
        } finally {
            canvas.restoreToCount(save);
        }
    }

    public static final void withMatrix(@zu2 Canvas canvas, @zu2 Matrix matrix, @zu2 ph0<? super Canvas, lx5> ph0Var) {
        fa1.m6826(canvas, "<this>");
        fa1.m6826(matrix, "matrix");
        fa1.m6826(ph0Var, "block");
        int save = canvas.save();
        canvas.concat(matrix);
        try {
            ph0Var.invoke(canvas);
        } finally {
            canvas.restoreToCount(save);
        }
    }

    public static /* synthetic */ void withMatrix$default(Canvas canvas, Matrix matrix, ph0 ph0Var, int i, Object obj) {
        if ((i & 1) != 0) {
            matrix = new Matrix();
        }
        fa1.m6826(canvas, "<this>");
        fa1.m6826(matrix, "matrix");
        fa1.m6826(ph0Var, "block");
        int save = canvas.save();
        canvas.concat(matrix);
        try {
            ph0Var.invoke(canvas);
        } finally {
            canvas.restoreToCount(save);
        }
    }

    public static final void withRotation(@zu2 Canvas canvas, float f, float f2, float f3, @zu2 ph0<? super Canvas, lx5> ph0Var) {
        fa1.m6826(canvas, "<this>");
        fa1.m6826(ph0Var, "block");
        int save = canvas.save();
        canvas.rotate(f, f2, f3);
        try {
            ph0Var.invoke(canvas);
        } finally {
            canvas.restoreToCount(save);
        }
    }

    public static /* synthetic */ void withRotation$default(Canvas canvas, float f, float f2, float f3, ph0 ph0Var, int i, Object obj) {
        if ((i & 1) != 0) {
            f = 0.0f;
        }
        if ((i & 2) != 0) {
            f2 = 0.0f;
        }
        if ((i & 4) != 0) {
            f3 = 0.0f;
        }
        fa1.m6826(canvas, "<this>");
        fa1.m6826(ph0Var, "block");
        int save = canvas.save();
        canvas.rotate(f, f2, f3);
        try {
            ph0Var.invoke(canvas);
        } finally {
            canvas.restoreToCount(save);
        }
    }

    public static final void withSave(@zu2 Canvas canvas, @zu2 ph0<? super Canvas, lx5> ph0Var) {
        fa1.m6826(canvas, "<this>");
        fa1.m6826(ph0Var, "block");
        int save = canvas.save();
        try {
            ph0Var.invoke(canvas);
        } finally {
            canvas.restoreToCount(save);
        }
    }

    public static final void withScale(@zu2 Canvas canvas, float f, float f2, float f3, float f4, @zu2 ph0<? super Canvas, lx5> ph0Var) {
        fa1.m6826(canvas, "<this>");
        fa1.m6826(ph0Var, "block");
        int save = canvas.save();
        canvas.scale(f, f2, f3, f4);
        try {
            ph0Var.invoke(canvas);
        } finally {
            canvas.restoreToCount(save);
        }
    }

    public static /* synthetic */ void withScale$default(Canvas canvas, float f, float f2, float f3, float f4, ph0 ph0Var, int i, Object obj) {
        if ((i & 1) != 0) {
            f = 1.0f;
        }
        if ((i & 2) != 0) {
            f2 = 1.0f;
        }
        if ((i & 4) != 0) {
            f3 = 0.0f;
        }
        if ((i & 8) != 0) {
            f4 = 0.0f;
        }
        fa1.m6826(canvas, "<this>");
        fa1.m6826(ph0Var, "block");
        int save = canvas.save();
        canvas.scale(f, f2, f3, f4);
        try {
            ph0Var.invoke(canvas);
        } finally {
            canvas.restoreToCount(save);
        }
    }

    public static final void withSkew(@zu2 Canvas canvas, float f, float f2, @zu2 ph0<? super Canvas, lx5> ph0Var) {
        fa1.m6826(canvas, "<this>");
        fa1.m6826(ph0Var, "block");
        int save = canvas.save();
        canvas.skew(f, f2);
        try {
            ph0Var.invoke(canvas);
        } finally {
            canvas.restoreToCount(save);
        }
    }

    public static /* synthetic */ void withSkew$default(Canvas canvas, float f, float f2, ph0 ph0Var, int i, Object obj) {
        if ((i & 1) != 0) {
            f = 0.0f;
        }
        if ((i & 2) != 0) {
            f2 = 0.0f;
        }
        fa1.m6826(canvas, "<this>");
        fa1.m6826(ph0Var, "block");
        int save = canvas.save();
        canvas.skew(f, f2);
        try {
            ph0Var.invoke(canvas);
        } finally {
            canvas.restoreToCount(save);
        }
    }

    public static final void withTranslation(@zu2 Canvas canvas, float f, float f2, @zu2 ph0<? super Canvas, lx5> ph0Var) {
        fa1.m6826(canvas, "<this>");
        fa1.m6826(ph0Var, "block");
        int save = canvas.save();
        canvas.translate(f, f2);
        try {
            ph0Var.invoke(canvas);
        } finally {
            canvas.restoreToCount(save);
        }
    }

    public static /* synthetic */ void withTranslation$default(Canvas canvas, float f, float f2, ph0 ph0Var, int i, Object obj) {
        if ((i & 1) != 0) {
            f = 0.0f;
        }
        if ((i & 2) != 0) {
            f2 = 0.0f;
        }
        fa1.m6826(canvas, "<this>");
        fa1.m6826(ph0Var, "block");
        int save = canvas.save();
        canvas.translate(f, f2);
        try {
            ph0Var.invoke(canvas);
        } finally {
            canvas.restoreToCount(save);
        }
    }
}
