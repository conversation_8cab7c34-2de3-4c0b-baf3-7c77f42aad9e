package okhttp3.internal.p042io;

/* renamed from: okhttp3.internal.io.vf */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5468vf extends RuntimeException {

    /* renamed from: ၥ */
    @zu2
    public final InterfaceC6710 f24153;

    public C5468vf(@zu2 InterfaceC6710 interfaceC6710) {
        this.f24153 = interfaceC6710;
    }

    @Override // java.lang.Throwable
    @zu2
    public final Throwable fillInStackTrace() {
        setStackTrace(new StackTraceElement[0]);
        return this;
    }

    @Override // java.lang.Throwable
    @zu2
    public final String getLocalizedMessage() {
        return this.f24153.toString();
    }
}
