package androidx.core.text;

import android.text.TextUtils;
import kotlin.Metadata;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.zu2;

@Metadata(m4114d1 = {"\u0000\b\n\u0000\n\u0002\u0010\u000e\n\u0000\u001a\r\u0010\u0000\u001a\u00020\u0001*\u00020\u0001H\u0086\b¨\u0006\u0002"}, m4115d2 = {"htmlEncode", "", "core-ktx_release"}, m4116k = 2, m4117mv = {1, 6, 0}, m4119xi = 48)
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class StringKt {
    @zu2
    public static final String htmlEncode(@zu2 String str) {
        fa1.m6826(str, "<this>");
        String htmlEncode = TextUtils.htmlEncode(str);
        fa1.m6825(htmlEncode, "htmlEncode(this)");
        return htmlEncode;
    }
}
