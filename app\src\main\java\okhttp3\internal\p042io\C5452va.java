package okhttp3.internal.p042io;

import android.app.TimePickerDialog;
import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.TimePicker;
import android.widget.Toast;
import androidx.exifinterface.media.ExifInterface;
import androidx.lifecycle.LifecycleOwnerKt;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.switchmaterial.SwitchMaterial;
import com.stardust.autojs.core.inputevent.InputEventCodes;
import com.stardust.autojs.engine.RhinoJavaScriptEngine;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;
import javax.xml.parsers.DocumentBuilderFactory;
import kotlin.Metadata;
import okhttp3.internal.p042io.C5452va;
import okhttp3.internal.p042io.eb6;
import okhttp3.internal.p042io.nj4;
import okhttp3.internal.p042io.uo5;
import org.autojs.autojs.p047ui.timing.TimedTaskSettingActivity;
import org.mozilla.javascript.optimizer.Codegen;
import org.opencv.videoio.Videoio;
import org.w3c.dom.Element;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\u0018\u00002\u00020\u0001:\u0001\u0004B\u0007¢\u0006\u0004\b\u0002\u0010\u0003¨\u0006\u0005"}, m4115d2 = {"Lokhttp3/internal/io/va;", "Lokhttp3/internal/io/ଠ;", RhinoJavaScriptEngine.SOURCE_NAME_INIT, Codegen.TEMPLATE_LITERAL_INIT_METHOD_SIGNATURE, "Ϳ", "visual-ui-editor_release"}, m4116k = 1, m4117mv = {1, 7, 1})
/* renamed from: okhttp3.internal.io.va */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5452va extends C7290 {

    /* renamed from: ၼ */
    @zu2
    public static final C9423 f23871 = new C9423();

    /* renamed from: ၯ */
    public boolean f23873;

    /* renamed from: ၰ */
    @wv2
    public ap5 f23874;

    /* renamed from: ၶ */
    public int f23876;

    /* renamed from: ၷ */
    public boolean f23877;

    /* renamed from: ၸ */
    public n63 f23878;

    /* renamed from: ၹ */
    public uo5 f23879;

    /* renamed from: ၺ */
    public nj4 f23880;

    /* renamed from: ၻ */
    @zu2
    public Map<Integer, View> f23881 = new LinkedHashMap();

    /* renamed from: ၮ */
    @zu2
    public ArrayList<ap5> f23872 = new ArrayList<>();

    /* renamed from: ၵ */
    @zu2
    public String f23875 = "";

    /* renamed from: okhttp3.internal.io.va$Ϳ, reason: contains not printable characters */
    public static final class C9423 {
    }

    @InterfaceC4988s2(m11868c = "org.autojs.hrapps.visual.lib.main.fragment.DesignFragment", m11869f = "DesignFragment.kt", m11870l = {197, InputEventCodes.KEY_CHAT}, m11871m = "deleteOne")
    /* renamed from: okhttp3.internal.io.va$Ԩ, reason: contains not printable characters */
    public static final class C9424 extends AbstractC6644 {

        /* renamed from: ၥ */
        public C5452va f23882;

        /* renamed from: ၦ */
        public ArrayList f23883;

        /* renamed from: ၮ */
        public boolean f23884;

        /* renamed from: ၯ */
        public int f23885;

        /* renamed from: ၰ */
        public /* synthetic */ Object f23886;

        /* renamed from: ၶ */
        public int f23888;

        public C9424(InterfaceC7155<? super C9424> interfaceC7155) {
            super(interfaceC7155);
        }

        @Override // okhttp3.internal.p042io.AbstractC7853
        @wv2
        public final Object invokeSuspend(@zu2 Object obj) {
            this.f23886 = obj;
            this.f23888 |= Integer.MIN_VALUE;
            C5452va c5452va = C5452va.this;
            C9423 c9423 = C5452va.f23871;
            return c5452va.m13163(null, false, this);
        }
    }

    /* renamed from: okhttp3.internal.io.va$Ԫ, reason: contains not printable characters */
    public static final class C9425 implements uo5.InterfaceC5347 {
        public C9425() {
        }

        @Override // okhttp3.internal.p042io.uo5.InterfaceC5347
        /* renamed from: Ϳ */
        public final void mo12946(int i) {
            ap5 ap5Var = C5452va.this.f23872.get(i);
            fa1.m6825(ap5Var, "list[i]");
            aa5 aa5Var = new aa5(2, fa1.m6819(ap5Var));
            zl4 m16876 = C5452va.this.m16876();
            Objects.requireNonNull(m16876);
            m16876.f27549 = aa5Var;
            C5452va.this.m16876().m14585(fi4.ATTR);
        }
    }

    @InterfaceC4988s2(m11868c = "org.autojs.hrapps.visual.lib.main.fragment.DesignFragment$onResume$1", m11869f = "DesignFragment.kt", m11870l = {67, 75, 76, 76, 78, 82, 86, 87, 87}, m11871m = "invokeSuspend")
    /* renamed from: okhttp3.internal.io.va$Ԭ, reason: contains not printable characters */
    public static final class C9426 extends u75 implements di0<InterfaceC7881, InterfaceC7155<? super lx5>, Object> {

        /* renamed from: ၥ */
        public C5452va f23890;

        /* renamed from: ၦ */
        public C5452va f23891;

        /* renamed from: ၮ */
        public int f23892;

        public C9426(InterfaceC7155<? super C9426> interfaceC7155) {
            super(2, interfaceC7155);
        }

        @Override // okhttp3.internal.p042io.AbstractC7853
        @zu2
        public final InterfaceC7155<lx5> create(@wv2 Object obj, @zu2 InterfaceC7155<?> interfaceC7155) {
            return C5452va.this.new C9426(interfaceC7155);
        }

        @Override // okhttp3.internal.p042io.di0
        /* renamed from: invoke */
        public final Object mo18338invoke(InterfaceC7881 interfaceC7881, InterfaceC7155<? super lx5> interfaceC7155) {
            return ((C9426) create(interfaceC7881, interfaceC7155)).invokeSuspend(lx5.f14876);
        }

        /* JADX WARN: Removed duplicated region for block: B:13:0x0149 A[RETURN] */
        /* JADX WARN: Removed duplicated region for block: B:14:0x014a  */
        /* JADX WARN: Removed duplicated region for block: B:18:0x012b A[RETURN] */
        /* JADX WARN: Removed duplicated region for block: B:19:0x012c  */
        /* JADX WARN: Removed duplicated region for block: B:26:0x00e0  */
        /* JADX WARN: Removed duplicated region for block: B:32:0x00d3 A[RETURN] */
        /* JADX WARN: Removed duplicated region for block: B:36:0x00aa  */
        /* JADX WARN: Removed duplicated region for block: B:39:0x00ef  */
        /* JADX WARN: Removed duplicated region for block: B:46:0x0091  */
        /* JADX WARN: Removed duplicated region for block: B:49:0x00ff  */
        @Override // okhttp3.internal.p042io.AbstractC7853
        @okhttp3.internal.p042io.wv2
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct code enable 'Show inconsistent code' option in preferences
        */
        public final java.lang.Object invokeSuspend(@okhttp3.internal.p042io.zu2 java.lang.Object r7) {
            /*
                Method dump skipped, instructions count: 368
                To view this dump change 'Code comments level' option to 'DEBUG'
            */
            throw new UnsupportedOperationException("Method not decompiled: okhttp3.internal.p042io.C5452va.C9426.invokeSuspend(java.lang.Object):java.lang.Object");
        }
    }

    @InterfaceC4988s2(m11868c = "org.autojs.hrapps.visual.lib.main.fragment.DesignFragment$onViewCreated$11", m11869f = "DesignFragment.kt", m11870l = {InputEventCodes.KEY_NUMERIC_8}, m11871m = "invokeSuspend")
    /* renamed from: okhttp3.internal.io.va$Ԯ, reason: contains not printable characters */
    public static final class C9427 extends u75 implements ph0<InterfaceC7155<? super lx5>, Object> {

        /* renamed from: ၥ */
        public int f23894;

        public C9427(InterfaceC7155<? super C9427> interfaceC7155) {
            super(1, interfaceC7155);
        }

        @Override // okhttp3.internal.p042io.AbstractC7853
        @zu2
        public final InterfaceC7155<lx5> create(@zu2 InterfaceC7155<?> interfaceC7155) {
            return C5452va.this.new C9427(interfaceC7155);
        }

        @Override // okhttp3.internal.p042io.ph0
        public final Object invoke(InterfaceC7155<? super lx5> interfaceC7155) {
            return ((C9427) create(interfaceC7155)).invokeSuspend(lx5.f14876);
        }

        @Override // okhttp3.internal.p042io.AbstractC7853
        @wv2
        public final Object invokeSuspend(@zu2 Object obj) {
            EnumC7329 enumC7329 = EnumC7329.COROUTINE_SUSPENDED;
            int i = this.f23894;
            if (i == 0) {
                C4350o9.m10270(obj);
                uo5 uo5Var = C5452va.this.f23879;
                if (uo5Var == null) {
                    fa1.m6848("treeAdapter");
                    throw null;
                }
                if (uo5Var.m12944().size() > 0) {
                    C5452va c5452va = C5452va.this;
                    this.f23894 = 1;
                    if (C5452va.m13159(c5452va, this) == enumC7329) {
                        return enumC7329;
                    }
                } else {
                    Toast.makeText(C5452va.this.getContext(), bq3.zero_choose_hint, 0).show();
                }
            } else {
                if (i != 1) {
                    throw new IllegalStateException("call to 'resume' before 'invoke' with coroutine");
                }
                C4350o9.m10270(obj);
            }
            return lx5.f14876;
        }
    }

    @InterfaceC4988s2(m11868c = "org.autojs.hrapps.visual.lib.main.fragment.DesignFragment$onViewCreated$12", m11869f = "DesignFragment.kt", m11870l = {InputEventCodes.KEY_NUMERIC_D}, m11871m = "invokeSuspend")
    /* renamed from: okhttp3.internal.io.va$֏, reason: contains not printable characters */
    public static final class C9428 extends u75 implements ph0<InterfaceC7155<? super lx5>, Object> {

        /* renamed from: ၥ */
        public int f23896;

        public C9428(InterfaceC7155<? super C9428> interfaceC7155) {
            super(1, interfaceC7155);
        }

        @Override // okhttp3.internal.p042io.AbstractC7853
        @zu2
        public final InterfaceC7155<lx5> create(@zu2 InterfaceC7155<?> interfaceC7155) {
            return C5452va.this.new C9428(interfaceC7155);
        }

        @Override // okhttp3.internal.p042io.ph0
        public final Object invoke(InterfaceC7155<? super lx5> interfaceC7155) {
            return ((C9428) create(interfaceC7155)).invokeSuspend(lx5.f14876);
        }

        @Override // okhttp3.internal.p042io.AbstractC7853
        @wv2
        public final Object invokeSuspend(@zu2 Object obj) {
            EnumC7329 enumC7329 = EnumC7329.COROUTINE_SUSPENDED;
            int i = this.f23896;
            if (i == 0) {
                C4350o9.m10270(obj);
                uo5 uo5Var = C5452va.this.f23879;
                if (uo5Var == null) {
                    fa1.m6848("treeAdapter");
                    throw null;
                }
                if (uo5Var.m12944().size() > 0) {
                    C5452va c5452va = C5452va.this;
                    uo5 uo5Var2 = c5452va.f23879;
                    if (uo5Var2 == null) {
                        fa1.m6848("treeAdapter");
                        throw null;
                    }
                    ArrayList<ap5> m12944 = uo5Var2.m12944();
                    this.f23896 = 1;
                    if (c5452va.m13163(m12944, true, this) == enumC7329) {
                        return enumC7329;
                    }
                } else {
                    Toast.makeText(C5452va.this.getContext(), bq3.zero_choose_hint, 0).show();
                }
            } else {
                if (i != 1) {
                    throw new IllegalStateException("call to 'resume' before 'invoke' with coroutine");
                }
                C4350o9.m10270(obj);
            }
            return lx5.f14876;
        }
    }

    @InterfaceC4988s2(m11868c = "org.autojs.hrapps.visual.lib.main.fragment.DesignFragment$onViewCreated$13", m11869f = "DesignFragment.kt", m11870l = {534}, m11871m = "invokeSuspend")
    /* renamed from: okhttp3.internal.io.va$ؠ, reason: contains not printable characters */
    public static final class C9429 extends u75 implements ph0<InterfaceC7155<? super lx5>, Object> {

        /* renamed from: ၥ */
        public int f23898;

        public C9429(InterfaceC7155<? super C9429> interfaceC7155) {
            super(1, interfaceC7155);
        }

        @Override // okhttp3.internal.p042io.AbstractC7853
        @zu2
        public final InterfaceC7155<lx5> create(@zu2 InterfaceC7155<?> interfaceC7155) {
            return C5452va.this.new C9429(interfaceC7155);
        }

        @Override // okhttp3.internal.p042io.ph0
        public final Object invoke(InterfaceC7155<? super lx5> interfaceC7155) {
            return ((C9429) create(interfaceC7155)).invokeSuspend(lx5.f14876);
        }

        @Override // okhttp3.internal.p042io.AbstractC7853
        @wv2
        public final Object invokeSuspend(@zu2 Object obj) {
            EnumC7329 enumC7329 = EnumC7329.COROUTINE_SUSPENDED;
            int i = this.f23898;
            if (i == 0) {
                C4350o9.m10270(obj);
                uo5 uo5Var = C5452va.this.f23879;
                if (uo5Var == null) {
                    fa1.m6848("treeAdapter");
                    throw null;
                }
                if (uo5Var.m12944().size() > 0) {
                    C5452va c5452va = C5452va.this;
                    this.f23898 = 1;
                    c5452va.m13166();
                    uo5 uo5Var2 = c5452va.f23879;
                    if (uo5Var2 == null) {
                        fa1.m6848("treeAdapter");
                        throw null;
                    }
                    Object m13163 = c5452va.m13163(uo5Var2.m12944(), true, this);
                    if (m13163 != enumC7329) {
                        m13163 = lx5.f14876;
                    }
                    if (m13163 == enumC7329) {
                        return enumC7329;
                    }
                } else {
                    Toast.makeText(C5452va.this.getContext(), bq3.zero_choose_hint, 0).show();
                }
            } else {
                if (i != 1) {
                    throw new IllegalStateException("call to 'resume' before 'invoke' with coroutine");
                }
                C4350o9.m10270(obj);
            }
            return lx5.f14876;
        }
    }

    @InterfaceC4988s2(m11868c = "org.autojs.hrapps.visual.lib.main.fragment.DesignFragment$onViewCreated$16", m11869f = "DesignFragment.kt", m11870l = {Videoio.CAP_PROP_XI_ACQ_TRANSPORT_BUFFER_SIZE}, m11871m = "invokeSuspend")
    /* renamed from: okhttp3.internal.io.va$ހ, reason: contains not printable characters */
    public static final class C9430 extends u75 implements ph0<InterfaceC7155<? super lx5>, Object> {

        /* renamed from: ၥ */
        public int f23900;

        public C9430(InterfaceC7155<? super C9430> interfaceC7155) {
            super(1, interfaceC7155);
        }

        @Override // okhttp3.internal.p042io.AbstractC7853
        @zu2
        public final InterfaceC7155<lx5> create(@zu2 InterfaceC7155<?> interfaceC7155) {
            return C5452va.this.new C9430(interfaceC7155);
        }

        @Override // okhttp3.internal.p042io.ph0
        public final Object invoke(InterfaceC7155<? super lx5> interfaceC7155) {
            return ((C9430) create(interfaceC7155)).invokeSuspend(lx5.f14876);
        }

        @Override // okhttp3.internal.p042io.AbstractC7853
        @wv2
        public final Object invokeSuspend(@zu2 Object obj) {
            EnumC7329 enumC7329 = EnumC7329.COROUTINE_SUSPENDED;
            int i = this.f23900;
            if (i == 0) {
                C4350o9.m10270(obj);
                C5452va c5452va = C5452va.this;
                this.f23900 = 1;
                if (C5452va.m13157(c5452va, this) == enumC7329) {
                    return enumC7329;
                }
            } else {
                if (i != 1) {
                    throw new IllegalStateException("call to 'resume' before 'invoke' with coroutine");
                }
                C4350o9.m10270(obj);
            }
            return lx5.f14876;
        }
    }

    @InterfaceC4988s2(m11868c = "org.autojs.hrapps.visual.lib.main.fragment.DesignFragment$onViewCreated$17", m11869f = "DesignFragment.kt", m11870l = {Videoio.CAP_PROP_XI_BUFFERS_QUEUE_SIZE}, m11871m = "invokeSuspend")
    /* renamed from: okhttp3.internal.io.va$ށ, reason: contains not printable characters */
    public static final class C9431 extends u75 implements ph0<InterfaceC7155<? super lx5>, Object> {

        /* renamed from: ၥ */
        public int f23902;

        public C9431(InterfaceC7155<? super C9431> interfaceC7155) {
            super(1, interfaceC7155);
        }

        @Override // okhttp3.internal.p042io.AbstractC7853
        @zu2
        public final InterfaceC7155<lx5> create(@zu2 InterfaceC7155<?> interfaceC7155) {
            return C5452va.this.new C9431(interfaceC7155);
        }

        @Override // okhttp3.internal.p042io.ph0
        public final Object invoke(InterfaceC7155<? super lx5> interfaceC7155) {
            return ((C9431) create(interfaceC7155)).invokeSuspend(lx5.f14876);
        }

        @Override // okhttp3.internal.p042io.AbstractC7853
        @wv2
        public final Object invokeSuspend(@zu2 Object obj) {
            EnumC7329 enumC7329 = EnumC7329.COROUTINE_SUSPENDED;
            int i = this.f23902;
            if (i == 0) {
                C4350o9.m10270(obj);
                C5452va c5452va = C5452va.this;
                this.f23902 = 1;
                if (C5452va.m13156(c5452va, this) == enumC7329) {
                    return enumC7329;
                }
            } else {
                if (i != 1) {
                    throw new IllegalStateException("call to 'resume' before 'invoke' with coroutine");
                }
                C4350o9.m10270(obj);
            }
            return lx5.f14876;
        }
    }

    @InterfaceC4988s2(m11868c = "org.autojs.hrapps.visual.lib.main.fragment.DesignFragment$onViewCreated$19", m11869f = "DesignFragment.kt", m11870l = {Videoio.CAP_PROP_XI_SENSOR_MODE}, m11871m = "invokeSuspend")
    /* renamed from: okhttp3.internal.io.va$ނ, reason: contains not printable characters */
    public static final class C9432 extends u75 implements ph0<InterfaceC7155<? super lx5>, Object> {

        /* renamed from: ၥ */
        public int f23904;

        public C9432(InterfaceC7155<? super C9432> interfaceC7155) {
            super(1, interfaceC7155);
        }

        @Override // okhttp3.internal.p042io.AbstractC7853
        @zu2
        public final InterfaceC7155<lx5> create(@zu2 InterfaceC7155<?> interfaceC7155) {
            return C5452va.this.new C9432(interfaceC7155);
        }

        @Override // okhttp3.internal.p042io.ph0
        public final Object invoke(InterfaceC7155<? super lx5> interfaceC7155) {
            return ((C9432) create(interfaceC7155)).invokeSuspend(lx5.f14876);
        }

        @Override // okhttp3.internal.p042io.AbstractC7853
        @wv2
        public final Object invokeSuspend(@zu2 Object obj) {
            EnumC7329 enumC7329 = EnumC7329.COROUTINE_SUSPENDED;
            int i = this.f23904;
            if (i == 0) {
                C4350o9.m10270(obj);
                C5452va c5452va = C5452va.this;
                this.f23904 = 1;
                if (C5452va.m13160(c5452va, this) == enumC7329) {
                    return enumC7329;
                }
            } else {
                if (i != 1) {
                    throw new IllegalStateException("call to 'resume' before 'invoke' with coroutine");
                }
                C4350o9.m10270(obj);
            }
            C5452va c5452va2 = C5452va.this;
            C9423 c9423 = C5452va.f23871;
            c5452va2.m13164();
            C5452va.this.m13165();
            return lx5.f14876;
        }
    }

    /* renamed from: okhttp3.internal.io.va$ރ, reason: contains not printable characters */
    public static final class C9433 implements nj4.InterfaceC4264 {
        public C9433() {
        }

        @Override // okhttp3.internal.p042io.nj4.InterfaceC4264
        /* renamed from: Ϳ */
        public final void mo10050(int i) {
            uo5 uo5Var = C5452va.this.f23879;
            if (uo5Var == null) {
                fa1.m6848("treeAdapter");
                throw null;
            }
            uo5Var.notifyItemChanged(i);
            C5452va.this.m13165();
        }
    }

    /* renamed from: okhttp3.internal.io.va$ބ, reason: contains not printable characters */
    public static final class C9434 implements nj4.InterfaceC4265 {
        public C9434() {
        }

        @Override // okhttp3.internal.p042io.nj4.InterfaceC4265
        /* renamed from: Ϳ */
        public final void mo10051(int i) {
            C5452va.this.m13169(i);
        }
    }

    @InterfaceC4988s2(m11868c = "org.autojs.hrapps.visual.lib.main.fragment.DesignFragment$onViewCreated$4", m11869f = "DesignFragment.kt", m11870l = {InputEventCodes.KEY_FN_F7}, m11871m = "invokeSuspend")
    /* renamed from: okhttp3.internal.io.va$ޅ, reason: contains not printable characters */
    public static final class C9435 extends u75 implements ph0<InterfaceC7155<? super lx5>, Object> {

        /* renamed from: ၥ */
        public int f23908;

        public C9435(InterfaceC7155<? super C9435> interfaceC7155) {
            super(1, interfaceC7155);
        }

        @Override // okhttp3.internal.p042io.AbstractC7853
        @zu2
        public final InterfaceC7155<lx5> create(@zu2 InterfaceC7155<?> interfaceC7155) {
            return C5452va.this.new C9435(interfaceC7155);
        }

        @Override // okhttp3.internal.p042io.ph0
        public final Object invoke(InterfaceC7155<? super lx5> interfaceC7155) {
            return ((C9435) create(interfaceC7155)).invokeSuspend(lx5.f14876);
        }

        @Override // okhttp3.internal.p042io.AbstractC7853
        @wv2
        public final Object invokeSuspend(@zu2 Object obj) {
            EnumC7329 enumC7329 = EnumC7329.COROUTINE_SUSPENDED;
            int i = this.f23908;
            if (i == 0) {
                C4350o9.m10270(obj);
                C5452va c5452va = C5452va.this;
                boolean isChecked = ((SwitchMaterial) c5452va.m13162(np3.moveAll)).isChecked();
                this.f23908 = 1;
                if (C5452va.m13161(c5452va, 0, isChecked, false, this) == enumC7329) {
                    return enumC7329;
                }
            } else {
                if (i != 1) {
                    throw new IllegalStateException("call to 'resume' before 'invoke' with coroutine");
                }
                C4350o9.m10270(obj);
            }
            return lx5.f14876;
        }
    }

    @InterfaceC4988s2(m11868c = "org.autojs.hrapps.visual.lib.main.fragment.DesignFragment$onViewCreated$5", m11869f = "DesignFragment.kt", m11870l = {475}, m11871m = "invokeSuspend")
    /* renamed from: okhttp3.internal.io.va$ކ, reason: contains not printable characters */
    public static final class C9436 extends u75 implements ph0<InterfaceC7155<? super lx5>, Object> {

        /* renamed from: ၥ */
        public int f23910;

        public C9436(InterfaceC7155<? super C9436> interfaceC7155) {
            super(1, interfaceC7155);
        }

        @Override // okhttp3.internal.p042io.AbstractC7853
        @zu2
        public final InterfaceC7155<lx5> create(@zu2 InterfaceC7155<?> interfaceC7155) {
            return C5452va.this.new C9436(interfaceC7155);
        }

        @Override // okhttp3.internal.p042io.ph0
        public final Object invoke(InterfaceC7155<? super lx5> interfaceC7155) {
            return ((C9436) create(interfaceC7155)).invokeSuspend(lx5.f14876);
        }

        @Override // okhttp3.internal.p042io.AbstractC7853
        @wv2
        public final Object invokeSuspend(@zu2 Object obj) {
            EnumC7329 enumC7329 = EnumC7329.COROUTINE_SUSPENDED;
            int i = this.f23910;
            if (i == 0) {
                C4350o9.m10270(obj);
                C5452va c5452va = C5452va.this;
                boolean isChecked = ((SwitchMaterial) c5452va.m13162(np3.moveAll)).isChecked();
                this.f23910 = 1;
                if (C5452va.m13161(c5452va, 1, isChecked, false, this) == enumC7329) {
                    return enumC7329;
                }
            } else {
                if (i != 1) {
                    throw new IllegalStateException("call to 'resume' before 'invoke' with coroutine");
                }
                C4350o9.m10270(obj);
            }
            return lx5.f14876;
        }
    }

    @InterfaceC4988s2(m11868c = "org.autojs.hrapps.visual.lib.main.fragment.DesignFragment$onViewCreated$6", m11869f = "DesignFragment.kt", m11870l = {478}, m11871m = "invokeSuspend")
    /* renamed from: okhttp3.internal.io.va$އ, reason: contains not printable characters */
    public static final class C9437 extends u75 implements ph0<InterfaceC7155<? super lx5>, Object> {

        /* renamed from: ၥ */
        public int f23912;

        public C9437(InterfaceC7155<? super C9437> interfaceC7155) {
            super(1, interfaceC7155);
        }

        @Override // okhttp3.internal.p042io.AbstractC7853
        @zu2
        public final InterfaceC7155<lx5> create(@zu2 InterfaceC7155<?> interfaceC7155) {
            return C5452va.this.new C9437(interfaceC7155);
        }

        @Override // okhttp3.internal.p042io.ph0
        public final Object invoke(InterfaceC7155<? super lx5> interfaceC7155) {
            return ((C9437) create(interfaceC7155)).invokeSuspend(lx5.f14876);
        }

        @Override // okhttp3.internal.p042io.AbstractC7853
        @wv2
        public final Object invokeSuspend(@zu2 Object obj) {
            EnumC7329 enumC7329 = EnumC7329.COROUTINE_SUSPENDED;
            int i = this.f23912;
            if (i == 0) {
                C4350o9.m10270(obj);
                C5452va c5452va = C5452va.this;
                boolean isChecked = ((SwitchMaterial) c5452va.m13162(np3.moveAll)).isChecked();
                this.f23912 = 1;
                if (C5452va.m13161(c5452va, 0, isChecked, true, this) == enumC7329) {
                    return enumC7329;
                }
            } else {
                if (i != 1) {
                    throw new IllegalStateException("call to 'resume' before 'invoke' with coroutine");
                }
                C4350o9.m10270(obj);
            }
            return lx5.f14876;
        }
    }

    @InterfaceC4988s2(m11868c = "org.autojs.hrapps.visual.lib.main.fragment.DesignFragment$onViewCreated$7", m11869f = "DesignFragment.kt", m11870l = {481}, m11871m = "invokeSuspend")
    /* renamed from: okhttp3.internal.io.va$ވ, reason: contains not printable characters */
    public static final class C9438 extends u75 implements ph0<InterfaceC7155<? super lx5>, Object> {

        /* renamed from: ၥ */
        public int f23914;

        public C9438(InterfaceC7155<? super C9438> interfaceC7155) {
            super(1, interfaceC7155);
        }

        @Override // okhttp3.internal.p042io.AbstractC7853
        @zu2
        public final InterfaceC7155<lx5> create(@zu2 InterfaceC7155<?> interfaceC7155) {
            return C5452va.this.new C9438(interfaceC7155);
        }

        @Override // okhttp3.internal.p042io.ph0
        public final Object invoke(InterfaceC7155<? super lx5> interfaceC7155) {
            return ((C9438) create(interfaceC7155)).invokeSuspend(lx5.f14876);
        }

        @Override // okhttp3.internal.p042io.AbstractC7853
        @wv2
        public final Object invokeSuspend(@zu2 Object obj) {
            EnumC7329 enumC7329 = EnumC7329.COROUTINE_SUSPENDED;
            int i = this.f23914;
            if (i == 0) {
                C4350o9.m10270(obj);
                C5452va c5452va = C5452va.this;
                boolean isChecked = ((SwitchMaterial) c5452va.m13162(np3.moveAll)).isChecked();
                this.f23914 = 1;
                if (C5452va.m13161(c5452va, 1, isChecked, true, this) == enumC7329) {
                    return enumC7329;
                }
            } else {
                if (i != 1) {
                    throw new IllegalStateException("call to 'resume' before 'invoke' with coroutine");
                }
                C4350o9.m10270(obj);
            }
            return lx5.f14876;
        }
    }

    @InterfaceC4988s2(m11868c = "org.autojs.hrapps.visual.lib.main.fragment.DesignFragment", m11869f = "DesignFragment.kt", m11870l = {Videoio.CAP_PROP_XI_SENSOR_FEATURE_SELECTOR}, m11871m = "setNewCode")
    /* renamed from: okhttp3.internal.io.va$މ, reason: contains not printable characters */
    public static final class C9439 extends AbstractC6644 {

        /* renamed from: ၥ */
        public C5452va f23916;

        /* renamed from: ၦ */
        public /* synthetic */ Object f23917;

        /* renamed from: ၯ */
        public int f23919;

        public C9439(InterfaceC7155<? super C9439> interfaceC7155) {
            super(interfaceC7155);
        }

        @Override // okhttp3.internal.p042io.AbstractC7853
        @wv2
        public final Object invokeSuspend(@zu2 Object obj) {
            this.f23917 = obj;
            this.f23919 |= Integer.MIN_VALUE;
            C5452va c5452va = C5452va.this;
            C9423 c9423 = C5452va.f23871;
            return c5452va.m13167(null, this);
        }
    }

    /* renamed from: okhttp3.internal.io.va$ފ, reason: contains not printable characters */
    public static final class C9440 extends lv1 implements ph0<bb6, lx5> {

        /* renamed from: ၦ */
        public final /* synthetic */ int f23921;

        /* renamed from: ၮ */
        public final /* synthetic */ ArrayList<ap5> f23922;

        /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
        public C9440(int i, ArrayList<ap5> arrayList) {
            super(1);
            this.f23921 = i;
            this.f23922 = arrayList;
        }

        @Override // okhttp3.internal.p042io.ph0
        public final lx5 invoke(bb6 bb6Var) {
            bb6 bb6Var2 = bb6Var;
            fa1.m6826(bb6Var2, "it");
            C6814.m16042(LifecycleOwnerKt.getLifecycleScope(C5452va.this), null, 0, new C3315fb(C5452va.this, this.f23921, this.f23922, bb6Var2, null), 3);
            return lx5.f14876;
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:21:0x0069  */
    /* JADX WARN: Removed duplicated region for block: B:53:? A[RETURN, SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:54:0x0044  */
    /* JADX WARN: Removed duplicated region for block: B:8:0x0025  */
    /* renamed from: ޖ */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public static final java.lang.Object m13155(okhttp3.internal.p042io.C5452va r11, int r12, java.util.ArrayList r13, okhttp3.internal.p042io.bb6 r14, okhttp3.internal.p042io.InterfaceC7155 r15) {
        /*
            Method dump skipped, instructions count: 319
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: okhttp3.internal.p042io.C5452va.m13155(okhttp3.internal.io.va, int, java.util.ArrayList, okhttp3.internal.io.bb6, okhttp3.internal.io.ৡ):java.lang.Object");
    }

    /* JADX WARN: Code restructure failed: missing block: B:94:0x032e, code lost:
    
        if (r1 == r2) goto L85;
     */
    /* JADX WARN: Removed duplicated region for block: B:25:0x0061  */
    /* JADX WARN: Removed duplicated region for block: B:52:0x0313 A[LOOP:1: B:43:0x00c7->B:52:0x0313, LOOP_END] */
    /* JADX WARN: Removed duplicated region for block: B:53:0x02ef A[SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:8:0x002c  */
    /* JADX WARN: Removed duplicated region for block: B:97:0x036c  */
    /* JADX WARN: Removed duplicated region for block: B:99:0x0047  */
    /* JADX WARN: Type inference failed for: r3v12 */
    /* JADX WARN: Type inference failed for: r3v13, types: [boolean, int] */
    /* JADX WARN: Type inference failed for: r3v17 */
    /* renamed from: ޗ */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public static final java.lang.Object m13156(okhttp3.internal.p042io.C5452va r18, okhttp3.internal.p042io.InterfaceC7155 r19) {
        /*
            Method dump skipped, instructions count: 883
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: okhttp3.internal.p042io.C5452va.m13156(okhttp3.internal.io.va, okhttp3.internal.io.ৡ):java.lang.Object");
    }

    /* JADX WARN: Removed duplicated region for block: B:100:0x0045  */
    /* JADX WARN: Removed duplicated region for block: B:25:0x005e  */
    /* JADX WARN: Removed duplicated region for block: B:51:0x0259 A[LOOP:1: B:43:0x00c4->B:51:0x0259, LOOP_END] */
    /* JADX WARN: Removed duplicated region for block: B:52:0x0235 A[SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:79:0x01c8  */
    /* JADX WARN: Removed duplicated region for block: B:81:0x01e8  */
    /* JADX WARN: Removed duplicated region for block: B:8:0x002c  */
    /* JADX WARN: Removed duplicated region for block: B:98:0x02a6  */
    /* JADX WARN: Type inference failed for: r8v13, types: [boolean, int] */
    /* JADX WARN: Type inference failed for: r8v19 */
    /* JADX WARN: Type inference failed for: r8v7 */
    /* renamed from: ޚ */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public static final java.lang.Object m13157(okhttp3.internal.p042io.C5452va r17, okhttp3.internal.p042io.InterfaceC7155 r18) {
        /*
            Method dump skipped, instructions count: 683
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: okhttp3.internal.p042io.C5452va.m13157(okhttp3.internal.io.va, okhttp3.internal.io.ৡ):java.lang.Object");
    }

    /* JADX WARN: Removed duplicated region for block: B:13:0x00cd  */
    /* JADX WARN: Removed duplicated region for block: B:17:0x00e6  */
    /* JADX WARN: Removed duplicated region for block: B:25:0x0065  */
    /* JADX WARN: Removed duplicated region for block: B:33:? A[RETURN, SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:34:0x0042  */
    /* JADX WARN: Removed duplicated region for block: B:8:0x0027  */
    /* renamed from: ޜ */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public static final java.lang.Object m13158(okhttp3.internal.p042io.C5452va r9, okhttp3.internal.p042io.aa5 r10, okhttp3.internal.p042io.InterfaceC7155 r11) {
        /*
            java.util.Objects.requireNonNull(r9)
            boolean r0 = r11 instanceof okhttp3.internal.p042io.C5914za
            if (r0 == 0) goto L16
            r0 = r11
            okhttp3.internal.io.za r0 = (okhttp3.internal.p042io.C5914za) r0
            int r1 = r0.f27204
            r2 = -2147483648(0xffffffff80000000, float:-0.0)
            r3 = r1 & r2
            if (r3 == 0) goto L16
            int r1 = r1 - r2
            r0.f27204 = r1
            goto L1b
        L16:
            okhttp3.internal.io.za r0 = new okhttp3.internal.io.za
            r0.<init>(r9, r11)
        L1b:
            java.lang.Object r11 = r0.f27202
            okhttp3.internal.io.ஐ r1 = okhttp3.internal.p042io.EnumC7329.COROUTINE_SUSPENDED
            int r2 = r0.f27204
            r3 = 0
            r4 = 2
            r5 = 1
            r6 = 0
            if (r2 == 0) goto L42
            if (r2 == r5) goto L3a
            if (r2 != r4) goto L32
            okhttp3.internal.io.va r9 = r0.f27200
            okhttp3.internal.p042io.C4350o9.m10270(r11)
            goto Lac
        L32:
            java.lang.IllegalStateException r9 = new java.lang.IllegalStateException
            java.lang.String r10 = "call to 'resume' before 'invoke' with coroutine"
            r9.<init>(r10)
            throw r9
        L3a:
            okhttp3.internal.io.aa5 r10 = r0.f27201
            okhttp3.internal.io.va r9 = r0.f27200
            okhttp3.internal.p042io.C4350o9.m10270(r11)
            goto L57
        L42:
            okhttp3.internal.p042io.C4350o9.m10270(r11)
            okhttp3.internal.io.zl4 r11 = r9.m16876()
            r0.f27200 = r9
            r0.f27201 = r10
            r0.f27204 = r5
            java.lang.Object r11 = r11.m14582(r0)
            if (r11 != r1) goto L57
            goto Le5
        L57:
            java.lang.String r11 = (java.lang.String) r11
            java.util.ArrayList<okhttp3.internal.io.ap5> r10 = r10.f6259
            java.util.Iterator r10 = r10.iterator()
        L5f:
            boolean r2 = r10.hasNext()
            if (r2 == 0) goto L9f
            java.lang.Object r2 = r10.next()
            okhttp3.internal.io.ap5 r2 = (okhttp3.internal.p042io.ap5) r2
            java.util.ArrayList<okhttp3.internal.io.ap5> r7 = r9.f23872
            okhttp3.internal.io.zl4 r8 = r9.m16876()
            java.util.HashMap<java.lang.String, java.util.ArrayList<java.lang.String>> r8 = r8.f27550
            java.lang.String r11 = okhttp3.internal.p042io.C6656.m15763(r11, r7, r2, r8)
            java.lang.String r7 = "changeAttr(newCode, list, tr, session.tips)"
            okhttp3.internal.p042io.fa1.m6825(r11, r7)
            java.lang.String r7 = "ERROR"
            boolean r7 = okhttp3.internal.p042io.fa1.m6818(r11, r7)
            if (r7 == 0) goto L9c
            android.content.Context r10 = r9.getContext()
            java.lang.StringBuilder r11 = new java.lang.StringBuilder
            r11.<init>()
            int r0 = okhttp3.internal.p042io.bq3.code_syn_error
            java.lang.String r9 = r9.getString(r0)
            r11.append(r9)
            java.lang.String r9 = "[Change]"
            r11.append(r9)
            goto Ld8
        L9c:
            r2.f6615 = r5
            goto L5f
        L9f:
            r0.f27200 = r9
            r0.f27201 = r6
            r0.f27204 = r4
            java.lang.Object r10 = r9.m13167(r11, r0)
            if (r10 != r1) goto Lac
            goto Le5
        Lac:
            r9.m13164()
            r9.m13165()
            android.content.Context r10 = r9.getContext()
            java.lang.StringBuilder r11 = new java.lang.StringBuilder
            r11.<init>()
            int r0 = okhttp3.internal.p042io.bq3.attr_change_success
            java.lang.String r0 = r9.getString(r0)
            r11.append(r0)
            java.lang.String r0 = " : "
            r11.append(r0)
            okhttp3.internal.io.uo5 r9 = r9.f23879
            if (r9 == 0) goto Le6
            java.util.ArrayList r9 = r9.m12944()
            int r9 = r9.size()
            r11.append(r9)
        Ld8:
            java.lang.String r9 = r11.toString()
            android.widget.Toast r9 = android.widget.Toast.makeText(r10, r9, r3)
            r9.show()
            okhttp3.internal.io.lx5 r1 = okhttp3.internal.p042io.lx5.f14876
        Le5:
            return r1
        Le6:
            java.lang.String r9 = "treeAdapter"
            okhttp3.internal.p042io.fa1.m6848(r9)
            throw r6
        */
        throw new UnsupportedOperationException("Method not decompiled: okhttp3.internal.p042io.C5452va.m13158(okhttp3.internal.io.va, okhttp3.internal.io.aa5, okhttp3.internal.io.ৡ):java.lang.Object");
    }

    /* JADX WARN: Removed duplicated region for block: B:13:0x00d0  */
    /* JADX WARN: Removed duplicated region for block: B:17:0x00e9  */
    /* JADX WARN: Removed duplicated region for block: B:24:0x005b  */
    /* JADX WARN: Removed duplicated region for block: B:36:0x00ed  */
    /* JADX WARN: Removed duplicated region for block: B:38:0x0042  */
    /* JADX WARN: Removed duplicated region for block: B:8:0x0029  */
    /* renamed from: ޝ */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public static final java.lang.Object m13159(okhttp3.internal.p042io.C5452va r11, okhttp3.internal.p042io.InterfaceC7155 r12) {
        /*
            Method dump skipped, instructions count: 241
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: okhttp3.internal.p042io.C5452va.m13159(okhttp3.internal.io.va, okhttp3.internal.io.ৡ):java.lang.Object");
    }

    /* JADX WARN: Removed duplicated region for block: B:22:0x0165  */
    /* JADX WARN: Removed duplicated region for block: B:23:? A[RETURN, SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:27:0x0146  */
    /* JADX WARN: Removed duplicated region for block: B:28:? A[RETURN, SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:32:0x0084  */
    /* JADX WARN: Removed duplicated region for block: B:34:0x0089  */
    /* JADX WARN: Removed duplicated region for block: B:36:0x0090  */
    /* JADX WARN: Removed duplicated region for block: B:57:0x0086  */
    /* JADX WARN: Removed duplicated region for block: B:58:0x0063  */
    /* JADX WARN: Removed duplicated region for block: B:8:0x002b  */
    /* renamed from: ޞ */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public static final java.lang.Object m13160(okhttp3.internal.p042io.C5452va r17, okhttp3.internal.p042io.InterfaceC7155 r18) {
        /*
            Method dump skipped, instructions count: 368
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: okhttp3.internal.p042io.C5452va.m13160(okhttp3.internal.io.va, okhttp3.internal.io.ৡ):java.lang.Object");
    }

    /* JADX WARN: Removed duplicated region for block: B:15:0x02de  */
    /* JADX WARN: Removed duplicated region for block: B:21:0x02e6  */
    /* JADX WARN: Removed duplicated region for block: B:26:0x02be  */
    /* JADX WARN: Removed duplicated region for block: B:30:0x02d5  */
    /* JADX WARN: Removed duplicated region for block: B:34:0x0158  */
    /* JADX WARN: Removed duplicated region for block: B:79:0x02b8  */
    /* JADX WARN: Removed duplicated region for block: B:80:? A[RETURN, SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:81:0x0073  */
    /* JADX WARN: Removed duplicated region for block: B:8:0x0032  */
    /* renamed from: ޟ */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public static final java.lang.Object m13161(okhttp3.internal.p042io.C5452va r18, int r19, boolean r20, boolean r21, okhttp3.internal.p042io.InterfaceC7155 r22) {
        /*
            Method dump skipped, instructions count: 786
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: okhttp3.internal.p042io.C5452va.m13161(okhttp3.internal.io.va, int, boolean, boolean, okhttp3.internal.io.ৡ):java.lang.Object");
    }

    @Override // okhttp3.internal.p042io.C7290, androidx.fragment.app.Fragment
    public void onCreate(@wv2 Bundle bundle) {
        super.onCreate(bundle);
        this.f23878 = (n63) new ViewModelProvider(this).get(n63.class);
        Bundle arguments = getArguments();
        int i = arguments != null ? arguments.getInt("section_number") : -1;
        n63 n63Var = this.f23878;
        if (n63Var != null) {
            n63Var.m9924(i);
        } else {
            fa1.m6848("pageViewModel");
            throw null;
        }
    }

    @Override // androidx.fragment.app.Fragment
    @wv2
    public final View onCreateView(@zu2 LayoutInflater layoutInflater, @wv2 ViewGroup viewGroup, @wv2 Bundle bundle) {
        fa1.m6826(layoutInflater, "inflater");
        View inflate = layoutInflater.inflate(qp3.fragment_design, viewGroup, false);
        fa1.m6825(inflate, "inflater.inflate(R.layou…design, container, false)");
        return inflate;
    }

    /* JADX WARN: Type inference failed for: r0v0, types: [java.util.LinkedHashMap, java.util.Map<java.lang.Integer, android.view.View>] */
    @Override // okhttp3.internal.p042io.C7290, androidx.fragment.app.Fragment
    public final void onDestroyView() {
        super.onDestroyView();
        this.f23881.clear();
    }

    @Override // androidx.fragment.app.Fragment
    public final void onResume() {
        super.onResume();
        C6814.m16042(LifecycleOwnerKt.getLifecycleScope(this), null, 0, new C9426(null), 3);
    }

    @Override // androidx.fragment.app.Fragment
    public final void onViewCreated(@zu2 View view, @wv2 Bundle bundle) {
        fa1.m6826(view, "view");
        super.onViewCreated(view, bundle);
        ((RecyclerView) m13162(np3.layoutTree)).setLayoutManager(new LinearLayoutManager(getContext()));
        ArrayList<ap5> arrayList = this.f23872;
        Context context = getContext();
        fa1.m6823(context);
        LinearLayout linearLayout = (LinearLayout) m13162(np3.f16233pl);
        fa1.m6825(linearLayout, "pl");
        nj4 nj4Var = new nj4(arrayList, context, linearLayout);
        this.f23880 = nj4Var;
        C9433 c9433 = new C9433();
        Objects.requireNonNull(nj4Var);
        nj4Var.f16112 = c9433;
        nj4 nj4Var2 = this.f23880;
        if (nj4Var2 == null) {
            fa1.m6848("showHelper");
            throw null;
        }
        C9434 c9434 = new C9434();
        Objects.requireNonNull(nj4Var2);
        nj4Var2.f16113 = c9434;
        ((Button) m13162(np3.cancelMove)).setOnClickListener(new View.OnClickListener() { // from class: okhttp3.internal.io.sa
            @Override // android.view.View.OnClickListener
            public final void onClick(View view2) {
                C5452va c5452va = C5452va.this;
                C5452va.C9423 c9423 = C5452va.f23871;
                fa1.m6826(c5452va, "this$0");
                ((LinearLayout) c5452va.m13162(np3.moveBar)).setVisibility(8);
                c5452va.f23874 = null;
            }
        });
        Button button = (Button) m13162(np3.useCopyIn);
        fa1.m6825(button, "useCopyIn");
        C6814.m16048(button, LifecycleOwnerKt.getLifecycleScope(this), new C9435(null));
        Button button2 = (Button) m13162(np3.useCopyAfter);
        fa1.m6825(button2, "useCopyAfter");
        C6814.m16048(button2, LifecycleOwnerKt.getLifecycleScope(this), new C9436(null));
        Button button3 = (Button) m13162(np3.useMoveIn);
        fa1.m6825(button3, "useMoveIn");
        C6814.m16048(button3, LifecycleOwnerKt.getLifecycleScope(this), new C9437(null));
        Button button4 = (Button) m13162(np3.useMoveAfter);
        fa1.m6825(button4, "useMoveAfter");
        C6814.m16048(button4, LifecycleOwnerKt.getLifecycleScope(this), new C9438(null));
        ((Button) m13162(np3.useAddIn)).setOnClickListener(new View.OnClickListener() { // from class: okhttp3.internal.io.ua
            @Override // android.view.View.OnClickListener
            public final void onClick(View view2) {
                C5452va c5452va = C5452va.this;
                C5452va.C9423 c9423 = C5452va.f23871;
                fa1.m6826(c5452va, "this$0");
                uo5 uo5Var = c5452va.f23879;
                if (uo5Var == null) {
                    fa1.m6848("treeAdapter");
                    throw null;
                }
                if (uo5Var.m12944().size() <= 0) {
                    Toast.makeText(c5452va.getContext(), bq3.zero_choose_hint, 0).show();
                    return;
                }
                Context context2 = view2.getContext();
                fa1.m6825(context2, "it.context");
                uo5 uo5Var2 = c5452va.f23879;
                if (uo5Var2 != null) {
                    c5452va.m13168(context2, 0, uo5Var2.m12944());
                } else {
                    fa1.m6848("treeAdapter");
                    throw null;
                }
            }
        });
        final int i = 1;
        ((Button) m13162(np3.useAddAfter)).setOnClickListener(new ViewOnClickListenerC6690(this, i));
        ((Button) m13162(np3.useAttr)).setOnClickListener(new View.OnClickListener() { // from class: okhttp3.internal.io.ta
            @Override // android.view.View.OnClickListener
            public final void onClick(View view2) {
                C5452va c5452va = C5452va.this;
                C5452va.C9423 c9423 = C5452va.f23871;
                fa1.m6826(c5452va, "this$0");
                C6814.m16042(LifecycleOwnerKt.getLifecycleScope(c5452va), null, 0, new C3210eb(view2.getContext(), c5452va, null), 3);
            }
        });
        Button button5 = (Button) m13162(np3.useClear);
        fa1.m6825(button5, "useClear");
        C6814.m16048(button5, LifecycleOwnerKt.getLifecycleScope(this), new C9427(null));
        Button button6 = (Button) m13162(np3.useDeleteOne);
        fa1.m6825(button6, "useDeleteOne");
        C6814.m16048(button6, LifecycleOwnerKt.getLifecycleScope(this), new C9428(null));
        Button button7 = (Button) m13162(np3.useDeleteAll);
        fa1.m6825(button7, "useDeleteAll");
        C6814.m16048(button7, LifecycleOwnerKt.getLifecycleScope(this), new C9429(null));
        ((Button) m13162(np3.useCollect)).setOnClickListener(new sj0(this, i));
        int i2 = 2;
        ((Button) m13162(np3.useUncheck)).setOnClickListener(new y26(this, i2));
        Button button8 = (Button) m13162(np3.useBindVar);
        fa1.m6825(button8, "useBindVar");
        C6814.m16048(button8, LifecycleOwnerKt.getLifecycleScope(this), new C9430(null));
        Button button9 = (Button) m13162(np3.useBindSdcard);
        fa1.m6825(button9, "useBindSdcard");
        C6814.m16048(button9, LifecycleOwnerKt.getLifecycleScope(this), new C9431(null));
        ((Button) m13162(np3.useEvent)).setOnClickListener(new View.OnClickListener() { // from class: okhttp3.internal.io.uj5
            @Override // android.view.View.OnClickListener
            public final void onClick(View view2) {
                switch (i) {
                    case 0:
                        final TimedTaskSettingActivity timedTaskSettingActivity = (TimedTaskSettingActivity) this;
                        TimedTaskSettingActivity.C8208 c8208 = TimedTaskSettingActivity.f34519;
                        fa1.m6826(timedTaskSettingActivity, "this$0");
                        n52 m18056 = TimedTaskSettingActivity.f34520.m18056(((TextView) timedTaskSettingActivity._$_findCachedViewById(mp3.disposableTaskTime)).getText().toString());
                        new TimePickerDialog(timedTaskSettingActivity, new TimePickerDialog.OnTimeSetListener() { // from class: okhttp3.internal.io.tj5
                            @Override // android.app.TimePickerDialog.OnTimeSetListener
                            public final void onTimeSet(TimePicker timePicker, int i3, int i4) {
                                TimedTaskSettingActivity timedTaskSettingActivity2 = TimedTaskSettingActivity.this;
                                TimedTaskSettingActivity.C8208 c82082 = TimedTaskSettingActivity.f34519;
                                fa1.m6826(timedTaskSettingActivity2, "this$0");
                                ((TextView) timedTaskSettingActivity2._$_findCachedViewById(mp3.disposableTaskTime)).setText(TimedTaskSettingActivity.f34520.m18060(new n52(i3, i4)));
                            }
                        }, m18056.m9919(), m18056.m9920(), true).show();
                        return;
                    default:
                        C5452va c5452va = (C5452va) this;
                        C5452va.C9423 c9423 = C5452va.f23871;
                        fa1.m6826(c5452va, "this$0");
                        uo5 uo5Var = c5452va.f23879;
                        if (uo5Var == null) {
                            fa1.m6848("treeAdapter");
                            throw null;
                        }
                        aa5 aa5Var = new aa5(3, uo5Var.m12944());
                        zl4 m16876 = c5452va.m16876();
                        Objects.requireNonNull(m16876);
                        m16876.f27549 = aa5Var;
                        c5452va.m16876().m14585(fi4.EVENT);
                        return;
                }
            }
        });
        Button button10 = (Button) m13162(np3.fresh);
        fa1.m6825(button10, "fresh");
        C6814.m16048(button10, LifecycleOwnerKt.getLifecycleScope(this), new C9432(null));
        ((Button) m13162(np3.change)).setOnClickListener(new uj0(this, i2));
    }

    /* JADX WARN: Type inference failed for: r0v0, types: [java.util.LinkedHashMap, java.util.Map<java.lang.Integer, android.view.View>] */
    @Override // okhttp3.internal.p042io.C7290
    /* renamed from: ޑ */
    public final void mo8446() {
        this.f23881.clear();
    }

    /* JADX WARN: Type inference failed for: r0v0, types: [java.util.LinkedHashMap, java.util.Map, java.util.Map<java.lang.Integer, android.view.View>] */
    @wv2
    /* renamed from: ޔ */
    public final View m13162(int i) {
        View findViewById;
        ?? r0 = this.f23881;
        View view = (View) r0.get(Integer.valueOf(i));
        if (view != null) {
            return view;
        }
        View view2 = getView();
        if (view2 == null || (findViewById = view2.findViewById(i)) == null) {
            return null;
        }
        r0.put(Integer.valueOf(i), findViewById);
        return findViewById;
    }

    /* JADX WARN: Removed duplicated region for block: B:13:0x0232  */
    /* JADX WARN: Removed duplicated region for block: B:22:0x0075 A[LOOP:0: B:22:0x0075->B:56:0x0204, LOOP_START, PHI: r1 r3 r4 r11 r12
  0x0075: PHI (r1v8 java.lang.String) = (r1v5 java.lang.String), (r1v10 java.lang.String) binds: [B:21:0x0073, B:56:0x0204] A[DONT_GENERATE, DONT_INLINE]
  0x0075: PHI (r3v2 okhttp3.internal.io.ஐ) = (r3v0 okhttp3.internal.io.ஐ), (r3v4 okhttp3.internal.io.ஐ) binds: [B:21:0x0073, B:56:0x0204] A[DONT_GENERATE, DONT_INLINE]
  0x0075: PHI (r4v4 java.util.ArrayList<okhttp3.internal.io.ap5>) = (r4v2 java.util.ArrayList<okhttp3.internal.io.ap5>), (r4v7 java.util.ArrayList<okhttp3.internal.io.ap5>) binds: [B:21:0x0073, B:56:0x0204] A[DONT_GENERATE, DONT_INLINE]
  0x0075: PHI (r11v2 int) = (r11v1 int), (r11v5 int) binds: [B:21:0x0073, B:56:0x0204] A[DONT_GENERATE, DONT_INLINE]
  0x0075: PHI (r12v1 int) = (r12v0 int), (r12v3 int) binds: [B:21:0x0073, B:56:0x0204] A[DONT_GENERATE, DONT_INLINE]] */
    /* JADX WARN: Removed duplicated region for block: B:48:0x01bc  */
    /* JADX WARN: Removed duplicated region for block: B:60:0x0226 A[RETURN] */
    /* JADX WARN: Removed duplicated region for block: B:61:0x0227  */
    /* JADX WARN: Removed duplicated region for block: B:63:0x0196 A[SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:71:0x0210  */
    /* JADX WARN: Removed duplicated region for block: B:72:0x004c  */
    /* JADX WARN: Removed duplicated region for block: B:8:0x0026  */
    /* renamed from: ޠ */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final java.lang.Object m13163(java.util.ArrayList<okhttp3.internal.p042io.ap5> r21, boolean r22, okhttp3.internal.p042io.InterfaceC7155<? super okhttp3.internal.p042io.lx5> r23) {
        /*
            Method dump skipped, instructions count: 603
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: okhttp3.internal.p042io.C5452va.m13163(java.util.ArrayList, boolean, okhttp3.internal.io.ৡ):java.lang.Object");
    }

    /* renamed from: ޡ */
    public final void m13164() {
        if (this.f23873) {
            nj4 nj4Var = this.f23880;
            if (nj4Var == null) {
                fa1.m6848("showHelper");
                throw null;
            }
            nj4Var.m10048(this.f23872);
        }
        if (this.f23872.size() > 0) {
            uo5 uo5Var = new uo5(this, this.f23872);
            this.f23879 = uo5Var;
            uo5Var.f23402 = new C9425();
            RecyclerView recyclerView = (RecyclerView) m13162(np3.layoutTree);
            uo5 uo5Var2 = this.f23879;
            if (uo5Var2 == null) {
                fa1.m6848("treeAdapter");
                throw null;
            }
            recyclerView.setAdapter(uo5Var2);
            uo5 uo5Var3 = this.f23879;
            if (uo5Var3 == null) {
                fa1.m6848("treeAdapter");
                throw null;
            }
            ap5 ap5Var = this.f23872.get(0);
            fa1.m6825(ap5Var, "list[0]");
            uo5Var3.m12945(ap5Var);
        }
    }

    /* renamed from: ޢ */
    public final void m13165() {
        uo5 uo5Var = this.f23879;
        if (uo5Var == null) {
            fa1.m6848("treeAdapter");
            throw null;
        }
        ArrayList<ap5> m12944 = uo5Var.m12944();
        Iterator<ap5> it = m12944.iterator();
        String str = "  [";
        while (it.hasNext()) {
            ap5 next = it.next();
            StringBuilder m9240 = lf2.m9240(str);
            Object[] array = new oy3(" ").m10541(next.m4872()).toArray(new String[0]);
            fa1.m6824(array, "null cannot be cast to non-null type kotlin.Array<T of kotlin.collections.ArraysKt__ArraysJVMKt.toTypedArray>");
            m9240.append(((String[]) array)[0]);
            str = xe2.m13920(m9240.toString(), ' ');
        }
        String m13920 = xe2.m13920(str, ']');
        ((TextView) m13162(np3.status)).setText(getString(bq3.had_choose) + ':' + m12944.size() + m13920);
        if (m12944.size() > 0) {
            aa5 aa5Var = m16876().f27549;
            Objects.requireNonNull(aa5Var);
            aa5Var.f6259 = m12944;
        }
    }

    /* renamed from: ޣ */
    public final void m13166() {
        int i;
        int i2 = 0;
        while (i2 < this.f23872.size()) {
            if (this.f23872.get(i2).f6614) {
                int size = this.f23872.size();
                i = 0;
                for (int i3 = i2 + 1; i3 < size && this.f23872.get(i2).f6616 < this.f23872.get(i3).f6616; i3++) {
                    this.f23872.get(i3).f6614 = true;
                    i++;
                }
            } else {
                i = 0;
            }
            i2 = i2 + i + 1;
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:15:0x0031  */
    /* JADX WARN: Removed duplicated region for block: B:8:0x0021  */
    /* renamed from: ޤ */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final java.lang.Object m13167(java.lang.String r5, okhttp3.internal.p042io.InterfaceC7155<? super okhttp3.internal.p042io.lx5> r6) {
        /*
            r4 = this;
            boolean r0 = r6 instanceof okhttp3.internal.p042io.C5452va.C9439
            if (r0 == 0) goto L13
            r0 = r6
            okhttp3.internal.io.va$މ r0 = (okhttp3.internal.p042io.C5452va.C9439) r0
            int r1 = r0.f23919
            r2 = -2147483648(0xffffffff80000000, float:-0.0)
            r3 = r1 & r2
            if (r3 == 0) goto L13
            int r1 = r1 - r2
            r0.f23919 = r1
            goto L18
        L13:
            okhttp3.internal.io.va$މ r0 = new okhttp3.internal.io.va$މ
            r0.<init>(r6)
        L18:
            java.lang.Object r6 = r0.f23917
            okhttp3.internal.io.ஐ r1 = okhttp3.internal.p042io.EnumC7329.COROUTINE_SUSPENDED
            int r2 = r0.f23919
            r3 = 1
            if (r2 == 0) goto L31
            if (r2 != r3) goto L29
            okhttp3.internal.io.va r5 = r0.f23916
            okhttp3.internal.p042io.C4350o9.m10270(r6)
            goto L44
        L29:
            java.lang.IllegalStateException r5 = new java.lang.IllegalStateException
            java.lang.String r6 = "call to 'resume' before 'invoke' with coroutine"
            r5.<init>(r6)
            throw r5
        L31:
            okhttp3.internal.p042io.C4350o9.m10270(r6)
            okhttp3.internal.io.zl4 r6 = r4.m16876()
            r0.f23916 = r4
            r0.f23919 = r3
            java.lang.Object r5 = r6.m14584(r5, r0)
            if (r5 != r1) goto L43
            return r1
        L43:
            r5 = r4
        L44:
            r5.f23877 = r3
            okhttp3.internal.io.lx5 r5 = okhttp3.internal.p042io.lx5.f14876
            return r5
        */
        throw new UnsupportedOperationException("Method not decompiled: okhttp3.internal.p042io.C5452va.m13167(java.lang.String, okhttp3.internal.io.ৡ):java.lang.Object");
    }

    /* renamed from: ޥ */
    public final void m13168(Context context, int i, ArrayList<ap5> arrayList) {
        eb6.C3211 c3211 = eb6.f9148;
        InputStream openRawResource = context.getResources().openRawResource(tp3.widgets);
        try {
            Element documentElement = DocumentBuilderFactory.newInstance().newDocumentBuilder().parse(openRawResource).getDocumentElement();
            fa1.m6825(documentElement, "document.documentElement");
            eb6 m6421 = c3211.m6421(documentElement);
            C7208.m16733(openRawResource, null);
            cb6 cb6Var = new cb6(context, m6421);
            cb6Var.f7756 = new C9440(i, arrayList);
            cb6Var.f7755.show();
        } finally {
        }
    }

    /* renamed from: ޱ */
    public final void m13169(int i) {
        this.f23874 = this.f23872.get(i);
        ((LinearLayout) m13162(np3.moveBar)).setVisibility(0);
        TextView textView = (TextView) m13162(np3.moveTip);
        StringBuilder sb = new StringBuilder();
        sb.append(getString(bq3.move_tip_start));
        ap5 ap5Var = this.f23874;
        sb.append(ap5Var != null ? ap5Var.f6617 : null);
        sb.append(getString(bq3.move_tip_end));
        textView.setText(sb.toString());
    }

    /* renamed from: ࡠ */
    public final ya6 m13170(bb6 bb6Var) {
        StringBuilder m16088;
        int i = !fa1.m6818(bb6Var.f7043, ExifInterface.GPS_MEASUREMENT_INTERRUPTED) ? 1 : 0;
        String str = bb6Var.f7040;
        String str2 = bb6Var.f7041;
        if (fa1.m6818(bb6Var.f7043, "VG")) {
            m16088 = C6858.m16088('<');
            m16088.append(bb6Var.f7040);
            m16088.append('>');
        } else {
            m16088 = C6858.m16088('<');
            m16088.append(bb6Var.f7040);
            m16088.append("/>");
        }
        return new ya6(str, str2, i, m16088.toString(), lg0.m9246(lf2.m9240("</"), bb6Var.f7040, '>'));
    }
}
