package okhttp3.internal.p042io;

import java.util.ArrayList;
import java.util.Comparator;

/* renamed from: okhttp3.internal.io.vy */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5530vy {

    /* renamed from: Ϳ */
    public C9456 f24531 = new C9456();

    /* renamed from: Ԩ */
    public ArrayList<InterfaceC5369uy> f24532 = new ArrayList<>();

    /* renamed from: ԩ */
    public ArrayList<InterfaceC5629wy> f24533 = new ArrayList<>();

    /* renamed from: okhttp3.internal.io.vy$Ϳ, reason: contains not printable characters */
    public static class C9456 {

        /* renamed from: Ԩ */
        public boolean f24535;

        /* renamed from: ԩ */
        public boolean f24536;

        /* renamed from: Ϳ */
        public int f24534 = 16;

        /* renamed from: Ԫ */
        public int f24537 = 16;
    }

    /* renamed from: Ϳ */
    public final Comparator<InterfaceC5369uy> m13430(int i) {
        if (i == 16) {
            return C3502gz.f11190;
        }
        if (i == 32) {
            return C3502gz.f11192;
        }
        if (i == 48) {
            return C3502gz.f11193;
        }
        if (i == 64) {
            return C3502gz.f11191;
        }
        throw new IllegalArgumentException(k76.m8852("unknown type ", i));
    }
}
