package android.view.compose;

import android.content.Context;
import android.content.ContextWrapper;
import android.view.result.ActivityResultRegistryOwner;
import com.stardust.autojs.engine.RhinoJavaScriptEngine;
import kotlin.Metadata;
import okhttp3.internal.p042io.C7313;
import okhttp3.internal.p042io.C7771;
import okhttp3.internal.p042io.InterfaceC6968;
import okhttp3.internal.p042io.InterfaceC7452;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.fz4;
import okhttp3.internal.p042io.ho3;
import okhttp3.internal.p042io.jo3;
import okhttp3.internal.p042io.k55;
import okhttp3.internal.p042io.wv2;
import okhttp3.internal.p042io.yo1;
import okhttp3.internal.p042io.zu2;
import org.mozilla.javascript.optimizer.Codegen;

@fz4
@Metadata(m4113bv = {}, m4114d1 = {"\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\bÇ\u0002\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\t\u0010\nJ\u0019\u0010\u0005\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H\u0086\u0004R\u0013\u0010\b\u001a\u0004\u0018\u00010\u00028G¢\u0006\u0006\u001a\u0004\b\u0006\u0010\u0007¨\u0006\u000b"}, m4115d2 = {"Landroidx/activity/compose/LocalActivityResultRegistryOwner;", "", "Landroidx/activity/result/ActivityResultRegistryOwner;", "registryOwner", "Lokhttp3/internal/io/jo3;", "provides", "getCurrent", "(Lokhttp3/internal/io/ࡊ;I)Landroidx/activity/result/ActivityResultRegistryOwner;", "current", RhinoJavaScriptEngine.SOURCE_NAME_INIT, Codegen.TEMPLATE_LITERAL_INIT_METHOD_SIGNATURE, "activity-compose_release"}, m4116k = 1, m4117mv = {1, 6, 0})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class LocalActivityResultRegistryOwner {
    public static final int $stable = 0;

    @zu2
    public static final LocalActivityResultRegistryOwner INSTANCE = new LocalActivityResultRegistryOwner();

    @zu2
    private static final ho3<ActivityResultRegistryOwner> LocalComposition;

    static {
        ho3<ActivityResultRegistryOwner> m17400;
        m17400 = C7771.m17400(k55.f13609, LocalActivityResultRegistryOwner$LocalComposition$1.INSTANCE);
        LocalComposition = m17400;
    }

    private LocalActivityResultRegistryOwner() {
    }

    @yo1
    @wv2
    @InterfaceC7452
    public final ActivityResultRegistryOwner getCurrent(@wv2 InterfaceC6968 interfaceC6968, int i) {
        interfaceC6968.mo16268(1418020823);
        ActivityResultRegistryOwner activityResultRegistryOwner = (ActivityResultRegistryOwner) interfaceC6968.mo16276(LocalComposition);
        if (activityResultRegistryOwner == null) {
            Object obj = (Context) interfaceC6968.mo16276(C7313.f31547);
            while (true) {
                if (!(obj instanceof ContextWrapper)) {
                    obj = null;
                    break;
                }
                if (obj instanceof ActivityResultRegistryOwner) {
                    break;
                }
                obj = ((ContextWrapper) obj).getBaseContext();
                fa1.m6825(obj, "innerContext.baseContext");
            }
            activityResultRegistryOwner = (ActivityResultRegistryOwner) obj;
        }
        interfaceC6968.mo16300();
        return activityResultRegistryOwner;
    }

    @zu2
    public final jo3<ActivityResultRegistryOwner> provides(@zu2 ActivityResultRegistryOwner registryOwner) {
        fa1.m6826(registryOwner, "registryOwner");
        return LocalComposition.m7927(registryOwner);
    }
}
