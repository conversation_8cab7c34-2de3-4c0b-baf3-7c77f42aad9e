package androidx.core.transition;

import android.transition.Transition;
import kotlin.Metadata;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.ph0;
import okhttp3.internal.p042io.zu2;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000\u0017\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006*\u0001\u0000\b\n\u0018\u00002\u00020\u0001J\u0010\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H\u0016J\u0010\u0010\u0006\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H\u0016J\u0010\u0010\u0007\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H\u0016J\u0010\u0010\b\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H\u0016J\u0010\u0010\t\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H\u0016¨\u0006\n"}, m4115d2 = {"androidx/core/transition/TransitionKt$addListener$listener$1", "Landroid/transition/Transition$TransitionListener;", "Landroid/transition/Transition;", "transition", "Lokhttp3/internal/io/lx5;", "onTransitionEnd", "onTransitionResume", "onTransitionPause", "onTransitionCancel", "onTransitionStart", "core-ktx_release"}, m4116k = 1, m4117mv = {1, 6, 0})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class TransitionKt$addListener$listener$1 implements Transition.TransitionListener {
    public final /* synthetic */ ph0<Transition, lx5> $onCancel;
    public final /* synthetic */ ph0<Transition, lx5> $onEnd;
    public final /* synthetic */ ph0<Transition, lx5> $onPause;
    public final /* synthetic */ ph0<Transition, lx5> $onResume;
    public final /* synthetic */ ph0<Transition, lx5> $onStart;

    /* JADX WARN: Multi-variable type inference failed */
    public TransitionKt$addListener$listener$1(ph0<? super Transition, lx5> ph0Var, ph0<? super Transition, lx5> ph0Var2, ph0<? super Transition, lx5> ph0Var3, ph0<? super Transition, lx5> ph0Var4, ph0<? super Transition, lx5> ph0Var5) {
        this.$onEnd = ph0Var;
        this.$onResume = ph0Var2;
        this.$onPause = ph0Var3;
        this.$onCancel = ph0Var4;
        this.$onStart = ph0Var5;
    }

    @Override // android.transition.Transition.TransitionListener
    public void onTransitionCancel(@zu2 Transition transition) {
        fa1.m6826(transition, "transition");
        this.$onCancel.invoke(transition);
    }

    @Override // android.transition.Transition.TransitionListener
    public void onTransitionEnd(@zu2 Transition transition) {
        fa1.m6826(transition, "transition");
        this.$onEnd.invoke(transition);
    }

    @Override // android.transition.Transition.TransitionListener
    public void onTransitionPause(@zu2 Transition transition) {
        fa1.m6826(transition, "transition");
        this.$onPause.invoke(transition);
    }

    @Override // android.transition.Transition.TransitionListener
    public void onTransitionResume(@zu2 Transition transition) {
        fa1.m6826(transition, "transition");
        this.$onResume.invoke(transition);
    }

    @Override // android.transition.Transition.TransitionListener
    public void onTransitionStart(@zu2 Transition transition) {
        fa1.m6826(transition, "transition");
        this.$onStart.invoke(transition);
    }
}
