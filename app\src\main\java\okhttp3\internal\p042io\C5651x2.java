package okhttp3.internal.p042io;

@InterfaceC4988s2(m11868c = "org.autojs.autojs.debugserver.rpc.DebugRpcService", m11869f = "DebugRpcService.kt", m11870l = {166}, m11871m = "takeScreenshot")
/* renamed from: okhttp3.internal.io.x2 */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5651x2 extends AbstractC6644 {

    /* renamed from: ၥ */
    public /* synthetic */ Object f25312;

    /* renamed from: ၦ */
    public final /* synthetic */ C5265u2 f25313;

    /* renamed from: ၮ */
    public int f25314;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public C5651x2(C5265u2 c5265u2, InterfaceC7155<? super C5651x2> interfaceC7155) {
        super(interfaceC7155);
        this.f25313 = c5265u2;
    }

    @Override // okhttp3.internal.p042io.AbstractC7853
    @wv2
    public final Object invokeSuspend(@zu2 Object obj) {
        this.f25312 = obj;
        this.f25314 |= Integer.MIN_VALUE;
        return C5265u2.m12667(this.f25313, null, this);
    }
}
