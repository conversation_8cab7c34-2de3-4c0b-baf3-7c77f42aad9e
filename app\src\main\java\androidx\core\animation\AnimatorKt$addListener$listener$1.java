package androidx.core.animation;

import android.animation.Animator;
import kotlin.Metadata;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.ph0;
import okhttp3.internal.p042io.zu2;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000\u0017\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005*\u0001\u0000\b\n\u0018\u00002\u00020\u0001J\u0010\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H\u0016J\u0010\u0010\u0006\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H\u0016J\u0010\u0010\u0007\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H\u0016J\u0010\u0010\b\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H\u0016¨\u0006\t"}, m4115d2 = {"androidx/core/animation/AnimatorKt$addListener$listener$1", "Landroid/animation/Animator$AnimatorListener;", "Landroid/animation/Animator;", "animator", "Lokhttp3/internal/io/lx5;", "onAnimationRepeat", "onAnimationEnd", "onAnimationCancel", "onAnimationStart", "core-ktx_release"}, m4116k = 1, m4117mv = {1, 6, 0})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class AnimatorKt$addListener$listener$1 implements Animator.AnimatorListener {
    public final /* synthetic */ ph0<Animator, lx5> $onCancel;
    public final /* synthetic */ ph0<Animator, lx5> $onEnd;
    public final /* synthetic */ ph0<Animator, lx5> $onRepeat;
    public final /* synthetic */ ph0<Animator, lx5> $onStart;

    /* JADX WARN: Multi-variable type inference failed */
    public AnimatorKt$addListener$listener$1(ph0<? super Animator, lx5> ph0Var, ph0<? super Animator, lx5> ph0Var2, ph0<? super Animator, lx5> ph0Var3, ph0<? super Animator, lx5> ph0Var4) {
        this.$onRepeat = ph0Var;
        this.$onEnd = ph0Var2;
        this.$onCancel = ph0Var3;
        this.$onStart = ph0Var4;
    }

    @Override // android.animation.Animator.AnimatorListener
    public void onAnimationCancel(@zu2 Animator animator) {
        fa1.m6826(animator, "animator");
        this.$onCancel.invoke(animator);
    }

    @Override // android.animation.Animator.AnimatorListener
    public void onAnimationEnd(@zu2 Animator animator) {
        fa1.m6826(animator, "animator");
        this.$onEnd.invoke(animator);
    }

    @Override // android.animation.Animator.AnimatorListener
    public void onAnimationRepeat(@zu2 Animator animator) {
        fa1.m6826(animator, "animator");
        this.$onRepeat.invoke(animator);
    }

    @Override // android.animation.Animator.AnimatorListener
    public void onAnimationStart(@zu2 Animator animator) {
        fa1.m6826(animator, "animator");
        this.$onStart.invoke(animator);
    }
}
