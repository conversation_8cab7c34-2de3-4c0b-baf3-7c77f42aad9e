package okhttp3.internal.p042io;

import androidx.annotation.NonNull;
import java.security.MessageDigest;

/* renamed from: okhttp3.internal.io.w */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes3.dex */
public final class C5542w implements ps1 {

    /* renamed from: Ԩ */
    public final ps1 f24570;

    /* renamed from: ԩ */
    public final ps1 f24571;

    public C5542w(ps1 ps1Var, ps1 ps1Var2) {
        this.f24570 = ps1Var;
        this.f24571 = ps1Var2;
    }

    @Override // okhttp3.internal.p042io.ps1
    public final boolean equals(Object obj) {
        if (!(obj instanceof C5542w)) {
            return false;
        }
        C5542w c5542w = (C5542w) obj;
        return this.f24570.equals(c5542w.f24570) && this.f24571.equals(c5542w.f24571);
    }

    @Override // okhttp3.internal.p042io.ps1
    public final int hashCode() {
        return this.f24571.hashCode() + (this.f24570.hashCode() * 31);
    }

    public final String toString() {
        StringBuilder m9240 = lf2.m9240("DataCacheKey{sourceKey=");
        m9240.append(this.f24570);
        m9240.append(", signature=");
        m9240.append(this.f24571);
        m9240.append('}');
        return m9240.toString();
    }

    @Override // okhttp3.internal.p042io.ps1
    /* renamed from: Ϳ */
    public final void mo5914(@NonNull MessageDigest messageDigest) {
        this.f24570.mo5914(messageDigest);
        this.f24571.mo5914(messageDigest);
    }
}
