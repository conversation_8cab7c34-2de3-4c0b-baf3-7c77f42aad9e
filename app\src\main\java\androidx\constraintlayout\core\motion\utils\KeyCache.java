package androidx.constraintlayout.core.motion.utils;

import java.util.Arrays;
import java.util.HashMap;

/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public class KeyCache {
    public HashMap<Object, HashMap<String, float[]>> map = new HashMap<>();

    public float getFloatValue(Object obj, String str, int i) {
        HashMap<String, float[]> hashMap;
        float[] fArr;
        if (this.map.containsKey(obj) && (hashMap = this.map.get(obj)) != null && hashMap.containsKey(str) && (fArr = hashMap.get(str)) != null && fArr.length > i) {
            return fArr[i];
        }
        return Float.NaN;
    }

    public void setFloatValue(Object obj, String str, int i, float f) {
        HashMap<String, float[]> hashMap;
        if (this.map.containsKey(obj)) {
            hashMap = this.map.get(obj);
            if (hashMap == null) {
                hashMap = new HashMap<>();
            }
            if (hashMap.containsKey(str)) {
                float[] fArr = hashMap.get(str);
                if (fArr == null) {
                    fArr = new float[0];
                }
                if (fArr.length <= i) {
                    fArr = Arrays.copyOf(fArr, i + 1);
                }
                fArr[i] = f;
                hashMap.put(str, fArr);
                return;
            }
            float[] fArr2 = new float[i + 1];
            fArr2[i] = f;
            hashMap.put(str, fArr2);
        } else {
            hashMap = new HashMap<>();
            float[] fArr3 = new float[i + 1];
            fArr3[i] = f;
            hashMap.put(str, fArr3);
        }
        this.map.put(obj, hashMap);
    }
}
