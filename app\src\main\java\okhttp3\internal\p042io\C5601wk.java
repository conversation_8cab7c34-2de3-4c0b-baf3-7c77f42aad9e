package okhttp3.internal.p042io;

import android.graphics.Bitmap;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.bumptech.glide.load.resource.gif.C0956;

/* renamed from: okhttp3.internal.io.wk */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes3.dex */
public final class C5601wk implements q34<Drawable, byte[]> {

    /* renamed from: Ϳ */
    public final InterfaceC6823 f25024;

    /* renamed from: Ԩ */
    public final q34<Bitmap, byte[]> f25025;

    /* renamed from: ԩ */
    public final q34<C0956, byte[]> f25026;

    public C5601wk(@NonNull InterfaceC6823 interfaceC6823, @NonNull q34<Bitmap, byte[]> q34Var, @NonNull q34<C0956, byte[]> q34Var2) {
        this.f25024 = interfaceC6823;
        this.f25025 = q34Var;
        this.f25026 = q34Var2;
    }

    @Override // okhttp3.internal.p042io.q34
    @Nullable
    /* renamed from: Ϳ */
    public final a34<byte[]> mo10985(@NonNull a34<Drawable> a34Var, @NonNull s23 s23Var) {
        Drawable drawable = a34Var.get();
        if (drawable instanceof BitmapDrawable) {
            return this.f25025.mo10985(C6412.m15324(((BitmapDrawable) drawable).getBitmap(), this.f25024), s23Var);
        }
        if (drawable instanceof C0956) {
            return this.f25026.mo10985(a34Var, s23Var);
        }
        return null;
    }
}
