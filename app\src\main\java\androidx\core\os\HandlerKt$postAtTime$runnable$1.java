package androidx.core.os;

import kotlin.Metadata;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.nh0;
import org.mozilla.javascript.optimizer.Codegen;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000\b\n\u0002\u0018\u0002\n\u0002\b\u0003\u0010\u0003\u001a\u00020\u0000H\n¢\u0006\u0004\b\u0001\u0010\u0002"}, m4115d2 = {"Lokhttp3/internal/io/lx5;", "run", Codegen.TEMPLATE_LITERAL_INIT_METHOD_SIGNATURE, "<anonymous>"}, m4116k = 3, m4117mv = {1, 6, 0})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class HandlerKt$postAtTime$runnable$1 implements Runnable {
    public final /* synthetic */ nh0<lx5> $action;

    public HandlerKt$postAtTime$runnable$1(nh0<lx5> nh0Var) {
        this.$action = nh0Var;
    }

    @Override // java.lang.Runnable
    public final void run() {
        this.$action.invoke();
    }
}
