package okhttp3.internal.p042io;

import okhttp3.internal.p042io.InterfaceC6710;

/* renamed from: okhttp3.internal.io.wi */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5592wi implements InterfaceC6710 {

    /* renamed from: ၥ */
    @ro1
    @zu2
    public final Throwable f24989;

    /* renamed from: ၦ */
    public final /* synthetic */ InterfaceC6710 f24990;

    public C5592wi(@zu2 Throwable th, @zu2 InterfaceC6710 interfaceC6710) {
        this.f24989 = th;
        this.f24990 = interfaceC6710;
    }

    @Override // okhttp3.internal.p042io.InterfaceC6710
    public final <R> R fold(R r, @zu2 di0<? super R, ? super InterfaceC6710.InterfaceC9868, ? extends R> di0Var) {
        return (R) this.f24990.fold(r, di0Var);
    }

    @Override // okhttp3.internal.p042io.InterfaceC6710
    @wv2
    public final <E extends InterfaceC6710.InterfaceC9868> E get(@zu2 InterfaceC6710.InterfaceC9870<E> interfaceC9870) {
        return (E) this.f24990.get(interfaceC9870);
    }

    @Override // okhttp3.internal.p042io.InterfaceC6710
    @zu2
    public final InterfaceC6710 minusKey(@zu2 InterfaceC6710.InterfaceC9870<?> interfaceC9870) {
        return this.f24990.minusKey(interfaceC9870);
    }

    @Override // okhttp3.internal.p042io.InterfaceC6710
    @zu2
    public final InterfaceC6710 plus(@zu2 InterfaceC6710 interfaceC6710) {
        return this.f24990.plus(interfaceC6710);
    }
}
