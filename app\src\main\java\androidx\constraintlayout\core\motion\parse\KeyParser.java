package androidx.constraintlayout.core.motion.parse;

import androidx.constraintlayout.core.motion.utils.TypedBundle;
import androidx.constraintlayout.core.parser.CLElement;
import androidx.constraintlayout.core.parser.CLKey;
import androidx.constraintlayout.core.parser.CLObject;
import androidx.constraintlayout.core.parser.CLParser;
import androidx.constraintlayout.core.parser.CLParsingException;
import java.io.PrintStream;
import okhttp3.internal.p042io.pu5;

/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public class KeyParser {

    public interface DataType {
        int get(int i);
    }

    public interface Ids {
        int get(String str);
    }

    public static void main(String[] strArr) {
        parseAttributes("{frame:22,\ntarget:'widget1',\neasing:'easeIn',\ncurveFit:'spline',\nprogress:0.3,\nalpha:0.2,\nelevation:0.7,\nrotationZ:23,\nrotationX:25.0,\nrotationY:27.0,\npivotX:15,\npivotY:17,\npivotTarget:'32',\npathRotate:23,\nscaleX:0.5,\nscaleY:0.7,\ntranslationX:5,\ntranslationY:7,\ntranslationZ:11,\n}");
    }

    private static TypedBundle parse(String str, Ids ids, DataType dataType) {
        PrintStream printStream;
        StringBuilder sb;
        TypedBundle typedBundle = new TypedBundle();
        try {
            CLObject parse = CLParser.parse(str);
            int size = parse.size();
            for (int i = 0; i < size; i++) {
                CLKey cLKey = (CLKey) parse.get(i);
                String content = cLKey.content();
                CLElement value = cLKey.getValue();
                int i2 = ids.get(content);
                if (i2 == -1) {
                    System.err.println("unknown type " + content);
                } else {
                    int i3 = dataType.get(i2);
                    if (i3 != 1) {
                        if (i3 == 2) {
                            typedBundle.add(i2, value.getInt());
                            printStream = System.out;
                            sb = new StringBuilder();
                            sb.append("parse ");
                            sb.append(content);
                            sb.append(" INT_MASK > ");
                            sb.append(value.getInt());
                        } else if (i3 == 4) {
                            typedBundle.add(i2, value.getFloat());
                            printStream = System.out;
                            sb = new StringBuilder();
                            sb.append("parse ");
                            sb.append(content);
                            sb.append(" FLOAT_MASK > ");
                            sb.append(value.getFloat());
                        } else if (i3 == 8) {
                            typedBundle.add(i2, value.content());
                            printStream = System.out;
                            sb = new StringBuilder();
                            sb.append("parse ");
                            sb.append(content);
                            sb.append(" STRING_MASK > ");
                            sb.append(value.content());
                        }
                        printStream.println(sb.toString());
                    } else {
                        typedBundle.add(i2, parse.getBoolean(i));
                    }
                }
            }
        } catch (CLParsingException unused) {
        }
        return typedBundle;
    }

    public static TypedBundle parseAttributes(String str) {
        return parse(str, new Ids() { // from class: androidx.constraintlayout.core.motion.parse.Ԩ
            @Override // androidx.constraintlayout.core.motion.parse.KeyParser.Ids
            public final int get(String str2) {
                return pu5.m10893(str2);
            }
        }, new DataType() { // from class: androidx.constraintlayout.core.motion.parse.Ϳ
            @Override // androidx.constraintlayout.core.motion.parse.KeyParser.DataType
            public final int get(int i) {
                return pu5.m10894(i);
            }
        });
    }
}
