package androidx.constraintlayout.widget;

/* renamed from: androidx.constraintlayout.widget.R */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class C0261R {

    /* renamed from: androidx.constraintlayout.widget.R$anim */
    public static final class anim {
        public static final int abc_fade_in = 2130771968;
        public static final int abc_fade_out = 2130771969;
        public static final int abc_grow_fade_in_from_bottom = 2130771970;
        public static final int abc_popup_enter = 2130771971;
        public static final int abc_popup_exit = 2130771972;
        public static final int abc_shrink_fade_out_from_bottom = 2130771973;
        public static final int abc_slide_in_bottom = 2130771974;
        public static final int abc_slide_in_top = 2130771975;
        public static final int abc_slide_out_bottom = 2130771976;
        public static final int abc_slide_out_top = 2130771977;
        public static final int abc_tooltip_enter = 2130771978;
        public static final int abc_tooltip_exit = 2130771979;
        public static final int btn_checkbox_to_checked_box_inner_merged_animation = 2130771981;
        public static final int btn_checkbox_to_checked_box_outer_merged_animation = 2130771982;
        public static final int btn_checkbox_to_checked_icon_null_animation = 2130771983;
        public static final int btn_checkbox_to_unchecked_box_inner_merged_animation = 2130771984;
        public static final int btn_checkbox_to_unchecked_check_path_merged_animation = 2130771985;
        public static final int btn_checkbox_to_unchecked_icon_null_animation = 2130771986;
        public static final int btn_radio_to_off_mtrl_dot_group_animation = 2130771987;
        public static final int btn_radio_to_off_mtrl_ring_outer_animation = 2130771988;
        public static final int btn_radio_to_off_mtrl_ring_outer_path_animation = 2130771989;
        public static final int btn_radio_to_on_mtrl_dot_group_animation = 2130771990;
        public static final int btn_radio_to_on_mtrl_ring_outer_animation = 2130771991;
        public static final int btn_radio_to_on_mtrl_ring_outer_path_animation = 2130771992;

        private anim() {
        }
    }

    /* renamed from: androidx.constraintlayout.widget.R$attr */
    public static final class attr {
        public static final int SharedValue = 2130968576;
        public static final int SharedValueId = 2130968577;
        public static final int actionBarDivider = 2130968578;
        public static final int actionBarItemBackground = 2130968579;
        public static final int actionBarPopupTheme = 2130968580;
        public static final int actionBarSize = 2130968581;
        public static final int actionBarSplitStyle = 2130968582;
        public static final int actionBarStyle = 2130968583;
        public static final int actionBarTabBarStyle = 2130968584;
        public static final int actionBarTabStyle = 2130968585;
        public static final int actionBarTabTextStyle = 2130968586;
        public static final int actionBarTheme = 2130968587;
        public static final int actionBarWidgetTheme = 2130968588;
        public static final int actionButtonStyle = 2130968589;
        public static final int actionDropDownStyle = 2130968590;
        public static final int actionLayout = 2130968591;
        public static final int actionMenuTextAppearance = 2130968592;
        public static final int actionMenuTextColor = 2130968593;
        public static final int actionModeBackground = 2130968594;
        public static final int actionModeCloseButtonStyle = 2130968595;
        public static final int actionModeCloseDrawable = 2130968597;
        public static final int actionModeCopyDrawable = 2130968598;
        public static final int actionModeCutDrawable = 2130968599;
        public static final int actionModeFindDrawable = 2130968600;
        public static final int actionModePasteDrawable = 2130968601;
        public static final int actionModePopupWindowStyle = 2130968602;
        public static final int actionModeSelectAllDrawable = **********;
        public static final int actionModeShareDrawable = **********;
        public static final int actionModeSplitBackground = **********;
        public static final int actionModeStyle = **********;
        public static final int actionModeWebSearchDrawable = **********;
        public static final int actionOverflowButtonStyle = **********;
        public static final int actionOverflowMenuStyle = **********;
        public static final int actionProviderClass = **********;
        public static final int actionViewClass = **********;
        public static final int activityChooserViewStyle = **********;
        public static final int alertDialogButtonGroupStyle = **********;
        public static final int alertDialogCenterButtons = **********;
        public static final int alertDialogStyle = **********;
        public static final int alertDialogTheme = **********;
        public static final int allowStacking = **********;
        public static final int alpha = **********;
        public static final int alphabeticModifiers = **********;
        public static final int altSrc = **********;
        public static final int animateCircleAngleTo = **********;
        public static final int animateRelativeTo = **********;
        public static final int applyMotionScene = **********;
        public static final int arcMode = **********;
        public static final int arrowHeadLength = **********;
        public static final int arrowShaftLength = 2130968648;
        public static final int attributeName = 2130968649;
        public static final int autoCompleteMode = 2130968650;
        public static final int autoCompleteTextViewStyle = 2130968651;
        public static final int autoSizeMaxTextSize = 2130968652;
        public static final int autoSizeMinTextSize = 2130968653;
        public static final int autoSizePresetSizes = 2130968654;
        public static final int autoSizeStepGranularity = 2130968655;
        public static final int autoSizeTextType = 2130968656;
        public static final int autoTransition = 2130968657;
        public static final int background = 2130968658;
        public static final int backgroundSplit = 2130968665;
        public static final int backgroundStacked = 2130968666;
        public static final int backgroundTint = 2130968667;
        public static final int backgroundTintMode = 2130968668;
        public static final int barLength = 2130968675;
        public static final int barrierAllowsGoneWidgets = 2130968676;
        public static final int barrierDirection = 2130968677;
        public static final int barrierMargin = 2130968678;
        public static final int blendSrc = 2130968690;
        public static final int borderRound = 2130968691;
        public static final int borderRoundPercent = 2130968692;
        public static final int borderlessButtonStyle = 2130968694;
        public static final int brightness = 2130968712;
        public static final int buttonBarButtonStyle = 2130968713;
        public static final int buttonBarNegativeButtonStyle = 2130968714;
        public static final int buttonBarNeutralButtonStyle = 2130968715;
        public static final int buttonBarPositiveButtonStyle = 2130968716;
        public static final int buttonBarStyle = 2130968717;
        public static final int buttonCompat = 2130968718;
        public static final int buttonGravity = 2130968719;
        public static final int buttonIconDimen = 2130968721;
        public static final int buttonPanelSideLayout = 2130968724;
        public static final int buttonStyle = 2130968725;
        public static final int buttonStyleSmall = 2130968726;
        public static final int buttonTint = 2130968727;
        public static final int buttonTintMode = 2130968728;
        public static final int carousel_backwardTransition = 2130968740;
        public static final int carousel_emptyViewsBehavior = 2130968741;
        public static final int carousel_firstView = 2130968742;
        public static final int carousel_forwardTransition = 2130968743;
        public static final int carousel_infinite = 2130968744;
        public static final int carousel_nextState = 2130968745;
        public static final int carousel_previousState = 2130968746;
        public static final int carousel_touchUpMode = 2130968747;
        public static final int carousel_touchUp_dampeningFactor = 2130968748;
        public static final int carousel_touchUp_velocityThreshold = 2130968749;
        public static final int chainUseRtl = 2130968751;
        public static final int checkboxStyle = 2130968756;
        public static final int checkedTextViewStyle = 2130968767;
        public static final int circleRadius = 2130968788;
        public static final int circularflow_angles = 2130968790;
        public static final int circularflow_defaultAngle = 2130968791;
        public static final int circularflow_defaultRadius = 2130968792;
        public static final int circularflow_radiusInDP = 2130968793;
        public static final int circularflow_viewCenter = 2130968794;
        public static final int clearsTag = 2130968796;
        public static final int clickAction = 2130968797;
        public static final int closeIcon = 2130968802;
        public static final int closeItemLayout = 2130968809;
        public static final int collapseContentDescription = 2130968810;
        public static final int collapseIcon = 2130968811;
        public static final int color = 2130968821;
        public static final int colorAccent = 2130968822;
        public static final int colorBackgroundFloating = 2130968823;
        public static final int colorButtonNormal = 2130968824;
        public static final int colorControlActivated = 2130968826;
        public static final int colorControlHighlight = 2130968827;
        public static final int colorControlNormal = 2130968828;
        public static final int colorError = 2130968829;
        public static final int colorPrimary = 2130968847;
        public static final int colorPrimaryDark = 2130968849;
        public static final int colorSwitchThumbNormal = 2130968859;
        public static final int commitIcon = 2130968878;
        public static final int constraintRotate = 2130968879;
        public static final int constraintSet = 2130968880;
        public static final int constraintSetEnd = 2130968881;
        public static final int constraintSetStart = 2130968882;
        public static final int constraint_referenced_ids = 2130968883;
        public static final int constraint_referenced_tags = 2130968884;
        public static final int constraints = 2130968885;
        public static final int content = 2130968886;
        public static final int contentDescription = 2130968887;
        public static final int contentInsetEnd = 2130968888;
        public static final int contentInsetEndWithActions = 2130968889;
        public static final int contentInsetLeft = 2130968890;
        public static final int contentInsetRight = 2130968891;
        public static final int contentInsetStart = 2130968892;
        public static final int contentInsetStartWithNavigation = 2130968893;
        public static final int contrast = 2130968902;
        public static final int controlBackground = 2130968903;
        public static final int crossfade = 2130968922;
        public static final int currentState = 2130968923;
        public static final int curveFit = 2130968924;
        public static final int customBoolean = 2130968925;
        public static final int customColorDrawableValue = 2130968926;
        public static final int customColorValue = 2130968927;
        public static final int customDimension = 2130968928;
        public static final int customFloatValue = 2130968929;
        public static final int customIntegerValue = 2130968930;
        public static final int customNavigationLayout = 2130968931;
        public static final int customPixelDimension = 2130968932;
        public static final int customReference = 2130968933;
        public static final int customStringValue = 2130968934;
        public static final int defaultDuration = 2130968939;
        public static final int defaultQueryHint = 2130968940;
        public static final int defaultState = 2130968941;
        public static final int deltaPolarAngle = 2130968943;
        public static final int deltaPolarRadius = 2130968944;
        public static final int deriveConstraintsFrom = 2130968946;
        public static final int dialogCornerRadius = 2130968947;
        public static final int dialogPreferredPadding = 2130968952;
        public static final int dialogTheme = 2130968953;
        public static final int displayOptions = 2130968956;
        public static final int divider = 2130968957;
        public static final int dividerHorizontal = 2130968962;
        public static final int dividerPadding = 2130968965;
        public static final int dividerVertical = 2130968967;
        public static final int dragDirection = 2130968968;
        public static final int dragScale = 2130968969;
        public static final int dragThreshold = 2130968970;
        public static final int drawPath = 2130968971;
        public static final int drawableBottomCompat = 2130968972;
        public static final int drawableEndCompat = 2130968973;
        public static final int drawableLeftCompat = 2130968974;
        public static final int drawableRightCompat = 2130968975;
        public static final int drawableSize = 2130968976;
        public static final int drawableStartCompat = 2130968977;
        public static final int drawableTint = 2130968978;
        public static final int drawableTintMode = 2130968979;
        public static final int drawableTopCompat = 2130968980;
        public static final int drawerArrowStyle = 2130968981;
        public static final int dropDownListViewStyle = 2130968986;
        public static final int dropdownListPreferredItemHeight = 2130968987;
        public static final int duration = 2130968989;
        public static final int editTextBackground = 2130968991;
        public static final int editTextColor = 2130968992;
        public static final int editTextStyle = 2130968994;
        public static final int elevation = 2130968995;
        public static final int expandActivityOverflowButtonDrawable = 2130969023;
        public static final int firstBaselineToTopHeight = 2130969057;
        public static final int flow_firstHorizontalBias = 2130969075;
        public static final int flow_firstHorizontalStyle = 2130969076;
        public static final int flow_firstVerticalBias = 2130969077;
        public static final int flow_firstVerticalStyle = 2130969078;
        public static final int flow_horizontalAlign = 2130969079;
        public static final int flow_horizontalBias = 2130969080;
        public static final int flow_horizontalGap = 2130969081;
        public static final int flow_horizontalStyle = 2130969082;
        public static final int flow_lastHorizontalBias = 2130969083;
        public static final int flow_lastHorizontalStyle = 2130969084;
        public static final int flow_lastVerticalBias = 2130969085;
        public static final int flow_lastVerticalStyle = 2130969086;
        public static final int flow_maxElementsWrap = 2130969087;
        public static final int flow_padding = **********;
        public static final int flow_verticalAlign = **********;
        public static final int flow_verticalBias = **********;
        public static final int flow_verticalGap = **********;
        public static final int flow_verticalStyle = **********;
        public static final int flow_wrapMode = **********;
        public static final int font = **********;
        public static final int fontFamily = **********;
        public static final int fontProviderAuthority = **********;
        public static final int fontProviderCerts = **********;
        public static final int fontProviderFetchStrategy = **********;
        public static final int fontProviderFetchTimeout = **********;
        public static final int fontProviderPackage = **********;
        public static final int fontProviderQuery = **********;
        public static final int fontStyle = **********;
        public static final int fontVariationSettings = **********;
        public static final int fontWeight = **********;
        public static final int framePosition = **********;
        public static final int gapBetweenBars = **********;
        public static final int goIcon = **********;
        public static final int guidelineUseRtl = **********;
        public static final int height = **********;
        public static final int hideOnContentScroll = **********;
        public static final int homeAsUpIndicator = **********;
        public static final int homeLayout = **********;
        public static final int icon = **********;
        public static final int iconTint = **********;
        public static final int iconTintMode = **********;
        public static final int iconifiedByDefault = **********;
        public static final int ifTagNotSet = **********;
        public static final int ifTagSet = 2130969147;
        public static final int imageButtonStyle = 2130969148;
        public static final int imagePanX = 2130969149;
        public static final int imagePanY = 2130969150;
        public static final int imageRotate = 2130969151;
        public static final int imageZoom = 2130969152;
        public static final int indeterminateProgressStyle = 2130969155;
        public static final int initialActivityCount = 2130969161;
        public static final int isLightTheme = 2130969164;
        public static final int itemPadding = 2130969180;
        public static final int keyPositionType = 2130969201;
        public static final int lastBaselineToBottomHeight = 2130969208;
        public static final int layout = 2130969210;
        public static final int layoutDescription = 2130969211;
        public static final int layoutDuringTransition = 2130969212;
        public static final int layout_constrainedHeight = 2130969220;
        public static final int layout_constrainedWidth = 2130969221;
        public static final int layout_constraintBaseline_creator = 2130969222;
        public static final int layout_constraintBaseline_toBaselineOf = 2130969223;
        public static final int layout_constraintBaseline_toBottomOf = 2130969224;
        public static final int layout_constraintBaseline_toTopOf = 2130969225;
        public static final int layout_constraintBottom_creator = 2130969226;
        public static final int layout_constraintBottom_toBottomOf = 2130969227;
        public static final int layout_constraintBottom_toTopOf = 2130969228;
        public static final int layout_constraintCircle = 2130969229;
        public static final int layout_constraintCircleAngle = 2130969230;
        public static final int layout_constraintCircleRadius = 2130969231;
        public static final int layout_constraintDimensionRatio = 2130969232;
        public static final int layout_constraintEnd_toEndOf = 2130969233;
        public static final int layout_constraintEnd_toStartOf = 2130969234;
        public static final int layout_constraintGuide_begin = 2130969235;
        public static final int layout_constraintGuide_end = 2130969236;
        public static final int layout_constraintGuide_percent = 2130969237;
        public static final int layout_constraintHeight = 2130969238;
        public static final int layout_constraintHeight_default = 2130969239;
        public static final int layout_constraintHeight_max = 2130969240;
        public static final int layout_constraintHeight_min = 2130969241;
        public static final int layout_constraintHeight_percent = 2130969242;
        public static final int layout_constraintHorizontal_bias = 2130969243;
        public static final int layout_constraintHorizontal_chainStyle = 2130969244;
        public static final int layout_constraintHorizontal_weight = 2130969245;
        public static final int layout_constraintLeft_creator = 2130969246;
        public static final int layout_constraintLeft_toLeftOf = 2130969247;
        public static final int layout_constraintLeft_toRightOf = 2130969248;
        public static final int layout_constraintRight_creator = 2130969249;
        public static final int layout_constraintRight_toLeftOf = 2130969250;
        public static final int layout_constraintRight_toRightOf = 2130969251;
        public static final int layout_constraintStart_toEndOf = 2130969252;
        public static final int layout_constraintStart_toStartOf = 2130969253;
        public static final int layout_constraintTag = 2130969254;
        public static final int layout_constraintTop_creator = 2130969255;
        public static final int layout_constraintTop_toBottomOf = 2130969256;
        public static final int layout_constraintTop_toTopOf = 2130969257;
        public static final int layout_constraintVertical_bias = 2130969258;
        public static final int layout_constraintVertical_chainStyle = 2130969259;
        public static final int layout_constraintVertical_weight = 2130969260;
        public static final int layout_constraintWidth = 2130969261;
        public static final int layout_constraintWidth_default = 2130969262;
        public static final int layout_constraintWidth_max = 2130969263;
        public static final int layout_constraintWidth_min = 2130969264;
        public static final int layout_constraintWidth_percent = 2130969265;
        public static final int layout_editor_absoluteX = 2130969267;
        public static final int layout_editor_absoluteY = 2130969268;
        public static final int layout_goneMarginBaseline = 2130969272;
        public static final int layout_goneMarginBottom = 2130969273;
        public static final int layout_goneMarginEnd = 2130969274;
        public static final int layout_goneMarginLeft = 2130969275;
        public static final int layout_goneMarginRight = 2130969276;
        public static final int layout_goneMarginStart = 2130969277;
        public static final int layout_goneMarginTop = 2130969278;
        public static final int layout_marginBaseline = 2130969281;
        public static final int layout_optimizationLevel = 2130969286;
        public static final int layout_wrapBehaviorInParent = 2130969292;
        public static final int limitBoundsTo = 2130969295;
        public static final int lineHeight = 2130969296;
        public static final int listChoiceBackgroundIndicator = 2130969300;
        public static final int listChoiceIndicatorMultipleAnimated = 2130969301;
        public static final int listChoiceIndicatorSingleAnimated = 2130969302;
        public static final int listDividerAlertDialog = 2130969303;
        public static final int listItemLayout = 2130969304;
        public static final int listLayout = 2130969305;
        public static final int listMenuViewStyle = 2130969306;
        public static final int listPopupWindowStyle = 2130969307;
        public static final int listPreferredItemHeight = 2130969308;
        public static final int listPreferredItemHeightLarge = 2130969309;
        public static final int listPreferredItemHeightSmall = 2130969310;
        public static final int listPreferredItemPaddingEnd = 2130969311;
        public static final int listPreferredItemPaddingLeft = 2130969312;
        public static final int listPreferredItemPaddingRight = 2130969313;
        public static final int listPreferredItemPaddingStart = 2130969314;
        public static final int logo = 2130969315;
        public static final int logoDescription = 2130969317;
        public static final int maxAcceleration = 2130969365;
        public static final int maxButtonHeight = 2130969367;
        public static final int maxHeight = 2130969369;
        public static final int maxVelocity = 2130969373;
        public static final int maxWidth = 2130969374;
        public static final int measureWithLargestChild = 2130969404;
        public static final int menu = 2130969405;
        public static final int methodName = 2130969408;
        public static final int minHeight = 2130969410;
        public static final int minWidth = 2130969414;
        public static final int mock_diagonalsColor = 2130969415;
        public static final int mock_label = 2130969416;
        public static final int mock_labelBackgroundColor = 2130969417;
        public static final int mock_labelColor = 2130969418;
        public static final int mock_showDiagonals = 2130969419;
        public static final int mock_showLabel = 2130969420;
        public static final int motionDebug = 2130969421;
        public static final int motionEffect_alpha = 2130969450;
        public static final int motionEffect_end = 2130969451;
        public static final int motionEffect_move = 2130969452;
        public static final int motionEffect_start = 2130969453;
        public static final int motionEffect_strict = 2130969454;
        public static final int motionEffect_translationX = 2130969455;
        public static final int motionEffect_translationY = 2130969456;
        public static final int motionEffect_viewTransition = 2130969457;
        public static final int motionInterpolator = 2130969458;
        public static final int motionPathRotate = 2130969460;
        public static final int motionProgress = 2130969461;
        public static final int motionStagger = 2130969462;
        public static final int motionTarget = 2130969463;
        public static final int motion_postLayoutCollision = 2130969464;
        public static final int motion_triggerOnCollision = 2130969465;
        public static final int moveWhenScrollAtTop = 2130969466;
        public static final int multiChoiceItemLayout = 2130969479;
        public static final int navigationContentDescription = 2130969480;
        public static final int navigationIcon = 2130969481;
        public static final int navigationMode = 2130969483;
        public static final int nestedScrollFlags = 2130969488;
        public static final int numericModifiers = 2130969492;
        public static final int onCross = 2130969493;
        public static final int onHide = 2130969494;
        public static final int onNegativeCross = 2130969495;
        public static final int onPositiveCross = 2130969496;
        public static final int onShow = 2130969497;
        public static final int onStateTransition = 2130969498;
        public static final int onTouchUp = 2130969499;
        public static final int overlapAnchor = 2130969502;
        public static final int overlay = 2130969503;
        public static final int paddingBottomNoButtons = 2130969504;
        public static final int paddingEnd = 2130969506;
        public static final int paddingStart = 2130969509;
        public static final int paddingTopNoTitle = 2130969510;
        public static final int panelBackground = 2130969512;
        public static final int panelMenuListTheme = 2130969513;
        public static final int panelMenuListWidth = 2130969514;
        public static final int pathMotionArc = 2130969520;
        public static final int path_percent = 2130969521;
        public static final int percentHeight = 2130969522;
        public static final int percentWidth = 2130969523;
        public static final int percentX = 2130969524;
        public static final int percentY = 2130969525;
        public static final int perpendicularPath_percent = 2130969526;
        public static final int pivotAnchor = 2130969528;
        public static final int placeholder_emptyVisibility = 2130969533;
        public static final int polarRelativeTo = 2130969534;
        public static final int popupMenuStyle = 2130969536;
        public static final int popupTheme = 2130969537;
        public static final int popupWindowStyle = 2130969538;
        public static final int preserveIconSpacing = 2130969555;
        public static final int progressBarPadding = 2130969558;
        public static final int progressBarStyle = 2130969559;
        public static final int quantizeMotionInterpolator = 2130969560;
        public static final int quantizeMotionPhase = 2130969561;
        public static final int quantizeMotionSteps = 2130969562;
        public static final int queryBackground = 2130969563;
        public static final int queryHint = 2130969564;
        public static final int radioButtonStyle = 2130969566;
        public static final int ratingBarStyle = 2130969568;
        public static final int ratingBarStyleIndicator = 2130969569;
        public static final int ratingBarStyleSmall = 2130969570;
        public static final int reactiveGuide_animateChange = 2130969571;
        public static final int reactiveGuide_applyToAllConstraintSets = 2130969572;
        public static final int reactiveGuide_applyToConstraintSet = 2130969573;
        public static final int reactiveGuide_valueId = 2130969574;
        public static final int region_heightLessThan = 2130969576;
        public static final int region_heightMoreThan = 2130969577;
        public static final int region_widthLessThan = 2130969578;
        public static final int region_widthMoreThan = 2130969579;
        public static final int rotationCenterId = 2130969595;
        public static final int round = 2130969596;
        public static final int roundPercent = 2130969597;
        public static final int saturation = 2130969598;
        public static final int scaleFromTextSize = 2130969599;
        public static final int searchHintIcon = 2130969603;
        public static final int searchIcon = 2130969604;
        public static final int searchViewStyle = 2130969605;
        public static final int seekBarStyle = 2130969610;
        public static final int selectableItemBackground = 2130969612;
        public static final int selectableItemBackgroundBorderless = 2130969613;
        public static final int setsTag = 2130969616;
        public static final int showAsAction = 2130969631;
        public static final int showDividers = 2130969636;
        public static final int showPaths = 2130969638;
        public static final int showText = 2130969640;
        public static final int showTitle = 2130969641;
        public static final int singleChoiceItemLayout = 2130969648;
        public static final int sizePercent = 2130969652;
        public static final int spinBars = 2130969658;
        public static final int spinnerDropDownItemStyle = 2130969659;
        public static final int spinnerStyle = 2130969660;
        public static final int splitTrack = 2130969665;
        public static final int springBoundary = 2130969666;
        public static final int springDamping = 2130969667;
        public static final int springMass = 2130969668;
        public static final int springStiffness = 2130969669;
        public static final int springStopThreshold = 2130969670;
        public static final int srcCompat = 2130969672;
        public static final int staggered = 2130969674;
        public static final int state_above_anchor = 2130969680;
        public static final int subMenuArrow = 2130969694;
        public static final int submitBackground = 2130969699;
        public static final int subtitle = 2130969700;
        public static final int subtitleTextAppearance = 2130969702;
        public static final int subtitleTextColor = 2130969703;
        public static final int subtitleTextStyle = 2130969704;
        public static final int suggestionRowLayout = 2130969708;
        public static final int switchMinWidth = 2130969713;
        public static final int switchPadding = 2130969714;
        public static final int switchStyle = 2130969717;
        public static final int switchTextAppearance = 2130969718;
        public static final int targetId = 2130969749;
        public static final int telltales_tailColor = 2130969750;
        public static final int telltales_tailScale = 2130969751;
        public static final int telltales_velocityMode = 2130969752;
        public static final int textAllCaps = 2130969754;
        public static final int textAppearanceLargePopupMenu = 2130969777;
        public static final int textAppearanceListItem = 2130969779;
        public static final int textAppearanceListItemSecondary = 2130969780;
        public static final int textAppearanceListItemSmall = 2130969781;
        public static final int textAppearancePopupMenuHeader = 2130969783;
        public static final int textAppearanceSearchResultSubtitle = 2130969784;
        public static final int textAppearanceSearchResultTitle = 2130969785;
        public static final int textAppearanceSmallPopupMenu = 2130969786;
        public static final int textBackground = 2130969792;
        public static final int textBackgroundPanX = 2130969793;
        public static final int textBackgroundPanY = 2130969794;
        public static final int textBackgroundRotate = 2130969795;
        public static final int textBackgroundZoom = 2130969796;
        public static final int textColorAlertDialogListItem = 2130969797;
        public static final int textColorSearchUrl = 2130969798;
        public static final int textFillColor = 2130969800;
        public static final int textLocale = 2130969809;
        public static final int textOutlineColor = 2130969810;
        public static final int textOutlineThickness = 2130969811;
        public static final int textPanX = 2130969812;
        public static final int textPanY = 2130969813;
        public static final int textureBlurFactor = 2130969815;
        public static final int textureEffect = 2130969816;
        public static final int textureHeight = 2130969817;
        public static final int textureWidth = 2130969818;
        public static final int theme = 2130969819;
        public static final int thickness = 2130969820;
        public static final int thumbTextPadding = 2130969829;
        public static final int thumbTint = 2130969830;
        public static final int thumbTintMode = 2130969831;
        public static final int tickMark = 2130969835;
        public static final int tickMarkTint = 2130969836;
        public static final int tickMarkTintMode = 2130969837;
        public static final int tint = 2130969839;
        public static final int tintMode = 2130969840;
        public static final int title = 2130969841;
        public static final int titleMargin = 2130969845;
        public static final int titleMarginBottom = 2130969846;
        public static final int titleMarginEnd = 2130969847;
        public static final int titleMarginStart = 2130969848;
        public static final int titleMarginTop = 2130969849;
        public static final int titleMargins = 2130969850;
        public static final int titleTextAppearance = 2130969852;
        public static final int titleTextColor = 2130969853;
        public static final int titleTextStyle = 2130969855;
        public static final int toolbarNavigationButtonStyle = 2130969858;
        public static final int toolbarStyle = 2130969859;
        public static final int tooltipForegroundColor = 2130969861;
        public static final int tooltipFrameBackground = 2130969862;
        public static final int tooltipText = 2130969864;
        public static final int touchAnchorId = 2130969866;
        public static final int touchAnchorSide = 2130969867;
        public static final int touchRegionId = 2130969868;
        public static final int track = 2130969869;
        public static final int trackTint = 2130969879;
        public static final int trackTintMode = 2130969880;
        public static final int transformPivotTarget = 2130969881;
        public static final int transitionDisable = 2130969882;
        public static final int transitionEasing = 2130969883;
        public static final int transitionFlags = 2130969884;
        public static final int transitionPathRotate = 2130969885;
        public static final int triggerId = 2130969887;
        public static final int triggerReceiver = 2130969888;
        public static final int triggerSlack = 2130969889;
        public static final int ttcIndex = 2130969890;
        public static final int upDuration = 2130969891;
        public static final int viewInflaterClass = 2130969899;
        public static final int viewTransitionMode = 2130969900;
        public static final int viewTransitionOnCross = 2130969901;
        public static final int viewTransitionOnNegativeCross = 2130969902;
        public static final int viewTransitionOnPositiveCross = 2130969903;
        public static final int visibilityMode = 2130969904;
        public static final int voiceIcon = 2130969905;
        public static final int warmth = 2130969906;
        public static final int waveDecay = 2130969907;
        public static final int waveOffset = 2130969908;
        public static final int wavePeriod = 2130969909;
        public static final int wavePhase = 2130969910;
        public static final int waveShape = 2130969911;
        public static final int waveVariesBy = 2130969912;
        public static final int windowActionBar = 2130969914;
        public static final int windowActionBarOverlay = 2130969915;
        public static final int windowActionModeOverlay = 2130969916;
        public static final int windowFixedHeightMajor = 2130969917;
        public static final int windowFixedHeightMinor = 2130969918;
        public static final int windowFixedWidthMajor = 2130969919;
        public static final int windowFixedWidthMinor = 2130969920;
        public static final int windowMinWidthMajor = 2130969921;
        public static final int windowMinWidthMinor = 2130969922;
        public static final int windowNoTitle = 2130969923;

        private attr() {
        }
    }

    /* renamed from: androidx.constraintlayout.widget.R$bool */
    public static final class bool {
        public static final int abc_action_bar_embed_tabs = 2131034112;
        public static final int abc_config_actionMenuItemAllCaps = 2131034113;

        private bool() {
        }
    }

    /* renamed from: androidx.constraintlayout.widget.R$color */
    public static final class color {
        public static final int abc_background_cache_hint_selector_material_dark = 2131099648;
        public static final int abc_background_cache_hint_selector_material_light = 2131099649;
        public static final int abc_btn_colored_borderless_text_material = 2131099650;
        public static final int abc_btn_colored_text_material = 2131099651;
        public static final int abc_color_highlight_material = 2131099652;
        public static final int abc_decor_view_status_guard = 2131099653;
        public static final int abc_decor_view_status_guard_light = 2131099654;
        public static final int abc_hint_foreground_material_dark = 2131099655;
        public static final int abc_hint_foreground_material_light = 2131099656;
        public static final int abc_primary_text_disable_only_material_dark = 2131099657;
        public static final int abc_primary_text_disable_only_material_light = 2131099658;
        public static final int abc_primary_text_material_dark = 2131099659;
        public static final int abc_primary_text_material_light = 2131099660;
        public static final int abc_search_url_text = 2131099661;
        public static final int abc_search_url_text_normal = 2131099662;
        public static final int abc_search_url_text_pressed = 2131099663;
        public static final int abc_search_url_text_selected = 2131099664;
        public static final int abc_secondary_text_material_dark = 2131099665;
        public static final int abc_secondary_text_material_light = 2131099666;
        public static final int abc_tint_btn_checkable = 2131099667;
        public static final int abc_tint_default = 2131099668;
        public static final int abc_tint_edittext = 2131099669;
        public static final int abc_tint_seek_thumb = 2131099670;
        public static final int abc_tint_spinner = 2131099671;
        public static final int abc_tint_switch_track = 2131099672;
        public static final int accent_material_dark = 2131099674;
        public static final int accent_material_light = 2131099675;
        public static final int androidx_core_ripple_material_light = 2131099677;
        public static final int androidx_core_secondary_text_default_material_light = 2131099678;
        public static final int background_floating_material_dark = 2131099680;
        public static final int background_floating_material_light = 2131099681;
        public static final int background_material_dark = 2131099682;
        public static final int background_material_light = 2131099683;
        public static final int bright_foreground_disabled_material_dark = 2131099689;
        public static final int bright_foreground_disabled_material_light = 2131099690;
        public static final int bright_foreground_inverse_material_dark = 2131099691;
        public static final int bright_foreground_inverse_material_light = 2131099692;
        public static final int bright_foreground_material_dark = 2131099693;
        public static final int bright_foreground_material_light = 2131099694;
        public static final int button_material_dark = 2131099696;
        public static final int button_material_light = 2131099697;
        public static final int dim_foreground_disabled_material_dark = 2131099776;
        public static final int dim_foreground_disabled_material_light = 2131099777;
        public static final int dim_foreground_material_dark = 2131099778;
        public static final int dim_foreground_material_light = 2131099779;
        public static final int error_color_material_dark = 2131099783;
        public static final int error_color_material_light = 2131099784;
        public static final int foreground_material_dark = 2131099785;
        public static final int foreground_material_light = 2131099786;
        public static final int highlighted_text_material_dark = 2131099788;
        public static final int highlighted_text_material_light = 2131099789;
        public static final int material_blue_grey_800 = 2131100121;
        public static final int material_blue_grey_900 = 2131100122;
        public static final int material_blue_grey_950 = 2131100123;
        public static final int material_deep_teal_200 = 2131100125;
        public static final int material_deep_teal_500 = 2131100126;
        public static final int material_grey_100 = 2131100193;
        public static final int material_grey_300 = 2131100194;
        public static final int material_grey_50 = 2131100195;
        public static final int material_grey_600 = 2131100196;
        public static final int material_grey_800 = 2131100197;
        public static final int material_grey_850 = 2131100198;
        public static final int material_grey_900 = 2131100199;
        public static final int notification_action_color_filter = 2131100288;
        public static final int notification_icon_bg_color = 2131100289;
        public static final int primary_dark_material_dark = 2131100292;
        public static final int primary_dark_material_light = 2131100293;
        public static final int primary_material_dark = 2131100294;
        public static final int primary_material_light = 2131100295;
        public static final int primary_text_default_material_dark = 2131100296;
        public static final int primary_text_default_material_light = 2131100297;
        public static final int primary_text_disabled_material_dark = 2131100298;
        public static final int primary_text_disabled_material_light = 2131100299;
        public static final int ripple_material_dark = 2131100300;
        public static final int ripple_material_light = 2131100301;
        public static final int secondary_text_default_material_dark = 2131100305;
        public static final int secondary_text_default_material_light = 2131100306;
        public static final int secondary_text_disabled_material_dark = 2131100307;
        public static final int secondary_text_disabled_material_light = 2131100308;
        public static final int switch_thumb_disabled_material_dark = 2131100310;
        public static final int switch_thumb_disabled_material_light = 2131100311;
        public static final int switch_thumb_material_dark = 2131100312;
        public static final int switch_thumb_material_light = 2131100313;
        public static final int switch_thumb_normal_material_dark = 2131100314;
        public static final int switch_thumb_normal_material_light = 2131100315;
        public static final int tooltip_background_dark = 2131100323;
        public static final int tooltip_background_light = 2131100324;

        private color() {
        }
    }

    /* renamed from: androidx.constraintlayout.widget.R$dimen */
    public static final class dimen {
        public static final int abc_action_bar_content_inset_material = 2131165184;
        public static final int abc_action_bar_content_inset_with_nav = 2131165185;
        public static final int abc_action_bar_default_height_material = 2131165186;
        public static final int abc_action_bar_default_padding_end_material = 2131165187;
        public static final int abc_action_bar_default_padding_start_material = 2131165188;
        public static final int abc_action_bar_elevation_material = 2131165189;
        public static final int abc_action_bar_icon_vertical_padding_material = 2131165190;
        public static final int abc_action_bar_overflow_padding_end_material = 2131165191;
        public static final int abc_action_bar_overflow_padding_start_material = 2131165192;
        public static final int abc_action_bar_stacked_max_height = 2131165193;
        public static final int abc_action_bar_stacked_tab_max_width = 2131165194;
        public static final int abc_action_bar_subtitle_bottom_margin_material = 2131165195;
        public static final int abc_action_bar_subtitle_top_margin_material = 2131165196;
        public static final int abc_action_button_min_height_material = 2131165197;
        public static final int abc_action_button_min_width_material = 2131165198;
        public static final int abc_action_button_min_width_overflow_material = 2131165199;
        public static final int abc_alert_dialog_button_bar_height = 2131165200;
        public static final int abc_alert_dialog_button_dimen = 2131165201;
        public static final int abc_button_inset_horizontal_material = 2131165202;
        public static final int abc_button_inset_vertical_material = 2131165203;
        public static final int abc_button_padding_horizontal_material = 2131165204;
        public static final int abc_button_padding_vertical_material = 2131165205;
        public static final int abc_cascading_menus_min_smallest_width = 2131165206;
        public static final int abc_config_prefDialogWidth = 2131165207;
        public static final int abc_control_corner_material = 2131165208;
        public static final int abc_control_inset_material = 2131165209;
        public static final int abc_control_padding_material = 2131165210;
        public static final int abc_dialog_corner_radius_material = 2131165211;
        public static final int abc_dialog_fixed_height_major = 2131165212;
        public static final int abc_dialog_fixed_height_minor = 2131165213;
        public static final int abc_dialog_fixed_width_major = 2131165214;
        public static final int abc_dialog_fixed_width_minor = 2131165215;
        public static final int abc_dialog_list_padding_bottom_no_buttons = 2131165216;
        public static final int abc_dialog_list_padding_top_no_title = 2131165217;
        public static final int abc_dialog_min_width_major = 2131165218;
        public static final int abc_dialog_min_width_minor = 2131165219;
        public static final int abc_dialog_padding_material = 2131165220;
        public static final int abc_dialog_padding_top_material = 2131165221;
        public static final int abc_dialog_title_divider_material = 2131165222;
        public static final int abc_disabled_alpha_material_dark = 2131165223;
        public static final int abc_disabled_alpha_material_light = 2131165224;
        public static final int abc_dropdownitem_icon_width = 2131165225;
        public static final int abc_dropdownitem_text_padding_left = 2131165226;
        public static final int abc_dropdownitem_text_padding_right = 2131165227;
        public static final int abc_edit_text_inset_bottom_material = 2131165228;
        public static final int abc_edit_text_inset_horizontal_material = 2131165229;
        public static final int abc_edit_text_inset_top_material = 2131165230;
        public static final int abc_floating_window_z = 2131165231;
        public static final int abc_list_item_height_large_material = 2131165232;
        public static final int abc_list_item_height_material = 2131165233;
        public static final int abc_list_item_height_small_material = 2131165234;
        public static final int abc_list_item_padding_horizontal_material = 2131165235;
        public static final int abc_panel_menu_list_width = 2131165236;
        public static final int abc_progress_bar_height_material = 2131165237;
        public static final int abc_search_view_preferred_height = 2131165238;
        public static final int abc_search_view_preferred_width = 2131165239;
        public static final int abc_seekbar_track_background_height_material = 2131165240;
        public static final int abc_seekbar_track_progress_height_material = 2131165241;
        public static final int abc_select_dialog_padding_start_material = 2131165242;
        public static final int abc_switch_padding = 2131165246;
        public static final int abc_text_size_body_1_material = 2131165247;
        public static final int abc_text_size_body_2_material = 2131165248;
        public static final int abc_text_size_button_material = 2131165249;
        public static final int abc_text_size_caption_material = 2131165250;
        public static final int abc_text_size_display_1_material = 2131165251;
        public static final int abc_text_size_display_2_material = 2131165252;
        public static final int abc_text_size_display_3_material = 2131165253;
        public static final int abc_text_size_display_4_material = 2131165254;
        public static final int abc_text_size_headline_material = 2131165255;
        public static final int abc_text_size_large_material = 2131165256;
        public static final int abc_text_size_medium_material = 2131165257;
        public static final int abc_text_size_menu_header_material = 2131165258;
        public static final int abc_text_size_menu_material = 2131165259;
        public static final int abc_text_size_small_material = 2131165260;
        public static final int abc_text_size_subhead_material = 2131165261;
        public static final int abc_text_size_subtitle_material_toolbar = 2131165262;
        public static final int abc_text_size_title_material = 2131165263;
        public static final int abc_text_size_title_material_toolbar = 2131165264;
        public static final int compat_button_inset_horizontal_material = 2131165275;
        public static final int compat_button_inset_vertical_material = 2131165276;
        public static final int compat_button_padding_horizontal_material = 2131165277;
        public static final int compat_button_padding_vertical_material = 2131165278;
        public static final int compat_control_corner_material = 2131165279;
        public static final int compat_notification_large_icon_max_height = 2131165280;
        public static final int compat_notification_large_icon_max_width = 2131165281;
        public static final int disabled_alpha_material_dark = 2131165331;
        public static final int disabled_alpha_material_light = 2131165332;
        public static final int highlight_alpha_material_colored = 2131165338;
        public static final int highlight_alpha_material_dark = 2131165339;
        public static final int highlight_alpha_material_light = 2131165340;
        public static final int hint_alpha_material_dark = 2131165341;
        public static final int hint_alpha_material_light = 2131165342;
        public static final int hint_pressed_alpha_material_dark = 2131165343;
        public static final int hint_pressed_alpha_material_light = 2131165344;
        public static final int notification_action_icon_size = 2131165823;
        public static final int notification_action_text_size = 2131165824;
        public static final int notification_big_circle_margin = 2131165825;
        public static final int notification_content_margin_start = 2131165826;
        public static final int notification_large_icon_height = 2131165827;
        public static final int notification_large_icon_width = 2131165828;
        public static final int notification_main_column_padding_top = 2131165829;
        public static final int notification_media_narrow_margin = 2131165830;
        public static final int notification_right_icon_size = 2131165831;
        public static final int notification_right_side_padding_top = 2131165832;
        public static final int notification_small_icon_background_padding = 2131165833;
        public static final int notification_small_icon_size_as_large = 2131165834;
        public static final int notification_subtext_size = 2131165835;
        public static final int notification_top_pad = 2131165836;
        public static final int notification_top_pad_large_text = 2131165837;
        public static final int tooltip_corner_radius = 2131165852;
        public static final int tooltip_horizontal_padding = 2131165853;
        public static final int tooltip_margin = 2131165854;
        public static final int tooltip_precise_anchor_extra_offset = 2131165855;
        public static final int tooltip_precise_anchor_threshold = 2131165856;
        public static final int tooltip_vertical_padding = 2131165857;
        public static final int tooltip_y_offset_non_touch = 2131165858;
        public static final int tooltip_y_offset_touch = 2131165859;

        private dimen() {
        }
    }

    /* renamed from: androidx.constraintlayout.widget.R$drawable */
    public static final class drawable {
        public static final int abc_ab_share_pack_mtrl_alpha = 2131230754;
        public static final int abc_action_bar_item_background_material = 2131230755;
        public static final int abc_btn_borderless_material = 2131230756;
        public static final int abc_btn_check_material = 2131230757;
        public static final int abc_btn_check_material_anim = 2131230758;
        public static final int abc_btn_check_to_on_mtrl_000 = 2131230759;
        public static final int abc_btn_check_to_on_mtrl_015 = 2131230760;
        public static final int abc_btn_colored_material = 2131230761;
        public static final int abc_btn_default_mtrl_shape = 2131230762;
        public static final int abc_btn_radio_material = 2131230763;
        public static final int abc_btn_radio_material_anim = 2131230764;
        public static final int abc_btn_radio_to_on_mtrl_000 = 2131230765;
        public static final int abc_btn_radio_to_on_mtrl_015 = 2131230766;
        public static final int abc_btn_switch_to_on_mtrl_00001 = 2131230767;
        public static final int abc_btn_switch_to_on_mtrl_00012 = 2131230768;
        public static final int abc_cab_background_internal_bg = 2131230769;
        public static final int abc_cab_background_top_material = 2131230770;
        public static final int abc_cab_background_top_mtrl_alpha = 2131230771;
        public static final int abc_control_background_material = 2131230772;
        public static final int abc_dialog_material_background = 2131230773;
        public static final int abc_edit_text_material = 2131230774;
        public static final int abc_ic_ab_back_material = 2131230775;
        public static final int abc_ic_arrow_drop_right_black_24dp = 2131230776;
        public static final int abc_ic_clear_material = 2131230777;
        public static final int abc_ic_commit_search_api_mtrl_alpha = 2131230778;
        public static final int abc_ic_go_search_api_material = 2131230779;
        public static final int abc_ic_menu_copy_mtrl_am_alpha = 2131230780;
        public static final int abc_ic_menu_cut_mtrl_alpha = 2131230781;
        public static final int abc_ic_menu_overflow_material = 2131230782;
        public static final int abc_ic_menu_paste_mtrl_am_alpha = 2131230783;
        public static final int abc_ic_menu_selectall_mtrl_alpha = 2131230784;
        public static final int abc_ic_menu_share_mtrl_alpha = 2131230785;
        public static final int abc_ic_search_api_material = 2131230786;
        public static final int abc_ic_voice_search_api_material = 2131230787;
        public static final int abc_item_background_holo_dark = 2131230788;
        public static final int abc_item_background_holo_light = 2131230789;
        public static final int abc_list_divider_material = 2131230790;
        public static final int abc_list_divider_mtrl_alpha = 2131230791;
        public static final int abc_list_focused_holo = 2131230792;
        public static final int abc_list_longpressed_holo = 2131230793;
        public static final int abc_list_pressed_holo_dark = 2131230794;
        public static final int abc_list_pressed_holo_light = 2131230795;
        public static final int abc_list_selector_background_transition_holo_dark = 2131230796;
        public static final int abc_list_selector_background_transition_holo_light = 2131230797;
        public static final int abc_list_selector_disabled_holo_dark = 2131230798;
        public static final int abc_list_selector_disabled_holo_light = 2131230799;
        public static final int abc_list_selector_holo_dark = 2131230800;
        public static final int abc_list_selector_holo_light = 2131230801;
        public static final int abc_menu_hardkey_panel_mtrl_mult = 2131230802;
        public static final int abc_popup_background_mtrl_mult = 2131230803;
        public static final int abc_ratingbar_indicator_material = 2131230804;
        public static final int abc_ratingbar_material = 2131230805;
        public static final int abc_ratingbar_small_material = 2131230806;
        public static final int abc_scrubber_control_off_mtrl_alpha = 2131230807;
        public static final int abc_scrubber_control_to_pressed_mtrl_000 = 2131230808;
        public static final int abc_scrubber_control_to_pressed_mtrl_005 = 2131230809;
        public static final int abc_scrubber_primary_mtrl_alpha = 2131230810;
        public static final int abc_scrubber_track_mtrl_alpha = 2131230811;
        public static final int abc_seekbar_thumb_material = 2131230812;
        public static final int abc_seekbar_tick_mark_material = 2131230813;
        public static final int abc_seekbar_track_material = 2131230814;
        public static final int abc_spinner_mtrl_am_alpha = 2131230815;
        public static final int abc_spinner_textfield_background_material = 2131230816;
        public static final int abc_switch_thumb_material = 2131230819;
        public static final int abc_switch_track_mtrl_alpha = 2131230820;
        public static final int abc_tab_indicator_material = 2131230821;
        public static final int abc_tab_indicator_mtrl_alpha = 2131230822;
        public static final int abc_text_cursor_material = 2131230823;
        public static final int abc_textfield_activated_mtrl_alpha = 2131230827;
        public static final int abc_textfield_default_mtrl_alpha = 2131230828;
        public static final int abc_textfield_search_activated_mtrl_alpha = 2131230829;
        public static final int abc_textfield_search_default_mtrl_alpha = 2131230830;
        public static final int abc_textfield_search_material = 2131230831;
        public static final int abc_vector_test = 2131230832;
        public static final int btn_checkbox_checked_mtrl = 2131230851;
        public static final int btn_checkbox_checked_to_unchecked_mtrl_animation = 2131230852;
        public static final int btn_checkbox_unchecked_mtrl = 2131230853;
        public static final int btn_checkbox_unchecked_to_checked_mtrl_animation = 2131230854;
        public static final int btn_radio_off_mtrl = 2131230855;
        public static final int btn_radio_off_to_on_mtrl_animation = 2131230856;
        public static final int btn_radio_on_mtrl = 2131230857;
        public static final int btn_radio_on_to_off_mtrl_animation = 2131230858;
        public static final int notification_action_background = 2131231994;
        public static final int notification_bg = 2131231995;
        public static final int notification_bg_low = 2131231996;
        public static final int notification_bg_low_normal = 2131231997;
        public static final int notification_bg_low_pressed = 2131231998;
        public static final int notification_bg_normal = 2131231999;
        public static final int notification_bg_normal_pressed = 2131232000;
        public static final int notification_icon_background = 2131232001;
        public static final int notification_template_icon_bg = 2131232002;
        public static final int notification_template_icon_low_bg = 2131232003;
        public static final int notification_tile_bg = 2131232004;
        public static final int notify_panel_notification_icon_bg = 2131232005;
        public static final int tooltip_frame_dark = 2131232022;
        public static final int tooltip_frame_light = 2131232023;

        private drawable() {
        }
    }

    /* renamed from: androidx.constraintlayout.widget.R$id */
    public static final class id {
        public static final int NO_DEBUG = 2131296262;
        public static final int SHOW_ALL = 2131296264;
        public static final int SHOW_PATH = 2131296265;
        public static final int SHOW_PROGRESS = 2131296266;
        public static final int accelerate = 2131296270;
        public static final int accessibility_action_clickable_span = 2131296272;
        public static final int accessibility_custom_action_0 = 2131296273;
        public static final int accessibility_custom_action_1 = 2131296274;
        public static final int accessibility_custom_action_10 = 2131296275;
        public static final int accessibility_custom_action_11 = 2131296276;
        public static final int accessibility_custom_action_12 = 2131296277;
        public static final int accessibility_custom_action_13 = 2131296278;
        public static final int accessibility_custom_action_14 = 2131296279;
        public static final int accessibility_custom_action_15 = 2131296280;
        public static final int accessibility_custom_action_16 = 2131296281;
        public static final int accessibility_custom_action_17 = 2131296282;
        public static final int accessibility_custom_action_18 = 2131296283;
        public static final int accessibility_custom_action_19 = 2131296284;
        public static final int accessibility_custom_action_2 = 2131296285;
        public static final int accessibility_custom_action_20 = 2131296286;
        public static final int accessibility_custom_action_21 = 2131296287;
        public static final int accessibility_custom_action_22 = 2131296288;
        public static final int accessibility_custom_action_23 = 2131296289;
        public static final int accessibility_custom_action_24 = 2131296290;
        public static final int accessibility_custom_action_25 = 2131296291;
        public static final int accessibility_custom_action_26 = 2131296292;
        public static final int accessibility_custom_action_27 = 2131296293;
        public static final int accessibility_custom_action_28 = 2131296294;
        public static final int accessibility_custom_action_29 = 2131296295;
        public static final int accessibility_custom_action_3 = 2131296296;
        public static final int accessibility_custom_action_30 = 2131296297;
        public static final int accessibility_custom_action_31 = 2131296298;
        public static final int accessibility_custom_action_4 = 2131296299;
        public static final int accessibility_custom_action_5 = 2131296300;
        public static final int accessibility_custom_action_6 = 2131296301;
        public static final int accessibility_custom_action_7 = 2131296302;
        public static final int accessibility_custom_action_8 = 2131296303;
        public static final int accessibility_custom_action_9 = 2131296304;
        public static final int actionDown = 2131296309;
        public static final int actionDownUp = 2131296310;
        public static final int actionUp = 2131296311;
        public static final int action_bar = 2131296312;
        public static final int action_bar_activity_content = 2131296313;
        public static final int action_bar_container = 2131296314;
        public static final int action_bar_root = 2131296315;
        public static final int action_bar_spinner = 2131296316;
        public static final int action_bar_subtitle = 2131296317;
        public static final int action_bar_title = 2131296318;
        public static final int action_container = 2131296332;
        public static final int action_context_bar = 2131296333;
        public static final int action_divider = 2131296346;
        public static final int action_image = 2131296360;
        public static final int action_menu_divider = 2131296378;
        public static final int action_menu_presenter = 2131296379;
        public static final int action_mode_bar = 2131296380;
        public static final int action_mode_bar_stub = 2131296381;
        public static final int action_mode_close_button = 2131296382;
        public static final int action_text = 2131296407;
        public static final int actions = 2131296409;
        public static final int activity_chooser_view_content = 2131296410;
        public static final int add = 2131296411;
        public static final int alertTitle = 2131296414;
        public static final int aligned = 2131296417;
        public static final int allStates = 2131296419;
        public static final int animateToEnd = 2131296423;
        public static final int animateToStart = 2131296424;
        public static final int antiClockwise = 2131296425;
        public static final int anticipate = 2131296426;
        public static final int asConfigured = 2131296433;
        public static final int async = 2131296434;
        public static final int auto = 2131296444;
        public static final int autoComplete = 2131296445;
        public static final int autoCompleteToEnd = 2131296446;
        public static final int autoCompleteToStart = 2131296447;
        public static final int baseline = 2131296451;
        public static final int bestChoice = 2131296454;
        public static final int blocking = 2131296455;
        public static final int bottom = 2131296456;
        public static final int bounce = 2131296457;
        public static final int buttonPanel = 2131296465;
        public static final int callMeasure = 2131296467;
        public static final int carryVelocity = 2131296475;
        public static final int center = 2131296476;
        public static final int chain = 2131296481;
        public static final int chain2 = 2131296482;
        public static final int checkbox = 2131296486;
        public static final int checked = 2131296490;
        public static final int chronometer = 2131296493;
        public static final int clockwise = 2131296504;
        public static final int closest = 2131296506;
        public static final int constraint = 2131296518;
        public static final int content = 2131296522;
        public static final int contentPanel = 2131296523;
        public static final int continuousVelocity = 2131296525;
        public static final int cos = 2131296528;
        public static final int currentState = 2131296533;
        public static final int custom = 2131296534;
        public static final int customPanel = 2131296535;
        public static final int decelerate = 2131296545;
        public static final int decelerateAndComplete = 2131296546;
        public static final int decor_content_parent = 2131296547;
        public static final int default_activity_button = 2131296548;
        public static final int deltaRelative = 2131296550;
        public static final int dialog_button = 2131296558;
        public static final int dragAnticlockwise = 2131296575;
        public static final int dragClockwise = 2131296576;
        public static final int dragDown = 2131296577;
        public static final int dragEnd = 2131296578;
        public static final int dragLeft = 2131296579;
        public static final int dragRight = 2131296580;
        public static final int dragStart = 2131296581;
        public static final int dragUp = 2131296582;
        public static final int easeIn = 2131296586;
        public static final int easeInOut = 2131296587;
        public static final int easeOut = 2131296588;
        public static final int east = 2131296589;
        public static final int edit_query = 2131296592;
        public static final int end = 2131296597;
        public static final int expand_activities_button = 2131296615;
        public static final int expanded_menu = 2131296616;
        public static final int flip = 2131296642;
        public static final int forever = 2131296644;
        public static final int frost = 2131296649;
        public static final int gone = 2131296656;
        public static final int group_divider = 2131296662;
        public static final int home = 2131296670;
        public static final int honorRequest = 2131296672;
        public static final int horizontal_only = 2131296674;
        public static final int icon = 2131296675;
        public static final int icon_group = 2131296677;
        public static final int ignore = 2131296679;
        public static final int ignoreRequest = 2131296680;
        public static final int image = 2131296681;
        public static final int immediateStop = 2131296682;
        public static final int included = 2131296683;
        public static final int info = 2131296686;
        public static final int invisible = 2131296696;
        public static final int italic = 2131296699;
        public static final int jumpToEnd = 2131296704;
        public static final int jumpToStart = 2131296705;
        public static final int layout = 2131296709;
        public static final int left = 2131296716;
        public static final int line1 = 2131296719;
        public static final int line3 = 2131296720;
        public static final int linear = 2131296721;
        public static final int listMode = 2131296725;
        public static final int list_item = 2131296726;
        public static final int match_constraint = 2131296738;
        public static final int match_parent = 2131296739;
        public static final int message = 2131296776;
        public static final int middle = 2131296777;
        public static final int motion_base = 2131296789;
        public static final int multiply = 2131296820;
        public static final int neverCompleteToEnd = 2131296830;
        public static final int neverCompleteToStart = 2131296831;
        public static final int noState = 2131296833;
        public static final int none = 2131296834;
        public static final int normal = 2131296835;
        public static final int north = 2131296836;
        public static final int notification_background = 2131296837;
        public static final int notification_main_column = 2131296838;
        public static final int notification_main_column_container = 2131296839;
        public static final int off = 2131296842;

        /* renamed from: on */
        public static final int f332on = 2131296843;
        public static final int overshoot = 2131296856;
        public static final int packed = 2131296857;
        public static final int parent = 2131296859;
        public static final int parentPanel = 2131296860;
        public static final int parentRelative = 2131296861;
        public static final int path = 2131296865;
        public static final int pathRelative = 2131296866;
        public static final int percent = 2131296868;
        public static final int position = 2131296873;
        public static final int postLayout = 2131296874;
        public static final int progress_circular = 2131296882;
        public static final int progress_horizontal = 2131296883;
        public static final int radio = 2131296887;
        public static final int rectangles = 2131296891;
        public static final int reverseSawtooth = 2131296904;
        public static final int right = 2131296905;
        public static final int right_icon = 2131296907;
        public static final int right_side = 2131296908;
        public static final int sawtooth = 2131296938;
        public static final int screen = 2131296940;
        public static final int scrollIndicatorDown = 2131296943;
        public static final int scrollIndicatorUp = 2131296944;
        public static final int scrollView = 2131296945;
        public static final int search_badge = 2131296947;
        public static final int search_bar = 2131296948;
        public static final int search_button = 2131296949;
        public static final int search_close_btn = 2131296950;
        public static final int search_edit_frame = 2131296951;
        public static final int search_go_btn = 2131296952;
        public static final int search_mag_icon = 2131296953;
        public static final int search_plate = 2131296954;
        public static final int search_src_text = 2131296955;
        public static final int search_voice_btn = 2131296956;
        public static final int select_dialog_listview = 2131296961;
        public static final int sharedValueSet = 2131296966;
        public static final int sharedValueUnset = 2131296967;
        public static final int shortcut = 2131296968;
        public static final int sin = 2131296974;
        public static final int skipped = 2131296977;
        public static final int south = 2131296984;
        public static final int spacer = 2131296988;
        public static final int spline = 2131296991;
        public static final int split_action_bar = 2131296992;
        public static final int spread = 2131296993;
        public static final int spread_inside = 2131296994;
        public static final int spring = 2131296995;
        public static final int square = 2131296996;
        public static final int src_atop = 2131296997;
        public static final int src_in = 2131296998;
        public static final int src_over = 2131296999;
        public static final int standard = 2131297000;
        public static final int start = 2131297001;
        public static final int startHorizontal = 2131297002;
        public static final int startVertical = 2131297004;
        public static final int staticLayout = 2131297006;
        public static final int staticPostLayout = 2131297007;
        public static final int stop = 2131297013;
        public static final int submenuarrow = 2131297017;
        public static final int submit_area = 2131297019;
        public static final int tabMode = 2131297026;
        public static final int tag_accessibility_actions = 2131297028;
        public static final int tag_accessibility_clickable_spans = 2131297029;
        public static final int tag_accessibility_heading = 2131297030;
        public static final int tag_accessibility_pane_title = 2131297031;
        public static final int tag_screen_reader_focusable = 2131297035;
        public static final int tag_transition_group = 2131297037;
        public static final int tag_unhandled_key_event_manager = 2131297038;
        public static final int tag_unhandled_key_listeners = 2131297039;
        public static final int text = 2131297043;
        public static final int text2 = 2131297044;
        public static final int textSpacerNoButtons = 2131297046;
        public static final int textSpacerNoTitle = 2131297047;
        public static final int time = 2131297059;
        public static final int title = 2131297062;
        public static final int titleDividerNoCustom = 2131297064;
        public static final int title_template = 2131297066;
        public static final int top = 2131297070;
        public static final int topPanel = 2131297071;
        public static final int triangle = 2131297080;
        public static final int unchecked = 2131297084;
        public static final int uniform = 2131297086;

        /* renamed from: up */
        public static final int f333up = 2131297088;
        public static final int vertical_only = 2131297112;
        public static final int view_transition = 2131297116;
        public static final int visible = 2131297121;
        public static final int west = 2131297129;
        public static final int wrap = 2131297135;
        public static final int wrap_content = 2131297136;
        public static final int wrap_content_constrained = 2131297137;
        public static final int x_left = 2131297140;
        public static final int x_right = 2131297141;

        private id() {
        }
    }

    /* renamed from: androidx.constraintlayout.widget.R$integer */
    public static final class integer {
        public static final int abc_config_activityDefaultDur = 2131361792;
        public static final int abc_config_activityShortDur = 2131361793;
        public static final int cancel_button_image_alpha = 2131361796;
        public static final int config_tooltipAnimTime = 2131361798;
        public static final int status_bar_notification_info_maxnum = 2131361870;

        private integer() {
        }
    }

    /* renamed from: androidx.constraintlayout.widget.R$interpolator */
    public static final class interpolator {
        public static final int btn_checkbox_checked_mtrl_animation_interpolator_0 = 2131427328;
        public static final int btn_checkbox_checked_mtrl_animation_interpolator_1 = 2131427329;
        public static final int btn_checkbox_unchecked_mtrl_animation_interpolator_0 = 2131427330;
        public static final int btn_checkbox_unchecked_mtrl_animation_interpolator_1 = 2131427331;
        public static final int btn_radio_to_off_mtrl_animation_interpolator_0 = 2131427332;
        public static final int btn_radio_to_on_mtrl_animation_interpolator_0 = 2131427333;
        public static final int fast_out_slow_in = 2131427334;

        private interpolator() {
        }
    }

    /* renamed from: androidx.constraintlayout.widget.R$layout */
    public static final class layout {
        public static final int abc_action_bar_title_item = 2131492864;
        public static final int abc_action_bar_up_container = 2131492865;
        public static final int abc_action_menu_item_layout = 2131492866;
        public static final int abc_action_menu_layout = 2131492867;
        public static final int abc_action_mode_bar = 2131492868;
        public static final int abc_action_mode_close_item_material = 2131492869;
        public static final int abc_activity_chooser_view = 2131492870;
        public static final int abc_activity_chooser_view_list_item = 2131492871;
        public static final int abc_alert_dialog_button_bar_material = 2131492872;
        public static final int abc_alert_dialog_material = 2131492873;
        public static final int abc_alert_dialog_title_material = 2131492874;
        public static final int abc_cascading_menu_item_layout = 2131492875;
        public static final int abc_dialog_title_material = 2131492876;
        public static final int abc_expanded_menu_layout = 2131492877;
        public static final int abc_list_menu_item_checkbox = 2131492878;
        public static final int abc_list_menu_item_icon = 2131492879;
        public static final int abc_list_menu_item_layout = 2131492880;
        public static final int abc_list_menu_item_radio = 2131492881;
        public static final int abc_popup_menu_header_item_layout = 2131492882;
        public static final int abc_popup_menu_item_layout = 2131492883;
        public static final int abc_screen_content_include = 2131492884;
        public static final int abc_screen_simple = 2131492885;
        public static final int abc_screen_simple_overlay_action_mode = 2131492886;
        public static final int abc_screen_toolbar = 2131492887;
        public static final int abc_search_dropdown_item_icons_2line = 2131492888;
        public static final int abc_search_view = 2131492889;
        public static final int abc_select_dialog_material = 2131492890;
        public static final int abc_tooltip = 2131492891;
        public static final int custom_dialog = 2131492922;
        public static final int notification_action = 2131493075;
        public static final int notification_action_tombstone = 2131493076;
        public static final int notification_template_custom_big = 2131493083;
        public static final int notification_template_icon_group = 2131493084;
        public static final int notification_template_part_chronometer = 2131493088;
        public static final int notification_template_part_time = 2131493089;
        public static final int select_dialog_item_material = 2131493112;
        public static final int select_dialog_multichoice_material = 2131493113;
        public static final int select_dialog_singlechoice_material = 2131493114;
        public static final int support_simple_spinner_dropdown_item = 2131493118;

        private layout() {
        }
    }

    /* renamed from: androidx.constraintlayout.widget.R$string */
    public static final class string {
        public static final int abc_action_bar_home_description = 2131820544;
        public static final int abc_action_bar_up_description = 2131820545;
        public static final int abc_action_menu_overflow_description = 2131820546;
        public static final int abc_action_mode_done = 2131820547;
        public static final int abc_activity_chooser_view_see_all = 2131820548;
        public static final int abc_activitychooserview_choose_application = 2131820549;
        public static final int abc_capital_off = 2131820550;
        public static final int abc_capital_on = 2131820551;
        public static final int abc_menu_alt_shortcut_label = 2131820552;
        public static final int abc_menu_ctrl_shortcut_label = 2131820553;
        public static final int abc_menu_delete_shortcut_label = 2131820554;
        public static final int abc_menu_enter_shortcut_label = 2131820555;
        public static final int abc_menu_function_shortcut_label = 2131820556;
        public static final int abc_menu_meta_shortcut_label = 2131820557;
        public static final int abc_menu_shift_shortcut_label = 2131820558;
        public static final int abc_menu_space_shortcut_label = 2131820559;
        public static final int abc_menu_sym_shortcut_label = 2131820560;
        public static final int abc_prepend_shortcut_label = 2131820561;
        public static final int abc_search_hint = **********;
        public static final int abc_searchview_description_clear = **********;
        public static final int abc_searchview_description_query = **********;
        public static final int abc_searchview_description_search = **********;
        public static final int abc_searchview_description_submit = **********;
        public static final int abc_searchview_description_voice = **********;
        public static final int abc_shareactionprovider_share_with = **********;
        public static final int abc_shareactionprovider_share_with_application = **********;
        public static final int abc_toolbar_collapse_description = **********;
        public static final int search_menu_title = **********;
        public static final int status_bar_notification_info_overflow = **********;

        private string() {
        }
    }

    /* renamed from: androidx.constraintlayout.widget.R$style */
    public static final class style {
        public static final int AlertDialog_AppCompat = **********;
        public static final int AlertDialog_AppCompat_Light = **********;
        public static final int Animation_AppCompat_Dialog = **********;
        public static final int Animation_AppCompat_DropDownUp = **********;
        public static final int Animation_AppCompat_Tooltip = **********;
        public static final int Base_AlertDialog_AppCompat = **********;
        public static final int Base_AlertDialog_AppCompat_Light = **********;
        public static final int Base_Animation_AppCompat_Dialog = 2131886095;
        public static final int Base_Animation_AppCompat_DropDownUp = 2131886096;
        public static final int Base_Animation_AppCompat_Tooltip = 2131886097;
        public static final int Base_DialogWindowTitleBackground_AppCompat = 2131886100;
        public static final int Base_DialogWindowTitle_AppCompat = 2131886099;
        public static final int Base_TextAppearance_AppCompat = 2131886104;
        public static final int Base_TextAppearance_AppCompat_Body1 = 2131886105;
        public static final int Base_TextAppearance_AppCompat_Body2 = 2131886106;
        public static final int Base_TextAppearance_AppCompat_Button = 2131886107;
        public static final int Base_TextAppearance_AppCompat_Caption = 2131886108;
        public static final int Base_TextAppearance_AppCompat_Display1 = 2131886109;
        public static final int Base_TextAppearance_AppCompat_Display2 = 2131886110;
        public static final int Base_TextAppearance_AppCompat_Display3 = 2131886111;
        public static final int Base_TextAppearance_AppCompat_Display4 = 2131886112;
        public static final int Base_TextAppearance_AppCompat_Headline = 2131886113;
        public static final int Base_TextAppearance_AppCompat_Inverse = 2131886114;
        public static final int Base_TextAppearance_AppCompat_Large = 2131886115;
        public static final int Base_TextAppearance_AppCompat_Large_Inverse = 2131886116;
        public static final int Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large = 2131886117;
        public static final int Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small = 2131886118;
        public static final int Base_TextAppearance_AppCompat_Medium = 2131886119;
        public static final int Base_TextAppearance_AppCompat_Medium_Inverse = 2131886120;
        public static final int Base_TextAppearance_AppCompat_Menu = 2131886121;
        public static final int Base_TextAppearance_AppCompat_SearchResult = 2131886122;
        public static final int Base_TextAppearance_AppCompat_SearchResult_Subtitle = 2131886123;
        public static final int Base_TextAppearance_AppCompat_SearchResult_Title = 2131886124;
        public static final int Base_TextAppearance_AppCompat_Small = 2131886125;
        public static final int Base_TextAppearance_AppCompat_Small_Inverse = 2131886126;
        public static final int Base_TextAppearance_AppCompat_Subhead = 2131886127;
        public static final int Base_TextAppearance_AppCompat_Subhead_Inverse = 2131886128;
        public static final int Base_TextAppearance_AppCompat_Title = 2131886129;
        public static final int Base_TextAppearance_AppCompat_Title_Inverse = 2131886130;
        public static final int Base_TextAppearance_AppCompat_Tooltip = 2131886131;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Menu = 2131886132;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle = 2131886133;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse = 2131886134;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Title = 2131886135;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse = 2131886136;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle = 2131886137;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionMode_Title = 2131886138;
        public static final int Base_TextAppearance_AppCompat_Widget_Button = 2131886139;
        public static final int Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored = 2131886140;
        public static final int Base_TextAppearance_AppCompat_Widget_Button_Colored = 2131886141;
        public static final int Base_TextAppearance_AppCompat_Widget_Button_Inverse = 2131886142;
        public static final int Base_TextAppearance_AppCompat_Widget_DropDownItem = 2131886143;
        public static final int Base_TextAppearance_AppCompat_Widget_PopupMenu_Header = 2131886144;
        public static final int Base_TextAppearance_AppCompat_Widget_PopupMenu_Large = 2131886145;
        public static final int Base_TextAppearance_AppCompat_Widget_PopupMenu_Small = 2131886146;
        public static final int Base_TextAppearance_AppCompat_Widget_Switch = 2131886147;
        public static final int Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem = 2131886148;
        public static final int Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item = 2131886153;
        public static final int Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle = 2131886154;
        public static final int Base_TextAppearance_Widget_AppCompat_Toolbar_Title = 2131886155;
        public static final int Base_ThemeOverlay_AppCompat = 2131886195;
        public static final int Base_ThemeOverlay_AppCompat_ActionBar = 2131886196;
        public static final int Base_ThemeOverlay_AppCompat_Dark = 2131886197;
        public static final int Base_ThemeOverlay_AppCompat_Dark_ActionBar = 2131886198;
        public static final int Base_ThemeOverlay_AppCompat_Dialog = 2131886199;
        public static final int Base_ThemeOverlay_AppCompat_Dialog_Alert = 2131886200;
        public static final int Base_ThemeOverlay_AppCompat_Light = 2131886201;
        public static final int Base_Theme_AppCompat = 2131886156;
        public static final int Base_Theme_AppCompat_CompactMenu = 2131886157;
        public static final int Base_Theme_AppCompat_Dialog = 2131886158;
        public static final int Base_Theme_AppCompat_DialogWhenLarge = 2131886162;
        public static final int Base_Theme_AppCompat_Dialog_Alert = 2131886159;
        public static final int Base_Theme_AppCompat_Dialog_FixedSize = 2131886160;
        public static final int Base_Theme_AppCompat_Dialog_MinWidth = 2131886161;
        public static final int Base_Theme_AppCompat_Light = 2131886163;
        public static final int Base_Theme_AppCompat_Light_DarkActionBar = 2131886164;
        public static final int Base_Theme_AppCompat_Light_Dialog = 2131886165;
        public static final int Base_Theme_AppCompat_Light_DialogWhenLarge = 2131886169;
        public static final int Base_Theme_AppCompat_Light_Dialog_Alert = 2131886166;
        public static final int Base_Theme_AppCompat_Light_Dialog_FixedSize = 2131886167;
        public static final int Base_Theme_AppCompat_Light_Dialog_MinWidth = 2131886168;
        public static final int Base_V21_ThemeOverlay_AppCompat_Dialog = 2131886240;
        public static final int Base_V21_Theme_AppCompat = 2131886232;
        public static final int Base_V21_Theme_AppCompat_Dialog = 2131886233;
        public static final int Base_V21_Theme_AppCompat_Light = 2131886234;
        public static final int Base_V21_Theme_AppCompat_Light_Dialog = 2131886235;
        public static final int Base_V22_Theme_AppCompat = 2131886243;
        public static final int Base_V22_Theme_AppCompat_Light = 2131886244;
        public static final int Base_V23_Theme_AppCompat = 2131886245;
        public static final int Base_V23_Theme_AppCompat_Light = 2131886246;
        public static final int Base_V26_Theme_AppCompat = 2131886251;
        public static final int Base_V26_Theme_AppCompat_Light = 2131886252;
        public static final int Base_V26_Widget_AppCompat_Toolbar = 2131886253;
        public static final int Base_V28_Theme_AppCompat = 2131886254;
        public static final int Base_V28_Theme_AppCompat_Light = 2131886255;
        public static final int Base_V7_ThemeOverlay_AppCompat_Dialog = 2131886260;
        public static final int Base_V7_Theme_AppCompat = 2131886256;
        public static final int Base_V7_Theme_AppCompat_Dialog = 2131886257;
        public static final int Base_V7_Theme_AppCompat_Light = 2131886258;
        public static final int Base_V7_Theme_AppCompat_Light_Dialog = 2131886259;
        public static final int Base_V7_Widget_AppCompat_AutoCompleteTextView = 2131886261;
        public static final int Base_V7_Widget_AppCompat_EditText = 2131886262;
        public static final int Base_V7_Widget_AppCompat_Toolbar = 2131886263;
        public static final int Base_Widget_AppCompat_ActionBar = 2131886264;
        public static final int Base_Widget_AppCompat_ActionBar_Solid = 2131886265;
        public static final int Base_Widget_AppCompat_ActionBar_TabBar = 2131886266;
        public static final int Base_Widget_AppCompat_ActionBar_TabText = 2131886267;
        public static final int Base_Widget_AppCompat_ActionBar_TabView = 2131886268;
        public static final int Base_Widget_AppCompat_ActionButton = 2131886269;
        public static final int Base_Widget_AppCompat_ActionButton_CloseMode = 2131886270;
        public static final int Base_Widget_AppCompat_ActionButton_Overflow = 2131886271;
        public static final int Base_Widget_AppCompat_ActionMode = 2131886272;
        public static final int Base_Widget_AppCompat_ActivityChooserView = 2131886273;
        public static final int Base_Widget_AppCompat_AutoCompleteTextView = 2131886274;
        public static final int Base_Widget_AppCompat_Button = 2131886275;
        public static final int Base_Widget_AppCompat_ButtonBar = 2131886281;
        public static final int Base_Widget_AppCompat_ButtonBar_AlertDialog = 2131886282;
        public static final int Base_Widget_AppCompat_Button_Borderless = 2131886276;
        public static final int Base_Widget_AppCompat_Button_Borderless_Colored = 2131886277;
        public static final int Base_Widget_AppCompat_Button_ButtonBar_AlertDialog = 2131886278;
        public static final int Base_Widget_AppCompat_Button_Colored = 2131886279;
        public static final int Base_Widget_AppCompat_Button_Small = 2131886280;
        public static final int Base_Widget_AppCompat_CompoundButton_CheckBox = 2131886283;
        public static final int Base_Widget_AppCompat_CompoundButton_RadioButton = 2131886284;
        public static final int Base_Widget_AppCompat_CompoundButton_Switch = 2131886285;
        public static final int Base_Widget_AppCompat_DrawerArrowToggle = 2131886286;
        public static final int Base_Widget_AppCompat_DrawerArrowToggle_Common = 2131886287;
        public static final int Base_Widget_AppCompat_DropDownItem_Spinner = 2131886288;
        public static final int Base_Widget_AppCompat_EditText = 2131886289;
        public static final int Base_Widget_AppCompat_ImageButton = 2131886290;
        public static final int Base_Widget_AppCompat_Light_ActionBar = 2131886291;
        public static final int Base_Widget_AppCompat_Light_ActionBar_Solid = 2131886292;
        public static final int Base_Widget_AppCompat_Light_ActionBar_TabBar = 2131886293;
        public static final int Base_Widget_AppCompat_Light_ActionBar_TabText = 2131886294;
        public static final int Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse = 2131886295;
        public static final int Base_Widget_AppCompat_Light_ActionBar_TabView = 2131886296;
        public static final int Base_Widget_AppCompat_Light_PopupMenu = 2131886297;
        public static final int Base_Widget_AppCompat_Light_PopupMenu_Overflow = 2131886298;
        public static final int Base_Widget_AppCompat_ListMenuView = 2131886299;
        public static final int Base_Widget_AppCompat_ListPopupWindow = 2131886300;
        public static final int Base_Widget_AppCompat_ListView = 2131886301;
        public static final int Base_Widget_AppCompat_ListView_DropDown = 2131886302;
        public static final int Base_Widget_AppCompat_ListView_Menu = 2131886303;
        public static final int Base_Widget_AppCompat_PopupMenu = 2131886304;
        public static final int Base_Widget_AppCompat_PopupMenu_Overflow = 2131886305;
        public static final int Base_Widget_AppCompat_PopupWindow = 2131886306;
        public static final int Base_Widget_AppCompat_ProgressBar = 2131886307;
        public static final int Base_Widget_AppCompat_ProgressBar_Horizontal = 2131886308;
        public static final int Base_Widget_AppCompat_RatingBar = 2131886309;
        public static final int Base_Widget_AppCompat_RatingBar_Indicator = 2131886310;
        public static final int Base_Widget_AppCompat_RatingBar_Small = 2131886311;
        public static final int Base_Widget_AppCompat_SearchView = 2131886312;
        public static final int Base_Widget_AppCompat_SearchView_ActionBar = 2131886313;
        public static final int Base_Widget_AppCompat_SeekBar = 2131886314;
        public static final int Base_Widget_AppCompat_SeekBar_Discrete = 2131886315;
        public static final int Base_Widget_AppCompat_Spinner = 2131886316;
        public static final int Base_Widget_AppCompat_Spinner_Underlined = 2131886317;
        public static final int Base_Widget_AppCompat_TextView = 2131886318;
        public static final int Base_Widget_AppCompat_TextView_SpinnerItem = 2131886319;
        public static final int Base_Widget_AppCompat_Toolbar = 2131886320;
        public static final int Base_Widget_AppCompat_Toolbar_Button_Navigation = 2131886321;
        public static final int Platform_AppCompat = 2131886392;
        public static final int Platform_AppCompat_Light = 2131886393;
        public static final int Platform_ThemeOverlay_AppCompat = 2131886398;
        public static final int Platform_ThemeOverlay_AppCompat_Dark = 2131886399;
        public static final int Platform_ThemeOverlay_AppCompat_Light = 2131886400;
        public static final int Platform_V21_AppCompat = 2131886401;
        public static final int Platform_V21_AppCompat_Light = 2131886402;
        public static final int Platform_V25_AppCompat = 2131886403;
        public static final int Platform_V25_AppCompat_Light = 2131886404;
        public static final int Platform_Widget_AppCompat_Spinner = 2131886405;
        public static final int RtlOverlay_DialogWindowTitle_AppCompat = 2131886437;
        public static final int RtlOverlay_Widget_AppCompat_ActionBar_TitleItem = 2131886438;
        public static final int RtlOverlay_Widget_AppCompat_DialogTitle_Icon = 2131886439;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem = 2131886440;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup = 2131886441;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut = 2131886442;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow = 2131886443;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_Text = 2131886444;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_Title = 2131886445;
        public static final int RtlOverlay_Widget_AppCompat_SearchView_MagIcon = 2131886451;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown = 2131886446;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 = 2131886447;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 = 2131886448;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Query = 2131886449;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Text = 2131886450;
        public static final int RtlUnderlay_Widget_AppCompat_ActionButton = 2131886452;
        public static final int RtlUnderlay_Widget_AppCompat_ActionButton_Overflow = 2131886453;
        public static final int TextAppearance_AppCompat = 2131886504;
        public static final int TextAppearance_AppCompat_Body1 = 2131886505;
        public static final int TextAppearance_AppCompat_Body2 = 2131886506;
        public static final int TextAppearance_AppCompat_Button = 2131886507;
        public static final int TextAppearance_AppCompat_Caption = 2131886508;
        public static final int TextAppearance_AppCompat_Display1 = 2131886509;
        public static final int TextAppearance_AppCompat_Display2 = 2131886510;
        public static final int TextAppearance_AppCompat_Display3 = 2131886511;
        public static final int TextAppearance_AppCompat_Display4 = 2131886512;
        public static final int TextAppearance_AppCompat_Headline = 2131886513;
        public static final int TextAppearance_AppCompat_Inverse = 2131886514;
        public static final int TextAppearance_AppCompat_Large = 2131886515;
        public static final int TextAppearance_AppCompat_Large_Inverse = 2131886516;
        public static final int TextAppearance_AppCompat_Light_SearchResult_Subtitle = 2131886517;
        public static final int TextAppearance_AppCompat_Light_SearchResult_Title = 2131886518;
        public static final int TextAppearance_AppCompat_Light_Widget_PopupMenu_Large = 2131886519;
        public static final int TextAppearance_AppCompat_Light_Widget_PopupMenu_Small = 2131886520;
        public static final int TextAppearance_AppCompat_Medium = 2131886521;
        public static final int TextAppearance_AppCompat_Medium_Inverse = 2131886522;
        public static final int TextAppearance_AppCompat_Menu = 2131886523;
        public static final int TextAppearance_AppCompat_SearchResult_Subtitle = 2131886524;
        public static final int TextAppearance_AppCompat_SearchResult_Title = 2131886525;
        public static final int TextAppearance_AppCompat_Small = 2131886526;
        public static final int TextAppearance_AppCompat_Small_Inverse = 2131886527;
        public static final int TextAppearance_AppCompat_Subhead = 2131886528;
        public static final int TextAppearance_AppCompat_Subhead_Inverse = 2131886529;
        public static final int TextAppearance_AppCompat_Title = 2131886530;
        public static final int TextAppearance_AppCompat_Title_Inverse = 2131886531;
        public static final int TextAppearance_AppCompat_Tooltip = 2131886532;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Menu = 2131886533;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Subtitle = 2131886534;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse = 2131886535;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Title = 2131886536;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse = 2131886537;
        public static final int TextAppearance_AppCompat_Widget_ActionMode_Subtitle = 2131886538;
        public static final int TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse = 2131886539;
        public static final int TextAppearance_AppCompat_Widget_ActionMode_Title = 2131886540;
        public static final int TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse = 2131886541;
        public static final int TextAppearance_AppCompat_Widget_Button = 2131886542;
        public static final int TextAppearance_AppCompat_Widget_Button_Borderless_Colored = 2131886543;
        public static final int TextAppearance_AppCompat_Widget_Button_Colored = 2131886544;
        public static final int TextAppearance_AppCompat_Widget_Button_Inverse = 2131886545;
        public static final int TextAppearance_AppCompat_Widget_DropDownItem = 2131886546;
        public static final int TextAppearance_AppCompat_Widget_PopupMenu_Header = 2131886547;
        public static final int TextAppearance_AppCompat_Widget_PopupMenu_Large = 2131886548;
        public static final int TextAppearance_AppCompat_Widget_PopupMenu_Small = 2131886549;
        public static final int TextAppearance_AppCompat_Widget_Switch = 2131886550;
        public static final int TextAppearance_AppCompat_Widget_TextView_SpinnerItem = 2131886551;
        public static final int TextAppearance_Compat_Notification = 2131886552;
        public static final int TextAppearance_Compat_Notification_Info = 2131886553;
        public static final int TextAppearance_Compat_Notification_Line2 = 2131886555;
        public static final int TextAppearance_Compat_Notification_Time = 2131886558;
        public static final int TextAppearance_Compat_Notification_Title = 2131886560;
        public static final int TextAppearance_Widget_AppCompat_ExpandedMenu_Item = 2131886623;
        public static final int TextAppearance_Widget_AppCompat_Toolbar_Subtitle = 2131886624;
        public static final int TextAppearance_Widget_AppCompat_Toolbar_Title = 2131886625;
        public static final int ThemeOverlay_AppCompat = 2131886731;
        public static final int ThemeOverlay_AppCompat_ActionBar = 2131886732;
        public static final int ThemeOverlay_AppCompat_Dark = 2131886733;
        public static final int ThemeOverlay_AppCompat_Dark_ActionBar = 2131886734;
        public static final int ThemeOverlay_AppCompat_DayNight = 2131886735;
        public static final int ThemeOverlay_AppCompat_DayNight_ActionBar = 2131886736;
        public static final int ThemeOverlay_AppCompat_Dialog = 2131886737;
        public static final int ThemeOverlay_AppCompat_Dialog_Alert = 2131886738;
        public static final int ThemeOverlay_AppCompat_Light = 2131886739;
        public static final int Theme_AppCompat = 2131886626;
        public static final int Theme_AppCompat_CompactMenu = 2131886627;
        public static final int Theme_AppCompat_DayNight = 2131886628;
        public static final int Theme_AppCompat_DayNight_DarkActionBar = 2131886629;
        public static final int Theme_AppCompat_DayNight_Dialog = 2131886630;
        public static final int Theme_AppCompat_DayNight_DialogWhenLarge = 2131886633;
        public static final int Theme_AppCompat_DayNight_Dialog_Alert = 2131886631;
        public static final int Theme_AppCompat_DayNight_Dialog_MinWidth = 2131886632;
        public static final int Theme_AppCompat_DayNight_NoActionBar = 2131886634;
        public static final int Theme_AppCompat_Dialog = 2131886635;
        public static final int Theme_AppCompat_DialogWhenLarge = 2131886638;
        public static final int Theme_AppCompat_Dialog_Alert = 2131886636;
        public static final int Theme_AppCompat_Dialog_MinWidth = 2131886637;
        public static final int Theme_AppCompat_Empty = 2131886639;
        public static final int Theme_AppCompat_Light = 2131886640;
        public static final int Theme_AppCompat_Light_DarkActionBar = 2131886641;
        public static final int Theme_AppCompat_Light_Dialog = 2131886642;
        public static final int Theme_AppCompat_Light_DialogWhenLarge = 2131886645;
        public static final int Theme_AppCompat_Light_Dialog_Alert = 2131886643;
        public static final int Theme_AppCompat_Light_Dialog_MinWidth = 2131886644;
        public static final int Theme_AppCompat_Light_NoActionBar = 2131886646;
        public static final int Theme_AppCompat_NoActionBar = 2131886647;
        public static final int Widget_AppCompat_ActionBar = 2131886839;
        public static final int Widget_AppCompat_ActionBar_Solid = 2131886840;
        public static final int Widget_AppCompat_ActionBar_TabBar = 2131886841;
        public static final int Widget_AppCompat_ActionBar_TabText = 2131886842;
        public static final int Widget_AppCompat_ActionBar_TabView = 2131886843;
        public static final int Widget_AppCompat_ActionButton = 2131886844;
        public static final int Widget_AppCompat_ActionButton_CloseMode = 2131886845;
        public static final int Widget_AppCompat_ActionButton_Overflow = 2131886846;
        public static final int Widget_AppCompat_ActionMode = 2131886847;
        public static final int Widget_AppCompat_ActivityChooserView = 2131886848;
        public static final int Widget_AppCompat_AutoCompleteTextView = 2131886849;
        public static final int Widget_AppCompat_Button = 2131886850;
        public static final int Widget_AppCompat_ButtonBar = 2131886856;
        public static final int Widget_AppCompat_ButtonBar_AlertDialog = 2131886857;
        public static final int Widget_AppCompat_Button_Borderless = 2131886851;
        public static final int Widget_AppCompat_Button_Borderless_Colored = 2131886852;
        public static final int Widget_AppCompat_Button_ButtonBar_AlertDialog = 2131886853;
        public static final int Widget_AppCompat_Button_Colored = 2131886854;
        public static final int Widget_AppCompat_Button_Small = 2131886855;
        public static final int Widget_AppCompat_CompoundButton_CheckBox = 2131886858;
        public static final int Widget_AppCompat_CompoundButton_RadioButton = 2131886859;
        public static final int Widget_AppCompat_CompoundButton_Switch = 2131886860;
        public static final int Widget_AppCompat_DrawerArrowToggle = 2131886861;
        public static final int Widget_AppCompat_DropDownItem_Spinner = 2131886862;
        public static final int Widget_AppCompat_EditText = 2131886863;
        public static final int Widget_AppCompat_ImageButton = 2131886864;
        public static final int Widget_AppCompat_Light_ActionBar = 2131886865;
        public static final int Widget_AppCompat_Light_ActionBar_Solid = 2131886866;
        public static final int Widget_AppCompat_Light_ActionBar_Solid_Inverse = 2131886867;
        public static final int Widget_AppCompat_Light_ActionBar_TabBar = 2131886868;
        public static final int Widget_AppCompat_Light_ActionBar_TabBar_Inverse = 2131886869;
        public static final int Widget_AppCompat_Light_ActionBar_TabText = 2131886870;
        public static final int Widget_AppCompat_Light_ActionBar_TabText_Inverse = 2131886871;
        public static final int Widget_AppCompat_Light_ActionBar_TabView = 2131886872;
        public static final int Widget_AppCompat_Light_ActionBar_TabView_Inverse = 2131886873;
        public static final int Widget_AppCompat_Light_ActionButton = 2131886874;
        public static final int Widget_AppCompat_Light_ActionButton_CloseMode = 2131886875;
        public static final int Widget_AppCompat_Light_ActionButton_Overflow = 2131886876;
        public static final int Widget_AppCompat_Light_ActionMode_Inverse = 2131886877;
        public static final int Widget_AppCompat_Light_ActivityChooserView = 2131886878;
        public static final int Widget_AppCompat_Light_AutoCompleteTextView = 2131886879;
        public static final int Widget_AppCompat_Light_DropDownItem_Spinner = 2131886880;
        public static final int Widget_AppCompat_Light_ListPopupWindow = 2131886881;
        public static final int Widget_AppCompat_Light_ListView_DropDown = 2131886882;
        public static final int Widget_AppCompat_Light_PopupMenu = 2131886883;
        public static final int Widget_AppCompat_Light_PopupMenu_Overflow = 2131886884;
        public static final int Widget_AppCompat_Light_SearchView = 2131886885;
        public static final int Widget_AppCompat_Light_Spinner_DropDown_ActionBar = 2131886886;
        public static final int Widget_AppCompat_ListMenuView = 2131886887;
        public static final int Widget_AppCompat_ListPopupWindow = 2131886888;
        public static final int Widget_AppCompat_ListView = 2131886889;
        public static final int Widget_AppCompat_ListView_DropDown = 2131886890;
        public static final int Widget_AppCompat_ListView_Menu = 2131886891;
        public static final int Widget_AppCompat_PopupMenu = 2131886892;
        public static final int Widget_AppCompat_PopupMenu_Overflow = 2131886893;
        public static final int Widget_AppCompat_PopupWindow = 2131886894;
        public static final int Widget_AppCompat_ProgressBar = 2131886895;
        public static final int Widget_AppCompat_ProgressBar_Horizontal = 2131886896;
        public static final int Widget_AppCompat_RatingBar = 2131886897;
        public static final int Widget_AppCompat_RatingBar_Indicator = 2131886898;
        public static final int Widget_AppCompat_RatingBar_Small = 2131886899;
        public static final int Widget_AppCompat_SearchView = 2131886900;
        public static final int Widget_AppCompat_SearchView_ActionBar = 2131886901;
        public static final int Widget_AppCompat_SeekBar = 2131886902;
        public static final int Widget_AppCompat_SeekBar_Discrete = 2131886903;
        public static final int Widget_AppCompat_Spinner = 2131886904;
        public static final int Widget_AppCompat_Spinner_DropDown = 2131886905;
        public static final int Widget_AppCompat_Spinner_DropDown_ActionBar = 2131886906;
        public static final int Widget_AppCompat_Spinner_Underlined = 2131886907;
        public static final int Widget_AppCompat_TextView = 2131886908;
        public static final int Widget_AppCompat_TextView_SpinnerItem = 2131886909;
        public static final int Widget_AppCompat_Toolbar = 2131886910;
        public static final int Widget_AppCompat_Toolbar_Button_Navigation = 2131886911;
        public static final int Widget_Compat_NotificationActionContainer = 2131886912;
        public static final int Widget_Compat_NotificationActionText = 2131886913;

        private style() {
        }
    }

    /* renamed from: androidx.constraintlayout.widget.R$styleable */
    public static final class styleable {
        public static final int ActionBarLayout_android_layout_gravity = 0;
        public static final int ActionBar_background = 0;
        public static final int ActionBar_backgroundSplit = 1;
        public static final int ActionBar_backgroundStacked = 2;
        public static final int ActionBar_contentInsetEnd = 3;
        public static final int ActionBar_contentInsetEndWithActions = 4;
        public static final int ActionBar_contentInsetLeft = 5;
        public static final int ActionBar_contentInsetRight = 6;
        public static final int ActionBar_contentInsetStart = 7;
        public static final int ActionBar_contentInsetStartWithNavigation = 8;
        public static final int ActionBar_customNavigationLayout = 9;
        public static final int ActionBar_displayOptions = 10;
        public static final int ActionBar_divider = 11;
        public static final int ActionBar_elevation = 12;
        public static final int ActionBar_height = 13;
        public static final int ActionBar_hideOnContentScroll = 14;
        public static final int ActionBar_homeAsUpIndicator = 15;
        public static final int ActionBar_homeLayout = 16;
        public static final int ActionBar_icon = 17;
        public static final int ActionBar_indeterminateProgressStyle = 18;
        public static final int ActionBar_itemPadding = 19;
        public static final int ActionBar_logo = 20;
        public static final int ActionBar_navigationMode = 21;
        public static final int ActionBar_popupTheme = 22;
        public static final int ActionBar_progressBarPadding = 23;
        public static final int ActionBar_progressBarStyle = 24;
        public static final int ActionBar_subtitle = 25;
        public static final int ActionBar_subtitleTextStyle = 26;
        public static final int ActionBar_title = 27;
        public static final int ActionBar_titleTextStyle = 28;
        public static final int ActionMenuItemView_android_minWidth = 0;
        public static final int ActionMode_background = 0;
        public static final int ActionMode_backgroundSplit = 1;
        public static final int ActionMode_closeItemLayout = 2;
        public static final int ActionMode_height = 3;
        public static final int ActionMode_subtitleTextStyle = 4;
        public static final int ActionMode_titleTextStyle = 5;
        public static final int ActivityChooserView_expandActivityOverflowButtonDrawable = 0;
        public static final int ActivityChooserView_initialActivityCount = 1;
        public static final int AlertDialog_android_layout = 0;
        public static final int AlertDialog_buttonIconDimen = 1;
        public static final int AlertDialog_buttonPanelSideLayout = 2;
        public static final int AlertDialog_listItemLayout = 3;
        public static final int AlertDialog_listLayout = 4;
        public static final int AlertDialog_multiChoiceItemLayout = 5;
        public static final int AlertDialog_showTitle = 6;
        public static final int AlertDialog_singleChoiceItemLayout = 7;
        public static final int AnimatedStateListDrawableCompat_android_constantSize = 3;
        public static final int AnimatedStateListDrawableCompat_android_dither = 0;
        public static final int AnimatedStateListDrawableCompat_android_enterFadeDuration = 4;
        public static final int AnimatedStateListDrawableCompat_android_exitFadeDuration = 5;
        public static final int AnimatedStateListDrawableCompat_android_variablePadding = 2;
        public static final int AnimatedStateListDrawableCompat_android_visible = 1;
        public static final int AnimatedStateListDrawableItem_android_drawable = 1;
        public static final int AnimatedStateListDrawableItem_android_id = 0;
        public static final int AnimatedStateListDrawableTransition_android_drawable = 0;
        public static final int AnimatedStateListDrawableTransition_android_fromId = 2;
        public static final int AnimatedStateListDrawableTransition_android_reversible = 3;
        public static final int AnimatedStateListDrawableTransition_android_toId = 1;
        public static final int AppCompatImageView_android_src = 0;
        public static final int AppCompatImageView_srcCompat = 1;
        public static final int AppCompatImageView_tint = 2;
        public static final int AppCompatImageView_tintMode = 3;
        public static final int AppCompatSeekBar_android_thumb = 0;
        public static final int AppCompatSeekBar_tickMark = 1;
        public static final int AppCompatSeekBar_tickMarkTint = 2;
        public static final int AppCompatSeekBar_tickMarkTintMode = 3;
        public static final int AppCompatTextHelper_android_drawableBottom = 2;
        public static final int AppCompatTextHelper_android_drawableEnd = 6;
        public static final int AppCompatTextHelper_android_drawableLeft = 3;
        public static final int AppCompatTextHelper_android_drawableRight = 4;
        public static final int AppCompatTextHelper_android_drawableStart = 5;
        public static final int AppCompatTextHelper_android_drawableTop = 1;
        public static final int AppCompatTextHelper_android_textAppearance = 0;
        public static final int AppCompatTextView_android_textAppearance = 0;
        public static final int AppCompatTextView_autoSizeMaxTextSize = 1;
        public static final int AppCompatTextView_autoSizeMinTextSize = 2;
        public static final int AppCompatTextView_autoSizePresetSizes = 3;
        public static final int AppCompatTextView_autoSizeStepGranularity = 4;
        public static final int AppCompatTextView_autoSizeTextType = 5;
        public static final int AppCompatTextView_drawableBottomCompat = 6;
        public static final int AppCompatTextView_drawableEndCompat = 7;
        public static final int AppCompatTextView_drawableLeftCompat = 8;
        public static final int AppCompatTextView_drawableRightCompat = 9;
        public static final int AppCompatTextView_drawableStartCompat = 10;
        public static final int AppCompatTextView_drawableTint = 11;
        public static final int AppCompatTextView_drawableTintMode = 12;
        public static final int AppCompatTextView_drawableTopCompat = 13;
        public static final int AppCompatTextView_emojiCompatEnabled = 14;
        public static final int AppCompatTextView_firstBaselineToTopHeight = 15;
        public static final int AppCompatTextView_fontFamily = 16;
        public static final int AppCompatTextView_fontVariationSettings = 17;
        public static final int AppCompatTextView_lastBaselineToBottomHeight = 18;
        public static final int AppCompatTextView_lineHeight = 19;
        public static final int AppCompatTextView_textAllCaps = 20;
        public static final int AppCompatTextView_textLocale = 21;
        public static final int AppCompatTheme_actionBarDivider = 2;
        public static final int AppCompatTheme_actionBarItemBackground = 3;
        public static final int AppCompatTheme_actionBarPopupTheme = 4;
        public static final int AppCompatTheme_actionBarSize = 5;
        public static final int AppCompatTheme_actionBarSplitStyle = 6;
        public static final int AppCompatTheme_actionBarStyle = 7;
        public static final int AppCompatTheme_actionBarTabBarStyle = 8;
        public static final int AppCompatTheme_actionBarTabStyle = 9;
        public static final int AppCompatTheme_actionBarTabTextStyle = 10;
        public static final int AppCompatTheme_actionBarTheme = 11;
        public static final int AppCompatTheme_actionBarWidgetTheme = 12;
        public static final int AppCompatTheme_actionButtonStyle = 13;
        public static final int AppCompatTheme_actionDropDownStyle = 14;
        public static final int AppCompatTheme_actionMenuTextAppearance = 15;
        public static final int AppCompatTheme_actionMenuTextColor = 16;
        public static final int AppCompatTheme_actionModeBackground = 17;
        public static final int AppCompatTheme_actionModeCloseButtonStyle = 18;
        public static final int AppCompatTheme_actionModeCloseContentDescription = 19;
        public static final int AppCompatTheme_actionModeCloseDrawable = 20;
        public static final int AppCompatTheme_actionModeCopyDrawable = 21;
        public static final int AppCompatTheme_actionModeCutDrawable = 22;
        public static final int AppCompatTheme_actionModeFindDrawable = 23;
        public static final int AppCompatTheme_actionModePasteDrawable = 24;
        public static final int AppCompatTheme_actionModePopupWindowStyle = 25;
        public static final int AppCompatTheme_actionModeSelectAllDrawable = 26;
        public static final int AppCompatTheme_actionModeShareDrawable = 27;
        public static final int AppCompatTheme_actionModeSplitBackground = 28;
        public static final int AppCompatTheme_actionModeStyle = 29;
        public static final int AppCompatTheme_actionModeTheme = 30;
        public static final int AppCompatTheme_actionModeWebSearchDrawable = 31;
        public static final int AppCompatTheme_actionOverflowButtonStyle = 32;
        public static final int AppCompatTheme_actionOverflowMenuStyle = 33;
        public static final int AppCompatTheme_activityChooserViewStyle = 34;
        public static final int AppCompatTheme_alertDialogButtonGroupStyle = 35;
        public static final int AppCompatTheme_alertDialogCenterButtons = 36;
        public static final int AppCompatTheme_alertDialogStyle = 37;
        public static final int AppCompatTheme_alertDialogTheme = 38;
        public static final int AppCompatTheme_android_windowAnimationStyle = 1;
        public static final int AppCompatTheme_android_windowIsFloating = 0;
        public static final int AppCompatTheme_autoCompleteTextViewStyle = 39;
        public static final int AppCompatTheme_borderlessButtonStyle = 40;
        public static final int AppCompatTheme_buttonBarButtonStyle = 41;
        public static final int AppCompatTheme_buttonBarNegativeButtonStyle = 42;
        public static final int AppCompatTheme_buttonBarNeutralButtonStyle = 43;
        public static final int AppCompatTheme_buttonBarPositiveButtonStyle = 44;
        public static final int AppCompatTheme_buttonBarStyle = 45;
        public static final int AppCompatTheme_buttonStyle = 46;
        public static final int AppCompatTheme_buttonStyleSmall = 47;
        public static final int AppCompatTheme_checkboxStyle = 48;
        public static final int AppCompatTheme_checkedTextViewStyle = 49;
        public static final int AppCompatTheme_colorAccent = 50;
        public static final int AppCompatTheme_colorBackgroundFloating = 51;
        public static final int AppCompatTheme_colorButtonNormal = 52;
        public static final int AppCompatTheme_colorControlActivated = 53;
        public static final int AppCompatTheme_colorControlHighlight = 54;
        public static final int AppCompatTheme_colorControlNormal = 55;
        public static final int AppCompatTheme_colorError = 56;
        public static final int AppCompatTheme_colorPrimary = 57;
        public static final int AppCompatTheme_colorPrimaryDark = 58;
        public static final int AppCompatTheme_colorSwitchThumbNormal = 59;
        public static final int AppCompatTheme_controlBackground = 60;
        public static final int AppCompatTheme_dialogCornerRadius = 61;
        public static final int AppCompatTheme_dialogPreferredPadding = 62;
        public static final int AppCompatTheme_dialogTheme = 63;
        public static final int AppCompatTheme_dividerHorizontal = 64;
        public static final int AppCompatTheme_dividerVertical = 65;
        public static final int AppCompatTheme_dropDownListViewStyle = 66;
        public static final int AppCompatTheme_dropdownListPreferredItemHeight = 67;
        public static final int AppCompatTheme_editTextBackground = 68;
        public static final int AppCompatTheme_editTextColor = 69;
        public static final int AppCompatTheme_editTextStyle = 70;
        public static final int AppCompatTheme_homeAsUpIndicator = 71;
        public static final int AppCompatTheme_imageButtonStyle = 72;
        public static final int AppCompatTheme_listChoiceBackgroundIndicator = 73;
        public static final int AppCompatTheme_listChoiceIndicatorMultipleAnimated = 74;
        public static final int AppCompatTheme_listChoiceIndicatorSingleAnimated = 75;
        public static final int AppCompatTheme_listDividerAlertDialog = 76;
        public static final int AppCompatTheme_listMenuViewStyle = 77;
        public static final int AppCompatTheme_listPopupWindowStyle = 78;
        public static final int AppCompatTheme_listPreferredItemHeight = 79;
        public static final int AppCompatTheme_listPreferredItemHeightLarge = 80;
        public static final int AppCompatTheme_listPreferredItemHeightSmall = 81;
        public static final int AppCompatTheme_listPreferredItemPaddingEnd = 82;
        public static final int AppCompatTheme_listPreferredItemPaddingLeft = 83;
        public static final int AppCompatTheme_listPreferredItemPaddingRight = 84;
        public static final int AppCompatTheme_listPreferredItemPaddingStart = 85;
        public static final int AppCompatTheme_panelBackground = 86;
        public static final int AppCompatTheme_panelMenuListTheme = 87;
        public static final int AppCompatTheme_panelMenuListWidth = 88;
        public static final int AppCompatTheme_popupMenuStyle = 89;
        public static final int AppCompatTheme_popupWindowStyle = 90;
        public static final int AppCompatTheme_radioButtonStyle = 91;
        public static final int AppCompatTheme_ratingBarStyle = 92;
        public static final int AppCompatTheme_ratingBarStyleIndicator = 93;
        public static final int AppCompatTheme_ratingBarStyleSmall = 94;
        public static final int AppCompatTheme_searchViewStyle = 95;
        public static final int AppCompatTheme_seekBarStyle = 96;
        public static final int AppCompatTheme_selectableItemBackground = 97;
        public static final int AppCompatTheme_selectableItemBackgroundBorderless = 98;
        public static final int AppCompatTheme_spinnerDropDownItemStyle = 99;
        public static final int AppCompatTheme_spinnerStyle = 100;
        public static final int AppCompatTheme_switchStyle = 101;
        public static final int AppCompatTheme_textAppearanceLargePopupMenu = 102;
        public static final int AppCompatTheme_textAppearanceListItem = 103;
        public static final int AppCompatTheme_textAppearanceListItemSecondary = 104;
        public static final int AppCompatTheme_textAppearanceListItemSmall = 105;
        public static final int AppCompatTheme_textAppearancePopupMenuHeader = 106;
        public static final int AppCompatTheme_textAppearanceSearchResultSubtitle = 107;
        public static final int AppCompatTheme_textAppearanceSearchResultTitle = 108;
        public static final int AppCompatTheme_textAppearanceSmallPopupMenu = 109;
        public static final int AppCompatTheme_textColorAlertDialogListItem = 110;
        public static final int AppCompatTheme_textColorSearchUrl = 111;
        public static final int AppCompatTheme_toolbarNavigationButtonStyle = 112;
        public static final int AppCompatTheme_toolbarStyle = 113;
        public static final int AppCompatTheme_tooltipForegroundColor = 114;
        public static final int AppCompatTheme_tooltipFrameBackground = 115;
        public static final int AppCompatTheme_viewInflaterClass = 116;
        public static final int AppCompatTheme_windowActionBar = 117;
        public static final int AppCompatTheme_windowActionBarOverlay = 118;
        public static final int AppCompatTheme_windowActionModeOverlay = 119;
        public static final int AppCompatTheme_windowFixedHeightMajor = 120;
        public static final int AppCompatTheme_windowFixedHeightMinor = 121;
        public static final int AppCompatTheme_windowFixedWidthMajor = 122;
        public static final int AppCompatTheme_windowFixedWidthMinor = 123;
        public static final int AppCompatTheme_windowMinWidthMajor = 124;
        public static final int AppCompatTheme_windowMinWidthMinor = 125;
        public static final int AppCompatTheme_windowNoTitle = 126;
        public static final int ButtonBarLayout_allowStacking = 0;
        public static final int Carousel_carousel_backwardTransition = 0;
        public static final int Carousel_carousel_emptyViewsBehavior = 1;
        public static final int Carousel_carousel_firstView = 2;
        public static final int Carousel_carousel_forwardTransition = 3;
        public static final int Carousel_carousel_infinite = 4;
        public static final int Carousel_carousel_nextState = 5;
        public static final int Carousel_carousel_previousState = 6;
        public static final int Carousel_carousel_touchUpMode = 7;
        public static final int Carousel_carousel_touchUp_dampeningFactor = 8;
        public static final int Carousel_carousel_touchUp_velocityThreshold = 9;
        public static final int ColorStateListItem_alpha = 3;
        public static final int ColorStateListItem_android_alpha = 1;
        public static final int ColorStateListItem_android_color = 0;
        public static final int ColorStateListItem_android_lStar = 2;
        public static final int ColorStateListItem_lStar = 4;
        public static final int CompoundButton_android_button = 0;
        public static final int CompoundButton_buttonCompat = 1;
        public static final int CompoundButton_buttonTint = 2;
        public static final int CompoundButton_buttonTintMode = 3;
        public static final int ConstraintLayout_Layout_android_elevation = 22;
        public static final int ConstraintLayout_Layout_android_layout_height = 8;
        public static final int ConstraintLayout_Layout_android_layout_margin = 9;
        public static final int ConstraintLayout_Layout_android_layout_marginBottom = 13;
        public static final int ConstraintLayout_Layout_android_layout_marginEnd = 21;
        public static final int ConstraintLayout_Layout_android_layout_marginHorizontal = 23;
        public static final int ConstraintLayout_Layout_android_layout_marginLeft = 10;
        public static final int ConstraintLayout_Layout_android_layout_marginRight = 12;
        public static final int ConstraintLayout_Layout_android_layout_marginStart = 20;
        public static final int ConstraintLayout_Layout_android_layout_marginTop = 11;
        public static final int ConstraintLayout_Layout_android_layout_marginVertical = 24;
        public static final int ConstraintLayout_Layout_android_layout_width = 7;
        public static final int ConstraintLayout_Layout_android_maxHeight = 15;
        public static final int ConstraintLayout_Layout_android_maxWidth = 14;
        public static final int ConstraintLayout_Layout_android_minHeight = 17;
        public static final int ConstraintLayout_Layout_android_minWidth = 16;
        public static final int ConstraintLayout_Layout_android_orientation = 0;
        public static final int ConstraintLayout_Layout_android_padding = 1;
        public static final int ConstraintLayout_Layout_android_paddingBottom = 5;
        public static final int ConstraintLayout_Layout_android_paddingEnd = 19;
        public static final int ConstraintLayout_Layout_android_paddingLeft = 2;
        public static final int ConstraintLayout_Layout_android_paddingRight = 4;
        public static final int ConstraintLayout_Layout_android_paddingStart = 18;
        public static final int ConstraintLayout_Layout_android_paddingTop = 3;
        public static final int ConstraintLayout_Layout_android_visibility = 6;
        public static final int ConstraintLayout_Layout_barrierAllowsGoneWidgets = 25;
        public static final int ConstraintLayout_Layout_barrierDirection = 26;
        public static final int ConstraintLayout_Layout_barrierMargin = 27;
        public static final int ConstraintLayout_Layout_chainUseRtl = 28;
        public static final int ConstraintLayout_Layout_circularflow_angles = 29;
        public static final int ConstraintLayout_Layout_circularflow_defaultAngle = 30;
        public static final int ConstraintLayout_Layout_circularflow_defaultRadius = 31;
        public static final int ConstraintLayout_Layout_circularflow_radiusInDP = 32;
        public static final int ConstraintLayout_Layout_circularflow_viewCenter = 33;
        public static final int ConstraintLayout_Layout_constraintSet = 34;
        public static final int ConstraintLayout_Layout_constraint_referenced_ids = 35;
        public static final int ConstraintLayout_Layout_constraint_referenced_tags = 36;
        public static final int ConstraintLayout_Layout_flow_firstHorizontalBias = 37;
        public static final int ConstraintLayout_Layout_flow_firstHorizontalStyle = 38;
        public static final int ConstraintLayout_Layout_flow_firstVerticalBias = 39;
        public static final int ConstraintLayout_Layout_flow_firstVerticalStyle = 40;
        public static final int ConstraintLayout_Layout_flow_horizontalAlign = 41;
        public static final int ConstraintLayout_Layout_flow_horizontalBias = 42;
        public static final int ConstraintLayout_Layout_flow_horizontalGap = 43;
        public static final int ConstraintLayout_Layout_flow_horizontalStyle = 44;
        public static final int ConstraintLayout_Layout_flow_lastHorizontalBias = 45;
        public static final int ConstraintLayout_Layout_flow_lastHorizontalStyle = 46;
        public static final int ConstraintLayout_Layout_flow_lastVerticalBias = 47;
        public static final int ConstraintLayout_Layout_flow_lastVerticalStyle = 48;
        public static final int ConstraintLayout_Layout_flow_maxElementsWrap = 49;
        public static final int ConstraintLayout_Layout_flow_verticalAlign = 50;
        public static final int ConstraintLayout_Layout_flow_verticalBias = 51;
        public static final int ConstraintLayout_Layout_flow_verticalGap = 52;
        public static final int ConstraintLayout_Layout_flow_verticalStyle = 53;
        public static final int ConstraintLayout_Layout_flow_wrapMode = 54;
        public static final int ConstraintLayout_Layout_guidelineUseRtl = 55;
        public static final int ConstraintLayout_Layout_layoutDescription = 56;
        public static final int ConstraintLayout_Layout_layout_constrainedHeight = 57;
        public static final int ConstraintLayout_Layout_layout_constrainedWidth = 58;
        public static final int ConstraintLayout_Layout_layout_constraintBaseline_creator = 59;
        public static final int ConstraintLayout_Layout_layout_constraintBaseline_toBaselineOf = 60;
        public static final int ConstraintLayout_Layout_layout_constraintBaseline_toBottomOf = 61;
        public static final int ConstraintLayout_Layout_layout_constraintBaseline_toTopOf = 62;
        public static final int ConstraintLayout_Layout_layout_constraintBottom_creator = 63;
        public static final int ConstraintLayout_Layout_layout_constraintBottom_toBottomOf = 64;
        public static final int ConstraintLayout_Layout_layout_constraintBottom_toTopOf = 65;
        public static final int ConstraintLayout_Layout_layout_constraintCircle = 66;
        public static final int ConstraintLayout_Layout_layout_constraintCircleAngle = 67;
        public static final int ConstraintLayout_Layout_layout_constraintCircleRadius = 68;
        public static final int ConstraintLayout_Layout_layout_constraintDimensionRatio = 69;
        public static final int ConstraintLayout_Layout_layout_constraintEnd_toEndOf = 70;
        public static final int ConstraintLayout_Layout_layout_constraintEnd_toStartOf = 71;
        public static final int ConstraintLayout_Layout_layout_constraintGuide_begin = 72;
        public static final int ConstraintLayout_Layout_layout_constraintGuide_end = 73;
        public static final int ConstraintLayout_Layout_layout_constraintGuide_percent = 74;
        public static final int ConstraintLayout_Layout_layout_constraintHeight = 75;
        public static final int ConstraintLayout_Layout_layout_constraintHeight_default = 76;
        public static final int ConstraintLayout_Layout_layout_constraintHeight_max = 77;
        public static final int ConstraintLayout_Layout_layout_constraintHeight_min = 78;
        public static final int ConstraintLayout_Layout_layout_constraintHeight_percent = 79;
        public static final int ConstraintLayout_Layout_layout_constraintHorizontal_bias = 80;
        public static final int ConstraintLayout_Layout_layout_constraintHorizontal_chainStyle = 81;
        public static final int ConstraintLayout_Layout_layout_constraintHorizontal_weight = 82;
        public static final int ConstraintLayout_Layout_layout_constraintLeft_creator = 83;
        public static final int ConstraintLayout_Layout_layout_constraintLeft_toLeftOf = 84;
        public static final int ConstraintLayout_Layout_layout_constraintLeft_toRightOf = 85;
        public static final int ConstraintLayout_Layout_layout_constraintRight_creator = 86;
        public static final int ConstraintLayout_Layout_layout_constraintRight_toLeftOf = 87;
        public static final int ConstraintLayout_Layout_layout_constraintRight_toRightOf = 88;
        public static final int ConstraintLayout_Layout_layout_constraintStart_toEndOf = 89;
        public static final int ConstraintLayout_Layout_layout_constraintStart_toStartOf = 90;
        public static final int ConstraintLayout_Layout_layout_constraintTag = 91;
        public static final int ConstraintLayout_Layout_layout_constraintTop_creator = 92;
        public static final int ConstraintLayout_Layout_layout_constraintTop_toBottomOf = 93;
        public static final int ConstraintLayout_Layout_layout_constraintTop_toTopOf = 94;
        public static final int ConstraintLayout_Layout_layout_constraintVertical_bias = 95;
        public static final int ConstraintLayout_Layout_layout_constraintVertical_chainStyle = 96;
        public static final int ConstraintLayout_Layout_layout_constraintVertical_weight = 97;
        public static final int ConstraintLayout_Layout_layout_constraintWidth = 98;
        public static final int ConstraintLayout_Layout_layout_constraintWidth_default = 99;
        public static final int ConstraintLayout_Layout_layout_constraintWidth_max = 100;
        public static final int ConstraintLayout_Layout_layout_constraintWidth_min = 101;
        public static final int ConstraintLayout_Layout_layout_constraintWidth_percent = 102;
        public static final int ConstraintLayout_Layout_layout_editor_absoluteX = 103;
        public static final int ConstraintLayout_Layout_layout_editor_absoluteY = 104;
        public static final int ConstraintLayout_Layout_layout_goneMarginBaseline = 105;
        public static final int ConstraintLayout_Layout_layout_goneMarginBottom = 106;
        public static final int ConstraintLayout_Layout_layout_goneMarginEnd = 107;
        public static final int ConstraintLayout_Layout_layout_goneMarginLeft = 108;
        public static final int ConstraintLayout_Layout_layout_goneMarginRight = 109;
        public static final int ConstraintLayout_Layout_layout_goneMarginStart = 110;
        public static final int ConstraintLayout_Layout_layout_goneMarginTop = 111;
        public static final int ConstraintLayout_Layout_layout_marginBaseline = 112;
        public static final int ConstraintLayout_Layout_layout_optimizationLevel = 113;
        public static final int ConstraintLayout_Layout_layout_wrapBehaviorInParent = 114;
        public static final int ConstraintLayout_ReactiveGuide_reactiveGuide_animateChange = 0;

        /* renamed from: ConstraintLayout_ReactiveGuide_reactiveGuide_applyToAllConstraintSets */
        public static final int f334xfdeff96 = 1;

        /* renamed from: ConstraintLayout_ReactiveGuide_reactiveGuide_applyToConstraintSet */
        public static final int f335x2694048c = 2;
        public static final int ConstraintLayout_ReactiveGuide_reactiveGuide_valueId = 3;
        public static final int ConstraintLayout_placeholder_content = 0;
        public static final int ConstraintLayout_placeholder_placeholder_emptyVisibility = 1;
        public static final int ConstraintOverride_android_alpha = 13;
        public static final int ConstraintOverride_android_elevation = 26;
        public static final int ConstraintOverride_android_id = 1;
        public static final int ConstraintOverride_android_layout_height = 4;
        public static final int ConstraintOverride_android_layout_marginBottom = 8;
        public static final int ConstraintOverride_android_layout_marginEnd = 24;
        public static final int ConstraintOverride_android_layout_marginLeft = 5;
        public static final int ConstraintOverride_android_layout_marginRight = 7;
        public static final int ConstraintOverride_android_layout_marginStart = 23;
        public static final int ConstraintOverride_android_layout_marginTop = 6;
        public static final int ConstraintOverride_android_layout_width = 3;
        public static final int ConstraintOverride_android_maxHeight = 10;
        public static final int ConstraintOverride_android_maxWidth = 9;
        public static final int ConstraintOverride_android_minHeight = 12;
        public static final int ConstraintOverride_android_minWidth = 11;
        public static final int ConstraintOverride_android_orientation = 0;
        public static final int ConstraintOverride_android_rotation = 20;
        public static final int ConstraintOverride_android_rotationX = 21;
        public static final int ConstraintOverride_android_rotationY = 22;
        public static final int ConstraintOverride_android_scaleX = 18;
        public static final int ConstraintOverride_android_scaleY = 19;
        public static final int ConstraintOverride_android_transformPivotX = 14;
        public static final int ConstraintOverride_android_transformPivotY = 15;
        public static final int ConstraintOverride_android_translationX = 16;
        public static final int ConstraintOverride_android_translationY = 17;
        public static final int ConstraintOverride_android_translationZ = 25;
        public static final int ConstraintOverride_android_visibility = 2;
        public static final int ConstraintOverride_animateCircleAngleTo = 27;
        public static final int ConstraintOverride_animateRelativeTo = 28;
        public static final int ConstraintOverride_barrierAllowsGoneWidgets = 29;
        public static final int ConstraintOverride_barrierDirection = 30;
        public static final int ConstraintOverride_barrierMargin = 31;
        public static final int ConstraintOverride_chainUseRtl = 32;
        public static final int ConstraintOverride_constraint_referenced_ids = 33;
        public static final int ConstraintOverride_drawPath = 34;
        public static final int ConstraintOverride_flow_firstHorizontalBias = 35;
        public static final int ConstraintOverride_flow_firstHorizontalStyle = 36;
        public static final int ConstraintOverride_flow_firstVerticalBias = 37;
        public static final int ConstraintOverride_flow_firstVerticalStyle = 38;
        public static final int ConstraintOverride_flow_horizontalAlign = 39;
        public static final int ConstraintOverride_flow_horizontalBias = 40;
        public static final int ConstraintOverride_flow_horizontalGap = 41;
        public static final int ConstraintOverride_flow_horizontalStyle = 42;
        public static final int ConstraintOverride_flow_lastHorizontalBias = 43;
        public static final int ConstraintOverride_flow_lastHorizontalStyle = 44;
        public static final int ConstraintOverride_flow_lastVerticalBias = 45;
        public static final int ConstraintOverride_flow_lastVerticalStyle = 46;
        public static final int ConstraintOverride_flow_maxElementsWrap = 47;
        public static final int ConstraintOverride_flow_verticalAlign = 48;
        public static final int ConstraintOverride_flow_verticalBias = 49;
        public static final int ConstraintOverride_flow_verticalGap = 50;
        public static final int ConstraintOverride_flow_verticalStyle = 51;
        public static final int ConstraintOverride_flow_wrapMode = 52;
        public static final int ConstraintOverride_guidelineUseRtl = 53;
        public static final int ConstraintOverride_layout_constrainedHeight = 54;
        public static final int ConstraintOverride_layout_constrainedWidth = 55;
        public static final int ConstraintOverride_layout_constraintBaseline_creator = 56;
        public static final int ConstraintOverride_layout_constraintBottom_creator = 57;
        public static final int ConstraintOverride_layout_constraintCircleAngle = 58;
        public static final int ConstraintOverride_layout_constraintCircleRadius = 59;
        public static final int ConstraintOverride_layout_constraintDimensionRatio = 60;
        public static final int ConstraintOverride_layout_constraintGuide_begin = 61;
        public static final int ConstraintOverride_layout_constraintGuide_end = 62;
        public static final int ConstraintOverride_layout_constraintGuide_percent = 63;
        public static final int ConstraintOverride_layout_constraintHeight = 64;
        public static final int ConstraintOverride_layout_constraintHeight_default = 65;
        public static final int ConstraintOverride_layout_constraintHeight_max = 66;
        public static final int ConstraintOverride_layout_constraintHeight_min = 67;
        public static final int ConstraintOverride_layout_constraintHeight_percent = 68;
        public static final int ConstraintOverride_layout_constraintHorizontal_bias = 69;
        public static final int ConstraintOverride_layout_constraintHorizontal_chainStyle = 70;
        public static final int ConstraintOverride_layout_constraintHorizontal_weight = 71;
        public static final int ConstraintOverride_layout_constraintLeft_creator = 72;
        public static final int ConstraintOverride_layout_constraintRight_creator = 73;
        public static final int ConstraintOverride_layout_constraintTag = 74;
        public static final int ConstraintOverride_layout_constraintTop_creator = 75;
        public static final int ConstraintOverride_layout_constraintVertical_bias = 76;
        public static final int ConstraintOverride_layout_constraintVertical_chainStyle = 77;
        public static final int ConstraintOverride_layout_constraintVertical_weight = 78;
        public static final int ConstraintOverride_layout_constraintWidth = 79;
        public static final int ConstraintOverride_layout_constraintWidth_default = 80;
        public static final int ConstraintOverride_layout_constraintWidth_max = 81;
        public static final int ConstraintOverride_layout_constraintWidth_min = 82;
        public static final int ConstraintOverride_layout_constraintWidth_percent = 83;
        public static final int ConstraintOverride_layout_editor_absoluteX = 84;
        public static final int ConstraintOverride_layout_editor_absoluteY = 85;
        public static final int ConstraintOverride_layout_goneMarginBaseline = 86;
        public static final int ConstraintOverride_layout_goneMarginBottom = 87;
        public static final int ConstraintOverride_layout_goneMarginEnd = 88;
        public static final int ConstraintOverride_layout_goneMarginLeft = 89;
        public static final int ConstraintOverride_layout_goneMarginRight = 90;
        public static final int ConstraintOverride_layout_goneMarginStart = 91;
        public static final int ConstraintOverride_layout_goneMarginTop = 92;
        public static final int ConstraintOverride_layout_marginBaseline = 93;
        public static final int ConstraintOverride_layout_wrapBehaviorInParent = 94;
        public static final int ConstraintOverride_motionProgress = 95;
        public static final int ConstraintOverride_motionStagger = 96;
        public static final int ConstraintOverride_motionTarget = 97;
        public static final int ConstraintOverride_pathMotionArc = 98;
        public static final int ConstraintOverride_pivotAnchor = 99;
        public static final int ConstraintOverride_polarRelativeTo = 100;
        public static final int ConstraintOverride_quantizeMotionInterpolator = 101;
        public static final int ConstraintOverride_quantizeMotionPhase = 102;
        public static final int ConstraintOverride_quantizeMotionSteps = 103;
        public static final int ConstraintOverride_transformPivotTarget = 104;
        public static final int ConstraintOverride_transitionEasing = 105;
        public static final int ConstraintOverride_transitionPathRotate = 106;
        public static final int ConstraintOverride_visibilityMode = 107;
        public static final int ConstraintSet_android_alpha = 15;
        public static final int ConstraintSet_android_elevation = 28;
        public static final int ConstraintSet_android_id = 1;
        public static final int ConstraintSet_android_layout_height = 4;
        public static final int ConstraintSet_android_layout_marginBottom = 8;
        public static final int ConstraintSet_android_layout_marginEnd = 26;
        public static final int ConstraintSet_android_layout_marginLeft = 5;
        public static final int ConstraintSet_android_layout_marginRight = 7;
        public static final int ConstraintSet_android_layout_marginStart = 25;
        public static final int ConstraintSet_android_layout_marginTop = 6;
        public static final int ConstraintSet_android_layout_width = 3;
        public static final int ConstraintSet_android_maxHeight = 10;
        public static final int ConstraintSet_android_maxWidth = 9;
        public static final int ConstraintSet_android_minHeight = 12;
        public static final int ConstraintSet_android_minWidth = 11;
        public static final int ConstraintSet_android_orientation = 0;
        public static final int ConstraintSet_android_pivotX = 13;
        public static final int ConstraintSet_android_pivotY = 14;
        public static final int ConstraintSet_android_rotation = 22;
        public static final int ConstraintSet_android_rotationX = 23;
        public static final int ConstraintSet_android_rotationY = 24;
        public static final int ConstraintSet_android_scaleX = 20;
        public static final int ConstraintSet_android_scaleY = 21;
        public static final int ConstraintSet_android_transformPivotX = 16;
        public static final int ConstraintSet_android_transformPivotY = 17;
        public static final int ConstraintSet_android_translationX = 18;
        public static final int ConstraintSet_android_translationY = 19;
        public static final int ConstraintSet_android_translationZ = 27;
        public static final int ConstraintSet_android_visibility = 2;
        public static final int ConstraintSet_animateCircleAngleTo = 29;
        public static final int ConstraintSet_animateRelativeTo = 30;
        public static final int ConstraintSet_barrierAllowsGoneWidgets = 31;
        public static final int ConstraintSet_barrierDirection = 32;
        public static final int ConstraintSet_barrierMargin = 33;
        public static final int ConstraintSet_chainUseRtl = 34;
        public static final int ConstraintSet_constraintRotate = 35;
        public static final int ConstraintSet_constraint_referenced_ids = 36;
        public static final int ConstraintSet_constraint_referenced_tags = 37;
        public static final int ConstraintSet_deriveConstraintsFrom = 38;
        public static final int ConstraintSet_drawPath = 39;
        public static final int ConstraintSet_flow_firstHorizontalBias = 40;
        public static final int ConstraintSet_flow_firstHorizontalStyle = 41;
        public static final int ConstraintSet_flow_firstVerticalBias = 42;
        public static final int ConstraintSet_flow_firstVerticalStyle = 43;
        public static final int ConstraintSet_flow_horizontalAlign = 44;
        public static final int ConstraintSet_flow_horizontalBias = 45;
        public static final int ConstraintSet_flow_horizontalGap = 46;
        public static final int ConstraintSet_flow_horizontalStyle = 47;
        public static final int ConstraintSet_flow_lastHorizontalBias = 48;
        public static final int ConstraintSet_flow_lastHorizontalStyle = 49;
        public static final int ConstraintSet_flow_lastVerticalBias = 50;
        public static final int ConstraintSet_flow_lastVerticalStyle = 51;
        public static final int ConstraintSet_flow_maxElementsWrap = 52;
        public static final int ConstraintSet_flow_verticalAlign = 53;
        public static final int ConstraintSet_flow_verticalBias = 54;
        public static final int ConstraintSet_flow_verticalGap = 55;
        public static final int ConstraintSet_flow_verticalStyle = 56;
        public static final int ConstraintSet_flow_wrapMode = 57;
        public static final int ConstraintSet_guidelineUseRtl = 58;
        public static final int ConstraintSet_layout_constrainedHeight = 59;
        public static final int ConstraintSet_layout_constrainedWidth = 60;
        public static final int ConstraintSet_layout_constraintBaseline_creator = 61;
        public static final int ConstraintSet_layout_constraintBaseline_toBaselineOf = 62;
        public static final int ConstraintSet_layout_constraintBaseline_toBottomOf = 63;
        public static final int ConstraintSet_layout_constraintBaseline_toTopOf = 64;
        public static final int ConstraintSet_layout_constraintBottom_creator = 65;
        public static final int ConstraintSet_layout_constraintBottom_toBottomOf = 66;
        public static final int ConstraintSet_layout_constraintBottom_toTopOf = 67;
        public static final int ConstraintSet_layout_constraintCircle = 68;
        public static final int ConstraintSet_layout_constraintCircleAngle = 69;
        public static final int ConstraintSet_layout_constraintCircleRadius = 70;
        public static final int ConstraintSet_layout_constraintDimensionRatio = 71;
        public static final int ConstraintSet_layout_constraintEnd_toEndOf = 72;
        public static final int ConstraintSet_layout_constraintEnd_toStartOf = 73;
        public static final int ConstraintSet_layout_constraintGuide_begin = 74;
        public static final int ConstraintSet_layout_constraintGuide_end = 75;
        public static final int ConstraintSet_layout_constraintGuide_percent = 76;
        public static final int ConstraintSet_layout_constraintHeight_default = 77;
        public static final int ConstraintSet_layout_constraintHeight_max = 78;
        public static final int ConstraintSet_layout_constraintHeight_min = 79;
        public static final int ConstraintSet_layout_constraintHeight_percent = 80;
        public static final int ConstraintSet_layout_constraintHorizontal_bias = 81;
        public static final int ConstraintSet_layout_constraintHorizontal_chainStyle = 82;
        public static final int ConstraintSet_layout_constraintHorizontal_weight = 83;
        public static final int ConstraintSet_layout_constraintLeft_creator = 84;
        public static final int ConstraintSet_layout_constraintLeft_toLeftOf = 85;
        public static final int ConstraintSet_layout_constraintLeft_toRightOf = 86;
        public static final int ConstraintSet_layout_constraintRight_creator = 87;
        public static final int ConstraintSet_layout_constraintRight_toLeftOf = 88;
        public static final int ConstraintSet_layout_constraintRight_toRightOf = 89;
        public static final int ConstraintSet_layout_constraintStart_toEndOf = 90;
        public static final int ConstraintSet_layout_constraintStart_toStartOf = 91;
        public static final int ConstraintSet_layout_constraintTag = 92;
        public static final int ConstraintSet_layout_constraintTop_creator = 93;
        public static final int ConstraintSet_layout_constraintTop_toBottomOf = 94;
        public static final int ConstraintSet_layout_constraintTop_toTopOf = 95;
        public static final int ConstraintSet_layout_constraintVertical_bias = 96;
        public static final int ConstraintSet_layout_constraintVertical_chainStyle = 97;
        public static final int ConstraintSet_layout_constraintVertical_weight = 98;
        public static final int ConstraintSet_layout_constraintWidth_default = 99;
        public static final int ConstraintSet_layout_constraintWidth_max = 100;
        public static final int ConstraintSet_layout_constraintWidth_min = 101;
        public static final int ConstraintSet_layout_constraintWidth_percent = 102;
        public static final int ConstraintSet_layout_editor_absoluteX = 103;
        public static final int ConstraintSet_layout_editor_absoluteY = 104;
        public static final int ConstraintSet_layout_goneMarginBaseline = 105;
        public static final int ConstraintSet_layout_goneMarginBottom = 106;
        public static final int ConstraintSet_layout_goneMarginEnd = 107;
        public static final int ConstraintSet_layout_goneMarginLeft = 108;
        public static final int ConstraintSet_layout_goneMarginRight = 109;
        public static final int ConstraintSet_layout_goneMarginStart = 110;
        public static final int ConstraintSet_layout_goneMarginTop = 111;
        public static final int ConstraintSet_layout_marginBaseline = 112;
        public static final int ConstraintSet_layout_wrapBehaviorInParent = 113;
        public static final int ConstraintSet_motionProgress = 114;
        public static final int ConstraintSet_motionStagger = 115;
        public static final int ConstraintSet_pathMotionArc = 116;
        public static final int ConstraintSet_pivotAnchor = 117;
        public static final int ConstraintSet_polarRelativeTo = 118;
        public static final int ConstraintSet_quantizeMotionSteps = 119;
        public static final int ConstraintSet_transitionEasing = 120;
        public static final int ConstraintSet_transitionPathRotate = 121;
        public static final int Constraint_android_alpha = 13;
        public static final int Constraint_android_elevation = 26;
        public static final int Constraint_android_id = 1;
        public static final int Constraint_android_layout_height = 4;
        public static final int Constraint_android_layout_marginBottom = 8;
        public static final int Constraint_android_layout_marginEnd = 24;
        public static final int Constraint_android_layout_marginLeft = 5;
        public static final int Constraint_android_layout_marginRight = 7;
        public static final int Constraint_android_layout_marginStart = 23;
        public static final int Constraint_android_layout_marginTop = 6;
        public static final int Constraint_android_layout_width = 3;
        public static final int Constraint_android_maxHeight = 10;
        public static final int Constraint_android_maxWidth = 9;
        public static final int Constraint_android_minHeight = 12;
        public static final int Constraint_android_minWidth = 11;
        public static final int Constraint_android_orientation = 0;
        public static final int Constraint_android_rotation = 20;
        public static final int Constraint_android_rotationX = 21;
        public static final int Constraint_android_rotationY = 22;
        public static final int Constraint_android_scaleX = 18;
        public static final int Constraint_android_scaleY = 19;
        public static final int Constraint_android_transformPivotX = 14;
        public static final int Constraint_android_transformPivotY = 15;
        public static final int Constraint_android_translationX = 16;
        public static final int Constraint_android_translationY = 17;
        public static final int Constraint_android_translationZ = 25;
        public static final int Constraint_android_visibility = 2;
        public static final int Constraint_animateCircleAngleTo = 27;
        public static final int Constraint_animateRelativeTo = 28;
        public static final int Constraint_barrierAllowsGoneWidgets = 29;
        public static final int Constraint_barrierDirection = 30;
        public static final int Constraint_barrierMargin = 31;
        public static final int Constraint_chainUseRtl = 32;
        public static final int Constraint_constraint_referenced_ids = 33;
        public static final int Constraint_constraint_referenced_tags = 34;
        public static final int Constraint_drawPath = 35;
        public static final int Constraint_flow_firstHorizontalBias = 36;
        public static final int Constraint_flow_firstHorizontalStyle = 37;
        public static final int Constraint_flow_firstVerticalBias = 38;
        public static final int Constraint_flow_firstVerticalStyle = 39;
        public static final int Constraint_flow_horizontalAlign = 40;
        public static final int Constraint_flow_horizontalBias = 41;
        public static final int Constraint_flow_horizontalGap = 42;
        public static final int Constraint_flow_horizontalStyle = 43;
        public static final int Constraint_flow_lastHorizontalBias = 44;
        public static final int Constraint_flow_lastHorizontalStyle = 45;
        public static final int Constraint_flow_lastVerticalBias = 46;
        public static final int Constraint_flow_lastVerticalStyle = 47;
        public static final int Constraint_flow_maxElementsWrap = 48;
        public static final int Constraint_flow_verticalAlign = 49;
        public static final int Constraint_flow_verticalBias = 50;
        public static final int Constraint_flow_verticalGap = 51;
        public static final int Constraint_flow_verticalStyle = 52;
        public static final int Constraint_flow_wrapMode = 53;
        public static final int Constraint_guidelineUseRtl = 54;
        public static final int Constraint_layout_constrainedHeight = 55;
        public static final int Constraint_layout_constrainedWidth = 56;
        public static final int Constraint_layout_constraintBaseline_creator = 57;
        public static final int Constraint_layout_constraintBaseline_toBaselineOf = 58;
        public static final int Constraint_layout_constraintBaseline_toBottomOf = 59;
        public static final int Constraint_layout_constraintBaseline_toTopOf = 60;
        public static final int Constraint_layout_constraintBottom_creator = 61;
        public static final int Constraint_layout_constraintBottom_toBottomOf = 62;
        public static final int Constraint_layout_constraintBottom_toTopOf = 63;
        public static final int Constraint_layout_constraintCircle = 64;
        public static final int Constraint_layout_constraintCircleAngle = 65;
        public static final int Constraint_layout_constraintCircleRadius = 66;
        public static final int Constraint_layout_constraintDimensionRatio = 67;
        public static final int Constraint_layout_constraintEnd_toEndOf = 68;
        public static final int Constraint_layout_constraintEnd_toStartOf = 69;
        public static final int Constraint_layout_constraintGuide_begin = 70;
        public static final int Constraint_layout_constraintGuide_end = 71;
        public static final int Constraint_layout_constraintGuide_percent = 72;
        public static final int Constraint_layout_constraintHeight = 73;
        public static final int Constraint_layout_constraintHeight_default = 74;
        public static final int Constraint_layout_constraintHeight_max = 75;
        public static final int Constraint_layout_constraintHeight_min = 76;
        public static final int Constraint_layout_constraintHeight_percent = 77;
        public static final int Constraint_layout_constraintHorizontal_bias = 78;
        public static final int Constraint_layout_constraintHorizontal_chainStyle = 79;
        public static final int Constraint_layout_constraintHorizontal_weight = 80;
        public static final int Constraint_layout_constraintLeft_creator = 81;
        public static final int Constraint_layout_constraintLeft_toLeftOf = 82;
        public static final int Constraint_layout_constraintLeft_toRightOf = 83;
        public static final int Constraint_layout_constraintRight_creator = 84;
        public static final int Constraint_layout_constraintRight_toLeftOf = 85;
        public static final int Constraint_layout_constraintRight_toRightOf = 86;
        public static final int Constraint_layout_constraintStart_toEndOf = 87;
        public static final int Constraint_layout_constraintStart_toStartOf = 88;
        public static final int Constraint_layout_constraintTag = 89;
        public static final int Constraint_layout_constraintTop_creator = 90;
        public static final int Constraint_layout_constraintTop_toBottomOf = 91;
        public static final int Constraint_layout_constraintTop_toTopOf = 92;
        public static final int Constraint_layout_constraintVertical_bias = 93;
        public static final int Constraint_layout_constraintVertical_chainStyle = 94;
        public static final int Constraint_layout_constraintVertical_weight = 95;
        public static final int Constraint_layout_constraintWidth = 96;
        public static final int Constraint_layout_constraintWidth_default = 97;
        public static final int Constraint_layout_constraintWidth_max = 98;
        public static final int Constraint_layout_constraintWidth_min = 99;
        public static final int Constraint_layout_constraintWidth_percent = 100;
        public static final int Constraint_layout_editor_absoluteX = 101;
        public static final int Constraint_layout_editor_absoluteY = 102;
        public static final int Constraint_layout_goneMarginBaseline = 103;
        public static final int Constraint_layout_goneMarginBottom = 104;
        public static final int Constraint_layout_goneMarginEnd = 105;
        public static final int Constraint_layout_goneMarginLeft = 106;
        public static final int Constraint_layout_goneMarginRight = 107;
        public static final int Constraint_layout_goneMarginStart = 108;
        public static final int Constraint_layout_goneMarginTop = 109;
        public static final int Constraint_layout_marginBaseline = 110;
        public static final int Constraint_layout_wrapBehaviorInParent = 111;
        public static final int Constraint_motionProgress = 112;
        public static final int Constraint_motionStagger = 113;
        public static final int Constraint_pathMotionArc = 114;
        public static final int Constraint_pivotAnchor = 115;
        public static final int Constraint_polarRelativeTo = 116;
        public static final int Constraint_quantizeMotionInterpolator = 117;
        public static final int Constraint_quantizeMotionPhase = 118;
        public static final int Constraint_quantizeMotionSteps = 119;
        public static final int Constraint_transformPivotTarget = 120;
        public static final int Constraint_transitionEasing = 121;
        public static final int Constraint_transitionPathRotate = 122;
        public static final int Constraint_visibilityMode = 123;
        public static final int CustomAttribute_attributeName = 0;
        public static final int CustomAttribute_customBoolean = 1;
        public static final int CustomAttribute_customColorDrawableValue = 2;
        public static final int CustomAttribute_customColorValue = 3;
        public static final int CustomAttribute_customDimension = 4;
        public static final int CustomAttribute_customFloatValue = 5;
        public static final int CustomAttribute_customIntegerValue = 6;
        public static final int CustomAttribute_customPixelDimension = 7;
        public static final int CustomAttribute_customReference = 8;
        public static final int CustomAttribute_customStringValue = 9;
        public static final int CustomAttribute_methodName = 10;
        public static final int DrawerArrowToggle_arrowHeadLength = 0;
        public static final int DrawerArrowToggle_arrowShaftLength = 1;
        public static final int DrawerArrowToggle_barLength = 2;
        public static final int DrawerArrowToggle_color = 3;
        public static final int DrawerArrowToggle_drawableSize = 4;
        public static final int DrawerArrowToggle_gapBetweenBars = 5;
        public static final int DrawerArrowToggle_spinBars = 6;
        public static final int DrawerArrowToggle_thickness = 7;
        public static final int FontFamilyFont_android_font = 0;
        public static final int FontFamilyFont_android_fontStyle = 2;
        public static final int FontFamilyFont_android_fontVariationSettings = 4;
        public static final int FontFamilyFont_android_fontWeight = 1;
        public static final int FontFamilyFont_android_ttcIndex = 3;
        public static final int FontFamilyFont_font = 5;
        public static final int FontFamilyFont_fontStyle = 6;
        public static final int FontFamilyFont_fontVariationSettings = 7;
        public static final int FontFamilyFont_fontWeight = 8;
        public static final int FontFamilyFont_ttcIndex = 9;
        public static final int FontFamily_fontProviderAuthority = 0;
        public static final int FontFamily_fontProviderCerts = 1;
        public static final int FontFamily_fontProviderFetchStrategy = 2;
        public static final int FontFamily_fontProviderFetchTimeout = 3;
        public static final int FontFamily_fontProviderPackage = 4;
        public static final int FontFamily_fontProviderQuery = 5;
        public static final int FontFamily_fontProviderSystemFontFamily = 6;
        public static final int GradientColorItem_android_color = 0;
        public static final int GradientColorItem_android_offset = 1;
        public static final int GradientColor_android_centerColor = 7;
        public static final int GradientColor_android_centerX = 3;
        public static final int GradientColor_android_centerY = 4;
        public static final int GradientColor_android_endColor = 1;
        public static final int GradientColor_android_endX = 10;
        public static final int GradientColor_android_endY = 11;
        public static final int GradientColor_android_gradientRadius = 5;
        public static final int GradientColor_android_startColor = 0;
        public static final int GradientColor_android_startX = 8;
        public static final int GradientColor_android_startY = 9;
        public static final int GradientColor_android_tileMode = 6;
        public static final int GradientColor_android_type = 2;
        public static final int ImageFilterView_altSrc = 0;
        public static final int ImageFilterView_blendSrc = 1;
        public static final int ImageFilterView_brightness = 2;
        public static final int ImageFilterView_contrast = 3;
        public static final int ImageFilterView_crossfade = 4;
        public static final int ImageFilterView_imagePanX = 5;
        public static final int ImageFilterView_imagePanY = 6;
        public static final int ImageFilterView_imageRotate = 7;
        public static final int ImageFilterView_imageZoom = 8;
        public static final int ImageFilterView_overlay = 9;
        public static final int ImageFilterView_round = 10;
        public static final int ImageFilterView_roundPercent = 11;
        public static final int ImageFilterView_saturation = 12;
        public static final int ImageFilterView_warmth = 13;
        public static final int KeyAttribute_android_alpha = 0;
        public static final int KeyAttribute_android_elevation = 11;
        public static final int KeyAttribute_android_rotation = 7;
        public static final int KeyAttribute_android_rotationX = 8;
        public static final int KeyAttribute_android_rotationY = 9;
        public static final int KeyAttribute_android_scaleX = 5;
        public static final int KeyAttribute_android_scaleY = 6;
        public static final int KeyAttribute_android_transformPivotX = 1;
        public static final int KeyAttribute_android_transformPivotY = 2;
        public static final int KeyAttribute_android_translationX = 3;
        public static final int KeyAttribute_android_translationY = 4;
        public static final int KeyAttribute_android_translationZ = 10;
        public static final int KeyAttribute_curveFit = 12;
        public static final int KeyAttribute_framePosition = 13;
        public static final int KeyAttribute_motionProgress = 14;
        public static final int KeyAttribute_motionTarget = 15;
        public static final int KeyAttribute_transformPivotTarget = 16;
        public static final int KeyAttribute_transitionEasing = 17;
        public static final int KeyAttribute_transitionPathRotate = 18;
        public static final int KeyCycle_android_alpha = 0;
        public static final int KeyCycle_android_elevation = 9;
        public static final int KeyCycle_android_rotation = 5;
        public static final int KeyCycle_android_rotationX = 6;
        public static final int KeyCycle_android_rotationY = 7;
        public static final int KeyCycle_android_scaleX = 3;
        public static final int KeyCycle_android_scaleY = 4;
        public static final int KeyCycle_android_translationX = 1;
        public static final int KeyCycle_android_translationY = 2;
        public static final int KeyCycle_android_translationZ = 8;
        public static final int KeyCycle_curveFit = 10;
        public static final int KeyCycle_framePosition = 11;
        public static final int KeyCycle_motionProgress = 12;
        public static final int KeyCycle_motionTarget = 13;
        public static final int KeyCycle_transitionEasing = 14;
        public static final int KeyCycle_transitionPathRotate = 15;
        public static final int KeyCycle_waveOffset = 16;
        public static final int KeyCycle_wavePeriod = 17;
        public static final int KeyCycle_wavePhase = 18;
        public static final int KeyCycle_waveShape = 19;
        public static final int KeyCycle_waveVariesBy = 20;
        public static final int KeyPosition_curveFit = 0;
        public static final int KeyPosition_drawPath = 1;
        public static final int KeyPosition_framePosition = 2;
        public static final int KeyPosition_keyPositionType = 3;
        public static final int KeyPosition_motionTarget = 4;
        public static final int KeyPosition_pathMotionArc = 5;
        public static final int KeyPosition_percentHeight = 6;
        public static final int KeyPosition_percentWidth = 7;
        public static final int KeyPosition_percentX = 8;
        public static final int KeyPosition_percentY = 9;
        public static final int KeyPosition_sizePercent = 10;
        public static final int KeyPosition_transitionEasing = 11;
        public static final int KeyTimeCycle_android_alpha = 0;
        public static final int KeyTimeCycle_android_elevation = 9;
        public static final int KeyTimeCycle_android_rotation = 5;
        public static final int KeyTimeCycle_android_rotationX = 6;
        public static final int KeyTimeCycle_android_rotationY = 7;
        public static final int KeyTimeCycle_android_scaleX = 3;
        public static final int KeyTimeCycle_android_scaleY = 4;
        public static final int KeyTimeCycle_android_translationX = 1;
        public static final int KeyTimeCycle_android_translationY = 2;
        public static final int KeyTimeCycle_android_translationZ = 8;
        public static final int KeyTimeCycle_curveFit = 10;
        public static final int KeyTimeCycle_framePosition = 11;
        public static final int KeyTimeCycle_motionProgress = 12;
        public static final int KeyTimeCycle_motionTarget = 13;
        public static final int KeyTimeCycle_transitionEasing = 14;
        public static final int KeyTimeCycle_transitionPathRotate = 15;
        public static final int KeyTimeCycle_waveDecay = 16;
        public static final int KeyTimeCycle_waveOffset = 17;
        public static final int KeyTimeCycle_wavePeriod = 18;
        public static final int KeyTimeCycle_wavePhase = 19;
        public static final int KeyTimeCycle_waveShape = 20;
        public static final int KeyTrigger_framePosition = 0;
        public static final int KeyTrigger_motionTarget = 1;
        public static final int KeyTrigger_motion_postLayoutCollision = 2;
        public static final int KeyTrigger_motion_triggerOnCollision = 3;
        public static final int KeyTrigger_onCross = 4;
        public static final int KeyTrigger_onNegativeCross = 5;
        public static final int KeyTrigger_onPositiveCross = 6;
        public static final int KeyTrigger_triggerId = 7;
        public static final int KeyTrigger_triggerReceiver = 8;
        public static final int KeyTrigger_triggerSlack = 9;
        public static final int KeyTrigger_viewTransitionOnCross = 10;
        public static final int KeyTrigger_viewTransitionOnNegativeCross = 11;
        public static final int KeyTrigger_viewTransitionOnPositiveCross = 12;
        public static final int Layout_android_layout_height = 2;
        public static final int Layout_android_layout_marginBottom = 6;
        public static final int Layout_android_layout_marginEnd = 8;
        public static final int Layout_android_layout_marginLeft = 3;
        public static final int Layout_android_layout_marginRight = 5;
        public static final int Layout_android_layout_marginStart = 7;
        public static final int Layout_android_layout_marginTop = 4;
        public static final int Layout_android_layout_width = 1;
        public static final int Layout_android_orientation = 0;
        public static final int Layout_barrierAllowsGoneWidgets = 9;
        public static final int Layout_barrierDirection = 10;
        public static final int Layout_barrierMargin = 11;
        public static final int Layout_chainUseRtl = 12;
        public static final int Layout_constraint_referenced_ids = 13;
        public static final int Layout_constraint_referenced_tags = 14;
        public static final int Layout_guidelineUseRtl = 15;
        public static final int Layout_layout_constrainedHeight = 16;
        public static final int Layout_layout_constrainedWidth = 17;
        public static final int Layout_layout_constraintBaseline_creator = 18;
        public static final int Layout_layout_constraintBaseline_toBaselineOf = 19;
        public static final int Layout_layout_constraintBaseline_toBottomOf = 20;
        public static final int Layout_layout_constraintBaseline_toTopOf = 21;
        public static final int Layout_layout_constraintBottom_creator = 22;
        public static final int Layout_layout_constraintBottom_toBottomOf = 23;
        public static final int Layout_layout_constraintBottom_toTopOf = 24;
        public static final int Layout_layout_constraintCircle = 25;
        public static final int Layout_layout_constraintCircleAngle = 26;
        public static final int Layout_layout_constraintCircleRadius = 27;
        public static final int Layout_layout_constraintDimensionRatio = 28;
        public static final int Layout_layout_constraintEnd_toEndOf = 29;
        public static final int Layout_layout_constraintEnd_toStartOf = 30;
        public static final int Layout_layout_constraintGuide_begin = 31;
        public static final int Layout_layout_constraintGuide_end = 32;
        public static final int Layout_layout_constraintGuide_percent = 33;
        public static final int Layout_layout_constraintHeight = 34;
        public static final int Layout_layout_constraintHeight_default = 35;
        public static final int Layout_layout_constraintHeight_max = 36;
        public static final int Layout_layout_constraintHeight_min = 37;
        public static final int Layout_layout_constraintHeight_percent = 38;
        public static final int Layout_layout_constraintHorizontal_bias = 39;
        public static final int Layout_layout_constraintHorizontal_chainStyle = 40;
        public static final int Layout_layout_constraintHorizontal_weight = 41;
        public static final int Layout_layout_constraintLeft_creator = 42;
        public static final int Layout_layout_constraintLeft_toLeftOf = 43;
        public static final int Layout_layout_constraintLeft_toRightOf = 44;
        public static final int Layout_layout_constraintRight_creator = 45;
        public static final int Layout_layout_constraintRight_toLeftOf = 46;
        public static final int Layout_layout_constraintRight_toRightOf = 47;
        public static final int Layout_layout_constraintStart_toEndOf = 48;
        public static final int Layout_layout_constraintStart_toStartOf = 49;
        public static final int Layout_layout_constraintTop_creator = 50;
        public static final int Layout_layout_constraintTop_toBottomOf = 51;
        public static final int Layout_layout_constraintTop_toTopOf = 52;
        public static final int Layout_layout_constraintVertical_bias = 53;
        public static final int Layout_layout_constraintVertical_chainStyle = 54;
        public static final int Layout_layout_constraintVertical_weight = 55;
        public static final int Layout_layout_constraintWidth = 56;
        public static final int Layout_layout_constraintWidth_default = 57;
        public static final int Layout_layout_constraintWidth_max = 58;
        public static final int Layout_layout_constraintWidth_min = 59;
        public static final int Layout_layout_constraintWidth_percent = 60;
        public static final int Layout_layout_editor_absoluteX = 61;
        public static final int Layout_layout_editor_absoluteY = 62;
        public static final int Layout_layout_goneMarginBaseline = 63;
        public static final int Layout_layout_goneMarginBottom = 64;
        public static final int Layout_layout_goneMarginEnd = 65;
        public static final int Layout_layout_goneMarginLeft = 66;
        public static final int Layout_layout_goneMarginRight = 67;
        public static final int Layout_layout_goneMarginStart = 68;
        public static final int Layout_layout_goneMarginTop = 69;
        public static final int Layout_layout_marginBaseline = 70;
        public static final int Layout_layout_wrapBehaviorInParent = 71;
        public static final int Layout_maxHeight = 72;
        public static final int Layout_maxWidth = 73;
        public static final int Layout_minHeight = 74;
        public static final int Layout_minWidth = 75;
        public static final int LinearLayoutCompat_Layout_android_layout_gravity = 0;
        public static final int LinearLayoutCompat_Layout_android_layout_height = 2;
        public static final int LinearLayoutCompat_Layout_android_layout_weight = 3;
        public static final int LinearLayoutCompat_Layout_android_layout_width = 1;
        public static final int LinearLayoutCompat_android_baselineAligned = 2;
        public static final int LinearLayoutCompat_android_baselineAlignedChildIndex = 3;
        public static final int LinearLayoutCompat_android_gravity = 0;
        public static final int LinearLayoutCompat_android_orientation = 1;
        public static final int LinearLayoutCompat_android_weightSum = 4;
        public static final int LinearLayoutCompat_divider = 5;
        public static final int LinearLayoutCompat_dividerPadding = 6;
        public static final int LinearLayoutCompat_measureWithLargestChild = 7;
        public static final int LinearLayoutCompat_showDividers = 8;
        public static final int ListPopupWindow_android_dropDownHorizontalOffset = 0;
        public static final int ListPopupWindow_android_dropDownVerticalOffset = 1;
        public static final int MenuGroup_android_checkableBehavior = 5;
        public static final int MenuGroup_android_enabled = 0;
        public static final int MenuGroup_android_id = 1;
        public static final int MenuGroup_android_menuCategory = 3;
        public static final int MenuGroup_android_orderInCategory = 4;
        public static final int MenuGroup_android_visible = 2;
        public static final int MenuItem_actionLayout = 13;
        public static final int MenuItem_actionProviderClass = 14;
        public static final int MenuItem_actionViewClass = 15;
        public static final int MenuItem_alphabeticModifiers = 16;
        public static final int MenuItem_android_alphabeticShortcut = 9;
        public static final int MenuItem_android_checkable = 11;
        public static final int MenuItem_android_checked = 3;
        public static final int MenuItem_android_enabled = 1;
        public static final int MenuItem_android_icon = 0;
        public static final int MenuItem_android_id = 2;
        public static final int MenuItem_android_menuCategory = 5;
        public static final int MenuItem_android_numericShortcut = 10;
        public static final int MenuItem_android_onClick = 12;
        public static final int MenuItem_android_orderInCategory = 6;
        public static final int MenuItem_android_title = 7;
        public static final int MenuItem_android_titleCondensed = 8;
        public static final int MenuItem_android_visible = 4;
        public static final int MenuItem_contentDescription = 17;
        public static final int MenuItem_iconTint = 18;
        public static final int MenuItem_iconTintMode = 19;
        public static final int MenuItem_numericModifiers = 20;
        public static final int MenuItem_showAsAction = 21;
        public static final int MenuItem_tooltipText = 22;
        public static final int MenuView_android_headerBackground = 4;
        public static final int MenuView_android_horizontalDivider = 2;
        public static final int MenuView_android_itemBackground = 5;
        public static final int MenuView_android_itemIconDisabledAlpha = 6;
        public static final int MenuView_android_itemTextAppearance = 1;
        public static final int MenuView_android_verticalDivider = 3;
        public static final int MenuView_android_windowAnimationStyle = 0;
        public static final int MenuView_preserveIconSpacing = 7;
        public static final int MenuView_subMenuArrow = 8;
        public static final int MockView_mock_diagonalsColor = 0;
        public static final int MockView_mock_label = 1;
        public static final int MockView_mock_labelBackgroundColor = 2;
        public static final int MockView_mock_labelColor = 3;
        public static final int MockView_mock_showDiagonals = 4;
        public static final int MockView_mock_showLabel = 5;
        public static final int MotionEffect_motionEffect_alpha = 0;
        public static final int MotionEffect_motionEffect_end = 1;
        public static final int MotionEffect_motionEffect_move = 2;
        public static final int MotionEffect_motionEffect_start = 3;
        public static final int MotionEffect_motionEffect_strict = 4;
        public static final int MotionEffect_motionEffect_translationX = 5;
        public static final int MotionEffect_motionEffect_translationY = 6;
        public static final int MotionEffect_motionEffect_viewTransition = 7;
        public static final int MotionHelper_onHide = 0;
        public static final int MotionHelper_onShow = 1;
        public static final int MotionLabel_android_autoSizeTextType = 8;
        public static final int MotionLabel_android_fontFamily = 7;
        public static final int MotionLabel_android_gravity = 4;
        public static final int MotionLabel_android_shadowRadius = 6;
        public static final int MotionLabel_android_text = 5;
        public static final int MotionLabel_android_textColor = 3;
        public static final int MotionLabel_android_textSize = 0;
        public static final int MotionLabel_android_textStyle = 2;
        public static final int MotionLabel_android_typeface = 1;
        public static final int MotionLabel_borderRound = 9;
        public static final int MotionLabel_borderRoundPercent = 10;
        public static final int MotionLabel_scaleFromTextSize = 11;
        public static final int MotionLabel_textBackground = 12;
        public static final int MotionLabel_textBackgroundPanX = 13;
        public static final int MotionLabel_textBackgroundPanY = 14;
        public static final int MotionLabel_textBackgroundRotate = 15;
        public static final int MotionLabel_textBackgroundZoom = 16;
        public static final int MotionLabel_textOutlineColor = 17;
        public static final int MotionLabel_textOutlineThickness = 18;
        public static final int MotionLabel_textPanX = 19;
        public static final int MotionLabel_textPanY = 20;
        public static final int MotionLabel_textureBlurFactor = 21;
        public static final int MotionLabel_textureEffect = 22;
        public static final int MotionLabel_textureHeight = 23;
        public static final int MotionLabel_textureWidth = 24;
        public static final int MotionLayout_applyMotionScene = 0;
        public static final int MotionLayout_currentState = 1;
        public static final int MotionLayout_layoutDescription = 2;
        public static final int MotionLayout_motionDebug = 3;
        public static final int MotionLayout_motionProgress = 4;
        public static final int MotionLayout_showPaths = 5;
        public static final int MotionScene_defaultDuration = 0;
        public static final int MotionScene_layoutDuringTransition = 1;
        public static final int MotionTelltales_telltales_tailColor = 0;
        public static final int MotionTelltales_telltales_tailScale = 1;
        public static final int MotionTelltales_telltales_velocityMode = 2;
        public static final int Motion_animateCircleAngleTo = 0;
        public static final int Motion_animateRelativeTo = 1;
        public static final int Motion_drawPath = 2;
        public static final int Motion_motionPathRotate = 3;
        public static final int Motion_motionStagger = 4;
        public static final int Motion_pathMotionArc = 5;
        public static final int Motion_quantizeMotionInterpolator = 6;
        public static final int Motion_quantizeMotionPhase = 7;
        public static final int Motion_quantizeMotionSteps = 8;
        public static final int Motion_transitionEasing = 9;
        public static final int OnClick_clickAction = 0;
        public static final int OnClick_targetId = 1;
        public static final int OnSwipe_autoCompleteMode = 0;
        public static final int OnSwipe_dragDirection = 1;
        public static final int OnSwipe_dragScale = 2;
        public static final int OnSwipe_dragThreshold = 3;
        public static final int OnSwipe_limitBoundsTo = 4;
        public static final int OnSwipe_maxAcceleration = 5;
        public static final int OnSwipe_maxVelocity = 6;
        public static final int OnSwipe_moveWhenScrollAtTop = 7;
        public static final int OnSwipe_nestedScrollFlags = 8;
        public static final int OnSwipe_onTouchUp = 9;
        public static final int OnSwipe_rotationCenterId = 10;
        public static final int OnSwipe_springBoundary = 11;
        public static final int OnSwipe_springDamping = 12;
        public static final int OnSwipe_springMass = 13;
        public static final int OnSwipe_springStiffness = 14;
        public static final int OnSwipe_springStopThreshold = 15;
        public static final int OnSwipe_touchAnchorId = 16;
        public static final int OnSwipe_touchAnchorSide = 17;
        public static final int OnSwipe_touchRegionId = 18;
        public static final int PopupWindowBackgroundState_state_above_anchor = 0;
        public static final int PopupWindow_android_popupAnimationStyle = 1;
        public static final int PopupWindow_android_popupBackground = 0;
        public static final int PopupWindow_overlapAnchor = 2;
        public static final int PropertySet_android_alpha = 1;
        public static final int PropertySet_android_visibility = 0;
        public static final int PropertySet_layout_constraintTag = 2;
        public static final int PropertySet_motionProgress = 3;
        public static final int PropertySet_visibilityMode = 4;
        public static final int RecycleListView_paddingBottomNoButtons = 0;
        public static final int RecycleListView_paddingTopNoTitle = 1;
        public static final int SearchView_android_focusable = 0;
        public static final int SearchView_android_imeOptions = 3;
        public static final int SearchView_android_inputType = 2;
        public static final int SearchView_android_maxWidth = 1;
        public static final int SearchView_closeIcon = 4;
        public static final int SearchView_commitIcon = 5;
        public static final int SearchView_defaultQueryHint = 6;
        public static final int SearchView_goIcon = 7;
        public static final int SearchView_iconifiedByDefault = 8;
        public static final int SearchView_layout = 9;
        public static final int SearchView_queryBackground = 10;
        public static final int SearchView_queryHint = 11;
        public static final int SearchView_searchHintIcon = 12;
        public static final int SearchView_searchIcon = 13;
        public static final int SearchView_submitBackground = 14;
        public static final int SearchView_suggestionRowLayout = 15;
        public static final int SearchView_voiceIcon = 16;
        public static final int Spinner_android_dropDownWidth = 3;
        public static final int Spinner_android_entries = 0;
        public static final int Spinner_android_popupBackground = 1;
        public static final int Spinner_android_prompt = 2;
        public static final int Spinner_popupTheme = 4;
        public static final int StateListDrawableItem_android_drawable = 0;
        public static final int StateListDrawable_android_constantSize = 3;
        public static final int StateListDrawable_android_dither = 0;
        public static final int StateListDrawable_android_enterFadeDuration = 4;
        public static final int StateListDrawable_android_exitFadeDuration = 5;
        public static final int StateListDrawable_android_variablePadding = 2;
        public static final int StateListDrawable_android_visible = 1;
        public static final int StateSet_defaultState = 0;
        public static final int State_android_id = 0;
        public static final int State_constraints = 1;
        public static final int SwitchCompat_android_textOff = 1;
        public static final int SwitchCompat_android_textOn = 0;
        public static final int SwitchCompat_android_thumb = 2;
        public static final int SwitchCompat_showText = 3;
        public static final int SwitchCompat_splitTrack = 4;
        public static final int SwitchCompat_switchMinWidth = 5;
        public static final int SwitchCompat_switchPadding = 6;
        public static final int SwitchCompat_switchTextAppearance = 7;
        public static final int SwitchCompat_thumbTextPadding = 8;
        public static final int SwitchCompat_thumbTint = 9;
        public static final int SwitchCompat_thumbTintMode = 10;
        public static final int SwitchCompat_track = 11;
        public static final int SwitchCompat_trackTint = 12;
        public static final int SwitchCompat_trackTintMode = 13;
        public static final int TextAppearance_android_fontFamily = 10;
        public static final int TextAppearance_android_shadowColor = 6;
        public static final int TextAppearance_android_shadowDx = 7;
        public static final int TextAppearance_android_shadowDy = 8;
        public static final int TextAppearance_android_shadowRadius = 9;
        public static final int TextAppearance_android_textColor = 3;
        public static final int TextAppearance_android_textColorHint = 4;
        public static final int TextAppearance_android_textColorLink = 5;
        public static final int TextAppearance_android_textFontWeight = 11;
        public static final int TextAppearance_android_textSize = 0;
        public static final int TextAppearance_android_textStyle = 2;
        public static final int TextAppearance_android_typeface = 1;
        public static final int TextAppearance_fontFamily = 12;
        public static final int TextAppearance_fontVariationSettings = 13;
        public static final int TextAppearance_textAllCaps = 14;
        public static final int TextAppearance_textLocale = 15;
        public static final int TextEffects_android_fontFamily = 8;
        public static final int TextEffects_android_shadowColor = 4;
        public static final int TextEffects_android_shadowDx = 5;
        public static final int TextEffects_android_shadowDy = 6;
        public static final int TextEffects_android_shadowRadius = 7;
        public static final int TextEffects_android_text = 3;
        public static final int TextEffects_android_textSize = 0;
        public static final int TextEffects_android_textStyle = 2;
        public static final int TextEffects_android_typeface = 1;
        public static final int TextEffects_borderRound = 9;
        public static final int TextEffects_borderRoundPercent = 10;
        public static final int TextEffects_textFillColor = 11;
        public static final int TextEffects_textOutlineColor = 12;
        public static final int TextEffects_textOutlineThickness = 13;
        public static final int Toolbar_android_gravity = 0;
        public static final int Toolbar_android_minHeight = 1;
        public static final int Toolbar_buttonGravity = 2;
        public static final int Toolbar_collapseContentDescription = 3;
        public static final int Toolbar_collapseIcon = 4;
        public static final int Toolbar_contentInsetEnd = 5;
        public static final int Toolbar_contentInsetEndWithActions = 6;
        public static final int Toolbar_contentInsetLeft = 7;
        public static final int Toolbar_contentInsetRight = 8;
        public static final int Toolbar_contentInsetStart = 9;
        public static final int Toolbar_contentInsetStartWithNavigation = 10;
        public static final int Toolbar_logo = 11;
        public static final int Toolbar_logoDescription = 12;
        public static final int Toolbar_maxButtonHeight = 13;
        public static final int Toolbar_menu = 14;
        public static final int Toolbar_navigationContentDescription = 15;
        public static final int Toolbar_navigationIcon = 16;
        public static final int Toolbar_popupTheme = 17;
        public static final int Toolbar_subtitle = 18;
        public static final int Toolbar_subtitleTextAppearance = 19;
        public static final int Toolbar_subtitleTextColor = 20;
        public static final int Toolbar_title = 21;
        public static final int Toolbar_titleMargin = 22;
        public static final int Toolbar_titleMarginBottom = 23;
        public static final int Toolbar_titleMarginEnd = 24;
        public static final int Toolbar_titleMarginStart = 25;
        public static final int Toolbar_titleMarginTop = 26;
        public static final int Toolbar_titleMargins = 27;
        public static final int Toolbar_titleTextAppearance = 28;
        public static final int Toolbar_titleTextColor = 29;
        public static final int Transform_android_elevation = 10;
        public static final int Transform_android_rotation = 6;
        public static final int Transform_android_rotationX = 7;
        public static final int Transform_android_rotationY = 8;
        public static final int Transform_android_scaleX = 4;
        public static final int Transform_android_scaleY = 5;
        public static final int Transform_android_transformPivotX = 0;
        public static final int Transform_android_transformPivotY = 1;
        public static final int Transform_android_translationX = 2;
        public static final int Transform_android_translationY = 3;
        public static final int Transform_android_translationZ = 9;
        public static final int Transform_transformPivotTarget = 11;
        public static final int Transition_android_id = 0;
        public static final int Transition_autoTransition = 1;
        public static final int Transition_constraintSetEnd = 2;
        public static final int Transition_constraintSetStart = 3;
        public static final int Transition_duration = 4;
        public static final int Transition_layoutDuringTransition = 5;
        public static final int Transition_motionInterpolator = 6;
        public static final int Transition_pathMotionArc = 7;
        public static final int Transition_staggered = 8;
        public static final int Transition_transitionDisable = 9;
        public static final int Transition_transitionFlags = 10;
        public static final int Variant_constraints = 0;
        public static final int Variant_region_heightLessThan = 1;
        public static final int Variant_region_heightMoreThan = 2;
        public static final int Variant_region_widthLessThan = 3;
        public static final int Variant_region_widthMoreThan = 4;
        public static final int ViewBackgroundHelper_android_background = 0;
        public static final int ViewBackgroundHelper_backgroundTint = 1;
        public static final int ViewBackgroundHelper_backgroundTintMode = 2;
        public static final int ViewStubCompat_android_id = 0;
        public static final int ViewStubCompat_android_inflatedId = 2;
        public static final int ViewStubCompat_android_layout = 1;
        public static final int ViewTransition_SharedValue = 1;
        public static final int ViewTransition_SharedValueId = 2;
        public static final int ViewTransition_android_id = 0;
        public static final int ViewTransition_clearsTag = 3;
        public static final int ViewTransition_duration = 4;
        public static final int ViewTransition_ifTagNotSet = 5;
        public static final int ViewTransition_ifTagSet = 6;
        public static final int ViewTransition_motionInterpolator = 7;
        public static final int ViewTransition_motionTarget = 8;
        public static final int ViewTransition_onStateTransition = 9;
        public static final int ViewTransition_pathMotionArc = 10;
        public static final int ViewTransition_setsTag = 11;
        public static final int ViewTransition_transitionDisable = 12;
        public static final int ViewTransition_upDuration = 13;
        public static final int ViewTransition_viewTransitionMode = 14;
        public static final int View_android_focusable = 1;
        public static final int View_android_theme = 0;
        public static final int View_paddingEnd = 2;
        public static final int View_paddingStart = 3;
        public static final int View_theme = 4;
        public static final int include_constraintSet = 0;
        public static final int[] ActionBar = {2130968658, 2130968665, 2130968666, 2130968888, 2130968889, 2130968890, 2130968891, 2130968892, 2130968893, 2130968931, 2130968956, 2130968957, 2130968995, **********, **********, **********, **********, **********, 2130969155, 2130969180, 2130969315, 2130969483, 2130969537, 2130969558, 2130969559, 2130969700, 2130969704, 2130969841, 2130969855};
        public static final int[] ActionBarLayout = {android.R.attr.layout_gravity};
        public static final int[] ActionMenuItemView = {android.R.attr.minWidth};
        public static final int[] ActionMenuView = new int[0];
        public static final int[] ActionMode = {2130968658, 2130968665, 2130968809, **********, 2130969704, 2130969855};
        public static final int[] ActivityChooserView = {2130969023, 2130969161};
        public static final int[] AlertDialog = {android.R.attr.layout, 2130968721, 2130968724, 2130969304, 2130969305, 2130969479, 2130969641, 2130969648};
        public static final int[] AnimatedStateListDrawableCompat = {android.R.attr.dither, android.R.attr.visible, android.R.attr.variablePadding, android.R.attr.constantSize, android.R.attr.enterFadeDuration, android.R.attr.exitFadeDuration};
        public static final int[] AnimatedStateListDrawableItem = {android.R.attr.id, android.R.attr.drawable};
        public static final int[] AnimatedStateListDrawableTransition = {android.R.attr.drawable, android.R.attr.toId, android.R.attr.fromId, android.R.attr.reversible};
        public static final int[] AppCompatImageView = {android.R.attr.src, 2130969672, 2130969839, 2130969840};
        public static final int[] AppCompatSeekBar = {android.R.attr.thumb, 2130969835, 2130969836, 2130969837};
        public static final int[] AppCompatTextHelper = {android.R.attr.textAppearance, android.R.attr.drawableTop, android.R.attr.drawableBottom, android.R.attr.drawableLeft, android.R.attr.drawableRight, android.R.attr.drawableStart, android.R.attr.drawableEnd};
        public static final int[] AppCompatTextView = {android.R.attr.textAppearance, 2130968652, 2130968653, 2130968654, 2130968655, 2130968656, 2130968972, 2130968973, 2130968974, 2130968975, 2130968977, 2130968978, 2130968979, 2130968980, 2130968999, 2130969057, **********, **********, 2130969208, 2130969296, 2130969754, 2130969809};
        public static final int[] AppCompatTheme = {android.R.attr.windowIsFloating, android.R.attr.windowAnimationStyle, 2130968578, 2130968579, 2130968580, 2130968581, 2130968582, 2130968583, 2130968584, 2130968585, 2130968586, 2130968587, 2130968588, 2130968589, 2130968590, 2130968592, 2130968593, 2130968594, 2130968595, 2130968596, 2130968597, 2130968598, 2130968599, 2130968600, 2130968601, 2130968602, **********, **********, **********, **********, 2130968607, **********, **********, **********, **********, **********, **********, **********, **********, 2130968651, 2130968694, 2130968713, 2130968714, 2130968715, 2130968716, 2130968717, 2130968725, 2130968726, 2130968756, 2130968767, 2130968822, 2130968823, 2130968824, 2130968826, 2130968827, 2130968828, 2130968829, 2130968847, 2130968849, 2130968859, 2130968903, 2130968947, 2130968952, 2130968953, 2130968962, 2130968967, 2130968986, 2130968987, 2130968991, 2130968992, 2130968994, **********, 2130969148, 2130969300, 2130969301, 2130969302, 2130969303, 2130969306, 2130969307, 2130969308, 2130969309, 2130969310, 2130969311, 2130969312, 2130969313, 2130969314, 2130969512, 2130969513, 2130969514, 2130969536, 2130969538, 2130969566, 2130969568, 2130969569, 2130969570, 2130969605, 2130969610, 2130969612, 2130969613, 2130969659, 2130969660, 2130969717, 2130969777, 2130969779, 2130969780, 2130969781, 2130969783, 2130969784, 2130969785, 2130969786, 2130969797, 2130969798, 2130969858, 2130969859, 2130969861, 2130969862, 2130969899, 2130969914, 2130969915, 2130969916, 2130969917, 2130969918, 2130969919, 2130969920, 2130969921, 2130969922, 2130969923};
        public static final int[] ButtonBarLayout = {**********};
        public static final int[] Carousel = {C0261R.attr.carousel_backwardTransition, C0261R.attr.carousel_emptyViewsBehavior, C0261R.attr.carousel_firstView, C0261R.attr.carousel_forwardTransition, C0261R.attr.carousel_infinite, C0261R.attr.carousel_nextState, C0261R.attr.carousel_previousState, C0261R.attr.carousel_touchUpMode, C0261R.attr.carousel_touchUp_dampeningFactor, C0261R.attr.carousel_touchUp_velocityThreshold};
        public static final int[] ColorStateListItem = {android.R.attr.color, android.R.attr.alpha, android.R.attr.lStar, **********, 2130969204};
        public static final int[] CompoundButton = {android.R.attr.button, 2130968718, 2130968727, 2130968728};
        public static final int[] Constraint = {android.R.attr.orientation, android.R.attr.id, android.R.attr.visibility, android.R.attr.layout_width, android.R.attr.layout_height, android.R.attr.layout_marginLeft, android.R.attr.layout_marginTop, android.R.attr.layout_marginRight, android.R.attr.layout_marginBottom, android.R.attr.maxWidth, android.R.attr.maxHeight, android.R.attr.minWidth, android.R.attr.minHeight, android.R.attr.alpha, android.R.attr.transformPivotX, android.R.attr.transformPivotY, android.R.attr.translationX, android.R.attr.translationY, android.R.attr.scaleX, android.R.attr.scaleY, android.R.attr.rotation, android.R.attr.rotationX, android.R.attr.rotationY, android.R.attr.layout_marginStart, android.R.attr.layout_marginEnd, android.R.attr.translationZ, android.R.attr.elevation, C0261R.attr.animateCircleAngleTo, C0261R.attr.animateRelativeTo, 2130968676, 2130968677, 2130968678, 2130968751, 2130968883, C0261R.attr.constraint_referenced_tags, 2130968971, 2130969075, 2130969076, 2130969077, 2130969078, 2130969079, 2130969080, 2130969081, 2130969082, 2130969083, 2130969084, 2130969085, 2130969086, 2130969087, **********, **********, **********, **********, **********, C0261R.attr.guidelineUseRtl, 2130969220, 2130969221, 2130969222, 2130969223, C0261R.attr.layout_constraintBaseline_toBottomOf, C0261R.attr.layout_constraintBaseline_toTopOf, 2130969226, 2130969227, 2130969228, 2130969229, 2130969230, 2130969231, 2130969232, 2130969233, 2130969234, 2130969235, 2130969236, 2130969237, C0261R.attr.layout_constraintHeight, 2130969239, 2130969240, 2130969241, 2130969242, 2130969243, 2130969244, 2130969245, 2130969246, 2130969247, 2130969248, 2130969249, 2130969250, 2130969251, 2130969252, 2130969253, 2130969254, 2130969255, 2130969256, 2130969257, 2130969258, 2130969259, 2130969260, C0261R.attr.layout_constraintWidth, 2130969262, 2130969263, 2130969264, 2130969265, 2130969267, 2130969268, C0261R.attr.layout_goneMarginBaseline, 2130969273, 2130969274, 2130969275, 2130969276, 2130969277, 2130969278, C0261R.attr.layout_marginBaseline, C0261R.attr.layout_wrapBehaviorInParent, 2130969461, 2130969462, 2130969520, 2130969528, C0261R.attr.polarRelativeTo, C0261R.attr.quantizeMotionInterpolator, C0261R.attr.quantizeMotionPhase, C0261R.attr.quantizeMotionSteps, C0261R.attr.transformPivotTarget, 2130969883, 2130969885, 2130969904};
        public static final int[] ConstraintLayout_Layout = {android.R.attr.orientation, android.R.attr.padding, android.R.attr.paddingLeft, android.R.attr.paddingTop, android.R.attr.paddingRight, android.R.attr.paddingBottom, android.R.attr.visibility, android.R.attr.layout_width, android.R.attr.layout_height, android.R.attr.layout_margin, android.R.attr.layout_marginLeft, android.R.attr.layout_marginTop, android.R.attr.layout_marginRight, android.R.attr.layout_marginBottom, android.R.attr.maxWidth, android.R.attr.maxHeight, android.R.attr.minWidth, android.R.attr.minHeight, android.R.attr.paddingStart, android.R.attr.paddingEnd, android.R.attr.layout_marginStart, android.R.attr.layout_marginEnd, android.R.attr.elevation, android.R.attr.layout_marginHorizontal, android.R.attr.layout_marginVertical, 2130968676, 2130968677, 2130968678, 2130968751, C0261R.attr.circularflow_angles, C0261R.attr.circularflow_defaultAngle, C0261R.attr.circularflow_defaultRadius, C0261R.attr.circularflow_radiusInDP, C0261R.attr.circularflow_viewCenter, 2130968880, 2130968883, C0261R.attr.constraint_referenced_tags, 2130969075, 2130969076, 2130969077, 2130969078, 2130969079, 2130969080, 2130969081, 2130969082, 2130969083, 2130969084, 2130969085, 2130969086, 2130969087, **********, **********, **********, **********, **********, C0261R.attr.guidelineUseRtl, 2130969211, 2130969220, 2130969221, 2130969222, 2130969223, C0261R.attr.layout_constraintBaseline_toBottomOf, C0261R.attr.layout_constraintBaseline_toTopOf, 2130969226, 2130969227, 2130969228, 2130969229, 2130969230, 2130969231, 2130969232, 2130969233, 2130969234, 2130969235, 2130969236, 2130969237, C0261R.attr.layout_constraintHeight, 2130969239, 2130969240, 2130969241, 2130969242, 2130969243, 2130969244, 2130969245, 2130969246, 2130969247, 2130969248, 2130969249, 2130969250, 2130969251, 2130969252, 2130969253, 2130969254, 2130969255, 2130969256, 2130969257, 2130969258, 2130969259, 2130969260, C0261R.attr.layout_constraintWidth, 2130969262, 2130969263, 2130969264, 2130969265, 2130969267, 2130969268, C0261R.attr.layout_goneMarginBaseline, 2130969273, 2130969274, 2130969275, 2130969276, 2130969277, 2130969278, C0261R.attr.layout_marginBaseline, 2130969286, C0261R.attr.layout_wrapBehaviorInParent};
        public static final int[] ConstraintLayout_ReactiveGuide = {C0261R.attr.reactiveGuide_animateChange, C0261R.attr.reactiveGuide_applyToAllConstraintSets, C0261R.attr.reactiveGuide_applyToConstraintSet, C0261R.attr.reactiveGuide_valueId};
        public static final int[] ConstraintLayout_placeholder = {2130968886, 2130969533};
        public static final int[] ConstraintOverride = {android.R.attr.orientation, android.R.attr.id, android.R.attr.visibility, android.R.attr.layout_width, android.R.attr.layout_height, android.R.attr.layout_marginLeft, android.R.attr.layout_marginTop, android.R.attr.layout_marginRight, android.R.attr.layout_marginBottom, android.R.attr.maxWidth, android.R.attr.maxHeight, android.R.attr.minWidth, android.R.attr.minHeight, android.R.attr.alpha, android.R.attr.transformPivotX, android.R.attr.transformPivotY, android.R.attr.translationX, android.R.attr.translationY, android.R.attr.scaleX, android.R.attr.scaleY, android.R.attr.rotation, android.R.attr.rotationX, android.R.attr.rotationY, android.R.attr.layout_marginStart, android.R.attr.layout_marginEnd, android.R.attr.translationZ, android.R.attr.elevation, C0261R.attr.animateCircleAngleTo, C0261R.attr.animateRelativeTo, 2130968676, 2130968677, 2130968678, 2130968751, 2130968883, 2130968971, 2130969075, 2130969076, 2130969077, 2130969078, 2130969079, 2130969080, 2130969081, 2130969082, 2130969083, 2130969084, 2130969085, 2130969086, 2130969087, **********, **********, **********, **********, **********, C0261R.attr.guidelineUseRtl, 2130969220, 2130969221, 2130969222, 2130969226, 2130969230, 2130969231, 2130969232, 2130969235, 2130969236, 2130969237, C0261R.attr.layout_constraintHeight, 2130969239, 2130969240, 2130969241, 2130969242, 2130969243, 2130969244, 2130969245, 2130969246, 2130969249, 2130969254, 2130969255, 2130969258, 2130969259, 2130969260, C0261R.attr.layout_constraintWidth, 2130969262, 2130969263, 2130969264, 2130969265, 2130969267, 2130969268, C0261R.attr.layout_goneMarginBaseline, 2130969273, 2130969274, 2130969275, 2130969276, 2130969277, 2130969278, C0261R.attr.layout_marginBaseline, C0261R.attr.layout_wrapBehaviorInParent, 2130969461, 2130969462, 2130969463, 2130969520, 2130969528, C0261R.attr.polarRelativeTo, C0261R.attr.quantizeMotionInterpolator, C0261R.attr.quantizeMotionPhase, C0261R.attr.quantizeMotionSteps, C0261R.attr.transformPivotTarget, 2130969883, 2130969885, 2130969904};
        public static final int[] ConstraintSet = {android.R.attr.orientation, android.R.attr.id, android.R.attr.visibility, android.R.attr.layout_width, android.R.attr.layout_height, android.R.attr.layout_marginLeft, android.R.attr.layout_marginTop, android.R.attr.layout_marginRight, android.R.attr.layout_marginBottom, android.R.attr.maxWidth, android.R.attr.maxHeight, android.R.attr.minWidth, android.R.attr.minHeight, android.R.attr.pivotX, android.R.attr.pivotY, android.R.attr.alpha, android.R.attr.transformPivotX, android.R.attr.transformPivotY, android.R.attr.translationX, android.R.attr.translationY, android.R.attr.scaleX, android.R.attr.scaleY, android.R.attr.rotation, android.R.attr.rotationX, android.R.attr.rotationY, android.R.attr.layout_marginStart, android.R.attr.layout_marginEnd, android.R.attr.translationZ, android.R.attr.elevation, C0261R.attr.animateCircleAngleTo, C0261R.attr.animateRelativeTo, 2130968676, 2130968677, 2130968678, 2130968751, C0261R.attr.constraintRotate, 2130968883, C0261R.attr.constraint_referenced_tags, 2130968946, 2130968971, 2130969075, 2130969076, 2130969077, 2130969078, 2130969079, 2130969080, 2130969081, 2130969082, 2130969083, 2130969084, 2130969085, 2130969086, 2130969087, **********, **********, **********, **********, **********, C0261R.attr.guidelineUseRtl, 2130969220, 2130969221, 2130969222, 2130969223, C0261R.attr.layout_constraintBaseline_toBottomOf, C0261R.attr.layout_constraintBaseline_toTopOf, 2130969226, 2130969227, 2130969228, 2130969229, 2130969230, 2130969231, 2130969232, 2130969233, 2130969234, 2130969235, 2130969236, 2130969237, 2130969239, 2130969240, 2130969241, 2130969242, 2130969243, 2130969244, 2130969245, 2130969246, 2130969247, 2130969248, 2130969249, 2130969250, 2130969251, 2130969252, 2130969253, 2130969254, 2130969255, 2130969256, 2130969257, 2130969258, 2130969259, 2130969260, 2130969262, 2130969263, 2130969264, 2130969265, 2130969267, 2130969268, C0261R.attr.layout_goneMarginBaseline, 2130969273, 2130969274, 2130969275, 2130969276, 2130969277, 2130969278, C0261R.attr.layout_marginBaseline, C0261R.attr.layout_wrapBehaviorInParent, 2130969461, 2130969462, 2130969520, 2130969528, C0261R.attr.polarRelativeTo, C0261R.attr.quantizeMotionSteps, 2130969883, 2130969885};
        public static final int[] CustomAttribute = {2130968649, 2130968925, 2130968926, 2130968927, 2130968928, 2130968929, 2130968930, 2130968932, C0261R.attr.customReference, 2130968934, C0261R.attr.methodName};
        public static final int[] DrawerArrowToggle = {**********, 2130968648, 2130968675, 2130968821, 2130968976, **********, 2130969658, 2130969820};
        public static final int[] FontFamily = {**********, **********, **********, **********, **********, **********, 2130969102};
        public static final int[] FontFamilyFont = {android.R.attr.font, android.R.attr.fontWeight, android.R.attr.fontStyle, android.R.attr.ttcIndex, android.R.attr.fontVariationSettings, **********, **********, **********, **********, 2130969890};
        public static final int[] GradientColor = {android.R.attr.startColor, android.R.attr.endColor, android.R.attr.type, android.R.attr.centerX, android.R.attr.centerY, android.R.attr.gradientRadius, android.R.attr.tileMode, android.R.attr.centerColor, android.R.attr.startX, android.R.attr.startY, android.R.attr.endX, android.R.attr.endY};
        public static final int[] GradientColorItem = {android.R.attr.color, android.R.attr.offset};
        public static final int[] ImageFilterView = {**********, C0261R.attr.blendSrc, 2130968712, 2130968902, 2130968922, C0261R.attr.imagePanX, C0261R.attr.imagePanY, C0261R.attr.imageRotate, C0261R.attr.imageZoom, 2130969503, 2130969596, 2130969597, 2130969598, 2130969906};
        public static final int[] KeyAttribute = {android.R.attr.alpha, android.R.attr.transformPivotX, android.R.attr.transformPivotY, android.R.attr.translationX, android.R.attr.translationY, android.R.attr.scaleX, android.R.attr.scaleY, android.R.attr.rotation, android.R.attr.rotationX, android.R.attr.rotationY, android.R.attr.translationZ, android.R.attr.elevation, 2130968924, **********, 2130969461, 2130969463, C0261R.attr.transformPivotTarget, 2130969883, 2130969885};
        public static final int[] KeyCycle = {android.R.attr.alpha, android.R.attr.translationX, android.R.attr.translationY, android.R.attr.scaleX, android.R.attr.scaleY, android.R.attr.rotation, android.R.attr.rotationX, android.R.attr.rotationY, android.R.attr.translationZ, android.R.attr.elevation, 2130968924, **********, 2130969461, 2130969463, 2130969883, 2130969885, 2130969908, 2130969909, C0261R.attr.wavePhase, 2130969911, 2130969912};
        public static final int[] KeyFrame = new int[0];
        public static final int[] KeyFramesAcceleration = new int[0];
        public static final int[] KeyFramesVelocity = new int[0];
        public static final int[] KeyPosition = {2130968924, 2130968971, **********, 2130969201, 2130969463, 2130969520, 2130969522, 2130969523, 2130969524, 2130969525, 2130969652, 2130969883};
        public static final int[] KeyTimeCycle = {android.R.attr.alpha, android.R.attr.translationX, android.R.attr.translationY, android.R.attr.scaleX, android.R.attr.scaleY, android.R.attr.rotation, android.R.attr.rotationX, android.R.attr.rotationY, android.R.attr.translationZ, android.R.attr.elevation, 2130968924, **********, 2130969461, 2130969463, 2130969883, 2130969885, 2130969907, 2130969908, 2130969909, C0261R.attr.wavePhase, 2130969911};
        public static final int[] KeyTrigger = {**********, 2130969463, 2130969464, 2130969465, 2130969493, 2130969495, 2130969496, 2130969887, 2130969888, 2130969889, C0261R.attr.viewTransitionOnCross, C0261R.attr.viewTransitionOnNegativeCross, C0261R.attr.viewTransitionOnPositiveCross};
        public static final int[] Layout = {android.R.attr.orientation, android.R.attr.layout_width, android.R.attr.layout_height, android.R.attr.layout_marginLeft, android.R.attr.layout_marginTop, android.R.attr.layout_marginRight, android.R.attr.layout_marginBottom, android.R.attr.layout_marginStart, android.R.attr.layout_marginEnd, 2130968676, 2130968677, 2130968678, 2130968751, 2130968883, C0261R.attr.constraint_referenced_tags, C0261R.attr.guidelineUseRtl, 2130969220, 2130969221, 2130969222, 2130969223, C0261R.attr.layout_constraintBaseline_toBottomOf, C0261R.attr.layout_constraintBaseline_toTopOf, 2130969226, 2130969227, 2130969228, 2130969229, 2130969230, 2130969231, 2130969232, 2130969233, 2130969234, 2130969235, 2130969236, 2130969237, C0261R.attr.layout_constraintHeight, 2130969239, 2130969240, 2130969241, 2130969242, 2130969243, 2130969244, 2130969245, 2130969246, 2130969247, 2130969248, 2130969249, 2130969250, 2130969251, 2130969252, 2130969253, 2130969255, 2130969256, 2130969257, 2130969258, 2130969259, 2130969260, C0261R.attr.layout_constraintWidth, 2130969262, 2130969263, 2130969264, 2130969265, 2130969267, 2130969268, C0261R.attr.layout_goneMarginBaseline, 2130969273, 2130969274, 2130969275, 2130969276, 2130969277, 2130969278, C0261R.attr.layout_marginBaseline, C0261R.attr.layout_wrapBehaviorInParent, 2130969369, 2130969374, 2130969410, 2130969414};
        public static final int[] LinearLayoutCompat = {android.R.attr.gravity, android.R.attr.orientation, android.R.attr.baselineAligned, android.R.attr.baselineAlignedChildIndex, android.R.attr.weightSum, 2130968957, 2130968965, 2130969404, 2130969636};
        public static final int[] LinearLayoutCompat_Layout = {android.R.attr.layout_gravity, android.R.attr.layout_width, android.R.attr.layout_height, android.R.attr.layout_weight};
        public static final int[] ListPopupWindow = {android.R.attr.dropDownHorizontalOffset, android.R.attr.dropDownVerticalOffset};
        public static final int[] MenuGroup = {android.R.attr.enabled, android.R.attr.id, android.R.attr.visible, android.R.attr.menuCategory, android.R.attr.orderInCategory, android.R.attr.checkableBehavior};
        public static final int[] MenuItem = {android.R.attr.icon, android.R.attr.enabled, android.R.attr.id, android.R.attr.checked, android.R.attr.visible, android.R.attr.menuCategory, android.R.attr.orderInCategory, android.R.attr.title, android.R.attr.titleCondensed, android.R.attr.alphabeticShortcut, android.R.attr.numericShortcut, android.R.attr.checkable, android.R.attr.onClick, 2130968591, **********, **********, **********, 2130968887, **********, **********, 2130969492, 2130969631, 2130969864};
        public static final int[] MenuView = {android.R.attr.windowAnimationStyle, android.R.attr.itemTextAppearance, android.R.attr.horizontalDivider, android.R.attr.verticalDivider, android.R.attr.headerBackground, android.R.attr.itemBackground, android.R.attr.itemIconDisabledAlpha, 2130969555, 2130969694};
        public static final int[] MockView = {2130969415, 2130969416, 2130969417, 2130969418, 2130969419, 2130969420};
        public static final int[] Motion = {C0261R.attr.animateCircleAngleTo, C0261R.attr.animateRelativeTo, 2130968971, 2130969460, 2130969462, 2130969520, C0261R.attr.quantizeMotionInterpolator, C0261R.attr.quantizeMotionPhase, C0261R.attr.quantizeMotionSteps, 2130969883};
        public static final int[] MotionEffect = {C0261R.attr.motionEffect_alpha, C0261R.attr.motionEffect_end, C0261R.attr.motionEffect_move, C0261R.attr.motionEffect_start, C0261R.attr.motionEffect_strict, C0261R.attr.motionEffect_translationX, C0261R.attr.motionEffect_translationY, C0261R.attr.motionEffect_viewTransition};
        public static final int[] MotionHelper = {2130969494, 2130969497};
        public static final int[] MotionLabel = {android.R.attr.textSize, android.R.attr.typeface, android.R.attr.textStyle, android.R.attr.textColor, android.R.attr.gravity, android.R.attr.text, android.R.attr.shadowRadius, android.R.attr.fontFamily, android.R.attr.autoSizeTextType, C0261R.attr.borderRound, C0261R.attr.borderRoundPercent, C0261R.attr.scaleFromTextSize, C0261R.attr.textBackground, C0261R.attr.textBackgroundPanX, C0261R.attr.textBackgroundPanY, C0261R.attr.textBackgroundRotate, C0261R.attr.textBackgroundZoom, C0261R.attr.textOutlineColor, C0261R.attr.textOutlineThickness, C0261R.attr.textPanX, C0261R.attr.textPanY, C0261R.attr.textureBlurFactor, C0261R.attr.textureEffect, C0261R.attr.textureHeight, C0261R.attr.textureWidth};
        public static final int[] MotionLayout = {**********, 2130968923, 2130969211, 2130969421, 2130969461, 2130969638};
        public static final int[] MotionScene = {2130968939, 2130969212};
        public static final int[] MotionTelltales = {2130969750, 2130969751, 2130969752};
        public static final int[] OnClick = {2130968797, 2130969749};
        public static final int[] OnSwipe = {C0261R.attr.autoCompleteMode, 2130968968, 2130968969, 2130968970, 2130969295, 2130969365, 2130969373, 2130969466, 2130969488, 2130969499, C0261R.attr.rotationCenterId, C0261R.attr.springBoundary, C0261R.attr.springDamping, C0261R.attr.springMass, C0261R.attr.springStiffness, C0261R.attr.springStopThreshold, 2130969866, 2130969867, 2130969868};
        public static final int[] PopupWindow = {android.R.attr.popupBackground, android.R.attr.popupAnimationStyle, 2130969502};
        public static final int[] PopupWindowBackgroundState = {2130969680};
        public static final int[] PropertySet = {android.R.attr.visibility, android.R.attr.alpha, 2130969254, 2130969461, 2130969904};
        public static final int[] RecycleListView = {2130969504, 2130969510};
        public static final int[] SearchView = {android.R.attr.focusable, android.R.attr.maxWidth, android.R.attr.inputType, android.R.attr.imeOptions, 2130968802, 2130968878, 2130968940, **********, **********, 2130969210, 2130969563, 2130969564, 2130969603, 2130969604, 2130969699, 2130969708, 2130969905};
        public static final int[] Spinner = {android.R.attr.entries, android.R.attr.popupBackground, android.R.attr.prompt, android.R.attr.dropDownWidth, 2130969537};
        public static final int[] State = {android.R.attr.id, 2130968885};
        public static final int[] StateListDrawable = {android.R.attr.dither, android.R.attr.visible, android.R.attr.variablePadding, android.R.attr.constantSize, android.R.attr.enterFadeDuration, android.R.attr.exitFadeDuration};
        public static final int[] StateListDrawableItem = {android.R.attr.drawable};
        public static final int[] StateSet = {2130968941};
        public static final int[] SwitchCompat = {android.R.attr.textOn, android.R.attr.textOff, android.R.attr.thumb, 2130969640, 2130969665, 2130969713, 2130969714, 2130969718, 2130969829, 2130969830, 2130969831, 2130969869, 2130969879, 2130969880};
        public static final int[] TextAppearance = {android.R.attr.textSize, android.R.attr.typeface, android.R.attr.textStyle, android.R.attr.textColor, android.R.attr.textColorHint, android.R.attr.textColorLink, android.R.attr.shadowColor, android.R.attr.shadowDx, android.R.attr.shadowDy, android.R.attr.shadowRadius, android.R.attr.fontFamily, android.R.attr.textFontWeight, **********, **********, 2130969754, 2130969809};
        public static final int[] TextEffects = {android.R.attr.textSize, android.R.attr.typeface, android.R.attr.textStyle, android.R.attr.text, android.R.attr.shadowColor, android.R.attr.shadowDx, android.R.attr.shadowDy, android.R.attr.shadowRadius, android.R.attr.fontFamily, C0261R.attr.borderRound, C0261R.attr.borderRoundPercent, C0261R.attr.textFillColor, C0261R.attr.textOutlineColor, C0261R.attr.textOutlineThickness};
        public static final int[] Toolbar = {android.R.attr.gravity, android.R.attr.minHeight, 2130968719, 2130968810, 2130968811, 2130968888, 2130968889, 2130968890, 2130968891, 2130968892, 2130968893, 2130969315, 2130969317, 2130969367, 2130969405, 2130969480, 2130969481, 2130969537, 2130969700, 2130969702, 2130969703, 2130969841, 2130969845, 2130969846, 2130969847, 2130969848, 2130969849, 2130969850, 2130969852, 2130969853};
        public static final int[] Transform = {android.R.attr.transformPivotX, android.R.attr.transformPivotY, android.R.attr.translationX, android.R.attr.translationY, android.R.attr.scaleX, android.R.attr.scaleY, android.R.attr.rotation, android.R.attr.rotationX, android.R.attr.rotationY, android.R.attr.translationZ, android.R.attr.elevation, C0261R.attr.transformPivotTarget};
        public static final int[] Transition = {android.R.attr.id, 2130968657, 2130968881, 2130968882, 2130968989, 2130969212, 2130969458, 2130969520, 2130969674, 2130969882, 2130969884};
        public static final int[] Variant = {2130968885, 2130969576, 2130969577, 2130969578, 2130969579};
        public static final int[] View = {android.R.attr.theme, android.R.attr.focusable, 2130969506, 2130969509, 2130969819};
        public static final int[] ViewBackgroundHelper = {android.R.attr.background, 2130968667, 2130968668};
        public static final int[] ViewStubCompat = {android.R.attr.id, android.R.attr.layout, android.R.attr.inflatedId};
        public static final int[] ViewTransition = {android.R.attr.id, C0261R.attr.SharedValue, C0261R.attr.SharedValueId, C0261R.attr.clearsTag, 2130968989, C0261R.attr.ifTagNotSet, C0261R.attr.ifTagSet, 2130969458, 2130969463, C0261R.attr.onStateTransition, 2130969520, C0261R.attr.setsTag, 2130969882, C0261R.attr.upDuration, C0261R.attr.viewTransitionMode};
        public static final int[] include = {2130968880};

        private styleable() {
        }
    }

    private C0261R() {
    }
}
