package androidx.coordinatorlayout;

/* renamed from: androidx.coordinatorlayout.R */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class C0262R {

    /* renamed from: androidx.coordinatorlayout.R$attr */
    public static final class attr {
        public static final int alpha = **********;
        public static final int coordinatorLayoutStyle = **********;
        public static final int font = **********;
        public static final int fontProviderAuthority = **********;
        public static final int fontProviderCerts = **********;
        public static final int fontProviderFetchStrategy = **********;
        public static final int fontProviderFetchTimeout = **********;
        public static final int fontProviderPackage = **********;
        public static final int fontProviderQuery = **********;
        public static final int fontStyle = **********;
        public static final int fontVariationSettings = **********;
        public static final int fontWeight = **********;
        public static final int keylines = **********;
        public static final int layout_anchor = **********;
        public static final int layout_anchorGravity = **********;
        public static final int layout_behavior = **********;
        public static final int layout_dodgeInsetEdges = **********;
        public static final int layout_insetEdge = **********;
        public static final int layout_keyline = **********;
        public static final int statusBarBackground = **********;
        public static final int ttcIndex = **********;

        private attr() {
        }
    }

    /* renamed from: androidx.coordinatorlayout.R$color */
    public static final class color {
        public static final int notification_action_color_filter = **********;
        public static final int notification_icon_bg_color = 2131100289;
        public static final int ripple_material_light = 2131100301;
        public static final int secondary_text_default_material_light = 2131100306;

        private color() {
        }
    }

    /* renamed from: androidx.coordinatorlayout.R$dimen */
    public static final class dimen {
        public static final int compat_button_inset_horizontal_material = 2131165275;
        public static final int compat_button_inset_vertical_material = 2131165276;
        public static final int compat_button_padding_horizontal_material = 2131165277;
        public static final int compat_button_padding_vertical_material = 2131165278;
        public static final int compat_control_corner_material = 2131165279;
        public static final int compat_notification_large_icon_max_height = 2131165280;
        public static final int compat_notification_large_icon_max_width = 2131165281;
        public static final int notification_action_icon_size = 2131165823;
        public static final int notification_action_text_size = 2131165824;
        public static final int notification_big_circle_margin = 2131165825;
        public static final int notification_content_margin_start = 2131165826;
        public static final int notification_large_icon_height = 2131165827;
        public static final int notification_large_icon_width = 2131165828;
        public static final int notification_main_column_padding_top = 2131165829;
        public static final int notification_media_narrow_margin = 2131165830;
        public static final int notification_right_icon_size = 2131165831;
        public static final int notification_right_side_padding_top = 2131165832;
        public static final int notification_small_icon_background_padding = 2131165833;
        public static final int notification_small_icon_size_as_large = 2131165834;
        public static final int notification_subtext_size = 2131165835;
        public static final int notification_top_pad = 2131165836;
        public static final int notification_top_pad_large_text = 2131165837;

        private dimen() {
        }
    }

    /* renamed from: androidx.coordinatorlayout.R$drawable */
    public static final class drawable {
        public static final int notification_action_background = 2131231994;
        public static final int notification_bg = 2131231995;
        public static final int notification_bg_low = 2131231996;
        public static final int notification_bg_low_normal = 2131231997;
        public static final int notification_bg_low_pressed = 2131231998;
        public static final int notification_bg_normal = 2131231999;
        public static final int notification_bg_normal_pressed = 2131232000;
        public static final int notification_icon_background = 2131232001;
        public static final int notification_template_icon_bg = 2131232002;
        public static final int notification_template_icon_low_bg = 2131232003;
        public static final int notification_tile_bg = 2131232004;
        public static final int notify_panel_notification_icon_bg = 2131232005;

        private drawable() {
        }
    }

    /* renamed from: androidx.coordinatorlayout.R$id */
    public static final class id {
        public static final int accessibility_action_clickable_span = 2131296272;
        public static final int accessibility_custom_action_0 = 2131296273;
        public static final int accessibility_custom_action_1 = 2131296274;
        public static final int accessibility_custom_action_10 = 2131296275;
        public static final int accessibility_custom_action_11 = 2131296276;
        public static final int accessibility_custom_action_12 = 2131296277;
        public static final int accessibility_custom_action_13 = 2131296278;
        public static final int accessibility_custom_action_14 = 2131296279;
        public static final int accessibility_custom_action_15 = 2131296280;
        public static final int accessibility_custom_action_16 = 2131296281;
        public static final int accessibility_custom_action_17 = 2131296282;
        public static final int accessibility_custom_action_18 = 2131296283;
        public static final int accessibility_custom_action_19 = 2131296284;
        public static final int accessibility_custom_action_2 = 2131296285;
        public static final int accessibility_custom_action_20 = 2131296286;
        public static final int accessibility_custom_action_21 = 2131296287;
        public static final int accessibility_custom_action_22 = 2131296288;
        public static final int accessibility_custom_action_23 = 2131296289;
        public static final int accessibility_custom_action_24 = 2131296290;
        public static final int accessibility_custom_action_25 = 2131296291;
        public static final int accessibility_custom_action_26 = 2131296292;
        public static final int accessibility_custom_action_27 = 2131296293;
        public static final int accessibility_custom_action_28 = 2131296294;
        public static final int accessibility_custom_action_29 = 2131296295;
        public static final int accessibility_custom_action_3 = 2131296296;
        public static final int accessibility_custom_action_30 = 2131296297;
        public static final int accessibility_custom_action_31 = 2131296298;
        public static final int accessibility_custom_action_4 = 2131296299;
        public static final int accessibility_custom_action_5 = 2131296300;
        public static final int accessibility_custom_action_6 = 2131296301;
        public static final int accessibility_custom_action_7 = 2131296302;
        public static final int accessibility_custom_action_8 = 2131296303;
        public static final int accessibility_custom_action_9 = 2131296304;
        public static final int action_container = 2131296332;
        public static final int action_divider = 2131296346;
        public static final int action_image = 2131296360;
        public static final int action_text = 2131296407;
        public static final int actions = 2131296409;
        public static final int async = 2131296434;
        public static final int blocking = 2131296455;
        public static final int bottom = 2131296456;
        public static final int chronometer = 2131296493;
        public static final int dialog_button = 2131296558;
        public static final int end = 2131296597;
        public static final int forever = 2131296644;
        public static final int icon = 2131296675;
        public static final int icon_group = 2131296677;
        public static final int info = 2131296686;
        public static final int italic = 2131296699;
        public static final int left = 2131296716;
        public static final int line1 = 2131296719;
        public static final int line3 = 2131296720;
        public static final int none = 2131296834;
        public static final int normal = 2131296835;
        public static final int notification_background = 2131296837;
        public static final int notification_main_column = 2131296838;
        public static final int notification_main_column_container = 2131296839;
        public static final int right = 2131296905;
        public static final int right_icon = 2131296907;
        public static final int right_side = 2131296908;
        public static final int start = 2131297001;
        public static final int tag_accessibility_actions = 2131297028;
        public static final int tag_accessibility_clickable_spans = 2131297029;
        public static final int tag_accessibility_heading = 2131297030;
        public static final int tag_accessibility_pane_title = 2131297031;
        public static final int tag_screen_reader_focusable = 2131297035;
        public static final int tag_transition_group = 2131297037;
        public static final int tag_unhandled_key_event_manager = 2131297038;
        public static final int tag_unhandled_key_listeners = 2131297039;
        public static final int text = 2131297043;
        public static final int text2 = 2131297044;
        public static final int time = 2131297059;
        public static final int title = 2131297062;
        public static final int top = 2131297070;

        private id() {
        }
    }

    /* renamed from: androidx.coordinatorlayout.R$integer */
    public static final class integer {
        public static final int status_bar_notification_info_maxnum = 2131361870;

        private integer() {
        }
    }

    /* renamed from: androidx.coordinatorlayout.R$layout */
    public static final class layout {
        public static final int custom_dialog = 2131492922;
        public static final int notification_action = 2131493075;
        public static final int notification_action_tombstone = 2131493076;
        public static final int notification_template_custom_big = 2131493083;
        public static final int notification_template_icon_group = 2131493084;
        public static final int notification_template_part_chronometer = 2131493088;
        public static final int notification_template_part_time = 2131493089;

        private layout() {
        }
    }

    /* renamed from: androidx.coordinatorlayout.R$string */
    public static final class string {
        public static final int status_bar_notification_info_overflow = 2131821138;

        private string() {
        }
    }

    /* renamed from: androidx.coordinatorlayout.R$style */
    public static final class style {
        public static final int TextAppearance_Compat_Notification = 2131886552;
        public static final int TextAppearance_Compat_Notification_Info = 2131886553;
        public static final int TextAppearance_Compat_Notification_Line2 = 2131886555;
        public static final int TextAppearance_Compat_Notification_Time = 2131886558;
        public static final int TextAppearance_Compat_Notification_Title = 2131886560;
        public static final int Widget_Compat_NotificationActionContainer = 2131886912;
        public static final int Widget_Compat_NotificationActionText = 2131886913;
        public static final int Widget_Support_CoordinatorLayout = 2131887205;

        private style() {
        }
    }

    /* renamed from: androidx.coordinatorlayout.R$styleable */
    public static final class styleable {
        public static final int ColorStateListItem_alpha = 3;
        public static final int ColorStateListItem_android_alpha = 1;
        public static final int ColorStateListItem_android_color = 0;
        public static final int ColorStateListItem_android_lStar = 2;
        public static final int ColorStateListItem_lStar = 4;
        public static final int CoordinatorLayout_Layout_android_layout_gravity = 0;
        public static final int CoordinatorLayout_Layout_layout_anchor = 1;
        public static final int CoordinatorLayout_Layout_layout_anchorGravity = 2;
        public static final int CoordinatorLayout_Layout_layout_behavior = 3;
        public static final int CoordinatorLayout_Layout_layout_dodgeInsetEdges = 4;
        public static final int CoordinatorLayout_Layout_layout_insetEdge = 5;
        public static final int CoordinatorLayout_Layout_layout_keyline = 6;
        public static final int CoordinatorLayout_keylines = 0;
        public static final int CoordinatorLayout_statusBarBackground = 1;
        public static final int FontFamilyFont_android_font = 0;
        public static final int FontFamilyFont_android_fontStyle = 2;
        public static final int FontFamilyFont_android_fontVariationSettings = 4;
        public static final int FontFamilyFont_android_fontWeight = 1;
        public static final int FontFamilyFont_android_ttcIndex = 3;
        public static final int FontFamilyFont_font = 5;
        public static final int FontFamilyFont_fontStyle = 6;
        public static final int FontFamilyFont_fontVariationSettings = 7;
        public static final int FontFamilyFont_fontWeight = 8;
        public static final int FontFamilyFont_ttcIndex = 9;
        public static final int FontFamily_fontProviderAuthority = 0;
        public static final int FontFamily_fontProviderCerts = 1;
        public static final int FontFamily_fontProviderFetchStrategy = 2;
        public static final int FontFamily_fontProviderFetchTimeout = 3;
        public static final int FontFamily_fontProviderPackage = 4;
        public static final int FontFamily_fontProviderQuery = 5;
        public static final int FontFamily_fontProviderSystemFontFamily = 6;
        public static final int GradientColorItem_android_color = 0;
        public static final int GradientColorItem_android_offset = 1;
        public static final int GradientColor_android_centerColor = 7;
        public static final int GradientColor_android_centerX = 3;
        public static final int GradientColor_android_centerY = 4;
        public static final int GradientColor_android_endColor = 1;
        public static final int GradientColor_android_endX = 10;
        public static final int GradientColor_android_endY = 11;
        public static final int GradientColor_android_gradientRadius = 5;
        public static final int GradientColor_android_startColor = 0;
        public static final int GradientColor_android_startX = 8;
        public static final int GradientColor_android_startY = 9;
        public static final int GradientColor_android_tileMode = 6;
        public static final int GradientColor_android_type = 2;
        public static final int[] ColorStateListItem = {android.R.attr.color, android.R.attr.alpha, android.R.attr.lStar, **********, 2130969204};
        public static final int[] CoordinatorLayout = {**********, **********};
        public static final int[] CoordinatorLayout_Layout = {android.R.attr.layout_gravity, **********, **********, **********, **********, **********, **********};
        public static final int[] FontFamily = {**********, **********, **********, **********, **********, **********, 2130969102};
        public static final int[] FontFamilyFont = {android.R.attr.font, android.R.attr.fontWeight, android.R.attr.fontStyle, android.R.attr.ttcIndex, android.R.attr.fontVariationSettings, **********, **********, **********, **********, **********};
        public static final int[] GradientColor = {android.R.attr.startColor, android.R.attr.endColor, android.R.attr.type, android.R.attr.centerX, android.R.attr.centerY, android.R.attr.gradientRadius, android.R.attr.tileMode, android.R.attr.centerColor, android.R.attr.startX, android.R.attr.startY, android.R.attr.endX, android.R.attr.endY};
        public static final int[] GradientColorItem = {android.R.attr.color, android.R.attr.offset};

        private styleable() {
        }
    }

    private C0262R() {
    }
}
