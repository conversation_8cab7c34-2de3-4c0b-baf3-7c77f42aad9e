package androidx.compose.foundation.layout;

import androidx.annotation.RequiresApi;
import kotlin.Metadata;

@RequiresApi(30)
@Metadata(m4113bv = {}, m4114d1 = {"\u0000\n\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\bc\u0018\u00002\u00020\u0001ø\u0001\u0000\u0082\u0002\u0006\n\u0004\b!0\u0001¨\u0006\u0002À\u0006\u0001"}, m4115d2 = {"Landroidx/compose/foundation/layout/Ԭ;", "", "foundation-layout_release"}, m4116k = 1, m4117mv = {1, 6, 0})
/* renamed from: androidx.compose.foundation.layout.Ԭ */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
interface InterfaceC0160 {
}
