package android.view.compose;

import android.view.result.ActivityResultCallback;
import android.view.result.ActivityResultRegistry;
import android.view.result.contract.ActivityResultContract;
import kotlin.Metadata;
import okhttp3.internal.p042io.C4389oh;
import okhttp3.internal.p042io.InterfaceC4249nh;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.g05;
import okhttp3.internal.p042io.lv1;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.ph0;
import okhttp3.internal.p042io.zu2;

@Metadata(m4116k = 3, m4117mv = {1, 6, 0}, m4119xi = 48)
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class ActivityResultRegistryKt$rememberLauncherForActivityResult$1 extends lv1 implements ph0<C4389oh, InterfaceC4249nh> {
    public final /* synthetic */ ActivityResultRegistry $activityResultRegistry;
    public final /* synthetic */ ActivityResultContract<I, O> $contract;
    public final /* synthetic */ g05<ph0<O, lx5>> $currentOnResult;
    public final /* synthetic */ String $key;
    public final /* synthetic */ ActivityResultLauncherHolder<I> $realLauncher;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    /* JADX WARN: Multi-variable type inference failed */
    public ActivityResultRegistryKt$rememberLauncherForActivityResult$1(ActivityResultLauncherHolder<I> activityResultLauncherHolder, ActivityResultRegistry activityResultRegistry, String str, ActivityResultContract<I, O> activityResultContract, g05<? extends ph0<? super O, lx5>> g05Var) {
        super(1);
        this.$realLauncher = activityResultLauncherHolder;
        this.$activityResultRegistry = activityResultRegistry;
        this.$key = str;
        this.$contract = activityResultContract;
        this.$currentOnResult = g05Var;
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: invoke$lambda-0, reason: not valid java name */
    public static final void m18332invoke$lambda0(g05 g05Var, Object obj) {
        fa1.m6826(g05Var, "$currentOnResult");
        ((ph0) g05Var.getValue()).invoke(obj);
    }

    @Override // okhttp3.internal.p042io.ph0
    @zu2
    public final InterfaceC4249nh invoke(@zu2 C4389oh c4389oh) {
        fa1.m6826(c4389oh, "$this$DisposableEffect");
        ActivityResultLauncherHolder<I> activityResultLauncherHolder = this.$realLauncher;
        ActivityResultRegistry activityResultRegistry = this.$activityResultRegistry;
        String str = this.$key;
        Object obj = this.$contract;
        final g05<ph0<O, lx5>> g05Var = this.$currentOnResult;
        activityResultLauncherHolder.setLauncher(activityResultRegistry.register(str, obj, new ActivityResultCallback() { // from class: androidx.activity.compose.Ϳ
            @Override // android.view.result.ActivityResultCallback
            public final void onActivityResult(Object obj2) {
                ActivityResultRegistryKt$rememberLauncherForActivityResult$1.m18332invoke$lambda0(g05.this, obj2);
            }
        }));
        final ActivityResultLauncherHolder<I> activityResultLauncherHolder2 = this.$realLauncher;
        return new InterfaceC4249nh() { // from class: androidx.activity.compose.ActivityResultRegistryKt$rememberLauncherForActivityResult$1$invoke$$inlined$onDispose$1
            @Override // okhttp3.internal.p042io.InterfaceC4249nh
            public void dispose() {
                ActivityResultLauncherHolder.this.unregister();
            }
        };
    }
}
