package okhttp3.internal.p042io;

import java.util.Iterator;
import java.util.List;
import okhttp3.internal.p042io.InterfaceC6254;

/* renamed from: okhttp3.internal.io.v9 */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public class C5451v9 implements InterfaceC6254 {

    /* renamed from: ၦ */
    public static final /* synthetic */ sr1<Object>[] f23863 = {zx3.m14663(new el3(zx3.m14661(C5451v9.class), "annotations", "getAnnotations()Ljava/util/List;"))};

    /* renamed from: ၥ */
    @zu2
    public final av2 f23864;

    public C5451v9(@zu2 v25 v25Var, @zu2 nh0<? extends List<? extends InterfaceC7823>> nh0Var) {
        fa1.m6826(v25Var, "storageManager");
        this.f23864 = v25Var.mo10231(nh0Var);
    }

    @Override // okhttp3.internal.p042io.InterfaceC6254
    public boolean isEmpty() {
        return ((List) C6430.m15375(this.f23864, f23863[0])).isEmpty();
    }

    @Override // java.lang.Iterable
    @zu2
    public final Iterator<InterfaceC7823> iterator() {
        return ((List) C6430.m15375(this.f23864, f23863[0])).iterator();
    }

    @Override // okhttp3.internal.p042io.InterfaceC6254
    @wv2
    /* renamed from: ԩ */
    public final InterfaceC7823 mo8514(@zu2 ig0 ig0Var) {
        return InterfaceC6254.C9630.m15012(this, ig0Var);
    }

    @Override // okhttp3.internal.p042io.InterfaceC6254
    /* renamed from: ޘ */
    public final boolean mo8515(@zu2 ig0 ig0Var) {
        return InterfaceC6254.C9630.m15013(this, ig0Var);
    }
}
