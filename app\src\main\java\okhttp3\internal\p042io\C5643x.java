package okhttp3.internal.p042io;

import androidx.annotation.NonNull;
import java.io.File;
import okhttp3.internal.p042io.InterfaceC5195tg;

/* renamed from: okhttp3.internal.io.x */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes3.dex */
public final class C5643x<DataType> implements InterfaceC5195tg.InterfaceC9358 {

    /* renamed from: Ϳ */
    public final InterfaceC5739xr<DataType> f25266;

    /* renamed from: Ԩ */
    public final DataType f25267;

    /* renamed from: ԩ */
    public final s23 f25268;

    public C5643x(InterfaceC5739xr<DataType> interfaceC5739xr, DataType datatype, s23 s23Var) {
        this.f25266 = interfaceC5739xr;
        this.f25267 = datatype;
        this.f25268 = s23Var;
    }

    /* renamed from: Ϳ */
    public final boolean m13789(@NonNull File file) {
        return this.f25266.mo4996(this.f25267, file, this.f25268);
    }
}
