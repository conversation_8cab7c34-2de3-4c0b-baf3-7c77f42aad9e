package androidx.compose.foundation.layout;

import okhttp3.internal.p042io.C5849yq;
import okhttp3.internal.p042io.C6492;
import okhttp3.internal.p042io.bc3;
import okhttp3.internal.p042io.bf2;
import okhttp3.internal.p042io.cw1;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.g71;
import okhttp3.internal.p042io.lv1;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.ph0;
import okhttp3.internal.p042io.rg0;
import okhttp3.internal.p042io.ue2;
import okhttp3.internal.p042io.ze2;
import okhttp3.internal.p042io.zu2;

/* renamed from: androidx.compose.foundation.layout.Ϳ */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final /* synthetic */ class C0157 {

    /* renamed from: androidx.compose.foundation.layout.Ϳ$Ϳ, reason: contains not printable characters */
    public static final class C8534 extends lv1 implements ph0<bc3.AbstractC2861, lx5> {

        /* renamed from: ၥ */
        public final /* synthetic */ bc3 f22;

        /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
        public C8534(bc3 bc3Var) {
            super(1);
            this.f22 = bc3Var;
        }

        @Override // okhttp3.internal.p042io.ph0
        public final lx5 invoke(bc3.AbstractC2861 abstractC2861) {
            long m5066;
            bc3.AbstractC2861 abstractC28612 = abstractC2861;
            fa1.m6826(abstractC28612, "$this$layout");
            bc3 bc3Var = this.f22;
            g71.C3412 c3412 = g71.f10637;
            long j = g71.f10638;
            fa1.m6826(bc3Var, "$this$placeRelative");
            if (abstractC28612.mo5079() == cw1.Ltr || abstractC28612.mo5080() == 0) {
                m5066 = bc3Var.m5066();
                g71.C3412 c34122 = g71.f10637;
            } else {
                int mo5080 = abstractC28612.mo5080() - bc3Var.f7055;
                g71.C3412 c34123 = g71.f10637;
                j = rg0.m11587(mo5080 - ((int) (j >> 32)), g71.m7254(j));
                m5066 = bc3Var.m5066();
            }
            bc3Var.mo5069(rg0.m11587(((int) (j >> 32)) + ((int) (m5066 >> 32)), g71.m7254(m5066) + g71.m7254(j)), 0.0f, null);
            return lx5.f14876;
        }
    }

    @zu2
    /* renamed from: Ϳ */
    public static ze2 m13(InterfaceC0158 interfaceC0158, @zu2 bf2 bf2Var, @zu2 ue2 ue2Var, long j) {
        fa1.m6826(bf2Var, "$this$measure");
        long mo14 = interfaceC0158.mo14(bf2Var, ue2Var, j);
        interfaceC0158.mo15();
        bc3 mo6258 = ue2Var.mo6258(C6492.m15459(j, mo14));
        return bf2Var.mo5129(mo6258.f7055, mo6258.f7056, C5849yq.f26859, new C8534(mo6258));
    }
}
