package okhttp3.internal.p042io;

import android.view.Choreographer;
import okhttp3.internal.p042io.InterfaceC6710;
import okhttp3.internal.p042io.sl2;

/* renamed from: okhttp3.internal.io.w4 */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes3.dex */
public final class C5556w4 implements sl2 {

    /* renamed from: ၥ */
    @zu2
    public static final C5556w4 f24620 = new C5556w4();

    /* renamed from: ၦ */
    public static final Choreographer f24621;

    @InterfaceC4988s2(m11868c = "androidx.compose.runtime.DefaultChoreographerFrameClock$choreographer$1", m11869f = "ActualAndroid.android.kt", m11870l = {}, m11871m = "invokeSuspend")
    /* renamed from: okhttp3.internal.io.w4$Ϳ, reason: contains not printable characters */
    public static final class C9457 extends u75 implements di0<InterfaceC7881, InterfaceC7155<? super Choreographer>, Object> {
        public C9457(InterfaceC7155<? super C9457> interfaceC7155) {
            super(2, interfaceC7155);
        }

        @Override // okhttp3.internal.p042io.AbstractC7853
        @zu2
        public final InterfaceC7155<lx5> create(@wv2 Object obj, @zu2 InterfaceC7155<?> interfaceC7155) {
            return new C9457(interfaceC7155);
        }

        @Override // okhttp3.internal.p042io.di0
        /* renamed from: invoke */
        public final Object mo18338invoke(InterfaceC7881 interfaceC7881, InterfaceC7155<? super Choreographer> interfaceC7155) {
            return new C9457(interfaceC7155).invokeSuspend(lx5.f14876);
        }

        @Override // okhttp3.internal.p042io.AbstractC7853
        @wv2
        public final Object invokeSuspend(@zu2 Object obj) {
            C4350o9.m10270(obj);
            return Choreographer.getInstance();
        }
    }

    /* renamed from: okhttp3.internal.io.w4$Ԩ, reason: contains not printable characters */
    public static final class C9458 extends lv1 implements ph0<Throwable, lx5> {

        /* renamed from: ၥ */
        public final /* synthetic */ Choreographer.FrameCallback f24622;

        /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
        public C9458(Choreographer.FrameCallback frameCallback) {
            super(1);
            this.f24622 = frameCallback;
        }

        @Override // okhttp3.internal.p042io.ph0
        public final lx5 invoke(Throwable th) {
            C5556w4.f24621.removeFrameCallback(this.f24622);
            return lx5.f14876;
        }
    }

    /* renamed from: okhttp3.internal.io.w4$Ԫ, reason: contains not printable characters */
    public static final class ChoreographerFrameCallbackC9459 implements Choreographer.FrameCallback {

        /* renamed from: ၥ */
        public final /* synthetic */ InterfaceC7300<R> f24623;

        /* renamed from: ၦ */
        public final /* synthetic */ ph0<Long, R> f24624;

        /* JADX WARN: Multi-variable type inference failed */
        public ChoreographerFrameCallbackC9459(InterfaceC7300<? super R> interfaceC7300, ph0<? super Long, ? extends R> ph0Var) {
            this.f24623 = interfaceC7300;
            this.f24624 = ph0Var;
        }

        @Override // android.view.Choreographer.FrameCallback
        public final void doFrame(long j) {
            Object m10267;
            InterfaceC7155 interfaceC7155 = this.f24623;
            C5556w4 c5556w4 = C5556w4.f24620;
            try {
                m10267 = this.f24624.invoke(Long.valueOf(j));
            } catch (Throwable th) {
                m10267 = C4350o9.m10267(th);
            }
            interfaceC7155.resumeWith(m10267);
        }
    }

    static {
        C3580hh c3580hh = C3580hh.f11573;
        f24621 = (Choreographer) C6814.m16046(ec2.f9151.mo5987(), new C9457(null));
    }

    @Override // okhttp3.internal.p042io.InterfaceC6710
    public final <R> R fold(R r, @zu2 di0<? super R, ? super InterfaceC6710.InterfaceC9868, ? extends R> di0Var) {
        fa1.m6826(di0Var, "operation");
        return di0Var.mo18338invoke(r, this);
    }

    @Override // okhttp3.internal.p042io.InterfaceC6710.InterfaceC9868, okhttp3.internal.p042io.InterfaceC6710
    @wv2
    public final <E extends InterfaceC6710.InterfaceC9868> E get(@zu2 InterfaceC6710.InterfaceC9870<E> interfaceC9870) {
        fa1.m6826(interfaceC9870, "key");
        return (E) InterfaceC6710.InterfaceC9868.C9869.m15851(this, interfaceC9870);
    }

    @Override // okhttp3.internal.p042io.InterfaceC6710.InterfaceC9868
    public final InterfaceC6710.InterfaceC9870 getKey() {
        return sl2.C5069.f21265;
    }

    @Override // okhttp3.internal.p042io.InterfaceC6710
    @zu2
    public final InterfaceC6710 minusKey(@zu2 InterfaceC6710.InterfaceC9870<?> interfaceC9870) {
        fa1.m6826(interfaceC9870, "key");
        return InterfaceC6710.InterfaceC9868.C9869.m15852(this, interfaceC9870);
    }

    @Override // okhttp3.internal.p042io.InterfaceC6710
    @zu2
    public final InterfaceC6710 plus(@zu2 InterfaceC6710 interfaceC6710) {
        fa1.m6826(interfaceC6710, "context");
        return InterfaceC6710.InterfaceC9868.C9869.m15853(this, interfaceC6710);
    }

    @Override // okhttp3.internal.p042io.sl2
    @wv2
    /* renamed from: ԯ */
    public final <R> Object mo9628(@zu2 ph0<? super Long, ? extends R> ph0Var, @zu2 InterfaceC7155<? super R> interfaceC7155) {
        C6570 c6570 = new C6570(C6698.m15836(interfaceC7155), 1);
        c6570.m15594();
        ChoreographerFrameCallbackC9459 choreographerFrameCallbackC9459 = new ChoreographerFrameCallbackC9459(c6570, ph0Var);
        f24621.postFrameCallback(choreographerFrameCallbackC9459);
        c6570.mo15598(new C9458(choreographerFrameCallbackC9459));
        return c6570.m15593();
    }
}
