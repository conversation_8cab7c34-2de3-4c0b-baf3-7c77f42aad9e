package android.view.compose;

import kotlin.Metadata;
import okhttp3.internal.p042io.lv1;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.nh0;

@Metadata(m4116k = 3, m4117mv = {1, 6, 0}, m4119xi = 48)
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class BackHandlerKt$BackHandler$1$1 extends lv1 implements nh0<lx5> {
    public final /* synthetic */ BackHandlerKt$BackHandler$backCallback$1$1 $backCallback;
    public final /* synthetic */ boolean $enabled;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public BackHandlerKt$BackHandler$1$1(BackHandlerKt$BackHandler$backCallback$1$1 backHandlerKt$BackHandler$backCallback$1$1, boolean z) {
        super(0);
        this.$backCallback = backHandlerKt$BackHandler$backCallback$1$1;
        this.$enabled = z;
    }

    @Override // okhttp3.internal.p042io.nh0
    public /* bridge */ /* synthetic */ lx5 invoke() {
        invoke2();
        return lx5.f14876;
    }

    /* renamed from: invoke, reason: avoid collision after fix types in other method */
    public final void invoke2() {
        setEnabled(this.$enabled);
    }
}
