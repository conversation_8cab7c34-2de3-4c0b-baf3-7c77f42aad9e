package androidx.compose.p000ui.platform;

import android.graphics.Matrix;
import android.view.View;
import androidx.annotation.DoNotInline;
import androidx.annotation.RequiresApi;
import okhttp3.internal.p042io.C7464;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.zu2;

@RequiresApi(29)
/* renamed from: androidx.compose.ui.platform.Ԫ */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class C0187 implements InterfaceC0185 {

    /* renamed from: Ϳ */
    @zu2
    public final Matrix f184 = new Matrix();

    /* renamed from: Ԩ */
    @zu2
    public final int[] f185 = new int[2];

    @Override // androidx.compose.p000ui.platform.InterfaceC0185
    @DoNotInline
    /* renamed from: Ϳ */
    public void mo56(@zu2 View view, @zu2 float[] fArr) {
        fa1.m6826(view, "view");
        fa1.m6826(fArr, "matrix");
        this.f184.reset();
        view.transformMatrixToGlobal(this.f184);
        while (true) {
            Object parent = view.getParent();
            if (!(parent instanceof View)) {
                view.getLocationOnScreen(this.f185);
                int[] iArr = this.f185;
                int i = iArr[0];
                int i2 = iArr[1];
                view.getLocationInWindow(iArr);
                int[] iArr2 = this.f185;
                this.f184.postTranslate(iArr2[0] - i, iArr2[1] - i2);
                C7464.m17057(fArr, this.f184);
                return;
            }
            view = (View) parent;
        }
    }
}
