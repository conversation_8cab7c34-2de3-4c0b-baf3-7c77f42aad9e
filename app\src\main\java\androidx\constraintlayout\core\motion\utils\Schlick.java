package androidx.constraintlayout.core.motion.utils;

/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public class Schlick extends Easing {
    private static final boolean DEBUG = false;
    public double eps;

    /* renamed from: mS */
    public double f305mS;

    /* renamed from: mT */
    public double f306mT;

    public Schlick(String str) {
        this.str = str;
        int indexOf = str.indexOf(40);
        int indexOf2 = str.indexOf(44, indexOf);
        this.f305mS = Double.parseDouble(str.substring(indexOf + 1, indexOf2).trim());
        int i = indexOf2 + 1;
        this.f306mT = Double.parseDouble(str.substring(i, str.indexOf(44, i)).trim());
    }

    private double dfunc(double d) {
        double d2 = this.f306mT;
        if (d < d2) {
            double d3 = this.f305mS;
            return ((d3 * d2) * d2) / ((((d2 - d) * d3) + d) * (((d2 - d) * d3) + d));
        }
        double d4 = this.f305mS;
        return ((d2 - 1.0d) * ((d2 - 1.0d) * d4)) / (((((d2 - d) * (-d4)) - d) + 1.0d) * ((((d2 - d) * (-d4)) - d) + 1.0d));
    }

    private double func(double d) {
        double d2 = this.f306mT;
        if (d < d2) {
            return (d2 * d) / (((d2 - d) * this.f305mS) + d);
        }
        return ((d - 1.0d) * (1.0d - d2)) / ((1.0d - d) - ((d2 - d) * this.f305mS));
    }

    @Override // androidx.constraintlayout.core.motion.utils.Easing
    public double get(double d) {
        return func(d);
    }

    @Override // androidx.constraintlayout.core.motion.utils.Easing
    public double getDiff(double d) {
        return dfunc(d);
    }
}
