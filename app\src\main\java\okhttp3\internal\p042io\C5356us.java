package okhttp3.internal.p042io;

import okhttp3.internal.p042io.vn5;

/* renamed from: okhttp3.internal.io.us */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes3.dex */
public final class C5356us extends lv1 implements fi0<vn5.InterfaceC5494<EnumC4542ps>, InterfaceC6968, Integer, u50<Float>> {

    /* renamed from: ၥ */
    public final /* synthetic */ AbstractC3067ct f23458;

    /* renamed from: ၦ */
    public final /* synthetic */ AbstractC3493gx f23459;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public C5356us(AbstractC3067ct abstractC3067ct, AbstractC3493gx abstractC3493gx) {
        super(3);
        this.f23458 = abstractC3067ct;
        this.f23459 = abstractC3493gx;
    }

    /* JADX WARN: Code restructure failed: missing block: B:16:0x0044, code lost:
    
        if (r1 == null) goto L17;
     */
    /* JADX WARN: Code restructure failed: missing block: B:6:0x002c, code lost:
    
        if (r1 == null) goto L17;
     */
    @Override // okhttp3.internal.p042io.fi0
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final okhttp3.internal.p042io.u50<java.lang.Float> invoke(okhttp3.internal.p042io.vn5.InterfaceC5494<okhttp3.internal.p042io.EnumC4542ps> r3, okhttp3.internal.p042io.InterfaceC6968 r4, java.lang.Integer r5) {
        /*
            r2 = this;
            okhttp3.internal.io.vn5$Ԩ r3 = (okhttp3.internal.p042io.vn5.InterfaceC5494) r3
            okhttp3.internal.io.ࡊ r4 = (okhttp3.internal.p042io.InterfaceC6968) r4
            java.lang.Number r5 = (java.lang.Number) r5
            r5.intValue()
            java.lang.String r5 = "$this$animateFloat"
            okhttp3.internal.p042io.fa1.m6826(r3, r5)
            r5 = -9520302(0xffffffffff6ebb52, float:-3.1732889E38)
            r4.mo16268(r5)
            okhttp3.internal.io.ps r5 = okhttp3.internal.p042io.EnumC4542ps.PreEnter
            okhttp3.internal.io.ps r0 = okhttp3.internal.p042io.EnumC4542ps.Visible
            boolean r5 = r3.mo13292(r5, r0)
            r1 = 0
            if (r5 == 0) goto L2f
            okhttp3.internal.io.ct r3 = r2.f23458
            okhttp3.internal.io.xn5 r3 = r3.mo5806()
            okhttp3.internal.io.c10 r3 = r3.f26097
            if (r3 != 0) goto L2a
            goto L2c
        L2a:
            okhttp3.internal.io.u50<java.lang.Float> r1 = r3.f7550
        L2c:
            if (r1 != 0) goto L48
            goto L46
        L2f:
            okhttp3.internal.io.ps r5 = okhttp3.internal.p042io.EnumC4542ps.PostExit
            boolean r3 = r3.mo13292(r0, r5)
            if (r3 == 0) goto L46
            okhttp3.internal.io.gx r3 = r2.f23459
            okhttp3.internal.io.xn5 r3 = r3.mo7492()
            okhttp3.internal.io.c10 r3 = r3.f26097
            if (r3 != 0) goto L42
            goto L44
        L42:
            okhttp3.internal.io.u50<java.lang.Float> r1 = r3.f7550
        L44:
            if (r1 != 0) goto L48
        L46:
            okhttp3.internal.io.ay4<java.lang.Float> r1 = okhttp3.internal.p042io.C4838qs.f19486
        L48:
            r4.mo16300()
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: okhttp3.internal.p042io.C5356us.invoke(java.lang.Object, java.lang.Object, java.lang.Object):java.lang.Object");
    }
}
