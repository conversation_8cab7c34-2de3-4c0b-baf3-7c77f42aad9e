package androidx.core.widget;

import kotlin.Metadata;
import okhttp3.internal.p042io.hi0;
import okhttp3.internal.p042io.lv1;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.wv2;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000\u0016\n\u0002\u0010\r\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\u0010\t\u001a\u00020\u00062\b\u0010\u0001\u001a\u0004\u0018\u00010\u00002\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0004\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u0002H\n¢\u0006\u0004\b\u0007\u0010\b"}, m4115d2 = {"", "<anonymous parameter 0>", "", "<anonymous parameter 1>", "<anonymous parameter 2>", "<anonymous parameter 3>", "Lokhttp3/internal/io/lx5;", "invoke", "(Ljava/lang/CharSequence;III)V", "<anonymous>"}, m4116k = 3, m4117mv = {1, 6, 0})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class TextViewKt$addTextChangedListener$2 extends lv1 implements hi0<CharSequence, Integer, Integer, Integer, lx5> {
    public static final TextViewKt$addTextChangedListener$2 INSTANCE = new TextViewKt$addTextChangedListener$2();

    public TextViewKt$addTextChangedListener$2() {
        super(4);
    }

    @Override // okhttp3.internal.p042io.hi0
    public /* bridge */ /* synthetic */ lx5 invoke(CharSequence charSequence, Integer num, Integer num2, Integer num3) {
        invoke(charSequence, num.intValue(), num2.intValue(), num3.intValue());
        return lx5.f14876;
    }

    public final void invoke(@wv2 CharSequence charSequence, int i, int i2, int i3) {
    }
}
