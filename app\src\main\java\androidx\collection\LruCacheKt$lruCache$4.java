package androidx.collection;

import kotlin.Metadata;
import okhttp3.internal.p042io.di0;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.hi0;
import okhttp3.internal.p042io.ph0;
import okhttp3.internal.p042io.wv2;
import okhttp3.internal.p042io.zu2;

/* JADX INFO: Add missing generic type declarations: [V, K] */
@Metadata(m4113bv = {}, m4114d1 = {"\u0000%\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003*\u0001\u0000\b\n\u0018\u00002\u000e\u0012\u0004\u0012\u00028\u0000\u0012\u0004\u0012\u00028\u00010\u0001J\u001f\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0002\u001a\u00028\u00002\u0006\u0010\u0003\u001a\u00028\u0001H\u0014¢\u0006\u0004\b\u0005\u0010\u0006J\u0019\u0010\u0007\u001a\u0004\u0018\u00018\u00012\u0006\u0010\u0002\u001a\u00028\u0000H\u0014¢\u0006\u0004\b\u0007\u0010\bJ1\u0010\u000e\u001a\u00020\r2\u0006\u0010\n\u001a\u00020\t2\u0006\u0010\u0002\u001a\u00028\u00002\u0006\u0010\u000b\u001a\u00028\u00012\b\u0010\f\u001a\u0004\u0018\u00018\u0001H\u0014¢\u0006\u0004\b\u000e\u0010\u000f¨\u0006\u0010"}, m4115d2 = {"androidx/collection/LruCacheKt$lruCache$4", "Landroidx/collection/LruCache;", "key", "value", "", "sizeOf", "(Ljava/lang/Object;Ljava/lang/Object;)I", "create", "(Ljava/lang/Object;)Ljava/lang/Object;", "", "evicted", "oldValue", "newValue", "Lokhttp3/internal/io/lx5;", "entryRemoved", "(ZLjava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)V", "collection-ktx"}, m4116k = 1, m4117mv = {1, 4, 0})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class LruCacheKt$lruCache$4<K, V> extends LruCache<K, V> {
    public final /* synthetic */ ph0 $create;
    public final /* synthetic */ int $maxSize;
    public final /* synthetic */ hi0 $onEntryRemoved;
    public final /* synthetic */ di0 $sizeOf;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public LruCacheKt$lruCache$4(di0 di0Var, ph0 ph0Var, hi0 hi0Var, int i, int i2) {
        super(i2);
        this.$sizeOf = di0Var;
        this.$create = ph0Var;
        this.$onEntryRemoved = hi0Var;
        this.$maxSize = i;
    }

    @Override // androidx.collection.LruCache
    @wv2
    public V create(@zu2 K key) {
        fa1.m6827(key, "key");
        return (V) this.$create.invoke(key);
    }

    @Override // androidx.collection.LruCache
    public void entryRemoved(boolean evicted, @zu2 K key, @zu2 V oldValue, @wv2 V newValue) {
        fa1.m6827(key, "key");
        fa1.m6827(oldValue, "oldValue");
        this.$onEntryRemoved.invoke(Boolean.valueOf(evicted), key, oldValue, newValue);
    }

    @Override // androidx.collection.LruCache
    public int sizeOf(@zu2 K key, @zu2 V value) {
        fa1.m6827(key, "key");
        fa1.m6827(value, "value");
        return ((Number) this.$sizeOf.mo18338invoke(key, value)).intValue();
    }
}
