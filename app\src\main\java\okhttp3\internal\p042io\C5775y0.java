package okhttp3.internal.p042io;

import java.lang.reflect.Constructor;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashSet;
import java.util.TimeZone;
import okhttp3.internal.p042io.ck1;

/* renamed from: okhttp3.internal.io.y0 */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes3.dex */
public final class C5775y0 {

    /* renamed from: Ϳ */
    public static final HashSet<String> f26305 = new HashSet<>();

    @xd1
    /* renamed from: okhttp3.internal.io.y0$Ϳ, reason: contains not printable characters */
    public static class C9498 extends AbstractC9499<Calendar> {

        /* renamed from: ၶ */
        public final Constructor<Calendar> f26306;

        public C9498() {
            super(Calendar.class);
            this.f26306 = null;
        }

        public C9498(Class<? extends Calendar> cls) {
            super(cls);
            this.f26306 = C6289.m15063(cls, false);
        }

        public C9498(C9498 c9498, DateFormat dateFormat, String str) {
            super(c9498, dateFormat, str);
            this.f26306 = c9498.f26306;
        }

        @Override // okhttp3.internal.p042io.uj1
        /* renamed from: Ԫ */
        public final Object mo5747(ml1 ml1Var, AbstractC5032s9 abstractC5032s9) {
            Date mo13815 = mo13815(ml1Var, abstractC5032s9);
            if (mo13815 == null) {
                return null;
            }
            Constructor<Calendar> constructor = this.f26306;
            if (constructor == null) {
                Calendar calendar = Calendar.getInstance(abstractC5032s9.m11936());
                calendar.setTime(mo13815);
                return calendar;
            }
            try {
                Calendar newInstance = constructor.newInstance(new Object[0]);
                newInstance.setTimeInMillis(mo13815.getTime());
                TimeZone m11936 = abstractC5032s9.m11936();
                if (m11936 != null) {
                    newInstance.setTimeZone(m11936);
                }
                return newInstance;
            } catch (Exception e) {
                abstractC5032s9.m11938(this.f25300, e);
                throw null;
            }
        }

        @Override // okhttp3.internal.p042io.C5775y0.AbstractC9499
        /* renamed from: ࢠ */
        public final AbstractC9499<Calendar> mo14109(DateFormat dateFormat, String str) {
            return new C9498(this, dateFormat, str);
        }
    }

    /* renamed from: okhttp3.internal.io.y0$Ԩ, reason: contains not printable characters */
    public static abstract class AbstractC9499<T> extends k25<T> implements InterfaceC7497 {

        /* renamed from: ၰ */
        public final DateFormat f26307;

        /* renamed from: ၵ */
        public final String f26308;

        public AbstractC9499(Class<?> cls) {
            super(cls);
            this.f26307 = null;
            this.f26308 = null;
        }

        public AbstractC9499(AbstractC9499<T> abstractC9499, DateFormat dateFormat, String str) {
            super(abstractC9499.f25300);
            this.f26307 = dateFormat;
            this.f26308 = str;
        }

        @Override // okhttp3.internal.p042io.InterfaceC7497
        /* renamed from: Ԩ */
        public final uj1<?> mo7487(AbstractC5032s9 abstractC5032s9, InterfaceC6600 interfaceC6600) {
            DateFormat dateFormat;
            DateFormat dateFormat2;
            ck1.C3050 m13832 = m13832(abstractC5032s9, interfaceC6600, this.f25300);
            if (m13832 != null) {
                TimeZone m5735 = m13832.m5735();
                Boolean bool = m13832.f8066;
                String str = m13832.f8062;
                if (str != null && str.length() > 0) {
                    String str2 = m13832.f8062;
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat(str2, m13832.m5736() ? m13832.f8064 : abstractC5032s9.f20975.f10762.f30339);
                    if (m5735 == null) {
                        m5735 = abstractC5032s9.m11936();
                    }
                    simpleDateFormat.setTimeZone(m5735);
                    if (bool != null) {
                        simpleDateFormat.setLenient(bool.booleanValue());
                    }
                    return mo14109(simpleDateFormat, str2);
                }
                if (m5735 != null) {
                    DateFormat dateFormat3 = abstractC5032s9.f20975.f10762.f30338;
                    if (dateFormat3.getClass() == u15.class) {
                        u15 m12665 = ((u15) dateFormat3).m12666(m5735).m12665(m13832.m5736() ? m13832.f8064 : abstractC5032s9.f20975.f10762.f30339);
                        dateFormat2 = m12665;
                        if (bool != null) {
                            dateFormat2 = m12665.m12664(bool);
                        }
                    } else {
                        DateFormat dateFormat4 = (DateFormat) dateFormat3.clone();
                        dateFormat4.setTimeZone(m5735);
                        dateFormat2 = dateFormat4;
                        if (bool != null) {
                            dateFormat4.setLenient(bool.booleanValue());
                            dateFormat2 = dateFormat4;
                        }
                    }
                    return mo14109(dateFormat2, this.f26308);
                }
                if (bool != null) {
                    DateFormat dateFormat5 = abstractC5032s9.f20975.f10762.f30338;
                    String str3 = this.f26308;
                    if (dateFormat5.getClass() == u15.class) {
                        u15 m12664 = ((u15) dateFormat5).m12664(bool);
                        StringBuilder sb = new StringBuilder(100);
                        sb.append("[one of: '");
                        sb.append("yyyy-MM-dd'T'HH:mm:ss.SSSZ");
                        sb.append("', '");
                        sb.append("EEE, dd MMM yyyy HH:mm:ss zzz");
                        sb.append("' (");
                        str3 = jf2.m8634(sb, Boolean.FALSE.equals(m12664.f22725) ? "strict" : "lenient", ")]");
                        dateFormat = m12664;
                    } else {
                        DateFormat dateFormat6 = (DateFormat) dateFormat5.clone();
                        dateFormat6.setLenient(bool.booleanValue());
                        boolean z = dateFormat6 instanceof SimpleDateFormat;
                        dateFormat = dateFormat6;
                        if (z) {
                            ((SimpleDateFormat) dateFormat6).toPattern();
                            dateFormat = dateFormat6;
                        }
                    }
                    if (str3 == null) {
                        str3 = "[unknown]";
                    }
                    return mo14109(dateFormat, str3);
                }
            }
            return this;
        }

        @Override // okhttp3.internal.p042io.x15
        /* renamed from: ޗ */
        public final Date mo13815(ml1 ml1Var, AbstractC5032s9 abstractC5032s9) {
            Date parse;
            if (this.f26307 == null || !ml1Var.mo9581(ym1.VALUE_STRING)) {
                return super.mo13815(ml1Var, abstractC5032s9);
            }
            String trim = ml1Var.mo8383().trim();
            if (trim.length() == 0) {
                return null;
            }
            synchronized (this.f26307) {
                try {
                    try {
                        parse = this.f26307.parse(trim);
                    } catch (ParseException unused) {
                        abstractC5032s9.m11948(this.f25300, trim, "expected format \"%s\"", this.f26308);
                        throw null;
                    }
                } catch (Throwable th) {
                    throw th;
                }
            }
            return parse;
        }

        /* renamed from: ࢠ */
        public abstract AbstractC9499<T> mo14109(DateFormat dateFormat, String str);
    }

    @xd1
    /* renamed from: okhttp3.internal.io.y0$Ԫ, reason: contains not printable characters */
    public static class C9500 extends AbstractC9499<Date> {

        /* renamed from: ၶ */
        public static final C9500 f26309 = new C9500();

        public C9500() {
            super(Date.class);
        }

        public C9500(C9500 c9500, DateFormat dateFormat, String str) {
            super(c9500, dateFormat, str);
        }

        @Override // okhttp3.internal.p042io.uj1
        /* renamed from: Ԫ */
        public final Object mo5747(ml1 ml1Var, AbstractC5032s9 abstractC5032s9) {
            return mo13815(ml1Var, abstractC5032s9);
        }

        @Override // okhttp3.internal.p042io.C5775y0.AbstractC9499
        /* renamed from: ࢠ */
        public final AbstractC9499<Date> mo14109(DateFormat dateFormat, String str) {
            return new C9500(this, dateFormat, str);
        }
    }

    /* renamed from: okhttp3.internal.io.y0$Ԭ, reason: contains not printable characters */
    public static class C9501 extends AbstractC9499<java.sql.Date> {
        public C9501() {
            super(java.sql.Date.class);
        }

        public C9501(C9501 c9501, DateFormat dateFormat, String str) {
            super(c9501, dateFormat, str);
        }

        @Override // okhttp3.internal.p042io.uj1
        /* renamed from: Ԫ */
        public final Object mo5747(ml1 ml1Var, AbstractC5032s9 abstractC5032s9) {
            Date mo13815 = mo13815(ml1Var, abstractC5032s9);
            if (mo13815 == null) {
                return null;
            }
            return new java.sql.Date(mo13815.getTime());
        }

        @Override // okhttp3.internal.p042io.C5775y0.AbstractC9499
        /* renamed from: ࢠ */
        public final AbstractC9499<java.sql.Date> mo14109(DateFormat dateFormat, String str) {
            return new C9501(this, dateFormat, str);
        }
    }

    /* renamed from: okhttp3.internal.io.y0$Ԯ, reason: contains not printable characters */
    public static class C9502 extends AbstractC9499<Timestamp> {
        public C9502() {
            super(Timestamp.class);
        }

        public C9502(C9502 c9502, DateFormat dateFormat, String str) {
            super(c9502, dateFormat, str);
        }

        @Override // okhttp3.internal.p042io.uj1
        /* renamed from: Ԫ */
        public final Object mo5747(ml1 ml1Var, AbstractC5032s9 abstractC5032s9) {
            Date mo13815 = mo13815(ml1Var, abstractC5032s9);
            if (mo13815 == null) {
                return null;
            }
            return new Timestamp(mo13815.getTime());
        }

        @Override // okhttp3.internal.p042io.C5775y0.AbstractC9499
        /* renamed from: ࢠ */
        public final AbstractC9499<Timestamp> mo14109(DateFormat dateFormat, String str) {
            return new C9502(this, dateFormat, str);
        }
    }

    static {
        Class[] clsArr = {Calendar.class, GregorianCalendar.class, java.sql.Date.class, Date.class, Timestamp.class};
        for (int i = 0; i < 5; i++) {
            f26305.add(clsArr[i].getName());
        }
    }
}
