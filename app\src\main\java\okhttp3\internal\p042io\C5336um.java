package okhttp3.internal.p042io;

import java.math.BigInteger;
import java.security.SecureRandom;

/* renamed from: okhttp3.internal.io.um */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5336um implements InterfaceC7538 {

    /* renamed from: ԭ */
    public C5838ym f23272;

    /* renamed from: Ԯ */
    public SecureRandom f23273;

    @Override // okhttp3.internal.p042io.InterfaceC7538
    /* renamed from: Ϳ */
    public final void mo5761(boolean z, InterfaceC6218 interfaceC6218) {
        C5838ym c5838ym;
        if (!z) {
            c5838ym = (C5218tn) interfaceC6218;
        } else {
            if (interfaceC6218 instanceof v73) {
                v73 v73Var = (v73) interfaceC6218;
                this.f23273 = v73Var.f23832;
                this.f23272 = (C4833qn) v73Var.f23833;
                return;
            }
            this.f23273 = new SecureRandom();
            c5838ym = (C4833qn) interfaceC6218;
        }
        this.f23272 = c5838ym;
    }

    @Override // okhttp3.internal.p042io.InterfaceC7538
    /* renamed from: Ԩ */
    public final BigInteger[] mo5762(byte[] bArr) {
        int length = bArr.length;
        byte[] bArr2 = new byte[length];
        for (int i = 0; i != length; i++) {
            bArr2[i] = bArr[(length - 1) - i];
        }
        BigInteger bigInteger = new BigInteger(1, bArr2);
        C5838ym c5838ym = this.f23272;
        C4526pm c4526pm = c5838ym.f26780;
        BigInteger bigInteger2 = c4526pm.f17744;
        BigInteger bigInteger3 = ((C4833qn) c5838ym).f19440;
        e60 e60Var = new e60();
        while (true) {
            BigInteger bigInteger4 = new BigInteger(bigInteger2.bitLength(), this.f23273);
            BigInteger bigInteger5 = InterfaceC3845jm.f13213;
            if (!bigInteger4.equals(bigInteger5)) {
                BigInteger mod = e60Var.mo5247(c4526pm.f17743, bigInteger4).m9326().m9316().mo4623().mod(bigInteger2);
                if (mod.equals(bigInteger5)) {
                    continue;
                } else {
                    BigInteger mod2 = bigInteger4.multiply(bigInteger).add(bigInteger3.multiply(mod)).mod(bigInteger2);
                    if (!mod2.equals(bigInteger5)) {
                        return new BigInteger[]{mod, mod2};
                    }
                }
            }
        }
    }

    @Override // okhttp3.internal.p042io.InterfaceC7538
    /* renamed from: ԩ */
    public final boolean mo5763(byte[] bArr, BigInteger bigInteger, BigInteger bigInteger2) {
        int length = bArr.length;
        byte[] bArr2 = new byte[length];
        for (int i = 0; i != length; i++) {
            bArr2[i] = bArr[(length - 1) - i];
        }
        BigInteger bigInteger3 = new BigInteger(1, bArr2);
        BigInteger bigInteger4 = this.f23272.f26780.f17744;
        BigInteger bigInteger5 = InterfaceC3845jm.f13214;
        if (bigInteger.compareTo(bigInteger5) < 0 || bigInteger.compareTo(bigInteger4) >= 0 || bigInteger2.compareTo(bigInteger5) < 0 || bigInteger2.compareTo(bigInteger4) >= 0) {
            return false;
        }
        BigInteger modInverse = bigInteger3.modInverse(bigInteger4);
        BigInteger mod = bigInteger2.multiply(modInverse).mod(bigInteger4);
        BigInteger mod2 = bigInteger4.subtract(bigInteger).multiply(modInverse).mod(bigInteger4);
        C5838ym c5838ym = this.f23272;
        AbstractC4094ln m9326 = C3750im.m8327(c5838ym.f26780.f17743, mod, ((C5218tn) c5838ym).f22381, mod2).m9326();
        if (m9326.m9322()) {
            return false;
        }
        return m9326.m9316().mo4623().mod(bigInteger4).equals(bigInteger);
    }
}
