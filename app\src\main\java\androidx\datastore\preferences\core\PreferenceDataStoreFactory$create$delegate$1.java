package androidx.datastore.preferences.core;

import androidx.autofill.HintConstants;
import java.io.File;
import kotlin.Metadata;
import okhttp3.internal.p042io.c55;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.lv1;
import okhttp3.internal.p042io.nh0;
import okhttp3.internal.p042io.zu2;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000\u0006\n\u0002\u0018\u0002\n\u0000\u0010\u0001\u001a\u00020\u0000H\n"}, m4115d2 = {"Ljava/io/File;", "<anonymous>"}, m4116k = 3, m4117mv = {1, 5, 1})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class PreferenceDataStoreFactory$create$delegate$1 extends lv1 implements nh0<File> {
    public final /* synthetic */ nh0<File> $produceFile;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    /* JADX WARN: Multi-variable type inference failed */
    public PreferenceDataStoreFactory$create$delegate$1(nh0<? extends File> nh0Var) {
        super(0);
        this.$produceFile = nh0Var;
    }

    /* JADX WARN: Can't rename method to resolve collision */
    @Override // okhttp3.internal.p042io.nh0
    @zu2
    public final File invoke() {
        File invoke = this.$produceFile.invoke();
        fa1.m6826(invoke, "<this>");
        String name = invoke.getName();
        fa1.m6825(name, HintConstants.AUTOFILL_HINT_NAME);
        String m5560 = c55.m5560(name, '.', "");
        PreferencesSerializer preferencesSerializer = PreferencesSerializer.INSTANCE;
        if (fa1.m6818(m5560, preferencesSerializer.getFileExtension())) {
            return invoke;
        }
        throw new IllegalStateException(("File extension for file: " + invoke + " does not match required extension for Preferences file: " + preferencesSerializer.getFileExtension()).toString());
    }
}
