package androidx.core.util;

import androidx.annotation.RequiresApi;
import androidx.exifinterface.media.ExifInterface;
import kotlin.Metadata;
import okhttp3.internal.p042io.InterfaceC7155;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.zu2;

@RequiresApi(24)
@Metadata(m4113bv = {}, m4114d1 = {"\u0000\n\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\u001a\u001e\u0010\u0003\u001a\b\u0012\u0004\u0012\u00028\u00000\u0002\"\u0004\b\u0000\u0010\u0000*\b\u0012\u0004\u0012\u00028\u00000\u0001H\u0007¨\u0006\u0004"}, m4115d2 = {ExifInterface.GPS_DIRECTION_TRUE, "Lokhttp3/internal/io/ৡ;", "j$/util/function/Consumer", "asConsumer", "core-ktx_release"}, m4116k = 2, m4117mv = {1, 6, 0})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class ConsumerKt {
    @RequiresApi(24)
    @zu2
    public static final <T> p041j$.util.function.Consumer<T> asConsumer(@zu2 InterfaceC7155<? super T> interfaceC7155) {
        fa1.m6826(interfaceC7155, "<this>");
        return new ContinuationConsumer(interfaceC7155);
    }
}
