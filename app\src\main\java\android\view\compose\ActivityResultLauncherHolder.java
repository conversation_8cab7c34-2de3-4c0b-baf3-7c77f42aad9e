package android.view.compose;

import android.view.result.ActivityResultLauncher;
import androidx.core.app.ActivityOptionsCompat;
import com.stardust.autojs.engine.RhinoJavaScriptEngine;
import kotlin.Metadata;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.wv2;
import org.mozilla.javascript.optimizer.Codegen;
import org.mozilla.javascript.optimizer.OptRuntime;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\t\b\u0000\u0018\u0000*\u0004\b\u0000\u0010\u00012\u00020\u0002B\u0007¢\u0006\u0004\b\u0011\u0010\u0012J!\u0010\u0007\u001a\u00020\u00062\b\u0010\u0003\u001a\u0004\u0018\u00018\u00002\b\u0010\u0005\u001a\u0004\u0018\u00010\u0004¢\u0006\u0004\b\u0007\u0010\bJ\u0006\u0010\t\u001a\u00020\u0006R*\u0010\u000b\u001a\n\u0012\u0004\u0012\u00028\u0000\u0018\u00010\n8\u0006@\u0006X\u0086\u000e¢\u0006\u0012\n\u0004\b\u000b\u0010\f\u001a\u0004\b\r\u0010\u000e\"\u0004\b\u000f\u0010\u0010¨\u0006\u0013"}, m4115d2 = {"Landroidx/activity/compose/ActivityResultLauncherHolder;", OptRuntime.GeneratorState.resumptionPoint_TYPE, "", "input", "Landroidx/core/app/ActivityOptionsCompat;", "options", "Lokhttp3/internal/io/lx5;", "launch", "(Ljava/lang/Object;Landroidx/core/app/ActivityOptionsCompat;)V", "unregister", "Landroidx/activity/result/ActivityResultLauncher;", "launcher", "Landroidx/activity/result/ActivityResultLauncher;", "getLauncher", "()Landroidx/activity/result/ActivityResultLauncher;", "setLauncher", "(Landroidx/activity/result/ActivityResultLauncher;)V", RhinoJavaScriptEngine.SOURCE_NAME_INIT, Codegen.TEMPLATE_LITERAL_INIT_METHOD_SIGNATURE, "activity-compose_release"}, m4116k = 1, m4117mv = {1, 6, 0})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class ActivityResultLauncherHolder<I> {

    @wv2
    private ActivityResultLauncher<I> launcher;

    @wv2
    public final ActivityResultLauncher<I> getLauncher() {
        return this.launcher;
    }

    public final void launch(@wv2 I input, @wv2 ActivityOptionsCompat options) {
        lx5 lx5Var;
        ActivityResultLauncher<I> activityResultLauncher = this.launcher;
        if (activityResultLauncher != null) {
            activityResultLauncher.launch(input, options);
            lx5Var = lx5.f14876;
        } else {
            lx5Var = null;
        }
        if (lx5Var == null) {
            throw new IllegalStateException("Launcher has not been initialized".toString());
        }
    }

    public final void setLauncher(@wv2 ActivityResultLauncher<I> activityResultLauncher) {
        this.launcher = activityResultLauncher;
    }

    public final void unregister() {
        lx5 lx5Var;
        ActivityResultLauncher<I> activityResultLauncher = this.launcher;
        if (activityResultLauncher != null) {
            activityResultLauncher.unregister();
            lx5Var = lx5.f14876;
        } else {
            lx5Var = null;
        }
        if (lx5Var == null) {
            throw new IllegalStateException("Launcher has not been initialized".toString());
        }
    }
}
