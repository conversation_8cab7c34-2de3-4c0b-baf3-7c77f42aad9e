package okhttp3.internal.p042io;

import androidx.constraintlayout.core.motion.utils.TypedValues;
import com.stardust.autojs.runtime.api.AbstractShell;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import okhttp3.internal.p042io.yk4;

/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public class c55 extends z45 {
    /* renamed from: ޏ */
    public static final boolean m5537(@zu2 CharSequence charSequence, @zu2 CharSequence charSequence2, boolean z) {
        fa1.m6826(charSequence, "<this>");
        fa1.m6826(charSequence2, "other");
        if (charSequence2 instanceof String) {
            if (m5545(charSequence, (String) charSequence2, 0, z, 2) < 0) {
                return false;
            }
        } else if (m5543(charSequence, charSequence2, 0, charSequence.length(), z, false) < 0) {
            return false;
        }
        return true;
    }

    /* renamed from: ސ */
    public static boolean m5538(CharSequence charSequence, char c) {
        fa1.m6826(charSequence, "<this>");
        return m5544(charSequence, c, 0, false, 2) >= 0;
    }

    /* renamed from: ޑ */
    public static boolean m5539(CharSequence charSequence, char c) {
        fa1.m6826(charSequence, "<this>");
        return charSequence.length() > 0 && C7451.m17035(charSequence.charAt(m5541(charSequence)), c, false);
    }

    @zu2
    /* renamed from: ޒ */
    public static final j71 m5540(@zu2 CharSequence charSequence) {
        fa1.m6826(charSequence, "<this>");
        return new j71(0, charSequence.length() - 1);
    }

    /* renamed from: ޓ */
    public static final int m5541(@zu2 CharSequence charSequence) {
        fa1.m6826(charSequence, "<this>");
        return charSequence.length() - 1;
    }

    /* renamed from: ޔ */
    public static final int m5542(@zu2 CharSequence charSequence, @zu2 String str, int i, boolean z) {
        fa1.m6826(charSequence, "<this>");
        fa1.m6826(str, TypedValues.Custom.S_STRING);
        return (z || !(charSequence instanceof String)) ? m5543(charSequence, str, i, charSequence.length(), z, false) : ((String) charSequence).indexOf(str, i);
    }

    /* renamed from: ޕ */
    public static final int m5543(CharSequence charSequence, CharSequence charSequence2, int i, int i2, boolean z, boolean z2) {
        h71 h71Var;
        if (z2) {
            int m5541 = m5541(charSequence);
            if (i > m5541) {
                i = m5541;
            }
            if (i2 < 0) {
                i2 = 0;
            }
            h71Var = new h71(i, i2, -1);
        } else {
            if (i < 0) {
                i = 0;
            }
            int length = charSequence.length();
            if (i2 > length) {
                i2 = length;
            }
            h71Var = new j71(i, i2);
        }
        if ((charSequence instanceof String) && (charSequence2 instanceof String)) {
            int i3 = h71Var.f11321;
            int i4 = h71Var.f11322;
            int i5 = h71Var.f11323;
            if ((i5 > 0 && i3 <= i4) || (i5 < 0 && i4 <= i3)) {
                while (!z45.m14449((String) charSequence2, 0, (String) charSequence, i3, charSequence2.length(), z)) {
                    if (i3 != i4) {
                        i3 += i5;
                    }
                }
                return i3;
            }
        } else {
            int i6 = h71Var.f11321;
            int i7 = h71Var.f11322;
            int i8 = h71Var.f11323;
            if ((i8 > 0 && i6 <= i7) || (i8 < 0 && i7 <= i6)) {
                while (!m5551(charSequence2, 0, charSequence, i6, charSequence2.length(), z)) {
                    if (i6 != i7) {
                        i6 += i8;
                    }
                }
                return i6;
            }
        }
        return -1;
    }

    /* renamed from: ޖ */
    public static int m5544(CharSequence charSequence, char c, int i, boolean z, int i2) {
        if ((i2 & 2) != 0) {
            i = 0;
        }
        if ((i2 & 4) != 0) {
            z = false;
        }
        fa1.m6826(charSequence, "<this>");
        return (z || !(charSequence instanceof String)) ? m5546(charSequence, new char[]{c}, i, z) : ((String) charSequence).indexOf(c, i);
    }

    /* renamed from: ޗ */
    public static /* synthetic */ int m5545(CharSequence charSequence, String str, int i, boolean z, int i2) {
        if ((i2 & 2) != 0) {
            i = 0;
        }
        if ((i2 & 4) != 0) {
            z = false;
        }
        return m5542(charSequence, str, i, z);
    }

    /* renamed from: ޘ */
    public static final int m5546(@zu2 CharSequence charSequence, @zu2 char[] cArr, int i, boolean z) {
        boolean z2;
        fa1.m6826(charSequence, "<this>");
        fa1.m6826(cArr, "chars");
        if (!z && cArr.length == 1 && (charSequence instanceof String)) {
            return ((String) charSequence).indexOf(C6063.m14790(cArr), i);
        }
        if (i < 0) {
            i = 0;
        }
        c71 it = new j71(i, m5541(charSequence)).iterator();
        while (((i71) it).f12157) {
            int nextInt = it.nextInt();
            char charAt = charSequence.charAt(nextInt);
            int length = cArr.length;
            int i2 = 0;
            while (true) {
                if (i2 >= length) {
                    z2 = false;
                    break;
                }
                if (C7451.m17035(cArr[i2], charAt, z)) {
                    z2 = true;
                    break;
                }
                i2++;
            }
            if (z2) {
                return nextInt;
            }
        }
        return -1;
    }

    /* renamed from: ޙ */
    public static int m5547(CharSequence charSequence, char c) {
        boolean z;
        int m5541 = m5541(charSequence);
        fa1.m6826(charSequence, "<this>");
        if (charSequence instanceof String) {
            return ((String) charSequence).lastIndexOf(c, m5541);
        }
        char[] cArr = {c};
        if (charSequence instanceof String) {
            return ((String) charSequence).lastIndexOf(C6063.m14790(cArr), m5541);
        }
        int m55412 = m5541(charSequence);
        if (m5541 > m55412) {
            m5541 = m55412;
        }
        while (-1 < m5541) {
            char charAt = charSequence.charAt(m5541);
            int i = 0;
            while (true) {
                if (i >= 1) {
                    z = false;
                    break;
                }
                if (C7451.m17035(cArr[i], charAt, false)) {
                    z = true;
                    break;
                }
                i++;
            }
            if (z) {
                return m5541;
            }
            m5541--;
        }
        return -1;
    }

    /* renamed from: ޚ */
    public static int m5548(CharSequence charSequence, String str, int i, int i2) {
        if ((i2 & 2) != 0) {
            i = m5541(charSequence);
        }
        int i3 = i;
        fa1.m6826(charSequence, "<this>");
        fa1.m6826(str, TypedValues.Custom.S_STRING);
        return !(charSequence instanceof String) ? m5543(charSequence, str, i3, 0, false, true) : ((String) charSequence).lastIndexOf(str, i3);
    }

    @zu2
    /* renamed from: ޛ */
    public static final List<String> m5549(@zu2 CharSequence charSequence) {
        fa1.m6826(charSequence, "<this>");
        return yk4.m14315(yk4.m14310(m5550(charSequence, new String[]{"\r\n", AbstractShell.COMMAND_LINE_END, "\r"}, false, 0), new b55(charSequence)));
    }

    /* renamed from: ޜ */
    public static pk4 m5550(CharSequence charSequence, String[] strArr, boolean z, int i) {
        m5554(i);
        return new C4893r7(charSequence, 0, i, new a55(C7251.m16819(strArr), z));
    }

    /* renamed from: ޝ */
    public static final boolean m5551(@zu2 CharSequence charSequence, int i, @zu2 CharSequence charSequence2, int i2, int i3, boolean z) {
        fa1.m6826(charSequence, "<this>");
        fa1.m6826(charSequence2, "other");
        if (i2 < 0 || i < 0 || i > charSequence.length() - i3 || i2 > charSequence2.length() - i3) {
            return false;
        }
        for (int i4 = 0; i4 < i3; i4++) {
            if (!C7451.m17035(charSequence.charAt(i + i4), charSequence2.charAt(i2 + i4), z)) {
                return false;
            }
        }
        return true;
    }

    @zu2
    /* renamed from: ޞ */
    public static final String m5552(@zu2 String str, @zu2 CharSequence charSequence) {
        fa1.m6826(str, "<this>");
        if (!(charSequence instanceof String ? z45.m14453(str, (String) charSequence) : m5551(str, 0, charSequence, 0, charSequence.length(), false))) {
            return str;
        }
        String substring = str.substring(charSequence.length());
        fa1.m6825(substring, "this as java.lang.String).substring(startIndex)");
        return substring;
    }

    @zu2
    /* renamed from: ޟ */
    public static final CharSequence m5553(@zu2 CharSequence charSequence) {
        StringBuilder sb;
        int length;
        if (charSequence instanceof String ? z45.m14446((String) charSequence, ", ") : m5551(charSequence, ((StringBuilder) charSequence).length() - ", ".length(), ", ", 0, ", ".length(), false)) {
            sb = (StringBuilder) charSequence;
            length = sb.length() - ", ".length();
        } else {
            sb = (StringBuilder) charSequence;
            length = sb.length();
        }
        return sb.subSequence(0, length);
    }

    /* renamed from: ޠ */
    public static final void m5554(int i) {
        if (!(i >= 0)) {
            throw new IllegalArgumentException(k76.m8852("Limit must be non-negative, but was ", i).toString());
        }
    }

    /* renamed from: ޡ */
    public static final List<String> m5555(CharSequence charSequence, String str, boolean z, int i) {
        m5554(i);
        int i2 = 0;
        int m5542 = m5542(charSequence, str, 0, z);
        if (m5542 == -1 || i == 1) {
            return fa1.m6837(charSequence.toString());
        }
        boolean z2 = i > 0;
        int i3 = 10;
        if (z2 && i <= 10) {
            i3 = i;
        }
        ArrayList arrayList = new ArrayList(i3);
        do {
            arrayList.add(charSequence.subSequence(i2, m5542).toString());
            i2 = str.length() + m5542;
            if (z2 && arrayList.size() == i - 1) {
                break;
            }
            m5542 = m5542(charSequence, str, i2, z);
        } while (m5542 != -1);
        arrayList.add(charSequence.subSequence(i2, charSequence.length()).toString());
        return arrayList;
    }

    /* renamed from: ޢ */
    public static List m5556(CharSequence charSequence, String[] strArr) {
        fa1.m6826(charSequence, "<this>");
        if (strArr.length == 1) {
            String str = strArr[0];
            if (!(str.length() == 0)) {
                return m5555(charSequence, str, false, 0);
            }
        }
        yk4.C5835 c5835 = new yk4.C5835(m5550(charSequence, strArr, false, 0));
        ArrayList arrayList = new ArrayList(C7188.m16620(c5835, 10));
        Iterator it = c5835.iterator();
        while (it.hasNext()) {
            arrayList.add(m5557(charSequence, (j71) it.next()));
        }
        return arrayList;
    }

    @zu2
    /* renamed from: ޣ */
    public static final String m5557(@zu2 CharSequence charSequence, @zu2 j71 j71Var) {
        fa1.m6826(charSequence, "<this>");
        fa1.m6826(j71Var, "range");
        return charSequence.subSequence(j71Var.getStart().intValue(), j71Var.getEndInclusive().intValue() + 1).toString();
    }

    @zu2
    /* renamed from: ޤ */
    public static final String m5558(@zu2 String str, @zu2 String str2, @zu2 String str3) {
        fa1.m6826(str2, "delimiter");
        fa1.m6826(str3, "missingDelimiterValue");
        int m5545 = m5545(str, str2, 0, false, 6);
        if (m5545 == -1) {
            return str3;
        }
        String substring = str.substring(str2.length() + m5545, str.length());
        fa1.m6825(substring, "this as java.lang.String…ing(startIndex, endIndex)");
        return substring;
    }

    /* renamed from: ޥ */
    public static String m5559(String str) {
        int m5544 = m5544(str, '$', 0, false, 6);
        if (m5544 == -1) {
            return str;
        }
        String substring = str.substring(m5544 + 1, str.length());
        fa1.m6825(substring, "this as java.lang.String…ing(startIndex, endIndex)");
        return substring;
    }

    @zu2
    /* renamed from: ޱ */
    public static final String m5560(@zu2 String str, char c, @zu2 String str2) {
        fa1.m6826(str, "<this>");
        fa1.m6826(str2, "missingDelimiterValue");
        int m5547 = m5547(str, c);
        if (m5547 == -1) {
            return str2;
        }
        String substring = str.substring(m5547 + 1, str.length());
        fa1.m6825(substring, "this as java.lang.String…ing(startIndex, endIndex)");
        return substring;
    }

    /* renamed from: ࡡ */
    public static String m5562(String str, char c) {
        fa1.m6826(str, "<this>");
        fa1.m6826(str, "missingDelimiterValue");
        int m5544 = m5544(str, c, 0, false, 6);
        if (m5544 == -1) {
            return str;
        }
        String substring = str.substring(0, m5544);
        fa1.m6825(substring, "this as java.lang.String…ing(startIndex, endIndex)");
        return substring;
    }

    /* renamed from: ࡢ */
    public static String m5563(String str, String str2) {
        fa1.m6826(str, "<this>");
        fa1.m6826(str, "missingDelimiterValue");
        int m5545 = m5545(str, str2, 0, false, 6);
        if (m5545 == -1) {
            return str;
        }
        String substring = str.substring(0, m5545);
        fa1.m6825(substring, "this as java.lang.String…ing(startIndex, endIndex)");
        return substring;
    }

    @zu2
    /* renamed from: ࡣ */
    public static final CharSequence m5564(@zu2 CharSequence charSequence) {
        fa1.m6826(charSequence, "<this>");
        int length = charSequence.length() - 1;
        int i = 0;
        boolean z = false;
        while (i <= length) {
            boolean m17043 = C7451.m17043(charSequence.charAt(!z ? i : length));
            if (z) {
                if (!m17043) {
                    break;
                }
                length--;
            } else if (m17043) {
                i++;
            } else {
                z = true;
            }
        }
        return charSequence.subSequence(i, length + 1);
    }
}
