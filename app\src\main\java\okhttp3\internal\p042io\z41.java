package okhttp3.internal.p042io;

@f01
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes3.dex */
public final class z41 {

    /* renamed from: Ϳ */
    public final int f27101;

    /* renamed from: Ԩ */
    public final int f27102;

    /* renamed from: ԩ */
    public final int f27103;

    /* renamed from: Ԫ */
    public final int f27104;

    public z41(int i, int i2, int i3, int i4) {
        this.f27101 = i;
        this.f27102 = i2;
        this.f27103 = i3;
        this.f27104 = i4;
    }

    public final boolean equals(@wv2 Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof z41)) {
            return false;
        }
        z41 z41Var = (z41) obj;
        return this.f27101 == z41Var.f27101 && this.f27102 == z41Var.f27102 && this.f27103 == z41Var.f27103 && this.f27104 == z41Var.f27104;
    }

    public final int hashCode() {
        return (((((this.f27101 * 31) + this.f27102) * 31) + this.f27103) * 31) + this.f27104;
    }

    @zu2
    public final String toString() {
        StringBuilder m9240 = lf2.m9240("InsetsValues(left=");
        m9240.append(this.f27101);
        m9240.append(", top=");
        m9240.append(this.f27102);
        m9240.append(", right=");
        m9240.append(this.f27103);
        m9240.append(", bottom=");
        return C6350.m15228(m9240, this.f27104, ')');
    }
}
