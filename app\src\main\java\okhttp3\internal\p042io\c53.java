package okhttp3.internal.p042io;

import java.security.PrivilegedAction;

/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class c53 implements PrivilegedAction {
    @Override // java.security.PrivilegedAction
    public final Object run() {
        return System.getProperty("org.spongycastle.pkcs1.not_strict");
    }
}
