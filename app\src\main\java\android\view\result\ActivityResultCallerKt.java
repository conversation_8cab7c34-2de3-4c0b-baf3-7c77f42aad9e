package android.view.result;

import android.view.result.contract.ActivityResultContract;
import kotlin.Metadata;
import okhttp3.internal.p042io.C6048;
import okhttp3.internal.p042io.C6049;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.ph0;
import okhttp3.internal.p042io.zu2;
import org.mozilla.javascript.optimizer.OptRuntime;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000(\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\u001a[\u0010\f\u001a\b\u0012\u0004\u0012\u00020\t0\u000b\"\u0004\b\u0000\u0010\u0000\"\u0004\b\u0001\u0010\u0001*\u00020\u00022\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00028\u0000\u0012\u0004\u0012\u00028\u00010\u00032\u0006\u0010\u0005\u001a\u00028\u00002\u0006\u0010\u0007\u001a\u00020\u00062\u0012\u0010\n\u001a\u000e\u0012\u0004\u0012\u00028\u0001\u0012\u0004\u0012\u00020\t0\b¢\u0006\u0004\b\f\u0010\r\u001aS\u0010\f\u001a\b\u0012\u0004\u0012\u00020\t0\u000b\"\u0004\b\u0000\u0010\u0000\"\u0004\b\u0001\u0010\u0001*\u00020\u00022\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00028\u0000\u0012\u0004\u0012\u00028\u00010\u00032\u0006\u0010\u0005\u001a\u00028\u00002\u0012\u0010\n\u001a\u000e\u0012\u0004\u0012\u00028\u0001\u0012\u0004\u0012\u00020\t0\b¢\u0006\u0004\b\f\u0010\u000e¨\u0006\u000f"}, m4115d2 = {OptRuntime.GeneratorState.resumptionPoint_TYPE, "O", "Landroidx/activity/result/ActivityResultCaller;", "Landroidx/activity/result/contract/ActivityResultContract;", "contract", "input", "Landroidx/activity/result/ActivityResultRegistry;", "registry", "Lkotlin/Function1;", "Lokhttp3/internal/io/lx5;", "callback", "Landroidx/activity/result/ActivityResultLauncher;", "registerForActivityResult", "(Landroidx/activity/result/ActivityResultCaller;Landroidx/activity/result/contract/ActivityResultContract;Ljava/lang/Object;Landroidx/activity/result/ActivityResultRegistry;Lokhttp3/internal/io/ph0;)Landroidx/activity/result/ActivityResultLauncher;", "(Landroidx/activity/result/ActivityResultCaller;Landroidx/activity/result/contract/ActivityResultContract;Ljava/lang/Object;Lokhttp3/internal/io/ph0;)Landroidx/activity/result/ActivityResultLauncher;", "activity-ktx_release"}, m4116k = 2, m4117mv = {1, 6, 0})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class ActivityResultCallerKt {
    @zu2
    public static final <I, O> ActivityResultLauncher<lx5> registerForActivityResult(@zu2 ActivityResultCaller activityResultCaller, @zu2 ActivityResultContract<I, O> activityResultContract, I i, @zu2 ActivityResultRegistry activityResultRegistry, @zu2 ph0<? super O, lx5> ph0Var) {
        fa1.m6826(activityResultCaller, "<this>");
        fa1.m6826(activityResultContract, "contract");
        fa1.m6826(activityResultRegistry, "registry");
        fa1.m6826(ph0Var, "callback");
        ActivityResultLauncher<I> registerForActivityResult = activityResultCaller.registerForActivityResult(activityResultContract, activityResultRegistry, new C6048(ph0Var));
        fa1.m6825(registerForActivityResult, "registerForActivityResul…egistry) { callback(it) }");
        return new ActivityResultCallerLauncher(registerForActivityResult, activityResultContract, i);
    }

    @zu2
    public static final <I, O> ActivityResultLauncher<lx5> registerForActivityResult(@zu2 ActivityResultCaller activityResultCaller, @zu2 ActivityResultContract<I, O> activityResultContract, I i, @zu2 ph0<? super O, lx5> ph0Var) {
        fa1.m6826(activityResultCaller, "<this>");
        fa1.m6826(activityResultContract, "contract");
        fa1.m6826(ph0Var, "callback");
        ActivityResultLauncher<I> registerForActivityResult = activityResultCaller.registerForActivityResult(activityResultContract, new C6049(ph0Var, 0));
        fa1.m6825(registerForActivityResult, "registerForActivityResul…ontract) { callback(it) }");
        return new ActivityResultCallerLauncher(registerForActivityResult, activityResultContract, i);
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: registerForActivityResult$lambda-0, reason: not valid java name */
    public static final void m18335registerForActivityResult$lambda0(ph0 ph0Var, Object obj) {
        fa1.m6826(ph0Var, "$callback");
        ph0Var.invoke(obj);
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: registerForActivityResult$lambda-1, reason: not valid java name */
    public static final void m18336registerForActivityResult$lambda1(ph0 ph0Var, Object obj) {
        fa1.m6826(ph0Var, "$callback");
        ph0Var.invoke(obj);
    }
}
