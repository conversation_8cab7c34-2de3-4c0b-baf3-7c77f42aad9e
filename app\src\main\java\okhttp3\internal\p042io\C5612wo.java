package okhttp3.internal.p042io;

import com.google.android.material.dialog.MaterialAlertDialogBuilder;
import com.stardust.autojs.core.inputevent.InputEventCodes;
import java.util.Objects;
import org.autojs.autojs.p047ui.edit.editor.InterfaceC8120;

@InterfaceC4988s2(m11868c = "org.autojs.autojs.ui.edit.EditorMenu$jumpToLine$1", m11869f = "EditorMenu.kt", m11870l = {InputEventCodes.BTN_TOUCH}, m11871m = "invokeSuspend")
/* renamed from: okhttp3.internal.io.wo */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5612wo extends u75 implements di0<InterfaceC7881, InterfaceC7155<? super lx5>, Object> {

    /* renamed from: ၥ */
    public C2798ap f25121;

    /* renamed from: ၦ */
    public int f25122;

    /* renamed from: ၮ */
    public final /* synthetic */ C2798ap f25123;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public C5612wo(C2798ap c2798ap, InterfaceC7155<? super C5612wo> interfaceC7155) {
        super(2, interfaceC7155);
        this.f25123 = c2798ap;
    }

    @Override // okhttp3.internal.p042io.AbstractC7853
    @zu2
    public final InterfaceC7155<lx5> create(@wv2 Object obj, @zu2 InterfaceC7155<?> interfaceC7155) {
        return new C5612wo(this.f25123, interfaceC7155);
    }

    @Override // okhttp3.internal.p042io.di0
    /* renamed from: invoke */
    public final Object mo18338invoke(InterfaceC7881 interfaceC7881, InterfaceC7155<? super lx5> interfaceC7155) {
        return ((C5612wo) create(interfaceC7881, interfaceC7155)).invokeSuspend(lx5.f14876);
    }

    @Override // okhttp3.internal.p042io.AbstractC7853
    @wv2
    public final Object invokeSuspend(@zu2 Object obj) {
        C2798ap c2798ap;
        EnumC7329 enumC7329 = EnumC7329.COROUTINE_SUSPENDED;
        int i = this.f25122;
        if (i == 0) {
            C4350o9.m10270(obj);
            C2798ap c2798ap2 = this.f25123;
            InterfaceC8120 interfaceC8120 = c2798ap2.f6601;
            this.f25121 = c2798ap2;
            this.f25122 = 1;
            Object lineCount = interfaceC8120.lineCount(this);
            if (lineCount == enumC7329) {
                return enumC7329;
            }
            c2798ap = c2798ap2;
            obj = lineCount;
        } else {
            if (i != 1) {
                throw new IllegalStateException("call to 'resume' before 'invoke' with coroutine");
            }
            c2798ap = this.f25121;
            C4350o9.m10270(obj);
        }
        int intValue = ((Number) obj).intValue();
        Objects.requireNonNull(c2798ap);
        String str = "1 ~ " + intValue;
        MaterialAlertDialogBuilder title = new MaterialAlertDialogBuilder(c2798ap.f6600).setTitle(2131821352);
        fa1.m6825(title, "MaterialAlertDialogBuild…string.text_jump_to_line)");
        rd3.m11574(title, str, "", 2, new C5969zo(c2798ap), 4);
        title.show();
        return lx5.f14876;
    }
}
