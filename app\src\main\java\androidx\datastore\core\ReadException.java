package androidx.datastore.core;

import androidx.exifinterface.media.ExifInterface;
import kotlin.Metadata;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.zu2;

@Metadata(m4114d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0003\n\u0002\b\u0004\b\u0002\u0018\u0000*\u0004\b\u0000\u0010\u00012\b\u0012\u0004\u0012\u0002H\u00010\u0002B\r\u0012\u0006\u0010\u0003\u001a\u00020\u0004¢\u0006\u0002\u0010\u0005R\u0011\u0010\u0003\u001a\u00020\u0004¢\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007¨\u0006\b"}, m4115d2 = {"Landroidx/datastore/core/ReadException;", ExifInterface.GPS_DIRECTION_TRUE, "Landroidx/datastore/core/State;", "readException", "", "(Ljava/lang/Throwable;)V", "getReadException", "()Ljava/lang/Throwable;", "datastore-core"}, m4116k = 1, m4117mv = {1, 5, 1}, m4119xi = 48)
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
final class ReadException<T> extends State<T> {

    @zu2
    private final Throwable readException;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public ReadException(@zu2 Throwable th) {
        super(null);
        fa1.m6826(th, "readException");
        this.readException = th;
    }

    @zu2
    public final Throwable getReadException() {
        return this.readException;
    }
}
