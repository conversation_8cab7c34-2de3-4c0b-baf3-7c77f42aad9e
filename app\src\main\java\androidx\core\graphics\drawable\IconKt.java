package androidx.core.graphics.drawable;

import android.annotation.SuppressLint;
import android.graphics.Bitmap;
import android.graphics.drawable.Icon;
import android.net.Uri;
import androidx.annotation.RequiresApi;
import kotlin.Metadata;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.zu2;

@Metadata(m4114d1 = {"\u0000\u0016\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0012\n\u0000\u001a\r\u0010\u0000\u001a\u00020\u0001*\u00020\u0002H\u0087\b\u001a\r\u0010\u0003\u001a\u00020\u0001*\u00020\u0002H\u0087\b\u001a\r\u0010\u0003\u001a\u00020\u0001*\u00020\u0004H\u0087\b\u001a\r\u0010\u0003\u001a\u00020\u0001*\u00020\u0005H\u0087\b¨\u0006\u0006"}, m4115d2 = {"toAdaptiveIcon", "Landroid/graphics/drawable/Icon;", "Landroid/graphics/Bitmap;", "toIcon", "Landroid/net/Uri;", "", "core-ktx_release"}, m4116k = 2, m4117mv = {1, 6, 0}, m4119xi = 48)
@SuppressLint({"ClassVerificationFailure"})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class IconKt {
    @RequiresApi(26)
    @zu2
    public static final Icon toAdaptiveIcon(@zu2 Bitmap bitmap) {
        fa1.m6826(bitmap, "<this>");
        Icon createWithAdaptiveBitmap = Icon.createWithAdaptiveBitmap(bitmap);
        fa1.m6825(createWithAdaptiveBitmap, "createWithAdaptiveBitmap(this)");
        return createWithAdaptiveBitmap;
    }

    @RequiresApi(26)
    @zu2
    public static final Icon toIcon(@zu2 Bitmap bitmap) {
        fa1.m6826(bitmap, "<this>");
        Icon createWithBitmap = Icon.createWithBitmap(bitmap);
        fa1.m6825(createWithBitmap, "createWithBitmap(this)");
        return createWithBitmap;
    }

    @RequiresApi(26)
    @zu2
    public static final Icon toIcon(@zu2 Uri uri) {
        fa1.m6826(uri, "<this>");
        Icon createWithContentUri = Icon.createWithContentUri(uri);
        fa1.m6825(createWithContentUri, "createWithContentUri(this)");
        return createWithContentUri;
    }

    @RequiresApi(26)
    @zu2
    public static final Icon toIcon(@zu2 byte[] bArr) {
        fa1.m6826(bArr, "<this>");
        Icon createWithData = Icon.createWithData(bArr, 0, bArr.length);
        fa1.m6825(createWithData, "createWithData(this, 0, size)");
        return createWithData;
    }
}
