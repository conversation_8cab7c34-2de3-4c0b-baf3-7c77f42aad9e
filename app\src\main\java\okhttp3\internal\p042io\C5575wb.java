package okhttp3.internal.p042io;

import com.google.common.collect.AbstractC1588;
import com.google.common.collect.AbstractC1592;
import com.google.common.collect.C1605;
import java.util.AbstractList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import okhttp3.internal.p042io.AbstractC6243;

/* renamed from: okhttp3.internal.io.wb */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5575wb extends AbstractC6951 implements InterfaceC6946 {

    /* renamed from: ၥ */
    @pu2
    public final C3318fc f24832;

    /* renamed from: ၦ */
    public final int f24833;

    /* renamed from: ၮ */
    @vv2
    public final C9468 f24834;

    /* renamed from: ၯ */
    public final int f24835;

    /* renamed from: ၰ */
    public int f24836 = 0;

    /* renamed from: ၵ */
    public int f24837 = 0;

    /* renamed from: ၶ */
    public int f24838 = 0;

    /* renamed from: ၷ */
    public final int f24839;

    /* renamed from: ၸ */
    public final int f24840;

    /* renamed from: ၹ */
    public final int f24841;

    /* renamed from: ၺ */
    public final int f24842;

    /* renamed from: ၻ */
    @vv2
    public AbstractC6243 f24843;

    /* renamed from: okhttp3.internal.io.wb$Ϳ, reason: contains not printable characters */
    public class C9467 extends AbstractList<String> {

        /* renamed from: ၥ */
        public final /* synthetic */ int f24844;

        /* renamed from: ၦ */
        public final /* synthetic */ int f24845;

        public C9467(int i, int i2) {
            this.f24844 = i;
            this.f24845 = i2;
        }

        /* JADX WARN: Type inference failed for: r1v0, types: [okhttp3.internal.io.fc$֏, okhttp3.internal.io.fc$ބ<java.lang.String>] */
        @Override // java.util.AbstractList, java.util.List
        @pu2
        public final Object get(int i) {
            C3318fc c3318fc = C5575wb.this.f24832;
            return c3318fc.f9960.get(c3318fc.f9943.m11105((i * 2) + this.f24844 + 4));
        }

        @Override // java.util.AbstractCollection, java.util.Collection, java.util.List
        public final int size() {
            return this.f24845;
        }
    }

    /* renamed from: okhttp3.internal.io.wb$Ԩ, reason: contains not printable characters */
    public class C9468 {

        /* renamed from: Ϳ */
        public final int f24847;

        /* renamed from: Ԩ */
        public int f24848;

        /* renamed from: ԩ */
        public int f24849;

        /* renamed from: Ԫ */
        public int f24850;

        public C9468(int i) {
            this.f24847 = i;
        }

        /* renamed from: Ϳ */
        public final int m13521() {
            if (this.f24849 == 0) {
                C4670qe c4670qe = C5575wb.this.f24832.f9943;
                int m13522 = m13522();
                Objects.requireNonNull(c4670qe);
                C4694qf c4694qf = new C4694qf(c4670qe, m13522);
                for (int i = 0; i < C5575wb.this.f24840; i++) {
                    c4694qf.m11118();
                }
                this.f24849 = c4694qf.f18583;
            }
            return this.f24849;
        }

        /* renamed from: Ԩ */
        public final int m13522() {
            if (this.f24848 == 0) {
                C4670qe c4670qe = C5575wb.this.f24832.f9943;
                int i = this.f24847;
                Objects.requireNonNull(c4670qe);
                C4694qf c4694qf = new C4694qf(c4670qe, i);
                for (int i2 = 0; i2 < C5575wb.this.f24839; i2++) {
                    c4694qf.m11118();
                }
                this.f24848 = c4694qf.f18583;
            }
            return this.f24848;
        }
    }

    public C5575wb(@pu2 C3318fc c3318fc, int i, int i2) {
        this.f24832 = c3318fc;
        this.f24833 = i;
        int m11103 = c3318fc.f9942.m11103(i + 24);
        if (m11103 == 0) {
            this.f24835 = -1;
            this.f24839 = 0;
            this.f24840 = 0;
            this.f24841 = 0;
            this.f24842 = 0;
        } else {
            C4670qe c4670qe = c3318fc.f9943;
            Objects.requireNonNull(c4670qe);
            C4694qf c4694qf = new C4694qf(c4670qe, m11103);
            this.f24839 = c4694qf.m11120(false);
            this.f24840 = c4694qf.m11120(false);
            this.f24841 = c4694qf.m11120(false);
            this.f24842 = c4694qf.m11120(false);
            this.f24835 = c4694qf.f18583;
        }
        this.f24834 = i2 != 0 ? new C9468(i2) : null;
    }

    @Override // okhttp3.internal.p042io.InterfaceC6946
    @pu2
    public final Set<? extends C4492pb> getAnnotations() {
        return m13518().mo14995();
    }

    /* JADX WARN: Type inference failed for: r1v0, types: [okhttp3.internal.io.fc$֏, okhttp3.internal.io.fc$ބ<java.lang.String>] */
    @Override // okhttp3.internal.p042io.lt5
    @pu2
    public final String getType() {
        C3318fc c3318fc = this.f24832;
        return c3318fc.f9960.get(c3318fc.f9942.m11103(this.f24833 + 0));
    }

    @Override // okhttp3.internal.p042io.InterfaceC6946
    /* renamed from: Ϳ */
    public final int mo7334() {
        return this.f24832.f9942.m11103(this.f24833 + 4);
    }

    /* JADX WARN: Type inference failed for: r1v0, types: [okhttp3.internal.io.fc$Ԯ, okhttp3.internal.io.fc$ބ<java.lang.String>] */
    @Override // okhttp3.internal.p042io.InterfaceC6946
    @vv2
    /* renamed from: ԫ */
    public final String mo7335() {
        C3318fc c3318fc = this.f24832;
        return (String) c3318fc.f9959.m6881(c3318fc.f9942.m11101(this.f24833 + 16));
    }

    @Override // okhttp3.internal.p042io.InterfaceC6946
    @pu2
    /* renamed from: ֏ */
    public final Iterable<? extends C3914kc> mo7336() {
        C2859bc c2859bc;
        if (this.f24839 <= 0) {
            this.f24836 = this.f24835;
            int i = AbstractC1592.f2771;
            return C1605.f2792;
        }
        C4670qe c4670qe = this.f24832.f9943;
        int i2 = this.f24835;
        Objects.requireNonNull(c4670qe);
        AbstractC6243 m13518 = m13518();
        int m11103 = this.f24832.f9942.m11103(this.f24833 + 28);
        C9468 c9468 = this.f24834;
        if (c9468 == null) {
            c2859bc = null;
        } else {
            C5575wb c5575wb = C5575wb.this;
            c2859bc = new C2859bc(c9468, c5575wb.f24832.f9943, c9468.f24847, c5575wb.f24839);
        }
        return new C5689xb(this, m13518, m11103, i2, c2859bc);
    }

    /* renamed from: ސ */
    public final AbstractC6243 m13518() {
        if (this.f24843 == null) {
            int m11103 = this.f24832.f9942.m11103(this.f24833 + 20);
            this.f24843 = m11103 == 0 ? AbstractC6243.f28406 : new AbstractC6243.C9623(this.f24832, m11103);
        }
        return this.f24843;
    }

    /* renamed from: ޑ */
    public final int m13519() {
        int i = this.f24837;
        if (i > 0) {
            return i;
        }
        C4670qe c4670qe = this.f24832.f9943;
        int m13520 = m13520();
        Objects.requireNonNull(c4670qe);
        C4694qf c4694qf = new C4694qf(c4670qe, m13520);
        int i2 = this.f24840;
        for (int i3 = 0; i3 < i2; i3++) {
            c4694qf.m11121();
            c4694qf.m11121();
        }
        int i4 = c4694qf.f18583;
        this.f24837 = i4;
        return i4;
    }

    /* renamed from: ޓ */
    public final int m13520() {
        int i = this.f24836;
        if (i > 0) {
            return i;
        }
        C4670qe c4670qe = this.f24832.f9943;
        int i2 = this.f24835;
        Objects.requireNonNull(c4670qe);
        C4694qf c4694qf = new C4694qf(c4670qe, i2);
        int i3 = this.f24839;
        for (int i4 = 0; i4 < i3; i4++) {
            c4694qf.m11121();
            c4694qf.m11121();
        }
        int i5 = c4694qf.f18583;
        this.f24836 = i5;
        return i5;
    }

    @Override // okhttp3.internal.p042io.InterfaceC6946
    @pu2
    /* renamed from: ޕ */
    public final List<String> mo7337() {
        int m11103 = this.f24832.f9942.m11103(this.f24833 + 12);
        if (m11103 > 0) {
            return new C9467(m11103, this.f24832.f9943.m11103(m11103));
        }
        AbstractC7161 abstractC7161 = AbstractC1588.f2751;
        return zy3.f27827;
    }

    @Override // okhttp3.internal.p042io.InterfaceC6946
    @pu2
    /* renamed from: ޝ */
    public final Iterable<? extends C3914kc> mo7338() {
        if (this.f24840 <= 0) {
            int i = this.f24836;
            if (i > 0) {
                this.f24837 = i;
            }
            int i2 = AbstractC1592.f2771;
            return C1605.f2792;
        }
        C4670qe c4670qe = this.f24832.f9943;
        int m13520 = m13520();
        Objects.requireNonNull(c4670qe);
        AbstractC6243 m13518 = m13518();
        C9468 c9468 = this.f24834;
        return new C5802yb(this, m13518, m13520, c9468 == null ? null : new C3008cc(c9468, C5575wb.this.f24832.f9943, c9468.m13522(), C5575wb.this.f24840));
    }

    @Override // okhttp3.internal.p042io.InterfaceC6946
    @pu2
    /* renamed from: ޥ */
    public final Iterable<? extends C5461vd> mo7339() {
        if (this.f24841 <= 0) {
            int i = this.f24837;
            if (i > 0) {
                this.f24838 = i;
            }
            int i2 = AbstractC1592.f2771;
            return C1605.f2792;
        }
        C4670qe c4670qe = this.f24832.f9943;
        int m13519 = m13519();
        Objects.requireNonNull(c4670qe);
        AbstractC6243 m13518 = m13518();
        C9468 c9468 = this.f24834;
        return new C5915zb(this, m13518, m13519, c9468 == null ? null : new C3112dc(c9468, C5575wb.this.f24832.f9943, c9468.m13521(), C5575wb.this.f24841));
    }

    @Override // okhttp3.internal.p042io.InterfaceC6946
    @pu2
    /* renamed from: ࡣ */
    public final Iterable<? extends C5461vd> mo7340() {
        C3212ec c3212ec;
        if (this.f24842 <= 0) {
            int i = AbstractC1592.f2771;
            return C1605.f2792;
        }
        C4670qe c4670qe = this.f24832.f9943;
        int i2 = this.f24838;
        if (i2 <= 0) {
            int m13519 = m13519();
            Objects.requireNonNull(c4670qe);
            C4694qf c4694qf = new C4694qf(c4670qe, m13519);
            int i3 = this.f24841;
            for (int i4 = 0; i4 < i3; i4++) {
                c4694qf.m11121();
                c4694qf.m11121();
                c4694qf.m11121();
            }
            i2 = c4694qf.f18583;
            this.f24838 = i2;
        }
        Objects.requireNonNull(c4670qe);
        AbstractC6243 m13518 = m13518();
        C9468 c9468 = this.f24834;
        if (c9468 == null) {
            c3212ec = null;
        } else {
            C4670qe c4670qe2 = C5575wb.this.f24832.f9943;
            if (c9468.f24850 == 0) {
                int m13521 = c9468.m13521();
                Objects.requireNonNull(c4670qe2);
                C4694qf c4694qf2 = new C4694qf(c4670qe2, m13521);
                for (int i5 = 0; i5 < C5575wb.this.f24841; i5++) {
                    c4694qf2.m11118();
                }
                c9468.f24850 = c4694qf2.f18583;
            }
            c3212ec = new C3212ec(c4670qe2, c9468.f24850, C5575wb.this.f24842);
        }
        return new C2735ac(this, m13518, i2, c3212ec);
    }

    /* JADX WARN: Type inference failed for: r1v0, types: [okhttp3.internal.io.fc$֏, okhttp3.internal.io.fc$ބ<java.lang.String>] */
    @Override // okhttp3.internal.p042io.InterfaceC6946
    @vv2
    /* renamed from: ࡤ */
    public final String mo7341() {
        C3318fc c3318fc = this.f24832;
        return (String) c3318fc.f9960.m6884(c3318fc.f9942.m11101(this.f24833 + 8));
    }
}
