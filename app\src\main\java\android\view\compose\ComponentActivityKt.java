package android.view.compose;

import android.R;
import android.view.C10501ViewTreeSavedStateRegistryOwner;
import android.view.ComponentActivity;
import android.view.View;
import android.view.ViewGroup;
import androidx.compose.p000ui.platform.ComposeView;
import androidx.lifecycle.ViewTreeLifecycleOwner;
import androidx.lifecycle.ViewTreeViewModelStoreOwner;
import kotlin.Metadata;
import okhttp3.internal.p042io.AbstractC6779;
import okhttp3.internal.p042io.InterfaceC6968;
import okhttp3.internal.p042io.di0;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.wv2;
import okhttp3.internal.p042io.zu2;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\u001a0\u0010\u0007\u001a\u00020\u0004*\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00012\u0011\u0010\u0006\u001a\r\u0012\u0004\u0012\u00020\u00040\u0003¢\u0006\u0002\b\u0005¢\u0006\u0004\b\u0007\u0010\b\u001a\f\u0010\t\u001a\u00020\u0004*\u00020\u0000H\u0002\"\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004¢\u0006\u0006\n\u0004\b\u000b\u0010\f¨\u0006\r"}, m4115d2 = {"Landroidx/activity/ComponentActivity;", "Lokhttp3/internal/io/ܔ;", "parent", "Lkotlin/Function0;", "Lokhttp3/internal/io/lx5;", "Lokhttp3/internal/io/ಭ;", "content", "setContent", "(Landroidx/activity/ComponentActivity;Lokhttp3/internal/io/ܔ;Lokhttp3/internal/io/di0;)V", "setOwners", "Landroid/view/ViewGroup$LayoutParams;", "DefaultActivityContentLayoutParams", "Landroid/view/ViewGroup$LayoutParams;", "activity-compose_release"}, m4116k = 2, m4117mv = {1, 6, 0})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class ComponentActivityKt {

    @zu2
    private static final ViewGroup.LayoutParams DefaultActivityContentLayoutParams = new ViewGroup.LayoutParams(-2, -2);

    public static final void setContent(@zu2 ComponentActivity componentActivity, @wv2 AbstractC6779 abstractC6779, @zu2 di0<? super InterfaceC6968, ? super Integer, lx5> di0Var) {
        fa1.m6826(componentActivity, "<this>");
        fa1.m6826(di0Var, "content");
        View childAt = ((ViewGroup) componentActivity.getWindow().getDecorView().findViewById(R.id.content)).getChildAt(0);
        ComposeView composeView = childAt instanceof ComposeView ? (ComposeView) childAt : null;
        if (composeView != null) {
            composeView.setParentCompositionContext(abstractC6779);
            composeView.setContent(di0Var);
            return;
        }
        ComposeView composeView2 = new ComposeView(componentActivity, null, 0, 6, null);
        composeView2.setParentCompositionContext(abstractC6779);
        composeView2.setContent(di0Var);
        setOwners(componentActivity);
        componentActivity.setContentView(composeView2, DefaultActivityContentLayoutParams);
    }

    public static /* synthetic */ void setContent$default(ComponentActivity componentActivity, AbstractC6779 abstractC6779, di0 di0Var, int i, Object obj) {
        if ((i & 1) != 0) {
            abstractC6779 = null;
        }
        setContent(componentActivity, abstractC6779, di0Var);
    }

    private static final void setOwners(ComponentActivity componentActivity) {
        View decorView = componentActivity.getWindow().getDecorView();
        fa1.m6825(decorView, "window.decorView");
        if (ViewTreeLifecycleOwner.get(decorView) == null) {
            ViewTreeLifecycleOwner.set(decorView, componentActivity);
        }
        if (ViewTreeViewModelStoreOwner.get(decorView) == null) {
            ViewTreeViewModelStoreOwner.set(decorView, componentActivity);
        }
        if (C10501ViewTreeSavedStateRegistryOwner.get(decorView) == null) {
            C10501ViewTreeSavedStateRegistryOwner.set(decorView, componentActivity);
        }
    }
}
