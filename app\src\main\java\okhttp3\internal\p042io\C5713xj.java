package okhttp3.internal.p042io;

/* renamed from: okhttp3.internal.io.xj */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes3.dex */
public final class C5713xj extends lv1 implements ph0<hf3, Boolean> {

    /* renamed from: ၥ */
    public static final C5713xj f25972 = new C5713xj();

    public C5713xj() {
        super(1);
    }

    @Override // okhttp3.internal.p042io.ph0
    public final Boolean invoke(hf3 hf3Var) {
        fa1.m6826(hf3Var, "it");
        return Boolean.TRUE;
    }
}
