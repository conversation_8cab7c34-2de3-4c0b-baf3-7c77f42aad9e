package okhttp3.internal.p042io;

/* renamed from: okhttp3.internal.io.vk */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes3.dex */
public final class C5476vk extends h51 implements InterfaceC4178mk {

    /* renamed from: ၦ */
    @zu2
    public final ph0<InterfaceC7548, lx5> f24236;

    /* JADX WARN: Illegal instructions before constructor call */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public C5476vk(@okhttp3.internal.p042io.zu2 okhttp3.internal.p042io.ph0 r3) {
        /*
            r2 = this;
            okhttp3.internal.io.ph0<okhttp3.internal.io.g51, okhttp3.internal.io.lx5> r0 = okhttp3.internal.p042io.e51.f9040
            java.lang.String r1 = "inspectorInfo"
            okhttp3.internal.p042io.fa1.m6826(r0, r1)
            r2.<init>(r0)
            r2.f24236 = r3
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: okhttp3.internal.p042io.C5476vk.<init>(okhttp3.internal.io.ph0):void");
    }

    public final boolean equals(@wv2 Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj instanceof C5476vk) {
            return fa1.m6818(this.f24236, ((C5476vk) obj).f24236);
        }
        return false;
    }

    public final int hashCode() {
        return this.f24236.hashCode();
    }

    @Override // okhttp3.internal.p042io.InterfaceC4178mk
    /* renamed from: ޅ */
    public final void mo5509(@zu2 InterfaceC7548 interfaceC7548) {
        fa1.m6826(interfaceC7548, "<this>");
        this.f24236.invoke(interfaceC7548);
    }

    @Override // okhttp3.internal.p042io.rk2
    /* renamed from: ޘ */
    public final /* synthetic */ boolean mo21(ph0 ph0Var) {
        return sk2.m12063(this, ph0Var);
    }

    @Override // okhttp3.internal.p042io.rk2
    /* renamed from: ޙ */
    public final Object mo22(Object obj, di0 di0Var) {
        fa1.m6826(di0Var, "operation");
        return di0Var.mo18338invoke(obj, this);
    }

    @Override // okhttp3.internal.p042io.rk2
    /* renamed from: ޡ */
    public final /* synthetic */ rk2 mo23(rk2 rk2Var) {
        return qk2.m11347(this, rk2Var);
    }
}
