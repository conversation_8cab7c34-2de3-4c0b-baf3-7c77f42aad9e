package androidx.core.view;

import android.view.View;
import android.view.ViewGroup;
import java.util.Objects;
import kotlin.Metadata;
import okhttp3.internal.p042io.C4350o9;
import okhttp3.internal.p042io.EnumC7329;
import okhttp3.internal.p042io.InterfaceC4988s2;
import okhttp3.internal.p042io.InterfaceC7155;
import okhttp3.internal.p042io.b44;
import okhttp3.internal.p042io.di0;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.pk4;
import okhttp3.internal.p042io.rk4;
import okhttp3.internal.p042io.wv2;
import okhttp3.internal.p042io.zu2;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000\u000e\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\u0010\u0003\u001a\u00020\u0002*\b\u0012\u0004\u0012\u00020\u00010\u0000H\u008a@"}, m4115d2 = {"Lokhttp3/internal/io/rk4;", "Landroid/view/View;", "Lokhttp3/internal/io/lx5;", "<anonymous>"}, m4116k = 3, m4117mv = {1, 6, 0})
@InterfaceC4988s2(m11868c = "androidx.core.view.ViewKt$allViews$1", m11869f = "View.kt", m11870l = {406, 408}, m11871m = "invokeSuspend")
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class ViewKt$allViews$1 extends b44 implements di0<rk4<? super View>, InterfaceC7155<? super lx5>, Object> {
    public final /* synthetic */ View $this_allViews;
    private /* synthetic */ Object L$0;
    public int label;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public ViewKt$allViews$1(View view, InterfaceC7155<? super ViewKt$allViews$1> interfaceC7155) {
        super(2, interfaceC7155);
        this.$this_allViews = view;
    }

    @Override // okhttp3.internal.p042io.AbstractC7853
    @zu2
    public final InterfaceC7155<lx5> create(@wv2 Object obj, @zu2 InterfaceC7155<?> interfaceC7155) {
        ViewKt$allViews$1 viewKt$allViews$1 = new ViewKt$allViews$1(this.$this_allViews, interfaceC7155);
        viewKt$allViews$1.L$0 = obj;
        return viewKt$allViews$1;
    }

    @Override // okhttp3.internal.p042io.di0
    @wv2
    /* renamed from: invoke, reason: avoid collision after fix types in other method and merged with bridge method [inline-methods] */
    public final Object mo18338invoke(@zu2 rk4<? super View> rk4Var, @wv2 InterfaceC7155<? super lx5> interfaceC7155) {
        return ((ViewKt$allViews$1) create(rk4Var, interfaceC7155)).invokeSuspend(lx5.f14876);
    }

    @Override // okhttp3.internal.p042io.AbstractC7853
    @wv2
    public final Object invokeSuspend(@zu2 Object obj) {
        EnumC7329 enumC7329 = EnumC7329.COROUTINE_SUSPENDED;
        int i = this.label;
        if (i == 0) {
            C4350o9.m10270(obj);
            rk4 rk4Var = (rk4) this.L$0;
            View view = this.$this_allViews;
            this.L$0 = rk4Var;
            this.label = 1;
            rk4Var.mo11350(view, this);
            return enumC7329;
        }
        if (i == 1) {
            rk4 rk4Var2 = (rk4) this.L$0;
            C4350o9.m10270(obj);
            View view2 = this.$this_allViews;
            if (view2 instanceof ViewGroup) {
                pk4<View> descendants = ViewGroupKt.getDescendants((ViewGroup) view2);
                this.L$0 = null;
                this.label = 2;
                Objects.requireNonNull(rk4Var2);
                Object mo11351 = rk4Var2.mo11351(descendants.iterator(), this);
                if (mo11351 != enumC7329) {
                    mo11351 = lx5.f14876;
                }
                if (mo11351 == enumC7329) {
                    return enumC7329;
                }
            }
        } else {
            if (i != 2) {
                throw new IllegalStateException("call to 'resume' before 'invoke' with coroutine");
            }
            C4350o9.m10270(obj);
        }
        return lx5.f14876;
    }
}
