package androidx.core.os;

import android.os.OutcomeReceiver;
import androidx.annotation.RequiresApi;
import androidx.exifinterface.media.ExifInterface;
import com.stardust.autojs.engine.RhinoJavaScriptEngine;
import com.stardust.autojs.project.ProjectConfig;
import java.lang.Throwable;
import java.util.concurrent.atomic.AtomicBoolean;
import kotlin.Metadata;
import okhttp3.internal.p042io.C4350o9;
import okhttp3.internal.p042io.InterfaceC7155;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.lf2;
import okhttp3.internal.p042io.zu2;

@RequiresApi(31)
@Metadata(m4113bv = {}, m4114d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0003\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0003\u0018\u0000*\u0004\b\u0000\u0010\u0001*\b\b\u0001\u0010\u0003*\u00020\u00022\u000e\u0012\u0004\u0012\u00028\u0000\u0012\u0004\u0012\u00028\u00010\u00042\u00020\u0005B\u0015\u0012\f\u0010\u0010\u001a\b\u0012\u0004\u0012\u00028\u00000\u000f¢\u0006\u0004\b\u0011\u0010\u0012J\u0017\u0010\b\u001a\u00020\u00072\u0006\u0010\u0006\u001a\u00028\u0000H\u0016¢\u0006\u0004\b\b\u0010\tJ\u0017\u0010\u000b\u001a\u00020\u00072\u0006\u0010\n\u001a\u00028\u0001H\u0016¢\u0006\u0004\b\u000b\u0010\fJ\b\u0010\u000e\u001a\u00020\rH\u0016¨\u0006\u0013"}, m4115d2 = {"Landroidx/core/os/ContinuationOutcomeReceiver;", "R", "", ExifInterface.LONGITUDE_EAST, "Landroid/os/OutcomeReceiver;", "Ljava/util/concurrent/atomic/AtomicBoolean;", "result", "Lokhttp3/internal/io/lx5;", "onResult", "(Ljava/lang/Object;)V", "error", "onError", "(Ljava/lang/Throwable;)V", "", "toString", "Lokhttp3/internal/io/ৡ;", ProjectConfig.FEATURE_CONTINUATION, RhinoJavaScriptEngine.SOURCE_NAME_INIT, "(Lokhttp3/internal/io/ৡ;)V", "core-ktx_release"}, m4116k = 1, m4117mv = {1, 6, 0})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
final class ContinuationOutcomeReceiver<R, E extends Throwable> extends AtomicBoolean implements OutcomeReceiver<R, E> {

    @zu2
    private final InterfaceC7155<R> continuation;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    /* JADX WARN: Multi-variable type inference failed */
    public ContinuationOutcomeReceiver(@zu2 InterfaceC7155<? super R> interfaceC7155) {
        super(false);
        fa1.m6826(interfaceC7155, ProjectConfig.FEATURE_CONTINUATION);
        this.continuation = interfaceC7155;
    }

    @Override // android.os.OutcomeReceiver
    public void onError(@zu2 E error) {
        fa1.m6826(error, "error");
        if (compareAndSet(false, true)) {
            this.continuation.resumeWith(C4350o9.m10267(error));
        }
    }

    @Override // android.os.OutcomeReceiver
    public void onResult(R result) {
        if (compareAndSet(false, true)) {
            this.continuation.resumeWith(result);
        }
    }

    @Override // java.util.concurrent.atomic.AtomicBoolean
    @zu2
    public String toString() {
        StringBuilder m9240 = lf2.m9240("ContinuationOutcomeReceiver(outcomeReceived = ");
        m9240.append(get());
        m9240.append(')');
        return m9240.toString();
    }
}
