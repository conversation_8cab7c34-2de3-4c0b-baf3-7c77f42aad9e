package androidx.constraintlayout.motion.widget;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.Resources;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import java.io.PrintStream;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.nio.CharBuffer;
import okhttp3.internal.p042io.C4118lz;
import okhttp3.internal.p042io.C5717xl;
import okhttp3.internal.p042io.C6134;
import okhttp3.internal.p042io.k76;
import okhttp3.internal.p042io.lf2;

@SuppressLint({"LogConditional"})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public class Debug {
    public static void dumpLayoutParams(ViewGroup.LayoutParams layoutParams, String str) {
        StackTraceElement stackTraceElement = new Throwable().getStackTrace()[1];
        StringBuilder m9240 = lf2.m9240(".(");
        m9240.append(stackTraceElement.getFileName());
        m9240.append(":");
        m9240.append(stackTraceElement.getLineNumber());
        m9240.append(") ");
        m9240.append(str);
        m9240.append("  ");
        String sb = m9240.toString();
        PrintStream printStream = System.out;
        StringBuilder m14894 = C6134.m14894(" >>>>>>>>>>>>>>>>>>. dump ", sb, "  ");
        m14894.append(layoutParams.getClass().getName());
        printStream.println(m14894.toString());
        for (Field field : layoutParams.getClass().getFields()) {
            try {
                Object obj = field.get(layoutParams);
                String name = field.getName();
                if (name.contains("To") && !obj.toString().equals("-1")) {
                    System.out.println(sb + "       " + name + " " + obj);
                }
            } catch (IllegalAccessException unused) {
            }
        }
        System.out.println(" <<<<<<<<<<<<<<<<< dump " + sb);
    }

    public static void dumpPoc(Object obj) {
        StackTraceElement stackTraceElement = new Throwable().getStackTrace()[1];
        StringBuilder m9240 = lf2.m9240(".(");
        m9240.append(stackTraceElement.getFileName());
        m9240.append(":");
        m9240.append(stackTraceElement.getLineNumber());
        m9240.append(")");
        String sb = m9240.toString();
        Class<?> cls = obj.getClass();
        PrintStream printStream = System.out;
        StringBuilder m14009 = C5717xl.m14009(sb, "------------- ");
        m14009.append(cls.getName());
        m14009.append(" --------------------");
        printStream.println(m14009.toString());
        for (Field field : cls.getFields()) {
            try {
                Object obj2 = field.get(obj);
                if (field.getName().startsWith("layout_constraint") && ((!(obj2 instanceof Integer) || !obj2.toString().equals("-1")) && ((!(obj2 instanceof Integer) || !obj2.toString().equals("0")) && ((!(obj2 instanceof Float) || !obj2.toString().equals("1.0")) && (!(obj2 instanceof Float) || !obj2.toString().equals("0.5")))))) {
                    System.out.println(sb + "    " + field.getName() + " " + obj2);
                }
            } catch (IllegalAccessException unused) {
            }
        }
        PrintStream printStream2 = System.out;
        StringBuilder m140092 = C5717xl.m14009(sb, "------------- ");
        m140092.append(cls.getSimpleName());
        m140092.append(" --------------------");
        printStream2.println(m140092.toString());
    }

    public static String getActionType(MotionEvent motionEvent) {
        int action = motionEvent.getAction();
        for (Field field : MotionEvent.class.getFields()) {
            try {
                if (Modifier.isStatic(field.getModifiers()) && field.getType().equals(Integer.TYPE) && field.getInt(null) == action) {
                    return field.getName();
                }
            } catch (IllegalAccessException unused) {
            }
        }
        return "---";
    }

    public static String getCallFrom(int i) {
        StackTraceElement stackTraceElement = new Throwable().getStackTrace()[i + 2];
        StringBuilder m9240 = lf2.m9240(".(");
        m9240.append(stackTraceElement.getFileName());
        m9240.append(":");
        m9240.append(stackTraceElement.getLineNumber());
        m9240.append(")");
        return m9240.toString();
    }

    public static String getLoc() {
        StackTraceElement stackTraceElement = new Throwable().getStackTrace()[1];
        StringBuilder m9240 = lf2.m9240(".(");
        m9240.append(stackTraceElement.getFileName());
        m9240.append(":");
        m9240.append(stackTraceElement.getLineNumber());
        m9240.append(") ");
        m9240.append(stackTraceElement.getMethodName());
        m9240.append("()");
        return m9240.toString();
    }

    public static String getLocation() {
        StackTraceElement stackTraceElement = new Throwable().getStackTrace()[1];
        StringBuilder m9240 = lf2.m9240(".(");
        m9240.append(stackTraceElement.getFileName());
        m9240.append(":");
        m9240.append(stackTraceElement.getLineNumber());
        m9240.append(")");
        return m9240.toString();
    }

    public static String getLocation2() {
        StackTraceElement stackTraceElement = new Throwable().getStackTrace()[2];
        StringBuilder m9240 = lf2.m9240(".(");
        m9240.append(stackTraceElement.getFileName());
        m9240.append(":");
        m9240.append(stackTraceElement.getLineNumber());
        m9240.append(")");
        return m9240.toString();
    }

    public static String getName(Context context, int i) {
        if (i == -1) {
            return "UNKNOWN";
        }
        try {
            return context.getResources().getResourceEntryName(i);
        } catch (Exception unused) {
            return k76.m8852("?", i);
        }
    }

    public static String getName(Context context, int[] iArr) {
        String str;
        try {
            String str2 = iArr.length + "[";
            int i = 0;
            while (i < iArr.length) {
                StringBuilder sb = new StringBuilder();
                sb.append(str2);
                sb.append(i == 0 ? "" : " ");
                String sb2 = sb.toString();
                try {
                    str = context.getResources().getResourceEntryName(iArr[i]);
                } catch (Resources.NotFoundException unused) {
                    str = "? " + iArr[i] + " ";
                }
                str2 = sb2 + str;
                i++;
            }
            return str2 + "]";
        } catch (Exception e) {
            e.toString();
            return "UNKNOWN";
        }
    }

    public static String getName(View view) {
        try {
            return view.getContext().getResources().getResourceEntryName(view.getId());
        } catch (Exception unused) {
            return "UNKNOWN";
        }
    }

    public static String getState(MotionLayout motionLayout, int i) {
        return getState(motionLayout, i, -1);
    }

    public static String getState(MotionLayout motionLayout, int i, int i2) {
        int length;
        if (i == -1) {
            return "UNDEFINED";
        }
        String resourceEntryName = motionLayout.getContext().getResources().getResourceEntryName(i);
        if (i2 == -1) {
            return resourceEntryName;
        }
        if (resourceEntryName.length() > i2) {
            resourceEntryName = resourceEntryName.replaceAll("([^_])[aeiou]+", "$1");
        }
        if (resourceEntryName.length() <= i2 || (length = resourceEntryName.replaceAll("[^_]", "").length()) <= 0) {
            return resourceEntryName;
        }
        return resourceEntryName.replaceAll(CharBuffer.allocate((resourceEntryName.length() - i2) / length).toString().replace((char) 0, '.') + "_", "_");
    }

    public static void logStack(String str, String str2, int i) {
        StackTraceElement[] stackTrace = new Throwable().getStackTrace();
        int min = Math.min(i, stackTrace.length - 1);
        String str3 = " ";
        for (int i2 = 1; i2 <= min; i2++) {
            StackTraceElement stackTraceElement = stackTrace[i2];
            stackTrace[i2].getFileName();
            stackTrace[i2].getLineNumber();
            stackTrace[i2].getMethodName();
            str3 = str3 + " ";
        }
    }

    public static void printStack(String str, int i) {
        StackTraceElement[] stackTrace = new Throwable().getStackTrace();
        int min = Math.min(i, stackTrace.length - 1);
        String str2 = " ";
        for (int i2 = 1; i2 <= min; i2++) {
            StackTraceElement stackTraceElement = stackTrace[i2];
            StringBuilder m9240 = lf2.m9240(".(");
            m9240.append(stackTrace[i2].getFileName());
            m9240.append(":");
            m9240.append(stackTrace[i2].getLineNumber());
            m9240.append(") ");
            String sb = m9240.toString();
            str2 = C4118lz.m9496(str2, " ");
            System.out.println(str + str2 + sb + str2);
        }
    }

    public static void dumpLayoutParams(ViewGroup viewGroup, String str) {
        StackTraceElement stackTraceElement = new Throwable().getStackTrace()[1];
        StringBuilder m9240 = lf2.m9240(".(");
        m9240.append(stackTraceElement.getFileName());
        m9240.append(":");
        m9240.append(stackTraceElement.getLineNumber());
        m9240.append(") ");
        m9240.append(str);
        m9240.append("  ");
        String sb = m9240.toString();
        int childCount = viewGroup.getChildCount();
        System.out.println(str + " children " + childCount);
        for (int i = 0; i < childCount; i++) {
            View childAt = viewGroup.getChildAt(i);
            PrintStream printStream = System.out;
            StringBuilder m14009 = C5717xl.m14009(sb, "     ");
            m14009.append(getName(childAt));
            printStream.println(m14009.toString());
            ViewGroup.LayoutParams layoutParams = childAt.getLayoutParams();
            for (Field field : layoutParams.getClass().getFields()) {
                try {
                    Object obj = field.get(layoutParams);
                    if (field.getName().contains("To") && !obj.toString().equals("-1")) {
                        System.out.println(sb + "       " + field.getName() + " " + obj);
                    }
                } catch (IllegalAccessException unused) {
                }
            }
        }
    }
}
