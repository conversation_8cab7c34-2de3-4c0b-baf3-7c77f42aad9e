package android.view;

import android.view.View;
import kotlin.Metadata;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.lv1;
import okhttp3.internal.p042io.ph0;
import okhttp3.internal.p042io.wv2;
import okhttp3.internal.p042io.zu2;

@Metadata(m4114d1 = {"\u0000\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u0010\u0000\u001a\u0004\u0018\u00010\u00012\u0006\u0010\u0002\u001a\u00020\u0003H\n¢\u0006\u0002\b\u0004"}, m4115d2 = {"<anonymous>", "Landroidx/savedstate/SavedStateRegistryOwner;", "view", "Landroid/view/View;", "invoke"}, m4116k = 3, m4117mv = {1, 6, 0}, m4119xi = 48)
/* renamed from: androidx.savedstate.ViewTreeSavedStateRegistryOwner$findViewTreeSavedStateRegistryOwner$2 */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class C0695x10fac0e3 extends lv1 implements ph0<View, SavedStateRegistryOwner> {
    public static final C0695x10fac0e3 INSTANCE = new C0695x10fac0e3();

    public C0695x10fac0e3() {
        super(1);
    }

    @Override // okhttp3.internal.p042io.ph0
    @wv2
    public final SavedStateRegistryOwner invoke(@zu2 View view) {
        fa1.m6826(view, "view");
        Object tag = view.getTag(C0693R.id.view_tree_saved_state_registry_owner);
        if (tag instanceof SavedStateRegistryOwner) {
            return (SavedStateRegistryOwner) tag;
        }
        return null;
    }
}
