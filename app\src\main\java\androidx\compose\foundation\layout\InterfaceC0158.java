package androidx.compose.foundation.layout;

import kotlin.Metadata;
import okhttp3.internal.p042io.ax1;
import okhttp3.internal.p042io.bf2;
import okhttp3.internal.p042io.ue2;
import okhttp3.internal.p042io.zu2;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000\n\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\bb\u0018\u00002\u00020\u0001ø\u0001\u0000\u0082\u0002\u0006\n\u0004\b!0\u0001¨\u0006\u0002À\u0006\u0001"}, m4115d2 = {"Landroidx/compose/foundation/layout/Ԩ;", "Lokhttp3/internal/io/ax1;", "foundation-layout_release"}, m4116k = 1, m4117mv = {1, 6, 0})
/* renamed from: androidx.compose.foundation.layout.Ԩ */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
interface InterfaceC0158 extends ax1 {
    /* renamed from: ޕ */
    long mo14(@zu2 bf2 bf2Var, @zu2 ue2 ue2Var, long j);

    /* renamed from: ࢥ */
    void mo15();
}
