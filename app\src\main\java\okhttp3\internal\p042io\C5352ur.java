package okhttp3.internal.p042io;

import java.util.List;
import java.util.Set;

/* renamed from: okhttp3.internal.io.ur */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5352ur implements a54<InterfaceC4949rr> {

    /* renamed from: Ϳ */
    @pu2
    public final g54 f23433;

    /* renamed from: okhttp3.internal.io.ur$Ϳ, reason: contains not printable characters */
    public class C9411 extends AbstractC7258 {

        /* renamed from: ၥ */
        @pu2
        public InterfaceC7239 f23434;

        public C9411(@pu2 InterfaceC7239 interfaceC7239) {
            this.f23434 = interfaceC7239;
        }

        @Override // okhttp3.internal.p042io.InterfaceC7239
        @pu2
        public final Set<? extends InterfaceC6917> getElements() {
            return new b54(this.f23434.getElements(), C5352ur.this.f23433.mo7231());
        }

        @Override // okhttp3.internal.p042io.InterfaceC7239
        @pu2
        public final String getType() {
            return C5352ur.this.f23433.mo7232().mo4402(this.f23434.getType());
        }
    }

    /* renamed from: okhttp3.internal.io.ur$Ԩ, reason: contains not printable characters */
    public class C9412 extends AbstractC6754 {

        /* renamed from: ၥ */
        @pu2
        public InterfaceC6034 f23436;

        public C9412(@pu2 InterfaceC6034 interfaceC6034) {
            this.f23436 = interfaceC6034;
        }

        @Override // okhttp3.internal.p042io.InterfaceC6034
        @pu2
        public final List<? extends InterfaceC4949rr> getValue() {
            return new c54(C5352ur.this.f23433.mo7238(), this.f23436.getValue());
        }
    }

    /* renamed from: okhttp3.internal.io.ur$Ԫ, reason: contains not printable characters */
    public class C9413 extends AbstractC7836 {

        /* renamed from: ၥ */
        @pu2
        public InterfaceC3771it f23438;

        public C9413(@pu2 InterfaceC3771it interfaceC3771it) {
            this.f23438 = interfaceC3771it;
        }

        @Override // okhttp3.internal.p042io.InterfaceC3771it
        @pu2
        public final j20 getValue() {
            return C5352ur.this.f23433.mo7229().mo4402(this.f23438.getValue());
        }
    }

    /* renamed from: okhttp3.internal.io.ur$Ԭ, reason: contains not printable characters */
    public class C9414 extends AbstractC7145 {

        /* renamed from: ၥ */
        @pu2
        public u10 f23440;

        public C9414(@pu2 u10 u10Var) {
            this.f23440 = u10Var;
        }

        @Override // okhttp3.internal.p042io.u10
        @pu2
        public final j20 getValue() {
            return C5352ur.this.f23433.mo7229().mo4402(this.f23440.getValue());
        }
    }

    /* renamed from: okhttp3.internal.io.ur$Ԯ, reason: contains not printable characters */
    public class C9415 extends AbstractC6526 {

        /* renamed from: ၥ */
        @pu2
        public di2 f23442;

        public C9415(@pu2 di2 di2Var) {
            this.f23442 = di2Var;
        }

        @Override // okhttp3.internal.p042io.di2
        @pu2
        public final xi2 getValue() {
            return C5352ur.this.f23433.mo7227().mo4402(this.f23442.getValue());
        }
    }

    /* renamed from: okhttp3.internal.io.ur$֏, reason: contains not printable characters */
    public class C9416 extends AbstractC7655 {

        /* renamed from: ၥ */
        @pu2
        public xr5 f23444;

        public C9416(@pu2 xr5 xr5Var) {
            this.f23444 = xr5Var;
        }

        @Override // okhttp3.internal.p042io.xr5
        @pu2
        public final String getValue() {
            return C5352ur.this.f23433.mo7232().mo4402(this.f23444.getValue());
        }
    }

    public C5352ur(@pu2 g54 g54Var) {
        this.f23433 = g54Var;
    }

    @Override // okhttp3.internal.p042io.a54
    @pu2
    /* renamed from: Ϳ */
    public final InterfaceC4949rr mo4402(@pu2 InterfaceC4949rr interfaceC4949rr) {
        InterfaceC4949rr c9416;
        InterfaceC4949rr interfaceC4949rr2 = interfaceC4949rr;
        switch (interfaceC4949rr2.mo4973()) {
            case 24:
                c9416 = new C9416((xr5) interfaceC4949rr2);
                break;
            case 25:
                c9416 = new C9414((u10) interfaceC4949rr2);
                break;
            case 26:
                c9416 = new C9415((di2) interfaceC4949rr2);
                break;
            case 27:
                c9416 = new C9413((InterfaceC3771it) interfaceC4949rr2);
                break;
            case 28:
                c9416 = new C9412((InterfaceC6034) interfaceC4949rr2);
                break;
            case 29:
                c9416 = new C9411((InterfaceC7239) interfaceC4949rr2);
                break;
            default:
                return interfaceC4949rr2;
        }
        return c9416;
    }
}
