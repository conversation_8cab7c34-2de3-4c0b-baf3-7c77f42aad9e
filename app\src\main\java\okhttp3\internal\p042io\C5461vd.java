package okhttp3.internal.p042io;

import com.google.common.collect.AbstractC1588;
import com.google.common.collect.AbstractC1592;
import com.google.common.collect.C1605;
import java.util.Collection;
import java.util.EnumSet;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import okhttp3.internal.p042io.AbstractC6243;

/* renamed from: okhttp3.internal.io.vd */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5461vd extends AbstractC6433 implements ph2 {

    /* renamed from: ၥ */
    @pu2
    public final C3318fc f24046;

    /* renamed from: ၦ */
    @pu2
    public final C5575wb f24047;

    /* renamed from: ၮ */
    public final int f24048;

    /* renamed from: ၯ */
    public final int f24049;

    /* renamed from: ၰ */
    public final int f24050;

    /* renamed from: ၵ */
    public final int f24051;

    /* renamed from: ၶ */
    public final int f24052;

    /* renamed from: ၷ */
    public final int f24053;

    /* renamed from: ၸ */
    public int f24054;

    /* renamed from: ၹ */
    public int f24055;

    /* renamed from: ၺ */
    public int f24056 = -1;

    /* renamed from: okhttp3.internal.io.vd$Ϳ, reason: contains not printable characters */
    public class C9443 extends AbstractC7022<si2> {

        /* renamed from: ၥ */
        public final /* synthetic */ List f24057;

        public C9443(List list) {
            this.f24057 = list;
        }

        @Override // okhttp3.internal.p042io.AbstractC7022, java.util.AbstractSequentialList, java.util.AbstractList, java.util.AbstractCollection, java.util.Collection, java.lang.Iterable, java.util.List
        @pu2
        public final Iterator<si2> iterator() {
            C5920zd c5920zd;
            List list = this.f24057;
            List<? extends Set<? extends C4492pb>> m13197 = C5461vd.this.m13197();
            C5461vd c5461vd = C5461vd.this;
            int i = c5461vd.f24049;
            if (i > 0) {
                C3318fc c3318fc = c5461vd.f24046;
                c5920zd = c3318fc.mo6870(c3318fc, c5461vd, i);
            } else {
                c5920zd = null;
            }
            return new n73(list, m13197, c5920zd != null ? c5920zd.m14495().mo8500() : C1605.f2792.iterator());
        }

        @Override // java.util.AbstractCollection, java.util.Collection, java.util.List
        public final int size() {
            return this.f24057.size();
        }
    }

    /* renamed from: okhttp3.internal.io.vd$Ԩ, reason: contains not printable characters */
    public class C9444 extends i60<String> {

        /* renamed from: ၥ */
        public final /* synthetic */ int f24059;

        /* renamed from: ၦ */
        public final /* synthetic */ int f24060;

        public C9444(int i, int i2) {
            this.f24059 = i;
            this.f24060 = i2;
        }

        @Override // java.util.AbstractCollection, java.util.Collection, java.util.List
        public final int size() {
            return this.f24060;
        }

        /* JADX WARN: Type inference failed for: r1v0, types: [okhttp3.internal.io.fc$֏, okhttp3.internal.io.fc$ބ<java.lang.String>] */
        @Override // okhttp3.internal.p042io.i60
        @pu2
        /* renamed from: Ϳ */
        public final String mo4590(int i) {
            C3318fc c3318fc = C5461vd.this.f24046;
            return c3318fc.f9960.get(c3318fc.f9943.m11105((i * 2) + this.f24059));
        }
    }

    public C5461vd(@pu2 C3318fc c3318fc, @pu2 C4694qf c4694qf, @pu2 C5575wb c5575wb, int i, @pu2 AbstractC6243.InterfaceC9621 interfaceC9621, @pu2 AbstractC6243.InterfaceC9621 interfaceC96212, int i2) {
        this.f24046 = c3318fc;
        this.f24047 = c5575wb;
        Objects.requireNonNull(c4694qf);
        int m11120 = c4694qf.m11120(true) + i;
        this.f24053 = m11120;
        this.f24048 = c4694qf.m11120(false);
        this.f24049 = c4694qf.m11120(false);
        this.f24052 = i2;
        this.f24051 = interfaceC9621.mo14999(m11120);
        this.f24050 = interfaceC96212.mo14999(m11120);
    }

    @Override // okhttp3.internal.p042io.ph2
    @pu2
    public final Set<? extends InterfaceC6280> getAnnotations() {
        return AbstractC6243.m14994(this.f24046, this.f24051);
    }

    /* JADX WARN: Type inference failed for: r0v5, types: [okhttp3.internal.io.fc$ހ, okhttp3.internal.io.fc$ނ<okhttp3.internal.io.be>] */
    /* JADX WARN: Type inference failed for: r1v0, types: [okhttp3.internal.io.fc$Ԯ, okhttp3.internal.io.fc$ބ<java.lang.String>] */
    @Override // okhttp3.internal.p042io.xi2
    @pu2
    public final String getName() {
        C3318fc c3318fc = this.f24046;
        ?? r1 = c3318fc.f9959;
        C4670qe c4670qe = c3318fc.f9942;
        if (this.f24054 == 0) {
            this.f24054 = c3318fc.f9962.m6886(this.f24053);
        }
        return r1.get(c4670qe.m11103(this.f24054 + 4));
    }

    @Override // okhttp3.internal.p042io.ph2
    @pu2
    public final List<? extends si2> getParameters() {
        if (m13198() > 0) {
            return new C9443(mo5125());
        }
        AbstractC7161 abstractC7161 = AbstractC1588.f2751;
        return zy3.f27827;
    }

    /* JADX WARN: Type inference failed for: r1v0, types: [okhttp3.internal.io.fc$֏, okhttp3.internal.io.fc$ބ<java.lang.String>] */
    @Override // okhttp3.internal.p042io.xi2
    @pu2
    public final String getReturnType() {
        C3318fc c3318fc = this.f24046;
        return c3318fc.f9960.get(c3318fc.f9942.m11103(m13199() + 4));
    }

    @Override // okhttp3.internal.p042io.ph2
    /* renamed from: Ϳ */
    public final int mo8250() {
        return this.f24048;
    }

    @Override // okhttp3.internal.p042io.xi2
    @pu2
    /* renamed from: Ԩ */
    public final String mo5124() {
        return this.f24047.getType();
    }

    @Override // okhttp3.internal.p042io.xi2
    @pu2
    /* renamed from: Ԫ */
    public final List<String> mo5125() {
        int m13198 = m13198();
        if (m13198 > 0) {
            return new C9444(m13198 + 4, this.f24046.f9943.m11103(m13198 + 0));
        }
        AbstractC7161 abstractC7161 = AbstractC1588.f2751;
        return zy3.f27827;
    }

    @Override // okhttp3.internal.p042io.ph2
    @pu2
    /* renamed from: Ԯ */
    public final Set<yr0> mo8251() {
        int i = this.f24052;
        if (i != 7) {
            return EnumSet.copyOf((Collection) yr0.m14366(i));
        }
        int i2 = AbstractC1592.f2771;
        return C1605.f2792;
    }

    @Override // okhttp3.internal.p042io.ph2
    @vv2
    /* renamed from: ކ */
    public final mi2 mo8252() {
        int i = this.f24049;
        if (i <= 0) {
            return null;
        }
        C3318fc c3318fc = this.f24046;
        return c3318fc.mo6870(c3318fc, this, i);
    }

    @pu2
    /* renamed from: ސ */
    public final List<? extends Set<? extends C4492pb>> m13197() {
        C3318fc c3318fc = this.f24046;
        int i = this.f24050;
        if (i > 0) {
            return new C6428(c3318fc, i, c3318fc.f9943.m11103(i));
        }
        AbstractC7161 abstractC7161 = AbstractC1588.f2751;
        return zy3.f27827;
    }

    /* renamed from: ޑ */
    public final int m13198() {
        if (this.f24056 == -1) {
            this.f24056 = this.f24046.f9942.m11103(m13199() + 8);
        }
        return this.f24056;
    }

    /* JADX WARN: Type inference failed for: r0v7, types: [okhttp3.internal.io.fc$ހ, okhttp3.internal.io.fc$ނ<okhttp3.internal.io.be>] */
    /* JADX WARN: Type inference failed for: r1v2, types: [okhttp3.internal.io.fc$ށ, okhttp3.internal.io.fc$ނ<okhttp3.internal.io.ae>] */
    /* renamed from: ޓ */
    public final int m13199() {
        if (this.f24055 == 0) {
            C3318fc c3318fc = this.f24046;
            C4670qe c4670qe = c3318fc.f9942;
            if (this.f24054 == 0) {
                this.f24054 = c3318fc.f9962.m6886(this.f24053);
            }
            this.f24055 = this.f24046.f9963.m6887(c4670qe.m11105(this.f24054 + 2));
        }
        return this.f24055;
    }
}
