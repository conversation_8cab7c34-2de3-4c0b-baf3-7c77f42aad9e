package androidx.core.graphics;

import android.annotation.SuppressLint;
import android.graphics.Matrix;
import android.graphics.Point;
import android.graphics.PointF;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.Region;
import kotlin.Metadata;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.zu2;

@Metadata(m4114d1 = {"\u0000<\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\u0010\u0007\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a\u0015\u0010\u0000\u001a\u00020\u0001*\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0001H\u0087\f\u001a\u0015\u0010\u0000\u001a\u00020\u0003*\u00020\u00032\u0006\u0010\u0002\u001a\u00020\u0003H\u0087\f\u001a\r\u0010\u0004\u001a\u00020\u0005*\u00020\u0001H\u0086\n\u001a\r\u0010\u0004\u001a\u00020\u0006*\u00020\u0003H\u0086\n\u001a\r\u0010\u0007\u001a\u00020\u0005*\u00020\u0001H\u0086\n\u001a\r\u0010\u0007\u001a\u00020\u0006*\u00020\u0003H\u0086\n\u001a\r\u0010\b\u001a\u00020\u0005*\u00020\u0001H\u0086\n\u001a\r\u0010\b\u001a\u00020\u0006*\u00020\u0003H\u0086\n\u001a\r\u0010\t\u001a\u00020\u0005*\u00020\u0001H\u0086\n\u001a\r\u0010\t\u001a\u00020\u0006*\u00020\u0003H\u0086\n\u001a\u0015\u0010\n\u001a\u00020\u000b*\u00020\u00012\u0006\u0010\f\u001a\u00020\rH\u0086\n\u001a\u0015\u0010\n\u001a\u00020\u000b*\u00020\u00032\u0006\u0010\f\u001a\u00020\u000eH\u0086\n\u001a\u0015\u0010\u000f\u001a\u00020\u0001*\u00020\u00012\u0006\u0010\u0010\u001a\u00020\rH\u0086\n\u001a\u0015\u0010\u000f\u001a\u00020\u0011*\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0001H\u0086\n\u001a\u0015\u0010\u000f\u001a\u00020\u0001*\u00020\u00012\u0006\u0010\u0010\u001a\u00020\u0005H\u0086\n\u001a\u0015\u0010\u000f\u001a\u00020\u0003*\u00020\u00032\u0006\u0010\u0010\u001a\u00020\u000eH\u0086\n\u001a\u0015\u0010\u000f\u001a\u00020\u0011*\u00020\u00032\u0006\u0010\u0002\u001a\u00020\u0003H\u0086\n\u001a\u0015\u0010\u000f\u001a\u00020\u0003*\u00020\u00032\u0006\u0010\u0010\u001a\u00020\u0006H\u0086\n\u001a\u0015\u0010\u0012\u001a\u00020\u0001*\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0001H\u0086\f\u001a\u0015\u0010\u0012\u001a\u00020\u0003*\u00020\u00032\u0006\u0010\u0002\u001a\u00020\u0003H\u0086\f\u001a\u0015\u0010\u0013\u001a\u00020\u0001*\u00020\u00012\u0006\u0010\u0010\u001a\u00020\rH\u0086\n\u001a\u0015\u0010\u0013\u001a\u00020\u0001*\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0001H\u0086\n\u001a\u0015\u0010\u0013\u001a\u00020\u0001*\u00020\u00012\u0006\u0010\u0010\u001a\u00020\u0005H\u0086\n\u001a\u0015\u0010\u0013\u001a\u00020\u0003*\u00020\u00032\u0006\u0010\u0010\u001a\u00020\u000eH\u0086\n\u001a\u0015\u0010\u0013\u001a\u00020\u0003*\u00020\u00032\u0006\u0010\u0002\u001a\u00020\u0003H\u0086\n\u001a\u0015\u0010\u0013\u001a\u00020\u0003*\u00020\u00032\u0006\u0010\u0010\u001a\u00020\u0006H\u0086\n\u001a\u0015\u0010\u0014\u001a\u00020\u0001*\u00020\u00012\u0006\u0010\u0015\u001a\u00020\u0005H\u0086\n\u001a\u0015\u0010\u0014\u001a\u00020\u0003*\u00020\u00032\u0006\u0010\u0015\u001a\u00020\u0006H\u0086\n\u001a\u0015\u0010\u0014\u001a\u00020\u0003*\u00020\u00032\u0006\u0010\u0015\u001a\u00020\u0005H\u0086\n\u001a\r\u0010\u0016\u001a\u00020\u0001*\u00020\u0003H\u0086\b\u001a\r\u0010\u0017\u001a\u00020\u0003*\u00020\u0001H\u0086\b\u001a\r\u0010\u0018\u001a\u00020\u0011*\u00020\u0001H\u0086\b\u001a\r\u0010\u0018\u001a\u00020\u0011*\u00020\u0003H\u0086\b\u001a\u0015\u0010\u0019\u001a\u00020\u0003*\u00020\u00032\u0006\u0010\u001a\u001a\u00020\u001bH\u0086\b\u001a\u0015\u0010\u001c\u001a\u00020\u0011*\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0001H\u0086\f\u001a\u0015\u0010\u001c\u001a\u00020\u0011*\u00020\u00032\u0006\u0010\u0002\u001a\u00020\u0003H\u0086\f¨\u0006\u001d"}, m4115d2 = {"and", "Landroid/graphics/Rect;", "r", "Landroid/graphics/RectF;", "component1", "", "", "component2", "component3", "component4", "contains", "", "p", "Landroid/graphics/Point;", "Landroid/graphics/PointF;", "minus", "xy", "Landroid/graphics/Region;", "or", "plus", "times", "factor", "toRect", "toRectF", "toRegion", "transform", "m", "Landroid/graphics/Matrix;", "xor", "core-ktx_release"}, m4116k = 2, m4117mv = {1, 6, 0}, m4119xi = 48)
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class RectKt {
    @SuppressLint({"CheckResult"})
    @zu2
    public static final Rect and(@zu2 Rect rect, @zu2 Rect rect2) {
        fa1.m6826(rect, "<this>");
        fa1.m6826(rect2, "r");
        Rect rect3 = new Rect(rect);
        rect3.intersect(rect2);
        return rect3;
    }

    @SuppressLint({"CheckResult"})
    @zu2
    public static final RectF and(@zu2 RectF rectF, @zu2 RectF rectF2) {
        fa1.m6826(rectF, "<this>");
        fa1.m6826(rectF2, "r");
        RectF rectF3 = new RectF(rectF);
        rectF3.intersect(rectF2);
        return rectF3;
    }

    public static final float component1(@zu2 RectF rectF) {
        fa1.m6826(rectF, "<this>");
        return rectF.left;
    }

    public static final int component1(@zu2 Rect rect) {
        fa1.m6826(rect, "<this>");
        return rect.left;
    }

    public static final float component2(@zu2 RectF rectF) {
        fa1.m6826(rectF, "<this>");
        return rectF.top;
    }

    public static final int component2(@zu2 Rect rect) {
        fa1.m6826(rect, "<this>");
        return rect.top;
    }

    public static final float component3(@zu2 RectF rectF) {
        fa1.m6826(rectF, "<this>");
        return rectF.right;
    }

    public static final int component3(@zu2 Rect rect) {
        fa1.m6826(rect, "<this>");
        return rect.right;
    }

    public static final float component4(@zu2 RectF rectF) {
        fa1.m6826(rectF, "<this>");
        return rectF.bottom;
    }

    public static final int component4(@zu2 Rect rect) {
        fa1.m6826(rect, "<this>");
        return rect.bottom;
    }

    public static final boolean contains(@zu2 Rect rect, @zu2 Point point) {
        fa1.m6826(rect, "<this>");
        fa1.m6826(point, "p");
        return rect.contains(point.x, point.y);
    }

    public static final boolean contains(@zu2 RectF rectF, @zu2 PointF pointF) {
        fa1.m6826(rectF, "<this>");
        fa1.m6826(pointF, "p");
        return rectF.contains(pointF.x, pointF.y);
    }

    @zu2
    public static final Rect minus(@zu2 Rect rect, int i) {
        fa1.m6826(rect, "<this>");
        Rect rect2 = new Rect(rect);
        int i2 = -i;
        rect2.offset(i2, i2);
        return rect2;
    }

    @zu2
    public static final Rect minus(@zu2 Rect rect, @zu2 Point point) {
        fa1.m6826(rect, "<this>");
        fa1.m6826(point, "xy");
        Rect rect2 = new Rect(rect);
        rect2.offset(-point.x, -point.y);
        return rect2;
    }

    @zu2
    public static final RectF minus(@zu2 RectF rectF, float f) {
        fa1.m6826(rectF, "<this>");
        RectF rectF2 = new RectF(rectF);
        float f2 = -f;
        rectF2.offset(f2, f2);
        return rectF2;
    }

    @zu2
    public static final RectF minus(@zu2 RectF rectF, @zu2 PointF pointF) {
        fa1.m6826(rectF, "<this>");
        fa1.m6826(pointF, "xy");
        RectF rectF2 = new RectF(rectF);
        rectF2.offset(-pointF.x, -pointF.y);
        return rectF2;
    }

    @zu2
    public static final Region minus(@zu2 Rect rect, @zu2 Rect rect2) {
        fa1.m6826(rect, "<this>");
        fa1.m6826(rect2, "r");
        Region region = new Region(rect);
        region.op(rect2, Region.Op.DIFFERENCE);
        return region;
    }

    @zu2
    public static final Region minus(@zu2 RectF rectF, @zu2 RectF rectF2) {
        fa1.m6826(rectF, "<this>");
        fa1.m6826(rectF2, "r");
        Rect rect = new Rect();
        rectF.roundOut(rect);
        Region region = new Region(rect);
        Rect rect2 = new Rect();
        rectF2.roundOut(rect2);
        region.op(rect2, Region.Op.DIFFERENCE);
        return region;
    }

    @zu2
    /* renamed from: or */
    public static final Rect m100or(@zu2 Rect rect, @zu2 Rect rect2) {
        fa1.m6826(rect, "<this>");
        fa1.m6826(rect2, "r");
        Rect rect3 = new Rect(rect);
        rect3.union(rect2);
        return rect3;
    }

    @zu2
    /* renamed from: or */
    public static final RectF m101or(@zu2 RectF rectF, @zu2 RectF rectF2) {
        fa1.m6826(rectF, "<this>");
        fa1.m6826(rectF2, "r");
        RectF rectF3 = new RectF(rectF);
        rectF3.union(rectF2);
        return rectF3;
    }

    @zu2
    public static final Rect plus(@zu2 Rect rect, int i) {
        fa1.m6826(rect, "<this>");
        Rect rect2 = new Rect(rect);
        rect2.offset(i, i);
        return rect2;
    }

    @zu2
    public static final Rect plus(@zu2 Rect rect, @zu2 Point point) {
        fa1.m6826(rect, "<this>");
        fa1.m6826(point, "xy");
        Rect rect2 = new Rect(rect);
        rect2.offset(point.x, point.y);
        return rect2;
    }

    @zu2
    public static final Rect plus(@zu2 Rect rect, @zu2 Rect rect2) {
        fa1.m6826(rect, "<this>");
        fa1.m6826(rect2, "r");
        Rect rect3 = new Rect(rect);
        rect3.union(rect2);
        return rect3;
    }

    @zu2
    public static final RectF plus(@zu2 RectF rectF, float f) {
        fa1.m6826(rectF, "<this>");
        RectF rectF2 = new RectF(rectF);
        rectF2.offset(f, f);
        return rectF2;
    }

    @zu2
    public static final RectF plus(@zu2 RectF rectF, @zu2 PointF pointF) {
        fa1.m6826(rectF, "<this>");
        fa1.m6826(pointF, "xy");
        RectF rectF2 = new RectF(rectF);
        rectF2.offset(pointF.x, pointF.y);
        return rectF2;
    }

    @zu2
    public static final RectF plus(@zu2 RectF rectF, @zu2 RectF rectF2) {
        fa1.m6826(rectF, "<this>");
        fa1.m6826(rectF2, "r");
        RectF rectF3 = new RectF(rectF);
        rectF3.union(rectF2);
        return rectF3;
    }

    @zu2
    public static final Rect times(@zu2 Rect rect, int i) {
        fa1.m6826(rect, "<this>");
        Rect rect2 = new Rect(rect);
        rect2.top *= i;
        rect2.left *= i;
        rect2.right *= i;
        rect2.bottom *= i;
        return rect2;
    }

    @zu2
    public static final RectF times(@zu2 RectF rectF, float f) {
        fa1.m6826(rectF, "<this>");
        RectF rectF2 = new RectF(rectF);
        rectF2.top *= f;
        rectF2.left *= f;
        rectF2.right *= f;
        rectF2.bottom *= f;
        return rectF2;
    }

    @zu2
    public static final RectF times(@zu2 RectF rectF, int i) {
        fa1.m6826(rectF, "<this>");
        float f = i;
        RectF rectF2 = new RectF(rectF);
        rectF2.top *= f;
        rectF2.left *= f;
        rectF2.right *= f;
        rectF2.bottom *= f;
        return rectF2;
    }

    @zu2
    public static final Rect toRect(@zu2 RectF rectF) {
        fa1.m6826(rectF, "<this>");
        Rect rect = new Rect();
        rectF.roundOut(rect);
        return rect;
    }

    @zu2
    public static final RectF toRectF(@zu2 Rect rect) {
        fa1.m6826(rect, "<this>");
        return new RectF(rect);
    }

    @zu2
    public static final Region toRegion(@zu2 Rect rect) {
        fa1.m6826(rect, "<this>");
        return new Region(rect);
    }

    @zu2
    public static final Region toRegion(@zu2 RectF rectF) {
        fa1.m6826(rectF, "<this>");
        Rect rect = new Rect();
        rectF.roundOut(rect);
        return new Region(rect);
    }

    @zu2
    public static final RectF transform(@zu2 RectF rectF, @zu2 Matrix matrix) {
        fa1.m6826(rectF, "<this>");
        fa1.m6826(matrix, "m");
        matrix.mapRect(rectF);
        return rectF;
    }

    @zu2
    public static final Region xor(@zu2 Rect rect, @zu2 Rect rect2) {
        fa1.m6826(rect, "<this>");
        fa1.m6826(rect2, "r");
        Region region = new Region(rect);
        region.op(rect2, Region.Op.XOR);
        return region;
    }

    @zu2
    public static final Region xor(@zu2 RectF rectF, @zu2 RectF rectF2) {
        fa1.m6826(rectF, "<this>");
        fa1.m6826(rectF2, "r");
        Rect rect = new Rect();
        rectF.roundOut(rect);
        Region region = new Region(rect);
        Rect rect2 = new Rect();
        rectF2.roundOut(rect2);
        region.op(rect2, Region.Op.XOR);
        return region;
    }
}
