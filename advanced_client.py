#!/usr/bin/env python3
"""
AutoJS9服务器高级客户端示例
展示各种功能的使用方法
"""

import asyncio
import json
import websockets
import uuid
import logging
from typing import Dict, Any, Optional

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AdvancedAutoJSClient:
    """高级AutoJS客户端"""
    
    def __init__(self, server_url: str = "ws://localhost:9317"):
        self.server_url = server_url
        self.websocket = None
        self.token = None
        self.device_id = str(uuid.uuid4())
        self.request_id = 0
    
    async def connect(self):
        """连接到服务器"""
        logger.info(f"连接到服务器: {self.server_url}")
        self.websocket = await websockets.connect(self.server_url)
        logger.info("连接成功")
    
    async def disconnect(self):
        """断开连接"""
        if self.websocket:
            await self.websocket.close()
            logger.info("连接已断开")
    
    def get_next_id(self) -> int:
        """获取下一个请求ID"""
        self.request_id += 1
        return self.request_id
    
    async def send_rpc(self, method: str, params: dict = None) -> dict:
        """发送RPC请求"""
        if not self.websocket:
            raise Exception("未连接到服务器")
        
        request = {
            "jsonrpc": "2.0",
            "method": method,
            "params": params or {},
            "id": self.get_next_id()
        }
        
        logger.info(f"发送RPC请求: {method}")
        await self.websocket.send(json.dumps(request))
        
        # 等待响应
        response_str = await self.websocket.recv()
        response = json.loads(response_str)
        
        if "error" in response:
            logger.error(f"RPC错误: {response['error']}")
            raise Exception(f"RPC错误: {response['error']['message']}")
        
        return response.get("result", {})
    
    async def authorize(self, device_name: str = "Advanced Test Client") -> bool:
        """请求授权"""
        logger.info("请求设备授权...")
        
        result = await self.send_rpc("debug.authorize", {
            "device_id": self.device_id,
            "device_name": device_name
        })
        
        if result.get("authorized"):
            self.token = result.get("token")
            logger.info(f"授权成功，获得token: {self.token[:8]}...")
            return True
        else:
            logger.error("授权失败")
            return False
    
    def _add_token(self, params: dict) -> dict:
        """为参数添加token"""
        if not self.token:
            raise Exception("未授权，请先调用authorize()")
        params = params.copy()
        params["token"] = self.token
        return params
    
    async def ping(self) -> dict:
        """发送ping请求"""
        return await self.send_rpc("ping", self._add_token({}))
    
    async def get_device_info(self) -> dict:
        """获取设备信息"""
        return await self.send_rpc("getDeviceInfo", self._add_token({}))
    
    async def execute_js_code(self, code: str) -> dict:
        """执行JavaScript代码"""
        return await self.send_rpc("execDebugCommand", self._add_token({
            "type": "eval",
            "data": code
        }))
    
    async def send_log(self, message: str) -> dict:
        """发送日志消息"""
        return await self.send_rpc("execDebugCommand", self._add_token({
            "type": "log",
            "data": message
        }))
    
    async def enable_vfs(self) -> dict:
        """启用虚拟文件系统"""
        return await self.send_rpc("enableVfs", self._add_token({}))
    
    async def enable_ftp_server(self) -> dict:
        """启用FTP服务器"""
        return await self.send_rpc("enableFtpServer", self._add_token({}))
    
    async def list_files(self, path: str = "/") -> dict:
        """列出文件"""
        return await self.send_rpc("listFiles", self._add_token({
            "path": path
        }))
    
    async def read_file(self, file_path: str) -> dict:
        """读取文件"""
        return await self.send_rpc("readFile", self._add_token({
            "path": file_path
        }))
    
    async def write_file(self, file_path: str, content: str) -> dict:
        """写入文件"""
        return await self.send_rpc("writeFile", self._add_token({
            "path": file_path,
            "content": content
        }))

async def demo_basic_functions():
    """演示基础功能"""
    print("\n=== 基础功能演示 ===")
    
    client = AdvancedAutoJSClient()
    
    try:
        await client.connect()
        
        if await client.authorize("Python高级客户端"):
            # 测试ping
            ping_result = await client.ping()
            print(f"✓ Ping: {ping_result}")
            
            # 获取设备信息
            device_info = await client.get_device_info()
            print(f"✓ 设备信息: {device_info}")
            
            # 发送日志
            await client.send_log("这是一条测试日志消息")
            print("✓ 日志发送成功")
            
        else:
            print("✗ 授权失败")
    
    except Exception as e:
        logger.error(f"基础功能演示失败: {e}")
    
    finally:
        await client.disconnect()

async def demo_file_operations():
    """演示文件操作"""
    print("\n=== 文件操作演示 ===")
    
    client = AdvancedAutoJSClient()
    
    try:
        await client.connect()
        
        if await client.authorize("文件操作客户端"):
            # 启用VFS
            vfs_result = await client.enable_vfs()
            print(f"✓ VFS启用: {vfs_result}")
            
            # 列出文件
            files = await client.list_files("/")
            print(f"✓ 文件列表: {files}")
            
            # 写入文件
            test_content = """
// AutoJS测试脚本
console.log("Hello from AutoJS!");

function greet(name) {
    return "Hello, " + name + "!";
}

console.log(greet("World"));
"""
            write_result = await client.write_file("/test.js", test_content)
            print(f"✓ 文件写入: {write_result}")
            
            # 读取文件
            read_result = await client.read_file("/test.js")
            print(f"✓ 文件读取: {read_result['path']} ({len(read_result['content'])} 字符)")
            
        else:
            print("✗ 授权失败")
    
    except Exception as e:
        logger.error(f"文件操作演示失败: {e}")
    
    finally:
        await client.disconnect()

async def demo_code_execution():
    """演示代码执行"""
    print("\n=== 代码执行演示 ===")
    
    client = AdvancedAutoJSClient()
    
    try:
        await client.connect()
        
        if await client.authorize("代码执行客户端"):
            # 执行简单表达式
            result1 = await client.execute_js_code("1 + 2 * 3")
            print(f"✓ 表达式计算: {result1}")
            
            # 执行函数定义和调用
            result2 = await client.execute_js_code("""
function fibonacci(n) {
    if (n <= 1) return n;
    return fibonacci(n - 1) + fibonacci(n - 2);
}
fibonacci(10);
""")
            print(f"✓ 函数执行: {result2}")
            
            # 执行日志输出
            result3 = await client.execute_js_code('console.log("Hello from executed code!")')
            print(f"✓ 日志输出: {result3}")
            
        else:
            print("✗ 授权失败")
    
    except Exception as e:
        logger.error(f"代码执行演示失败: {e}")
    
    finally:
        await client.disconnect()

async def demo_service_features():
    """演示服务功能"""
    print("\n=== 服务功能演示 ===")
    
    client = AdvancedAutoJSClient()
    
    try:
        await client.connect()
        
        if await client.authorize("服务功能客户端"):
            # 启用FTP服务器
            ftp_result = await client.enable_ftp_server()
            print(f"✓ FTP服务器: {ftp_result}")
            
            if ftp_result.get("enabled"):
                print(f"  - 端口: {ftp_result.get('port')}")
                print(f"  - 用户名: {ftp_result.get('username')}")
                print(f"  - 密码: {ftp_result.get('password')}")
            
        else:
            print("✗ 授权失败")
    
    except Exception as e:
        logger.error(f"服务功能演示失败: {e}")
    
    finally:
        await client.disconnect()

async def main():
    """主函数"""
    print("=== AutoJS9高级客户端演示 ===")
    print("请确保服务器已启动 (python autojs_server.py)")
    print("在每个演示中，请在服务器控制台输入 'y' 来授权设备")
    
    # 运行各种演示
    await demo_basic_functions()
    await demo_file_operations()
    await demo_code_execution()
    await demo_service_features()
    
    print("\n=== 演示完成 ===")

if __name__ == "__main__":
    asyncio.run(main())
