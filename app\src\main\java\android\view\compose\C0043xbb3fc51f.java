package android.view.compose;

import android.view.OnBackPressedDispatcherOwner;
import kotlin.Metadata;
import okhttp3.internal.p042io.lv1;
import okhttp3.internal.p042io.nh0;
import okhttp3.internal.p042io.wv2;

@Metadata(m4114d1 = {"\u0000\b\n\u0000\n\u0002\u0018\u0002\n\u0000\u0010\u0000\u001a\u0004\u0018\u00010\u0001H\n¢\u0006\u0002\b\u0002"}, m4115d2 = {"<anonymous>", "Landroidx/activity/OnBackPressedDispatcherOwner;", "invoke"}, m4116k = 3, m4117mv = {1, 6, 0}, m4119xi = 48)
/* renamed from: androidx.activity.compose.LocalOnBackPressedDispatcherOwner$LocalOnBackPressedDispatcherOwner$1 */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class C0043xbb3fc51f extends lv1 implements nh0<OnBackPressedDispatcherOwner> {
    public static final C0043xbb3fc51f INSTANCE = new C0043xbb3fc51f();

    public C0043xbb3fc51f() {
        super(0);
    }

    /* JADX WARN: Can't rename method to resolve collision */
    @Override // okhttp3.internal.p042io.nh0
    @wv2
    public final OnBackPressedDispatcherOwner invoke() {
        return null;
    }
}
