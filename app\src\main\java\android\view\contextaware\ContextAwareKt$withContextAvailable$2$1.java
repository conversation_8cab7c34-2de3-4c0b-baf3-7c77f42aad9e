package android.view.contextaware;

import kotlin.Metadata;
import okhttp3.internal.p042io.lv1;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.ph0;
import okhttp3.internal.p042io.wv2;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000\u0010\n\u0000\n\u0002\u0010\u0003\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\u0010\u0006\u001a\u00020\u0003\"\u0004\b\u0000\u0010\u00002\b\u0010\u0002\u001a\u0004\u0018\u00010\u0001H\n¢\u0006\u0004\b\u0004\u0010\u0005"}, m4115d2 = {"R", "", "it", "Lokhttp3/internal/io/lx5;", "invoke", "(Ljava/lang/Throwable;)V", "<anonymous>"}, m4116k = 3, m4117mv = {1, 6, 0})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class ContextAwareKt$withContextAvailable$2$1 extends lv1 implements ph0<Throwable, lx5> {
    public final /* synthetic */ ContextAwareKt$withContextAvailable$2$listener$1 $listener;
    public final /* synthetic */ ContextAware $this_withContextAvailable;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public ContextAwareKt$withContextAvailable$2$1(ContextAware contextAware, ContextAwareKt$withContextAvailable$2$listener$1 contextAwareKt$withContextAvailable$2$listener$1) {
        super(1);
        this.$this_withContextAvailable = contextAware;
        this.$listener = contextAwareKt$withContextAvailable$2$listener$1;
    }

    @Override // okhttp3.internal.p042io.ph0
    public /* bridge */ /* synthetic */ lx5 invoke(Throwable th) {
        invoke2(th);
        return lx5.f14876;
    }

    /* renamed from: invoke, reason: avoid collision after fix types in other method */
    public final void invoke2(@wv2 Throwable th) {
        this.$this_withContextAvailable.removeOnContextAvailableListener(this.$listener);
    }
}
