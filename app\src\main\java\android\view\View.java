package android.view;

import kotlin.Metadata;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.uk4;
import okhttp3.internal.p042io.wv2;
import okhttp3.internal.p042io.yk4;
import okhttp3.internal.p042io.yo1;
import okhttp3.internal.p042io.zu2;

@yo1
@Metadata(m4113bv = {}, m4114d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\u001a\u001b\u0010\u0006\u001a\u00020\u0003*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u0001H\u0007¢\u0006\u0004\b\u0004\u0010\u0005\u001a\u0015\u0010\t\u001a\u0004\u0018\u00010\u0001*\u00020\u0000H\u0007¢\u0006\u0004\b\u0007\u0010\b¨\u0006\n"}, m4115d2 = {"Landroid/view/View;", "Landroidx/activity/OnBackPressedDispatcherOwner;", "onBackPressedDispatcherOwner", "Lokhttp3/internal/io/lx5;", "set", "(Landroid/view/View;Landroidx/activity/OnBackPressedDispatcherOwner;)V", "setViewTreeOnBackPressedDispatcherOwner", "get", "(Landroid/view/View;)Landroidx/activity/OnBackPressedDispatcherOwner;", "findViewTreeOnBackPressedDispatcherOwner", "activity_release"}, m4116k = 2, m4117mv = {1, 6, 0})
/* renamed from: androidx.activity.ViewTreeOnBackPressedDispatcherOwner, reason: from Kotlin metadata */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class View {
    @yo1
    @wv2
    public static final OnBackPressedDispatcherOwner get(@zu2 android.view.View view) {
        fa1.m6826(view, "<this>");
        return (OnBackPressedDispatcherOwner) yk4.m14308(yk4.m14311(uk4.m12898(view, C0040x8c7c000a.INSTANCE), C0041x8c7c000b.INSTANCE));
    }

    @yo1
    public static final void set(@zu2 android.view.View view, @zu2 OnBackPressedDispatcherOwner onBackPressedDispatcherOwner) {
        fa1.m6826(view, "<this>");
        fa1.m6826(onBackPressedDispatcherOwner, "onBackPressedDispatcherOwner");
        view.setTag(C0039R.id.view_tree_on_back_pressed_dispatcher_owner, onBackPressedDispatcherOwner);
    }
}
