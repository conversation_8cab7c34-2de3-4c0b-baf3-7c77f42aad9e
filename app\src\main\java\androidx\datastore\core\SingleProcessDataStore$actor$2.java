package androidx.datastore.core;

import androidx.core.app.NotificationCompat;
import androidx.datastore.core.SingleProcessDataStore;
import androidx.exifinterface.media.ExifInterface;
import java.util.concurrent.CancellationException;
import kotlin.Metadata;
import okhttp3.internal.p042io.InterfaceC6260;
import okhttp3.internal.p042io.di0;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.lv1;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.wv2;
import okhttp3.internal.p042io.zu2;

/* JADX INFO: Add missing generic type declarations: [T] */
@Metadata(m4113bv = {}, m4114d1 = {"\u0000\u0014\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0003\n\u0000\n\u0002\u0018\u0002\n\u0000\u0010\u0006\u001a\u00020\u0005\"\u0004\b\u0000\u0010\u00002\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00028\u00000\u00012\b\u0010\u0004\u001a\u0004\u0018\u00010\u0003H\n"}, m4115d2 = {ExifInterface.GPS_DIRECTION_TRUE, "Landroidx/datastore/core/SingleProcessDataStore$Message;", NotificationCompat.CATEGORY_MESSAGE, "", "ex", "Lokhttp3/internal/io/lx5;", "<anonymous>"}, m4116k = 3, m4117mv = {1, 5, 1})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class SingleProcessDataStore$actor$2<T> extends lv1 implements di0<SingleProcessDataStore.Message<T>, Throwable, lx5> {
    public static final SingleProcessDataStore$actor$2 INSTANCE = new SingleProcessDataStore$actor$2();

    public SingleProcessDataStore$actor$2() {
        super(2);
    }

    @Override // okhttp3.internal.p042io.di0
    /* renamed from: invoke */
    public /* bridge */ /* synthetic */ lx5 mo18338invoke(Object obj, Throwable th) {
        invoke((SingleProcessDataStore.Message) obj, th);
        return lx5.f14876;
    }

    public final void invoke(@zu2 SingleProcessDataStore.Message<T> message, @wv2 Throwable th) {
        fa1.m6826(message, NotificationCompat.CATEGORY_MESSAGE);
        if (message instanceof SingleProcessDataStore.Message.Update) {
            InterfaceC6260<T> ack = ((SingleProcessDataStore.Message.Update) message).getAck();
            if (th == null) {
                th = new CancellationException("DataStore scope was cancelled before updateData could complete");
            }
            ack.mo15020(th);
        }
    }
}
