package android.view.compose;

import android.view.result.ActivityResultRegistry;
import android.view.result.ActivityResultRegistryOwner;
import android.view.result.contract.ActivityResultContract;
import kotlin.Metadata;
import okhttp3.internal.p042io.C4173mh;
import okhttp3.internal.p042io.C7171;
import okhttp3.internal.p042io.InterfaceC6575;
import okhttp3.internal.p042io.InterfaceC6968;
import okhttp3.internal.p042io.InterfaceC7452;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.fi0;
import okhttp3.internal.p042io.g05;
import okhttp3.internal.p042io.hz3;
import okhttp3.internal.p042io.lu4;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.mz3;
import okhttp3.internal.p042io.ov4;
import okhttp3.internal.p042io.ph0;
import okhttp3.internal.p042io.wv2;
import okhttp3.internal.p042io.zu2;
import org.mozilla.javascript.optimizer.OptRuntime;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000\u001c\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\u001aO\u0010\b\u001a\u000e\u0012\u0004\u0012\u00028\u0000\u0012\u0004\u0012\u00028\u00010\u0007\"\u0004\b\u0000\u0010\u0000\"\u0004\b\u0001\u0010\u00012\u0012\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00028\u0000\u0012\u0004\u0012\u00028\u00010\u00022\u0012\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00028\u0001\u0012\u0004\u0012\u00020\u00050\u0004H\u0007¢\u0006\u0004\b\b\u0010\t¨\u0006\n"}, m4115d2 = {OptRuntime.GeneratorState.resumptionPoint_TYPE, "O", "Landroidx/activity/result/contract/ActivityResultContract;", "contract", "Lkotlin/Function1;", "Lokhttp3/internal/io/lx5;", "onResult", "Landroidx/activity/compose/ManagedActivityResultLauncher;", "rememberLauncherForActivityResult", "(Landroidx/activity/result/contract/ActivityResultContract;Lokhttp3/internal/io/ph0;Lokhttp3/internal/io/ࡊ;I)Landroidx/activity/compose/ManagedActivityResultLauncher;", "activity-compose_release"}, m4116k = 2, m4117mv = {1, 6, 0})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class ActivityResultRegistryKt {
    @InterfaceC7452
    @zu2
    public static final <I, O> ManagedActivityResultLauncher<I, O> rememberLauncherForActivityResult(@zu2 ActivityResultContract<I, O> activityResultContract, @zu2 ph0<? super O, lx5> ph0Var, @wv2 InterfaceC6968 interfaceC6968, int i) {
        fa1.m6826(activityResultContract, "contract");
        fa1.m6826(ph0Var, "onResult");
        interfaceC6968.mo16268(-1408504823);
        g05 m10511 = ov4.m10511(activityResultContract, interfaceC6968);
        g05 m105112 = ov4.m10511(ph0Var, interfaceC6968);
        Object m9827 = mz3.m9827(new Object[0], null, ActivityResultRegistryKt$rememberLauncherForActivityResult$key$1.INSTANCE, interfaceC6968, 6);
        fa1.m6825(m9827, "rememberSaveable { UUID.randomUUID().toString() }");
        String str = (String) m9827;
        ActivityResultRegistryOwner current = LocalActivityResultRegistryOwner.INSTANCE.getCurrent(interfaceC6968, 6);
        if (current == null) {
            throw new IllegalStateException("No ActivityResultRegistryOwner was provided via LocalActivityResultRegistryOwner".toString());
        }
        ActivityResultRegistry activityResultRegistry = current.getActivityResultRegistry();
        fa1.m6825(activityResultRegistry, "checkNotNull(LocalActivi… }.activityResultRegistry");
        interfaceC6968.mo16268(-3687241);
        Object mo16269 = interfaceC6968.mo16269();
        Object obj = InterfaceC6968.C9945.f30561;
        if (mo16269 == obj) {
            mo16269 = new ActivityResultLauncherHolder();
            interfaceC6968.mo16296(mo16269);
        }
        interfaceC6968.mo16300();
        ActivityResultLauncherHolder activityResultLauncherHolder = (ActivityResultLauncherHolder) mo16269;
        interfaceC6968.mo16268(-3687241);
        Object mo162692 = interfaceC6968.mo16269();
        if (mo162692 == obj) {
            mo162692 = new ManagedActivityResultLauncher(activityResultLauncherHolder, m10511);
            interfaceC6968.mo16296(mo162692);
        }
        interfaceC6968.mo16300();
        ManagedActivityResultLauncher<I, O> managedActivityResultLauncher = (ManagedActivityResultLauncher) mo162692;
        ActivityResultRegistryKt$rememberLauncherForActivityResult$1 activityResultRegistryKt$rememberLauncherForActivityResult$1 = new ActivityResultRegistryKt$rememberLauncherForActivityResult$1(activityResultLauncherHolder, activityResultRegistry, str, activityResultContract, m105112);
        interfaceC6968.mo16268(-1239538271);
        fi0<InterfaceC6575<?>, lu4, hz3, lx5> fi0Var = C7171.f31108;
        interfaceC6968.mo16268(1618982084);
        boolean mo16303 = interfaceC6968.mo16303(activityResultContract) | interfaceC6968.mo16303(str) | interfaceC6968.mo16303(activityResultRegistry);
        Object mo162693 = interfaceC6968.mo16269();
        if (mo16303 || mo162693 == obj) {
            interfaceC6968.mo16296(new C4173mh(activityResultRegistryKt$rememberLauncherForActivityResult$1));
        }
        interfaceC6968.mo16300();
        interfaceC6968.mo16300();
        interfaceC6968.mo16300();
        return managedActivityResultLauncher;
    }
}
