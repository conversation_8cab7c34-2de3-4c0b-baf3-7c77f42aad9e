package okhttp3.internal.p042io;

/* renamed from: okhttp3.internal.io.ws */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes3.dex */
public final class C5622ws extends lv1 implements ph0<Integer, Integer> {

    /* renamed from: ၥ */
    public static final C5622ws f25156 = new C5622ws();

    public C5622ws() {
        super(1);
    }

    @Override // okhttp3.internal.p042io.ph0
    public final Integer invoke(Integer num) {
        num.intValue();
        return 0;
    }
}
