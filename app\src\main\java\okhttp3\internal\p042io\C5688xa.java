package okhttp3.internal.p042io;

@InterfaceC4988s2(m11868c = "org.autojs.hrapps.visual.lib.main.fragment.DesignFragment", m11869f = "DesignFragment.kt", m11870l = {159, 182}, m11871m = "bindSdcard")
/* renamed from: okhttp3.internal.io.xa */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5688xa extends AbstractC6644 {

    /* renamed from: ၥ */
    public C5452va f25733;

    /* renamed from: ၦ */
    public /* synthetic */ Object f25734;

    /* renamed from: ၮ */
    public final /* synthetic */ C5452va f25735;

    /* renamed from: ၯ */
    public int f25736;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public C5688xa(C5452va c5452va, InterfaceC7155<? super C5688xa> interfaceC7155) {
        super(interfaceC7155);
        this.f25735 = c5452va;
    }

    @Override // okhttp3.internal.p042io.AbstractC7853
    @wv2
    public final Object invokeSuspend(@zu2 Object obj) {
        this.f25734 = obj;
        this.f25736 |= Integer.MIN_VALUE;
        return C5452va.m13156(this.f25735, this);
    }
}
