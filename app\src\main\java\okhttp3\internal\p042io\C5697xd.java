package okhttp3.internal.p042io;

/* renamed from: okhttp3.internal.io.xd */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5697xd extends AbstractC7298 {

    /* renamed from: ၥ */
    @pu2
    public final C3318fc f25808;

    /* renamed from: ၦ */
    public final int f25809;

    public C5697xd(@pu2 C3318fc c3318fc, @pu2 C4694qf c4694qf, int i) {
        this.f25808 = c3318fc;
        this.f25809 = c4694qf.m11116(i + 1);
    }

    @Override // okhttp3.internal.p042io.ei2
    @pu2
    public final gi2 getValue() {
        return new C5809yd(this.f25808, this.f25809);
    }
}
