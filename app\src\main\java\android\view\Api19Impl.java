package android.view;

import android.view.View;
import androidx.annotation.RequiresApi;
import kotlin.Metadata;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.zu2;
import org.mozilla.javascript.optimizer.Codegen;

@RequiresApi(19)
@Metadata(m4114d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\bÁ\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002¢\u0006\u0002\u0010\u0002J\u000e\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006¨\u0006\u0007"}, m4115d2 = {"Landroidx/activity/Api19Impl;", "", Codegen.TEMPLATE_LITERAL_INIT_METHOD_SIGNATURE, "isAttachedToWindow", "", "view", "Landroid/view/View;", "activity-ktx_release"}, m4116k = 1, m4117mv = {1, 6, 0}, m4119xi = 48)
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class Api19Impl {

    @zu2
    public static final Api19Impl INSTANCE = new Api19Impl();

    private Api19Impl() {
    }

    public final boolean isAttachedToWindow(@zu2 View view) {
        fa1.m6826(view, "view");
        return view.isAttachedToWindow();
    }
}
