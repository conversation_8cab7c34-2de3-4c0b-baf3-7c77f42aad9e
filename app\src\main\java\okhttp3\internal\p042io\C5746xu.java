package okhttp3.internal.p042io;

@fz4
/* renamed from: okhttp3.internal.io.xu */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5746xu {

    /* renamed from: Ϳ */
    @il4("expression")
    @zu2
    private final String f26183;

    /* renamed from: Ԩ */
    @il4("frame_id")
    private final long f26184;

    public final boolean equals(@wv2 Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof C5746xu)) {
            return false;
        }
        C5746xu c5746xu = (C5746xu) obj;
        return fa1.m6818(this.f26183, c5746xu.f26183) && this.f26184 == c5746xu.f26184;
    }

    public final int hashCode() {
        int hashCode = this.f26183.hashCode() * 31;
        long j = this.f26184;
        return hashCode + ((int) (j ^ (j >>> 32)));
    }

    @zu2
    public final String toString() {
        StringBuilder m9240 = lf2.m9240("EvaluateRequest(expression=");
        m9240.append(this.f26183);
        m9240.append(", frameId=");
        m9240.append(this.f26184);
        m9240.append(')');
        return m9240.toString();
    }

    @rl1("expression")
    @zu2
    /* renamed from: Ϳ */
    public final String m14064() {
        return this.f26183;
    }

    @rl1("frame_id")
    /* renamed from: Ԩ */
    public final long m14065() {
        return this.f26184;
    }
}
