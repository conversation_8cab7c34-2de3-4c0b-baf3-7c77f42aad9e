package okhttp3.internal.p042io;

import android.content.Context;
import android.os.RemoteException;
import androidx.autofill.HintConstants;
import com.stardust.autojs.ScriptService;
import com.stardust.autojs.project.Features;
import java.util.Map;
import kotlin.collections.C2629;
import okhttp3.internal.p042io.C2712a3;

@fz4
/* renamed from: okhttp3.internal.io.y2 */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5778y2 extends pz3 implements nz3 {

    /* renamed from: Ϳ */
    @zu2
    public static final C5778y2 f26334 = new C5778y2();

    /* renamed from: Ԩ */
    @zu2
    public static final String f26335 = f76.m6783(C5778y2.class, new StringBuilder(), ".state");

    @fz4
    /* renamed from: okhttp3.internal.io.y2$Ϳ, reason: contains not printable characters */
    public static final class C9503 {

        /* renamed from: Ϳ */
        @wv2
        public final C2712a3.EnumC8905 f26336;

        /* renamed from: Ԩ */
        @wv2
        public final C2712a3.EnumC8904 f26337;

        /* renamed from: ԩ */
        public final int f26338;

        /* renamed from: Ԫ */
        @wv2
        public final Integer f26339;

        public C9503(@wv2 C2712a3.EnumC8905 enumC8905, @wv2 C2712a3.EnumC8904 enumC8904, int i, @wv2 Integer num) {
            this.f26336 = enumC8905;
            this.f26337 = enumC8904;
            this.f26338 = i;
            this.f26339 = num;
        }

        public final boolean equals(@wv2 Object obj) {
            if (this == obj) {
                return true;
            }
            if (!(obj instanceof C9503)) {
                return false;
            }
            C9503 c9503 = (C9503) obj;
            return this.f26336 == c9503.f26336 && this.f26337 == c9503.f26337 && this.f26338 == c9503.f26338 && fa1.m6818(this.f26339, c9503.f26339);
        }

        public final int hashCode() {
            C2712a3.EnumC8905 enumC8905 = this.f26336;
            int hashCode = (enumC8905 == null ? 0 : enumC8905.hashCode()) * 31;
            C2712a3.EnumC8904 enumC8904 = this.f26337;
            int hashCode2 = (((hashCode + (enumC8904 == null ? 0 : enumC8904.hashCode())) * 31) + this.f26338) * 31;
            Integer num = this.f26339;
            return hashCode2 + (num != null ? num.hashCode() : 0);
        }

        @zu2
        public final String toString() {
            StringBuilder m9240 = lf2.m9240("State(serverState=");
            m9240.append(this.f26336);
            m9240.append(", clientState=");
            m9240.append(this.f26337);
            m9240.append(", connectionCount=");
            m9240.append(this.f26338);
            m9240.append(", fileServerPort=");
            m9240.append(this.f26339);
            m9240.append(')');
            return m9240.toString();
        }
    }

    /* renamed from: okhttp3.internal.io.y2$Ԩ, reason: contains not printable characters */
    public static final class C9504 extends lv1 implements ph0<yx0, lx5> {

        /* renamed from: ၥ */
        public final /* synthetic */ ph0<C9503, lx5> f26340;

        /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
        /* JADX WARN: Multi-variable type inference failed */
        public C9504(ph0<? super C9503, lx5> ph0Var) {
            super(1);
            this.f26340 = ph0Var;
        }

        @Override // okhttp3.internal.p042io.ph0
        public final lx5 invoke(yx0 yx0Var) {
            yx0 yx0Var2 = yx0Var;
            fa1.m6826(yx0Var2, "i");
            try {
                Map<?, ? extends Object> mo1675 = yx0Var2.mo1675(C5778y2.class.getName(), "state");
                ph0<C9503, lx5> ph0Var = this.f26340;
                C5778y2 c5778y2 = C5778y2.f26334;
                fa1.m6825(mo1675, "state");
                ph0Var.invoke(c5778y2.m14119(mo1675));
            } catch (RemoteException unused) {
            }
            return lx5.f14876;
        }
    }

    @Override // okhttp3.internal.p042io.nz3
    @zu2
    /* renamed from: Ϳ */
    public final Object mo10185(@zu2 String str, @zu2 Map<String, ? extends Object> map) {
        fa1.m6826(str, HintConstants.AUTOFILL_HINT_NAME);
        fa1.m6826(map, "params");
        if (fa1.m6818(str, f26335)) {
            return m14119(map);
        }
        throw new IllegalArgumentException(str);
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:36:0x00bd A[EXC_TOP_SPLITTER, SYNTHETIC] */
    @Override // okhttp3.internal.p042io.pz3
    @okhttp3.internal.p042io.zu2
    /* renamed from: Ԩ */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final java.util.Map<java.lang.Object, java.lang.Object> mo8449(@okhttp3.internal.p042io.zu2 java.lang.String r7, @okhttp3.internal.p042io.zu2 java.util.Map<java.lang.Object, ? extends java.lang.Object> r8) {
        /*
            Method dump skipped, instructions count: 221
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: okhttp3.internal.p042io.C5778y2.mo8449(java.lang.String, java.util.Map):java.util.Map");
    }

    @Override // okhttp3.internal.p042io.pz3
    @zu2
    /* renamed from: ԩ */
    public final Map<Object, Object> mo8450(@zu2 String str) {
        fa1.m6826(str, HintConstants.AUTOFILL_HINT_NAME);
        if (!fa1.m6818(str, "state")) {
            return C5849yq.f26859;
        }
        C2712a3 c2712a3 = C2712a3.f6076;
        return C2629.m4124(new v63("server_state", c2712a3.m4352().name()), new v63("client_state", C2712a3.f6080.getValue(c2712a3, C2712a3.f6077[1]).name()), new v63("count", 0));
    }

    /* renamed from: Ԫ */
    public final void m14117(@zu2 Context context) {
        fa1.m6826(context, "context");
        m14120(context, C2629.m4124(new v63(Features.NODEJS_ENABLED, Boolean.TRUE), new v63("mode", 2)));
    }

    /* renamed from: ԫ */
    public final void m14118(@zu2 Context context, @zu2 ph0<? super C9503, lx5> ph0Var) {
        fa1.m6826(context, "context");
        ScriptService.f2875.m1663(context, new C9504(ph0Var));
    }

    /* renamed from: Ԭ */
    public final C9503 m14119(Map<?, ? extends Object> map) {
        String str = (String) map.get("server_state");
        C2712a3.EnumC8905 valueOf = str != null ? C2712a3.EnumC8905.valueOf(str) : null;
        String str2 = (String) map.get("client_state");
        C2712a3.EnumC8904 valueOf2 = str2 != null ? C2712a3.EnumC8904.valueOf(str2) : null;
        Integer num = (Integer) map.get("file_server_port");
        Object obj = map.get("count");
        fa1.m6824(obj, "null cannot be cast to non-null type kotlin.Int");
        return new C9503(valueOf, valueOf2, ((Integer) obj).intValue(), num);
    }

    /* renamed from: ԭ */
    public final void m14120(Context context, Map<String, ? extends Object> map) {
        ScriptService.f2875.m1663(context, new C5890z2(map));
    }
}
