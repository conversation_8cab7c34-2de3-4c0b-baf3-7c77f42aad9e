package okhttp3.internal.p042io;

import android.annotation.TargetApi;
import android.os.Build;
import android.os.StrictMode;
import com.stardust.autojs.runtime.api.AbstractShell;
import java.io.BufferedWriter;
import java.io.Closeable;
import java.io.EOFException;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.Writer;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.concurrent.Callable;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import okhttp3.internal.cache.DiskLruCache;

/* renamed from: okhttp3.internal.io.xg */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes3.dex */
public final class C5709xg implements Closeable {

    /* renamed from: ၥ */
    public final File f25902;

    /* renamed from: ၦ */
    public final File f25903;

    /* renamed from: ၮ */
    public final File f25904;

    /* renamed from: ၯ */
    public final File f25905;

    /* renamed from: ၵ */
    public long f25907;

    /* renamed from: ၸ */
    public BufferedWriter f25910;

    /* renamed from: ၺ */
    public int f25912;

    /* renamed from: ၷ */
    public long f25909 = 0;

    /* renamed from: ၹ */
    public final LinkedHashMap<String, C9489> f25911 = new LinkedHashMap<>(0, 0.75f, true);

    /* renamed from: ၻ */
    public long f25913 = 0;

    /* renamed from: ၼ */
    public final ThreadPoolExecutor f25914 = new ThreadPoolExecutor(0, 1, 60, TimeUnit.SECONDS, new LinkedBlockingQueue(), new ThreadFactoryC9487());

    /* renamed from: ၽ */
    public final Callable<Void> f25915 = new CallableC9486();

    /* renamed from: ၰ */
    public final int f25906 = 1;

    /* renamed from: ၶ */
    public final int f25908 = 1;

    /* renamed from: okhttp3.internal.io.xg$Ϳ, reason: contains not printable characters */
    public class CallableC9486 implements Callable<Void> {
        public CallableC9486() {
        }

        @Override // java.util.concurrent.Callable
        public final Void call() {
            synchronized (C5709xg.this) {
                C5709xg c5709xg = C5709xg.this;
                if (c5709xg.f25910 != null) {
                    c5709xg.m13948();
                    if (C5709xg.this.m13943()) {
                        C5709xg.this.m13947();
                        C5709xg.this.f25912 = 0;
                    }
                }
            }
            return null;
        }
    }

    /* renamed from: okhttp3.internal.io.xg$Ԩ, reason: contains not printable characters */
    public static final class ThreadFactoryC9487 implements ThreadFactory {
        @Override // java.util.concurrent.ThreadFactory
        public final synchronized Thread newThread(Runnable runnable) {
            Thread thread;
            thread = new Thread(runnable, "glide-disk-lru-cache-thread");
            thread.setPriority(1);
            return thread;
        }
    }

    /* renamed from: okhttp3.internal.io.xg$Ԫ, reason: contains not printable characters */
    public final class C9488 {

        /* renamed from: Ϳ */
        public final C9489 f25917;

        /* renamed from: Ԩ */
        public final boolean[] f25918;

        /* renamed from: ԩ */
        public boolean f25919;

        public C9488(C9489 c9489) {
            this.f25917 = c9489;
            this.f25918 = c9489.f25925 ? null : new boolean[C5709xg.this.f25908];
        }

        /* renamed from: Ϳ */
        public final void m13949() {
            C5709xg.m13934(C5709xg.this, this, false);
        }

        /* renamed from: Ԩ */
        public final File m13950() {
            File file;
            synchronized (C5709xg.this) {
                C9489 c9489 = this.f25917;
                if (c9489.f25926 != this) {
                    throw new IllegalStateException();
                }
                if (!c9489.f25925) {
                    this.f25918[0] = true;
                }
                file = c9489.f25924[0];
                C5709xg.this.f25902.mkdirs();
            }
            return file;
        }
    }

    /* renamed from: okhttp3.internal.io.xg$Ԭ, reason: contains not printable characters */
    public final class C9489 {

        /* renamed from: Ϳ */
        public final String f25921;

        /* renamed from: Ԩ */
        public final long[] f25922;

        /* renamed from: ԩ */
        public File[] f25923;

        /* renamed from: Ԫ */
        public File[] f25924;

        /* renamed from: ԫ */
        public boolean f25925;

        /* renamed from: Ԭ */
        public C9488 f25926;

        /* renamed from: ԭ */
        public long f25927;

        public C9489(String str) {
            this.f25921 = str;
            int i = C5709xg.this.f25908;
            this.f25922 = new long[i];
            this.f25923 = new File[i];
            this.f25924 = new File[i];
            StringBuilder sb = new StringBuilder(str);
            sb.append('.');
            int length = sb.length();
            for (int i2 = 0; i2 < C5709xg.this.f25908; i2++) {
                sb.append(i2);
                this.f25923[i2] = new File(C5709xg.this.f25902, sb.toString());
                sb.append(".tmp");
                this.f25924[i2] = new File(C5709xg.this.f25902, sb.toString());
                sb.setLength(length);
            }
        }

        /* renamed from: Ϳ */
        public final String m13951() {
            StringBuilder sb = new StringBuilder();
            for (long j : this.f25922) {
                sb.append(' ');
                sb.append(j);
            }
            return sb.toString();
        }

        /* renamed from: Ԩ */
        public final IOException m13952(String[] strArr) {
            StringBuilder m9240 = lf2.m9240("unexpected journal line: ");
            m9240.append(Arrays.toString(strArr));
            throw new IOException(m9240.toString());
        }
    }

    /* renamed from: okhttp3.internal.io.xg$Ԯ, reason: contains not printable characters */
    public final class C9490 {

        /* renamed from: Ϳ */
        public final File[] f25929;

        public C9490(File[] fileArr) {
            this.f25929 = fileArr;
        }
    }

    public C5709xg(File file, long j) {
        this.f25902 = file;
        this.f25903 = new File(file, DiskLruCache.JOURNAL_FILE);
        this.f25904 = new File(file, DiskLruCache.JOURNAL_FILE_TEMP);
        this.f25905 = new File(file, DiskLruCache.JOURNAL_FILE_BACKUP);
        this.f25907 = j;
    }

    /* renamed from: Ϳ */
    public static void m13934(C5709xg c5709xg, C9488 c9488, boolean z) {
        synchronized (c5709xg) {
            C9489 c9489 = c9488.f25917;
            if (c9489.f25926 != c9488) {
                throw new IllegalStateException();
            }
            if (z && !c9489.f25925) {
                for (int i = 0; i < c5709xg.f25908; i++) {
                    if (!c9488.f25918[i]) {
                        c9488.m13949();
                        throw new IllegalStateException("Newly created entry didn't create value for index " + i);
                    }
                    if (!c9489.f25924[i].exists()) {
                        c9488.m13949();
                        break;
                    }
                }
            }
            for (int i2 = 0; i2 < c5709xg.f25908; i2++) {
                File file = c9489.f25924[i2];
                if (!z) {
                    m13936(file);
                } else if (file.exists()) {
                    File file2 = c9489.f25923[i2];
                    file.renameTo(file2);
                    long j = c9489.f25922[i2];
                    long length = file2.length();
                    c9489.f25922[i2] = length;
                    c5709xg.f25909 = (c5709xg.f25909 - j) + length;
                }
            }
            c5709xg.f25912++;
            c9489.f25926 = null;
            if (c9489.f25925 || z) {
                c9489.f25925 = true;
                c5709xg.f25910.append((CharSequence) "CLEAN");
                c5709xg.f25910.append(' ');
                c5709xg.f25910.append((CharSequence) c9489.f25921);
                c5709xg.f25910.append((CharSequence) c9489.m13951());
                c5709xg.f25910.append('\n');
                if (z) {
                    long j2 = c5709xg.f25913;
                    c5709xg.f25913 = 1 + j2;
                    c9489.f25927 = j2;
                }
            } else {
                c5709xg.f25911.remove(c9489.f25921);
                c5709xg.f25910.append((CharSequence) "REMOVE");
                c5709xg.f25910.append(' ');
                c5709xg.f25910.append((CharSequence) c9489.f25921);
                c5709xg.f25910.append('\n');
            }
            m13937(c5709xg.f25910);
            if (c5709xg.f25909 > c5709xg.f25907 || c5709xg.m13943()) {
                c5709xg.f25914.submit(c5709xg.f25915);
            }
        }
    }

    @TargetApi(26)
    /* renamed from: ԩ */
    public static void m13935(Writer writer) {
        if (Build.VERSION.SDK_INT < 26) {
            writer.close();
            return;
        }
        StrictMode.ThreadPolicy threadPolicy = StrictMode.getThreadPolicy();
        StrictMode.setThreadPolicy(new StrictMode.ThreadPolicy.Builder(threadPolicy).permitUnbufferedIo().build());
        try {
            writer.close();
        } finally {
            StrictMode.setThreadPolicy(threadPolicy);
        }
    }

    /* renamed from: Ԫ */
    public static void m13936(File file) {
        if (file.exists() && !file.delete()) {
            throw new IOException();
        }
    }

    @TargetApi(26)
    /* renamed from: Ԭ */
    public static void m13937(Writer writer) {
        if (Build.VERSION.SDK_INT < 26) {
            writer.flush();
            return;
        }
        StrictMode.ThreadPolicy threadPolicy = StrictMode.getThreadPolicy();
        StrictMode.setThreadPolicy(new StrictMode.ThreadPolicy.Builder(threadPolicy).permitUnbufferedIo().build());
        try {
            writer.flush();
        } finally {
            StrictMode.setThreadPolicy(threadPolicy);
        }
    }

    /* renamed from: ֏ */
    public static C5709xg m13938(File file, long j) {
        if (j <= 0) {
            throw new IllegalArgumentException("maxSize <= 0");
        }
        File file2 = new File(file, DiskLruCache.JOURNAL_FILE_BACKUP);
        if (file2.exists()) {
            File file3 = new File(file, DiskLruCache.JOURNAL_FILE);
            if (file3.exists()) {
                file2.delete();
            } else {
                m13939(file2, file3, false);
            }
        }
        C5709xg c5709xg = new C5709xg(file, j);
        if (c5709xg.f25903.exists()) {
            try {
                c5709xg.m13945();
                c5709xg.m13944();
                return c5709xg;
            } catch (IOException e) {
                System.out.println("DiskLruCache " + file + " is corrupt: " + e.getMessage() + ", removing");
                c5709xg.close();
                zz5.m14670(c5709xg.f25902);
            }
        }
        file.mkdirs();
        C5709xg c5709xg2 = new C5709xg(file, j);
        c5709xg2.m13947();
        return c5709xg2;
    }

    /* renamed from: ކ */
    public static void m13939(File file, File file2, boolean z) {
        if (z) {
            m13936(file2);
        }
        if (!file.renameTo(file2)) {
            throw new IOException();
        }
    }

    @Override // java.io.Closeable, java.lang.AutoCloseable
    public final synchronized void close() {
        if (this.f25910 == null) {
            return;
        }
        Iterator it = new ArrayList(this.f25911.values()).iterator();
        while (it.hasNext()) {
            C9488 c9488 = ((C9489) it.next()).f25926;
            if (c9488 != null) {
                c9488.m13949();
            }
        }
        m13948();
        m13935(this.f25910);
        this.f25910 = null;
    }

    /* renamed from: Ԩ */
    public final void m13940() {
        if (this.f25910 == null) {
            throw new IllegalStateException("cache is closed");
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:12:0x001c, code lost:
    
        if (r0.f25926 != null) goto L8;
     */
    /* renamed from: ԫ */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final okhttp3.internal.p042io.C5709xg.C9488 m13941(java.lang.String r4) {
        /*
            r3 = this;
            monitor-enter(r3)
            r3.m13940()     // Catch: java.lang.Throwable -> L48
            java.util.LinkedHashMap<java.lang.String, okhttp3.internal.io.xg$Ԭ> r0 = r3.f25911     // Catch: java.lang.Throwable -> L48
            java.lang.Object r0 = r0.get(r4)     // Catch: java.lang.Throwable -> L48
            okhttp3.internal.io.xg$Ԭ r0 = (okhttp3.internal.p042io.C5709xg.C9489) r0     // Catch: java.lang.Throwable -> L48
            r1 = 0
            if (r0 != 0) goto L1a
            okhttp3.internal.io.xg$Ԭ r0 = new okhttp3.internal.io.xg$Ԭ     // Catch: java.lang.Throwable -> L48
            r0.<init>(r4)     // Catch: java.lang.Throwable -> L48
            java.util.LinkedHashMap<java.lang.String, okhttp3.internal.io.xg$Ԭ> r1 = r3.f25911     // Catch: java.lang.Throwable -> L48
            r1.put(r4, r0)     // Catch: java.lang.Throwable -> L48
            goto L20
        L1a:
            okhttp3.internal.io.xg$Ԫ r2 = r0.f25926     // Catch: java.lang.Throwable -> L48
            if (r2 == 0) goto L20
        L1e:
            monitor-exit(r3)
            goto L47
        L20:
            okhttp3.internal.io.xg$Ԫ r1 = new okhttp3.internal.io.xg$Ԫ     // Catch: java.lang.Throwable -> L48
            r1.<init>(r0)     // Catch: java.lang.Throwable -> L48
            r0.f25926 = r1     // Catch: java.lang.Throwable -> L48
            java.io.BufferedWriter r0 = r3.f25910     // Catch: java.lang.Throwable -> L48
            java.lang.String r2 = "DIRTY"
            r0.append(r2)     // Catch: java.lang.Throwable -> L48
            java.io.BufferedWriter r0 = r3.f25910     // Catch: java.lang.Throwable -> L48
            r2 = 32
            r0.append(r2)     // Catch: java.lang.Throwable -> L48
            java.io.BufferedWriter r0 = r3.f25910     // Catch: java.lang.Throwable -> L48
            r0.append(r4)     // Catch: java.lang.Throwable -> L48
            java.io.BufferedWriter r4 = r3.f25910     // Catch: java.lang.Throwable -> L48
            r0 = 10
            r4.append(r0)     // Catch: java.lang.Throwable -> L48
            java.io.BufferedWriter r4 = r3.f25910     // Catch: java.lang.Throwable -> L48
            m13937(r4)     // Catch: java.lang.Throwable -> L48
            goto L1e
        L47:
            return r1
        L48:
            r4 = move-exception
            monitor-exit(r3)
            throw r4
        */
        throw new UnsupportedOperationException("Method not decompiled: okhttp3.internal.p042io.C5709xg.m13941(java.lang.String):okhttp3.internal.io.xg$Ԫ");
    }

    /* renamed from: Ԯ */
    public final synchronized C9490 m13942(String str) {
        m13940();
        C9489 c9489 = this.f25911.get(str);
        if (c9489 == null) {
            return null;
        }
        if (!c9489.f25925) {
            return null;
        }
        for (File file : c9489.f25923) {
            if (!file.exists()) {
                return null;
            }
        }
        this.f25912++;
        this.f25910.append((CharSequence) "READ");
        this.f25910.append(' ');
        this.f25910.append((CharSequence) str);
        this.f25910.append('\n');
        if (m13943()) {
            this.f25914.submit(this.f25915);
        }
        return new C9490(c9489.f25923);
    }

    /* renamed from: ԯ */
    public final boolean m13943() {
        int i = this.f25912;
        return i >= 2000 && i >= this.f25911.size();
    }

    /* renamed from: ؠ */
    public final void m13944() {
        m13936(this.f25904);
        Iterator<C9489> it = this.f25911.values().iterator();
        while (it.hasNext()) {
            C9489 next = it.next();
            int i = 0;
            if (next.f25926 == null) {
                while (i < this.f25908) {
                    this.f25909 += next.f25922[i];
                    i++;
                }
            } else {
                next.f25926 = null;
                while (i < this.f25908) {
                    m13936(next.f25923[i]);
                    m13936(next.f25924[i]);
                    i++;
                }
                it.remove();
            }
        }
    }

    /* renamed from: ނ */
    public final void m13945() {
        m35 m35Var = new m35(new FileInputStream(this.f25903), zz5.f27840);
        try {
            String m9550 = m35Var.m9550();
            String m95502 = m35Var.m9550();
            String m95503 = m35Var.m9550();
            String m95504 = m35Var.m9550();
            String m95505 = m35Var.m9550();
            if (!DiskLruCache.MAGIC.equals(m9550) || !DiskLruCache.VERSION_1.equals(m95502) || !Integer.toString(this.f25906).equals(m95503) || !Integer.toString(this.f25908).equals(m95504) || !"".equals(m95505)) {
                throw new IOException("unexpected journal header: [" + m9550 + ", " + m95502 + ", " + m95504 + ", " + m95505 + "]");
            }
            int i = 0;
            while (true) {
                try {
                    m13946(m35Var.m9550());
                    i++;
                } catch (EOFException unused) {
                    this.f25912 = i - this.f25911.size();
                    if (m35Var.f14984 == -1) {
                        m13947();
                    } else {
                        this.f25910 = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(this.f25903, true), zz5.f27840));
                    }
                    try {
                        m35Var.close();
                        return;
                    } catch (RuntimeException e) {
                        throw e;
                    } catch (Exception unused2) {
                        return;
                    }
                }
            }
        } catch (Throwable th) {
            try {
                m35Var.close();
            } catch (RuntimeException e2) {
                throw e2;
            } catch (Exception unused3) {
            }
            throw th;
        }
    }

    /* renamed from: ބ */
    public final void m13946(String str) {
        String substring;
        int indexOf = str.indexOf(32);
        if (indexOf == -1) {
            throw new IOException(C4118lz.m9496("unexpected journal line: ", str));
        }
        int i = indexOf + 1;
        int indexOf2 = str.indexOf(32, i);
        if (indexOf2 == -1) {
            substring = str.substring(i);
            if (indexOf == 6 && str.startsWith("REMOVE")) {
                this.f25911.remove(substring);
                return;
            }
        } else {
            substring = str.substring(i, indexOf2);
        }
        C9489 c9489 = this.f25911.get(substring);
        if (c9489 == null) {
            c9489 = new C9489(substring);
            this.f25911.put(substring, c9489);
        }
        if (indexOf2 == -1 || indexOf != 5 || !str.startsWith("CLEAN")) {
            if (indexOf2 == -1 && indexOf == 5 && str.startsWith("DIRTY")) {
                c9489.f25926 = new C9488(c9489);
                return;
            } else {
                if (indexOf2 != -1 || indexOf != 4 || !str.startsWith("READ")) {
                    throw new IOException(C4118lz.m9496("unexpected journal line: ", str));
                }
                return;
            }
        }
        String[] split = str.substring(indexOf2 + 1).split(" ");
        c9489.f25925 = true;
        c9489.f25926 = null;
        if (split.length != C5709xg.this.f25908) {
            c9489.m13952(split);
            throw null;
        }
        for (int i2 = 0; i2 < split.length; i2++) {
            try {
                c9489.f25922[i2] = Long.parseLong(split[i2]);
            } catch (NumberFormatException unused) {
                c9489.m13952(split);
                throw null;
            }
        }
    }

    /* renamed from: ޅ */
    public final synchronized void m13947() {
        StringBuilder sb;
        BufferedWriter bufferedWriter = this.f25910;
        if (bufferedWriter != null) {
            m13935(bufferedWriter);
        }
        BufferedWriter bufferedWriter2 = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(this.f25904), zz5.f27840));
        try {
            bufferedWriter2.write(DiskLruCache.MAGIC);
            bufferedWriter2.write(AbstractShell.COMMAND_LINE_END);
            bufferedWriter2.write(DiskLruCache.VERSION_1);
            bufferedWriter2.write(AbstractShell.COMMAND_LINE_END);
            bufferedWriter2.write(Integer.toString(this.f25906));
            bufferedWriter2.write(AbstractShell.COMMAND_LINE_END);
            bufferedWriter2.write(Integer.toString(this.f25908));
            bufferedWriter2.write(AbstractShell.COMMAND_LINE_END);
            bufferedWriter2.write(AbstractShell.COMMAND_LINE_END);
            for (C9489 c9489 : this.f25911.values()) {
                if (c9489.f25926 != null) {
                    sb = new StringBuilder();
                    sb.append("DIRTY ");
                    sb.append(c9489.f25921);
                    sb.append('\n');
                } else {
                    sb = new StringBuilder();
                    sb.append("CLEAN ");
                    sb.append(c9489.f25921);
                    sb.append(c9489.m13951());
                    sb.append('\n');
                }
                bufferedWriter2.write(sb.toString());
            }
            m13935(bufferedWriter2);
            if (this.f25903.exists()) {
                m13939(this.f25903, this.f25905, true);
            }
            m13939(this.f25904, this.f25903, false);
            this.f25905.delete();
            this.f25910 = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(this.f25903, true), zz5.f27840));
        } catch (Throwable th) {
            m13935(bufferedWriter2);
            throw th;
        }
    }

    /* renamed from: އ */
    public final void m13948() {
        while (this.f25909 > this.f25907) {
            String key = this.f25911.entrySet().iterator().next().getKey();
            synchronized (this) {
                m13940();
                C9489 c9489 = this.f25911.get(key);
                if (c9489 != null && c9489.f25926 == null) {
                    for (int i = 0; i < this.f25908; i++) {
                        File file = c9489.f25923[i];
                        if (file.exists() && !file.delete()) {
                            throw new IOException("failed to delete " + file);
                        }
                        long j = this.f25909;
                        long[] jArr = c9489.f25922;
                        this.f25909 = j - jArr[i];
                        jArr[i] = 0;
                    }
                    this.f25912++;
                    this.f25910.append((CharSequence) "REMOVE");
                    this.f25910.append(' ');
                    this.f25910.append((CharSequence) key);
                    this.f25910.append('\n');
                    this.f25911.remove(key);
                    if (m13943()) {
                        this.f25914.submit(this.f25915);
                    }
                }
            }
        }
    }
}
