package okhttp3.internal.p042io;

import java.security.SecureRandom;

/* renamed from: okhttp3.internal.io.xm */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5721xm extends ft1 {

    /* renamed from: ԩ */
    public C4526pm f26058;

    public C5721xm(C4526pm c4526pm, SecureRandom secureRandom) {
        super(secureRandom, c4526pm.f17744.bitLength());
        this.f26058 = c4526pm;
    }
}
