package androidx.constraintlayout.core.motion.utils;

import com.stardust.autojs.runtime.api.AbstractShell;
import okhttp3.internal.p042io.C5717xl;
import okhttp3.internal.p042io.C6134;
import okhttp3.internal.p042io.jf2;
import okhttp3.internal.p042io.kf2;

/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public class StopLogicEngine implements StopEngine {
    private static final float EPSILON = 1.0E-5f;
    private boolean mBackwards = false;
    private boolean mDone = false;
    private float mLastPosition;
    private int mNumberOfStages;
    private float mStage1Duration;
    private float mStage1EndPosition;
    private float mStage1Velocity;
    private float mStage2Duration;
    private float mStage2EndPosition;
    private float mStage2Velocity;
    private float mStage3Duration;
    private float mStage3EndPosition;
    private float mStage3Velocity;
    private float mStartPosition;
    private String mType;

    private float calcY(float f) {
        this.mDone = false;
        float f2 = this.mStage1Duration;
        if (f <= f2) {
            float f3 = this.mStage1Velocity;
            return ((((this.mStage2Velocity - f3) * f) * f) / (f2 * 2.0f)) + (f3 * f);
        }
        int i = this.mNumberOfStages;
        if (i == 1) {
            return this.mStage1EndPosition;
        }
        float f4 = f - f2;
        float f5 = this.mStage2Duration;
        if (f4 < f5) {
            float f6 = this.mStage1EndPosition;
            float f7 = this.mStage2Velocity;
            return ((((this.mStage3Velocity - f7) * f4) * f4) / (f5 * 2.0f)) + (f7 * f4) + f6;
        }
        if (i == 2) {
            return this.mStage2EndPosition;
        }
        float f8 = f4 - f5;
        float f9 = this.mStage3Duration;
        if (f8 > f9) {
            this.mDone = true;
            return this.mStage3EndPosition;
        }
        float f10 = this.mStage2EndPosition;
        float f11 = this.mStage3Velocity;
        return ((f11 * f8) + f10) - (((f11 * f8) * f8) / (f9 * 2.0f));
    }

    private void setup(float f, float f2, float f3, float f4, float f5) {
        this.mDone = false;
        if (f == 0.0f) {
            f = 1.0E-4f;
        }
        this.mStage1Velocity = f;
        float f6 = f / f3;
        float f7 = (f6 * f) / 2.0f;
        if (f < 0.0f) {
            float sqrt = (float) Math.sqrt((f2 - ((((-f) / f3) * f) / 2.0f)) * f3);
            if (sqrt < f4) {
                this.mType = "backward accelerate, decelerate";
                this.mNumberOfStages = 2;
                this.mStage1Velocity = f;
                this.mStage2Velocity = sqrt;
                this.mStage3Velocity = 0.0f;
                float f8 = (sqrt - f) / f3;
                this.mStage1Duration = f8;
                this.mStage2Duration = sqrt / f3;
                this.mStage1EndPosition = ((f + sqrt) * f8) / 2.0f;
                this.mStage2EndPosition = f2;
                this.mStage3EndPosition = f2;
                return;
            }
            this.mType = "backward accelerate cruse decelerate";
            this.mNumberOfStages = 3;
            this.mStage1Velocity = f;
            this.mStage2Velocity = f4;
            this.mStage3Velocity = f4;
            float f9 = (f4 - f) / f3;
            this.mStage1Duration = f9;
            float f10 = f4 / f3;
            this.mStage3Duration = f10;
            float f11 = ((f + f4) * f9) / 2.0f;
            float f12 = (f10 * f4) / 2.0f;
            this.mStage2Duration = ((f2 - f11) - f12) / f4;
            this.mStage1EndPosition = f11;
            this.mStage2EndPosition = f2 - f12;
            this.mStage3EndPosition = f2;
            return;
        }
        if (f7 >= f2) {
            this.mType = "hard stop";
            this.mNumberOfStages = 1;
            this.mStage1Velocity = f;
            this.mStage2Velocity = 0.0f;
            this.mStage1EndPosition = f2;
            this.mStage1Duration = (2.0f * f2) / f;
            return;
        }
        float f13 = f2 - f7;
        float f14 = f13 / f;
        if (f14 + f6 < f5) {
            this.mType = "cruse decelerate";
            this.mNumberOfStages = 2;
            this.mStage1Velocity = f;
            this.mStage2Velocity = f;
            this.mStage3Velocity = 0.0f;
            this.mStage1EndPosition = f13;
            this.mStage2EndPosition = f2;
            this.mStage1Duration = f14;
            this.mStage2Duration = f6;
            return;
        }
        float sqrt2 = (float) Math.sqrt(((f * f) / 2.0f) + (f3 * f2));
        float f15 = (sqrt2 - f) / f3;
        this.mStage1Duration = f15;
        float f16 = sqrt2 / f3;
        this.mStage2Duration = f16;
        if (sqrt2 < f4) {
            this.mType = "accelerate decelerate";
            this.mNumberOfStages = 2;
            this.mStage1Velocity = f;
            this.mStage2Velocity = sqrt2;
            this.mStage3Velocity = 0.0f;
            this.mStage1Duration = f15;
            this.mStage2Duration = f16;
            this.mStage1EndPosition = ((f + sqrt2) * f15) / 2.0f;
            this.mStage2EndPosition = f2;
            return;
        }
        this.mType = "accelerate cruse decelerate";
        this.mNumberOfStages = 3;
        this.mStage1Velocity = f;
        this.mStage2Velocity = f4;
        this.mStage3Velocity = f4;
        float f17 = (f4 - f) / f3;
        this.mStage1Duration = f17;
        float f18 = f4 / f3;
        this.mStage3Duration = f18;
        float f19 = ((f + f4) * f17) / 2.0f;
        float f20 = (f18 * f4) / 2.0f;
        this.mStage2Duration = ((f2 - f19) - f20) / f4;
        this.mStage1EndPosition = f19;
        this.mStage2EndPosition = f2 - f20;
        this.mStage3EndPosition = f2;
    }

    public void config(float f, float f2, float f3, float f4, float f5, float f6) {
        float f7;
        StopLogicEngine stopLogicEngine;
        float f8;
        this.mDone = false;
        this.mStartPosition = f;
        boolean z = f > f2;
        this.mBackwards = z;
        if (z) {
            f8 = -f3;
            f7 = f - f2;
            stopLogicEngine = this;
        } else {
            f7 = f2 - f;
            stopLogicEngine = this;
            f8 = f3;
        }
        stopLogicEngine.setup(f8, f7, f5, f6, f4);
    }

    @Override // androidx.constraintlayout.core.motion.utils.StopEngine
    public String debug(String str, float f) {
        StringBuilder m14009;
        String str2;
        StringBuilder m140092 = C5717xl.m14009(jf2.m8634(C5717xl.m14009(str, " ===== "), this.mType, AbstractShell.COMMAND_LINE_END), str);
        m140092.append(this.mBackwards ? "backwards" : "forward ");
        m140092.append(" time = ");
        m140092.append(f);
        m140092.append("  stages ");
        StringBuilder m14894 = C6134.m14894(kf2.m8930(m140092, this.mNumberOfStages, AbstractShell.COMMAND_LINE_END), str, " dur ");
        m14894.append(this.mStage1Duration);
        m14894.append(" vel ");
        m14894.append(this.mStage1Velocity);
        m14894.append(" pos ");
        m14894.append(this.mStage1EndPosition);
        m14894.append(AbstractShell.COMMAND_LINE_END);
        String sb = m14894.toString();
        if (this.mNumberOfStages > 1) {
            StringBuilder m148942 = C6134.m14894(sb, str, " dur ");
            m148942.append(this.mStage2Duration);
            m148942.append(" vel ");
            m148942.append(this.mStage2Velocity);
            m148942.append(" pos ");
            m148942.append(this.mStage2EndPosition);
            m148942.append(AbstractShell.COMMAND_LINE_END);
            sb = m148942.toString();
        }
        if (this.mNumberOfStages > 2) {
            StringBuilder m148943 = C6134.m14894(sb, str, " dur ");
            m148943.append(this.mStage3Duration);
            m148943.append(" vel ");
            m148943.append(this.mStage3Velocity);
            m148943.append(" pos ");
            m148943.append(this.mStage3EndPosition);
            m148943.append(AbstractShell.COMMAND_LINE_END);
            sb = m148943.toString();
        }
        float f2 = this.mStage1Duration;
        if (f <= f2) {
            m14009 = C5717xl.m14009(sb, str);
            str2 = "stage 0\n";
        } else {
            int i = this.mNumberOfStages;
            if (i == 1) {
                m14009 = C5717xl.m14009(sb, str);
                str2 = "end stage 0\n";
            } else {
                float f3 = f - f2;
                float f4 = this.mStage2Duration;
                if (f3 < f4) {
                    m14009 = C5717xl.m14009(sb, str);
                    str2 = " stage 1\n";
                } else if (i == 2) {
                    m14009 = C5717xl.m14009(sb, str);
                    str2 = "end stage 1\n";
                } else if (f3 - f4 < this.mStage3Duration) {
                    m14009 = C5717xl.m14009(sb, str);
                    str2 = " stage 2\n";
                } else {
                    m14009 = C5717xl.m14009(sb, str);
                    str2 = " end stage 2\n";
                }
            }
        }
        m14009.append(str2);
        return m14009.toString();
    }

    @Override // androidx.constraintlayout.core.motion.utils.StopEngine
    public float getInterpolation(float f) {
        float calcY = calcY(f);
        this.mLastPosition = f;
        return this.mBackwards ? this.mStartPosition - calcY : this.mStartPosition + calcY;
    }

    @Override // androidx.constraintlayout.core.motion.utils.StopEngine
    public float getVelocity() {
        return this.mBackwards ? -getVelocity(this.mLastPosition) : getVelocity(this.mLastPosition);
    }

    @Override // androidx.constraintlayout.core.motion.utils.StopEngine
    public float getVelocity(float f) {
        float f2;
        float f3;
        float f4 = this.mStage1Duration;
        if (f <= f4) {
            f2 = this.mStage1Velocity;
            f3 = this.mStage2Velocity;
        } else {
            int i = this.mNumberOfStages;
            if (i == 1) {
                return 0.0f;
            }
            f -= f4;
            f4 = this.mStage2Duration;
            if (f >= f4) {
                if (i == 2) {
                    return this.mStage2EndPosition;
                }
                float f5 = f - f4;
                float f6 = this.mStage3Duration;
                if (f5 >= f6) {
                    return this.mStage3EndPosition;
                }
                float f7 = this.mStage3Velocity;
                return f7 - ((f5 * f7) / f6);
            }
            f2 = this.mStage2Velocity;
            f3 = this.mStage3Velocity;
        }
        return (((f3 - f2) * f) / f4) + f2;
    }

    @Override // androidx.constraintlayout.core.motion.utils.StopEngine
    public boolean isStopped() {
        return getVelocity() < EPSILON && Math.abs(this.mStage3EndPosition - this.mLastPosition) < EPSILON;
    }
}
