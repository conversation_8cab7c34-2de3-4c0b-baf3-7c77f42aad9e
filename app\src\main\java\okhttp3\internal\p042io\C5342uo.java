package okhttp3.internal.p042io;

import android.content.Context;
import android.content.DialogInterface;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.widget.AppCompatEditText;
import com.google.android.material.dialog.MaterialAlertDialogBuilder;
import com.stardust.autojs.core.inputevent.InputEventCodes;
import java.util.Objects;
import okhttp3.internal.p042io.AbstractC7040;
import okhttp3.internal.p042io.C7822;
import org.autojs.autojs.p047ui.edit.editor.InterfaceC8120;
import org.autojs.autojs.p047ui.user.WebActivity;

@InterfaceC4988s2(m11868c = "org.autojs.autojs.ui.edit.EditorMenu$importJavaPackageOrClass$1", m11869f = "EditorMenu.kt", m11870l = {InputEventCodes.KEY_FINANCE}, m11871m = "invokeSuspend")
/* renamed from: okhttp3.internal.io.uo */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5342uo extends u75 implements di0<InterfaceC7881, InterfaceC7155<? super lx5>, Object> {

    /* renamed from: ၥ */
    public int f23372;

    /* renamed from: ၦ */
    public final /* synthetic */ C2798ap f23373;

    /* renamed from: okhttp3.internal.io.uo$Ϳ, reason: contains not printable characters */
    public static final class C9409 implements C7822.InterfaceC10379 {

        /* renamed from: Ϳ */
        public final /* synthetic */ C2798ap f23374;

        public C9409(C2798ap c2798ap) {
            this.f23374 = c2798ap;
        }

        @Override // okhttp3.internal.p042io.C7822.InterfaceC10379
        /* renamed from: Ϳ */
        public final void mo12933(@zu2 AlertDialog alertDialog, @zu2 final AbstractC7040 abstractC7040) {
            String str;
            final String str2;
            final C2798ap c2798ap = this.f23374;
            Objects.requireNonNull(c2798ap);
            if (abstractC7040 instanceof AbstractC7040.C9983) {
                C6912 c6912 = ((AbstractC7040.C9983) abstractC7040).f30743;
                str = c6912.m16180();
                fa1.m6825(str, "androidClass.className");
                str2 = c6912.m16181();
                fa1.m6825(str2, "androidClass.fullName");
            } else {
                str = ((AbstractC7040.C9984) abstractC7040).f30745;
                fa1.m6825(str, "item as PackageItem).packageName");
                str2 = str;
            }
            new MaterialAlertDialogBuilder(c2798ap.f6600).setTitle((CharSequence) str).setMessage((CharSequence) str2).setPositiveButton(2131821235, new DialogInterface.OnClickListener() { // from class: okhttp3.internal.io.oo
                @Override // android.content.DialogInterface.OnClickListener
                public final void onClick(DialogInterface dialogInterface, int i) {
                    C2798ap c2798ap2 = C2798ap.this;
                    String str3 = str2;
                    fa1.m6826(c2798ap2, "this$0");
                    fa1.m6826(str3, "$desc");
                    C6456.m15392(c2798ap2.f6600, str3);
                    js1.m8719(c2798ap2.f6600, xp3.text_already_copy_to_clip, 0).show();
                }
            }).setNegativeButton(2131821293, (DialogInterface.OnClickListener) new sn0(c2798ap, abstractC7040, 1)).setNeutralButton(2131821540, new DialogInterface.OnClickListener() { // from class: okhttp3.internal.io.po
                @Override // android.content.DialogInterface.OnClickListener
                public final void onClick(DialogInterface dialogInterface, int i) {
                    C2798ap c2798ap2 = C2798ap.this;
                    AbstractC7040 abstractC70402 = abstractC7040;
                    fa1.m6826(c2798ap2, "this$0");
                    fa1.m6826(abstractC70402, "$item");
                    WebActivity.C8209 c8209 = WebActivity.f34532;
                    Context context = c2798ap2.f6600;
                    fa1.m6825(context, "mContext");
                    String mo16399 = abstractC70402.mo16399();
                    fa1.m6825(mo16399, "item.url");
                    WebActivity.C8209.m17990(context, mo16399);
                }
            }).show();
        }
    }

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public C5342uo(C2798ap c2798ap, InterfaceC7155<? super C5342uo> interfaceC7155) {
        super(2, interfaceC7155);
        this.f23373 = c2798ap;
    }

    @Override // okhttp3.internal.p042io.AbstractC7853
    @zu2
    public final InterfaceC7155<lx5> create(@wv2 Object obj, @zu2 InterfaceC7155<?> interfaceC7155) {
        return new C5342uo(this.f23373, interfaceC7155);
    }

    @Override // okhttp3.internal.p042io.di0
    /* renamed from: invoke */
    public final Object mo18338invoke(InterfaceC7881 interfaceC7881, InterfaceC7155<? super lx5> interfaceC7155) {
        return ((C5342uo) create(interfaceC7881, interfaceC7155)).invokeSuspend(lx5.f14876);
    }

    @Override // okhttp3.internal.p042io.AbstractC7853
    @wv2
    public final Object invokeSuspend(@zu2 Object obj) {
        EnumC7329 enumC7329 = EnumC7329.COROUTINE_SUSPENDED;
        int i = this.f23372;
        if (i == 0) {
            C4350o9.m10270(obj);
            InterfaceC8120 interfaceC8120 = this.f23373.f6601;
            this.f23372 = 1;
            obj = interfaceC8120.getSelection(this);
            if (obj == enumC7329) {
                return enumC7329;
            }
        } else {
            if (i != 1) {
                throw new IllegalStateException("call to 'resume' before 'invoke' with coroutine");
            }
            C4350o9.m10270(obj);
        }
        Context context = this.f23373.f6600;
        fa1.m6825(context, "mContext");
        C7822 c7822 = new C7822(context);
        ((AppCompatEditText) c7822.f33072.findViewById(mp3.keywords)).setText((String) obj);
        c7822.f33073 = new C9409(this.f23373);
        c7822.setTitle(2131821468).show();
        return lx5.f14876;
    }
}
