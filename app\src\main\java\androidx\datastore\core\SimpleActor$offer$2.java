package androidx.datastore.core;

import androidx.exifinterface.media.ExifInterface;
import kotlin.Metadata;
import okhttp3.internal.p042io.InterfaceC4988s2;
import okhttp3.internal.p042io.InterfaceC7155;
import okhttp3.internal.p042io.InterfaceC7881;
import okhttp3.internal.p042io.di0;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.u75;
import okhttp3.internal.p042io.wv2;
import okhttp3.internal.p042io.zu2;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000\f\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\u0010\u0003\u001a\u00020\u0002\"\u0004\b\u0000\u0010\u0000*\u00020\u0001H\u008a@"}, m4115d2 = {ExifInterface.GPS_DIRECTION_TRUE, "Lokhttp3/internal/io/ღ;", "Lokhttp3/internal/io/lx5;", "<anonymous>"}, m4116k = 3, m4117mv = {1, 5, 1})
@InterfaceC4988s2(m11868c = "androidx.datastore.core.SimpleActor$offer$2", m11869f = "SimpleActor.kt", m11870l = {122, 122}, m11871m = "invokeSuspend")
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class SimpleActor$offer$2 extends u75 implements di0<InterfaceC7881, InterfaceC7155<? super lx5>, Object> {
    public Object L$0;
    public int label;
    public final /* synthetic */ SimpleActor<T> this$0;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public SimpleActor$offer$2(SimpleActor<T> simpleActor, InterfaceC7155<? super SimpleActor$offer$2> interfaceC7155) {
        super(2, interfaceC7155);
        this.this$0 = simpleActor;
    }

    @Override // okhttp3.internal.p042io.AbstractC7853
    @zu2
    public final InterfaceC7155<lx5> create(@wv2 Object obj, @zu2 InterfaceC7155<?> interfaceC7155) {
        return new SimpleActor$offer$2(this.this$0, interfaceC7155);
    }

    @Override // okhttp3.internal.p042io.di0
    @wv2
    /* renamed from: invoke, reason: avoid collision after fix types in other method and merged with bridge method [inline-methods] */
    public final Object mo18338invoke(@zu2 InterfaceC7881 interfaceC7881, @wv2 InterfaceC7155<? super lx5> interfaceC7155) {
        return ((SimpleActor$offer$2) create(interfaceC7881, interfaceC7155)).invokeSuspend(lx5.f14876);
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x005d A[RETURN] */
    /* JADX WARN: Removed duplicated region for block: B:12:0x005e  */
    /* JADX WARN: Removed duplicated region for block: B:15:0x006e A[RETURN] */
    /* JADX WARN: Removed duplicated region for block: B:16:0x006f  */
    /* JADX WARN: Removed duplicated region for block: B:17:0x007d  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:16:0x006f -> B:6:0x0071). Please report as a decompilation issue!!! */
    @Override // okhttp3.internal.p042io.AbstractC7853
    @okhttp3.internal.p042io.wv2
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final java.lang.Object invokeSuspend(@okhttp3.internal.p042io.zu2 java.lang.Object r8) {
        /*
            r7 = this;
            okhttp3.internal.io.ஐ r0 = okhttp3.internal.p042io.EnumC7329.COROUTINE_SUSPENDED
            int r1 = r7.label
            r2 = 2
            r3 = 1
            if (r1 == 0) goto L25
            if (r1 == r3) goto L1a
            if (r1 != r2) goto L12
            okhttp3.internal.p042io.C4350o9.m10270(r8)
            r8 = r7
            goto L71
        L12:
            java.lang.IllegalStateException r8 = new java.lang.IllegalStateException
            java.lang.String r0 = "call to 'resume' before 'invoke' with coroutine"
            r8.<init>(r0)
            throw r8
        L1a:
            java.lang.Object r1 = r7.L$0
            okhttp3.internal.io.di0 r1 = (okhttp3.internal.p042io.di0) r1
            okhttp3.internal.p042io.C4350o9.m10270(r8)
            r4 = r1
            r1 = r0
            r0 = r7
            goto L63
        L25:
            okhttp3.internal.p042io.C4350o9.m10270(r8)
            androidx.datastore.core.SimpleActor<T> r8 = r7.this$0
            java.util.concurrent.atomic.AtomicInteger r8 = androidx.datastore.core.SimpleActor.access$getRemainingMessages$p(r8)
            int r8 = r8.get()
            if (r8 <= 0) goto L36
            r8 = 1
            goto L37
        L36:
            r8 = 0
        L37:
            if (r8 == 0) goto L80
            r8 = r7
        L3a:
            androidx.datastore.core.SimpleActor<T> r1 = r8.this$0
            okhttp3.internal.io.ღ r1 = androidx.datastore.core.SimpleActor.access$getScope$p(r1)
            okhttp3.internal.io.ڛ r1 = r1.getCoroutineContext()
            okhttp3.internal.p042io.ns2.m10134(r1)
            androidx.datastore.core.SimpleActor<T> r1 = r8.this$0
            okhttp3.internal.io.di0 r1 = androidx.datastore.core.SimpleActor.access$getConsumeMessage$p(r1)
            androidx.datastore.core.SimpleActor<T> r4 = r8.this$0
            okhttp3.internal.io.ڨ r4 = androidx.datastore.core.SimpleActor.access$getMessageQueue$p(r4)
            r8.L$0 = r1
            r8.label = r3
            java.lang.Object r4 = r4.mo11824(r8)
            if (r4 != r0) goto L5e
            return r0
        L5e:
            r6 = r0
            r0 = r8
            r8 = r4
            r4 = r1
            r1 = r6
        L63:
            r5 = 0
            r0.L$0 = r5
            r0.label = r2
            java.lang.Object r8 = r4.mo18338invoke(r8, r0)
            if (r8 != r1) goto L6f
            return r1
        L6f:
            r8 = r0
            r0 = r1
        L71:
            androidx.datastore.core.SimpleActor<T> r1 = r8.this$0
            java.util.concurrent.atomic.AtomicInteger r1 = androidx.datastore.core.SimpleActor.access$getRemainingMessages$p(r1)
            int r1 = r1.decrementAndGet()
            if (r1 != 0) goto L3a
            okhttp3.internal.io.lx5 r8 = okhttp3.internal.p042io.lx5.f14876
            return r8
        L80:
            java.lang.IllegalStateException r8 = new java.lang.IllegalStateException
            java.lang.String r0 = "Check failed."
            java.lang.String r0 = r0.toString()
            r8.<init>(r0)
            throw r8
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.datastore.core.SimpleActor$offer$2.invokeSuspend(java.lang.Object):java.lang.Object");
    }
}
