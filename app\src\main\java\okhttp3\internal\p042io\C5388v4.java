package okhttp3.internal.p042io;

import java.lang.annotation.Annotation;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import okhttp3.internal.p042io.InterfaceC7108;

/* renamed from: okhttp3.internal.io.v4 */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5388v4 extends InterfaceC7108.AbstractC10014 {

    /* renamed from: Ϳ */
    public static final C5388v4 f23681 = new C5388v4();

    /* renamed from: okhttp3.internal.io.v4$Ϳ, reason: contains not printable characters */
    public class C9418 implements InterfaceC7108<Object, InterfaceC7310<?>> {

        /* renamed from: Ϳ */
        public final /* synthetic */ Type f23682;

        public C9418(Type type) {
            this.f23682 = type;
        }

        @Override // okhttp3.internal.p042io.InterfaceC7108
        /* renamed from: Ϳ */
        public final InterfaceC7310<?> mo5955(InterfaceC7310<Object> interfaceC7310) {
            return interfaceC7310;
        }

        @Override // okhttp3.internal.p042io.InterfaceC7108
        /* renamed from: Ԩ */
        public final Type mo5956() {
            return this.f23682;
        }
    }

    @Override // okhttp3.internal.p042io.InterfaceC7108.AbstractC10014
    /* renamed from: Ϳ */
    public final InterfaceC7108<?, ?> mo6370(Type type, Annotation[] annotationArr, m44 m44Var) {
        if (c06.m5495(type) != InterfaceC7310.class) {
            return null;
        }
        if (type instanceof ParameterizedType) {
            return new C9418(c06.m5494(0, (ParameterizedType) type));
        }
        throw new IllegalArgumentException("Call return type must be parameterized as Call<Foo> or Call<? extends Foo>");
    }
}
