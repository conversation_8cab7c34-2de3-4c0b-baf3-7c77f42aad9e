package androidx.compose.p000ui.viewinterop;

import android.content.Context;
import android.view.View;
import androidx.compose.p000ui.platform.AbstractComposeView;
import androidx.exifinterface.media.ExifInterface;
import com.stardust.autojs.engine.RhinoJavaScriptEngine;
import kotlin.Metadata;
import okhttp3.internal.p042io.AbstractC6779;
import okhttp3.internal.p042io.C2849b5;
import okhttp3.internal.p042io.C7747;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.fr2;
import okhttp3.internal.p042io.lv1;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.nh0;
import okhttp3.internal.p042io.ph0;
import okhttp3.internal.p042io.s86;
import okhttp3.internal.p042io.wv2;
import okhttp3.internal.p042io.zu2;

@Metadata(m4113bv = {}, m4114d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0000\u0018\u0000*\b\b\u0000\u0010\u0002*\u00020\u00012\u00020\u00032\u00020\u0004B%\u0012\u0006\u0010\u001b\u001a\u00020\u000f\u0012\n\b\u0002\u0010\u001d\u001a\u0004\u0018\u00010\u001c\u0012\b\b\u0002\u0010\u001f\u001a\u00020\u001e¢\u0006\u0004\b \u0010!R$\u0010\u000b\u001a\u0004\u0018\u00018\u00008\u0000@\u0000X\u0080\u000e¢\u0006\u0012\n\u0004\b\u0005\u0010\u0006\u001a\u0004\b\u0007\u0010\b\"\u0004\b\t\u0010\nR\u0014\u0010\r\u001a\u00020\u00018VX\u0096\u0004¢\u0006\u0006\u001a\u0004\b\f\u0010\bRF\u0010\u0011\u001a\u0010\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00028\u0000\u0018\u00010\u000e2\u0014\u0010\u0010\u001a\u0010\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00028\u0000\u0018\u00010\u000e8\u0006@FX\u0086\u000e¢\u0006\u0012\n\u0004\b\u0011\u0010\u0012\u001a\u0004\b\u0013\u0010\u0014\"\u0004\b\u0015\u0010\u0016RB\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00028\u0000\u0012\u0004\u0012\u00020\u00170\u000e2\u0012\u0010\u0010\u001a\u000e\u0012\u0004\u0012\u00028\u0000\u0012\u0004\u0012\u00020\u00170\u000e8\u0006@FX\u0086\u000e¢\u0006\u0012\n\u0004\b\u0018\u0010\u0012\u001a\u0004\b\u0019\u0010\u0014\"\u0004\b\u001a\u0010\u0016¨\u0006\""}, m4115d2 = {"Landroidx/compose/ui/viewinterop/ViewFactoryHolder;", "Landroid/view/View;", ExifInterface.GPS_DIRECTION_TRUE, "Landroidx/compose/ui/viewinterop/AndroidViewHolder;", "Lokhttp3/internal/io/s86;", "Ⴧ", "Landroid/view/View;", "getTypedView$ui_release", "()Landroid/view/View;", "setTypedView$ui_release", "(Landroid/view/View;)V", "typedView", "getViewRoot", "viewRoot", "Lkotlin/Function1;", "Landroid/content/Context;", "value", "factory", "Lokhttp3/internal/io/ph0;", "getFactory", "()Lokhttp3/internal/io/ph0;", "setFactory", "(Lokhttp3/internal/io/ph0;)V", "Lokhttp3/internal/io/lx5;", "updateBlock", "getUpdateBlock", "setUpdateBlock", "context", "Lokhttp3/internal/io/ܔ;", "parentContext", "Lokhttp3/internal/io/fr2;", "dispatcher", RhinoJavaScriptEngine.SOURCE_NAME_INIT, "(Landroid/content/Context;Lokhttp3/internal/io/ܔ;Lokhttp3/internal/io/fr2;)V", "ui_release"}, m4116k = 1, m4117mv = {1, 7, 1})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class ViewFactoryHolder<T extends View> extends AndroidViewHolder implements s86 {

    /* renamed from: Ⴧ, reason: from kotlin metadata */
    @wv2
    public T typedView;

    /* renamed from: Ⴭ */
    @wv2
    public ph0<? super Context, ? extends T> f261;

    /* renamed from: ჽ */
    @zu2
    public ph0<? super T, lx5> f262;

    /* renamed from: androidx.compose.ui.viewinterop.ViewFactoryHolder$Ϳ */
    public static final class C0211 extends lv1 implements nh0<lx5> {

        /* renamed from: ၥ */
        public final /* synthetic */ ViewFactoryHolder<T> f263;

        /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
        public C0211(ViewFactoryHolder<T> viewFactoryHolder) {
            super(0);
            this.f263 = viewFactoryHolder;
        }

        @Override // okhttp3.internal.p042io.nh0
        public final lx5 invoke() {
            T typedView$ui_release = this.f263.getTypedView$ui_release();
            if (typedView$ui_release != null) {
                this.f263.getUpdateBlock().invoke(typedView$ui_release);
            }
            return lx5.f14876;
        }
    }

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public ViewFactoryHolder(@zu2 Context context, @wv2 AbstractC6779 abstractC6779, @zu2 fr2 fr2Var) {
        super(context, abstractC6779, fr2Var);
        fa1.m6826(context, "context");
        fa1.m6826(fr2Var, "dispatcher");
        setClipChildren(false);
        ph0<View, lx5> ph0Var = C7747.f32813;
        this.f262 = C7747.f32813;
    }

    public /* synthetic */ ViewFactoryHolder(Context context, AbstractC6779 abstractC6779, fr2 fr2Var, int i, C2849b5 c2849b5) {
        this(context, (i & 2) != 0 ? null : abstractC6779, (i & 4) != 0 ? new fr2() : fr2Var);
    }

    @wv2
    public final ph0<Context, T> getFactory() {
        return this.f261;
    }

    @wv2
    public /* bridge */ /* synthetic */ AbstractComposeView getSubCompositionView() {
        return null;
    }

    @wv2
    public final T getTypedView$ui_release() {
        return this.typedView;
    }

    @zu2
    public final ph0<T, lx5> getUpdateBlock() {
        return this.f262;
    }

    @zu2
    public View getViewRoot() {
        return this;
    }

    public final void setFactory(@wv2 ph0<? super Context, ? extends T> ph0Var) {
        this.f261 = ph0Var;
        if (ph0Var != null) {
            Context context = getContext();
            fa1.m6825(context, "context");
            T invoke = ph0Var.invoke(context);
            this.typedView = invoke;
            setView$ui_release(invoke);
        }
    }

    public final void setTypedView$ui_release(@wv2 T t) {
        this.typedView = t;
    }

    public final void setUpdateBlock(@zu2 ph0<? super T, lx5> ph0Var) {
        fa1.m6826(ph0Var, "value");
        this.f262 = ph0Var;
        setUpdate(new C0211(this));
    }
}
