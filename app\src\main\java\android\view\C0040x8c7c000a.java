package android.view;

import android.view.View;
import kotlin.Metadata;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.lv1;
import okhttp3.internal.p042io.ph0;
import okhttp3.internal.p042io.wv2;
import okhttp3.internal.p042io.zu2;

@Metadata(m4114d1 = {"\u0000\n\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\u0010\u0000\u001a\u0004\u0018\u00010\u00012\u0006\u0010\u0002\u001a\u00020\u0001H\n¢\u0006\u0002\b\u0003"}, m4115d2 = {"<anonymous>", "Landroid/view/View;", "it", "invoke"}, m4116k = 3, m4117mv = {1, 6, 0}, m4119xi = 48)
/* renamed from: androidx.activity.ViewTreeOnBackPressedDispatcherOwner$findViewTreeOnBackPressedDispatcherOwner$1 */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class C0040x8c7c000a extends lv1 implements ph0<View, View> {
    public static final C0040x8c7c000a INSTANCE = new C0040x8c7c000a();

    public C0040x8c7c000a() {
        super(1);
    }

    @Override // okhttp3.internal.p042io.ph0
    @wv2
    public final View invoke(@zu2 View view) {
        fa1.m6826(view, "it");
        Object parent = view.getParent();
        if (parent instanceof View) {
            return (View) parent;
        }
        return null;
    }
}
