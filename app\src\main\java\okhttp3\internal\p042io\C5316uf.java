package okhttp3.internal.p042io;

import java.util.Comparator;
import java.util.Map;

/* renamed from: okhttp3.internal.io.uf */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5316uf implements Comparator<Map.Entry<Comparable<Object>, ?>> {
    @Override // java.util.Comparator
    public final int compare(Map.Entry<Comparable<Object>, ?> entry, Map.Entry<Comparable<Object>, ?> entry2) {
        return entry.getKey().compareTo(entry2.getKey());
    }
}
