package okhttp3.internal.p042io;

import com.stardust.autojs.engine.RhinoJavaScriptEngine;
import java.util.ArrayList;
import java.util.BitSet;
import java.util.HashSet;
import java.util.Iterator;

/* renamed from: okhttp3.internal.io.wu */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes3.dex */
public final class C5625wu {

    /* renamed from: Ϳ */
    public wy4 f25170;

    /* renamed from: Ԩ */
    public int f25171;

    /* renamed from: ԩ */
    public ArrayList<C9472> f25172 = new ArrayList<>();

    /* renamed from: okhttp3.internal.io.wu$Ϳ, reason: contains not printable characters */
    public static class C9472 {

        /* renamed from: Ϳ */
        public BitSet f25173;

        /* renamed from: Ԩ */
        public int f25174;

        /* renamed from: ԩ */
        public ArrayList<C9472> f25175;

        /* renamed from: Ԫ */
        public ArrayList<C9472> f25176;

        /* renamed from: ԫ */
        public boolean f25177;

        public C9472(int i, int i2, int i3) {
            BitSet bitSet = new BitSet(i2);
            this.f25173 = bitSet;
            bitSet.set(i);
            this.f25174 = i3;
            this.f25175 = new ArrayList<>();
            this.f25176 = new ArrayList<>();
            this.f25177 = false;
        }
    }

    public C5625wu(wy4 wy4Var) {
        this.f25170 = wy4Var;
        this.f25171 = wy4Var.f25239;
    }

    /* renamed from: Ϳ */
    public final void m13718(C9472 c9472, C9472 c94722) {
        if (!c94722.f25176.contains(c9472)) {
            c94722.f25176.add(c9472);
        }
        if (c9472.f25175.contains(c94722)) {
            return;
        }
        c9472.f25175.add(c94722);
    }

    /* renamed from: Ԩ */
    public final int m13719(ty3 ty3Var) {
        int i = 0;
        while (i < this.f25172.size() && !this.f25172.get(i).f25173.get(ty3Var.f22640)) {
            i++;
        }
        return i;
    }

    /* renamed from: ԩ */
    public final vy4 m13720(vy4 vy4Var) {
        return this.f25170.f25236.get(vy4Var.f24560.f22647.nextSetBit(0)).f22646.get(r2.size() - 1);
    }

    /* renamed from: Ԫ */
    public final vy4 m13721(vy4 vy4Var) {
        return this.f25170.f25236.get(vy4Var.f24560.f22648.nextSetBit(0)).f22646.get(0);
    }

    /* renamed from: ԫ */
    public final void m13722(vy4 vy4Var, ty3 ty3Var, HashSet<vy4> hashSet) {
        C7869 c7869 = new C7869(C3492gw.f11080);
        uy3 uy3Var = uy3.f23576;
        m13724(vy4Var, uy3Var, 40, c7869);
        ty4 ty4Var = vy4Var.f24560;
        ty4 m12645 = ty4Var.m12645(ty4Var.m12643());
        vy4 vy4Var2 = m12645.f22646.get(0);
        ty3 m12625 = ty3.m12625(this.f25170.m13772(), c7869, null);
        m13723(vy4Var2, uy3Var, m12625, 56, null);
        ty4 m126452 = m12645.m12645(m12645.m12643());
        vy4 vy4Var3 = m126452.f22646.get(0);
        m13724(vy4Var3, uy3.m13018(m12625, ty3Var), 52, new C7753(c7869, new C7225(new C7212(RhinoJavaScriptEngine.SOURCE_NAME_INIT), new C7212("(I)V"))));
        hashSet.add(vy4Var3);
        ty4 m126453 = m126452.m12645(m126452.m12643());
        vy4 vy4Var4 = m126453.f22646.get(0);
        m13724(vy4Var4, uy3.m13017(m12625), 35, null);
        m126453.m12649(m126453.f22650, this.f25170.m13767().f22653);
        hashSet.add(vy4Var4);
    }

    /* renamed from: Ԭ */
    public final void m13723(vy4 vy4Var, uy3 uy3Var, ty3 ty3Var, int i, AbstractC6941 abstractC6941) {
        a51 mo12143 = vy4Var.mo12143();
        n64 m13878 = i == 56 ? x64.m13878(ty3Var.getType()) : x64.m13881(i, ty3Var, uy3Var, abstractC6941);
        su2 su2Var = new su2(abstractC6941 == null ? new kc3(m13878, mo12143.f6114, ty3Var, uy3Var) : new jc3(m13878, mo12143.f6114, ty3Var, uy3Var, abstractC6941), vy4Var.f24560);
        ArrayList<vy4> arrayList = vy4Var.f24560.f22646;
        arrayList.add(arrayList.lastIndexOf(vy4Var), su2Var);
        this.f25170.m13774(su2Var);
    }

    /* renamed from: ԭ */
    public final void m13724(vy4 vy4Var, uy3 uy3Var, int i, AbstractC6941 abstractC6941) {
        a51 mo12143 = vy4Var.mo12143();
        n64 m13881 = x64.m13881(i, null, uy3Var, abstractC6941);
        su2 su2Var = new su2(abstractC6941 == null ? new pi5(m13881, mo12143.f6114, uy3Var, o25.f16481) : new oi5(m13881, mo12143.f6114, uy3Var, o25.f16481, abstractC6941), vy4Var.f24560);
        ArrayList<vy4> arrayList = vy4Var.f24560.f22646;
        arrayList.add(arrayList.lastIndexOf(vy4Var), su2Var);
        this.f25170.m13774(su2Var);
    }

    /* renamed from: Ԯ */
    public final void m13725(ty3 ty3Var, C9472 c9472) {
        int i;
        ArrayList arrayList = new ArrayList();
        arrayList.add(ty3Var);
        while (!arrayList.isEmpty()) {
            ty3 ty3Var2 = (ty3) arrayList.remove(arrayList.size() - 1);
            for (vy4 vy4Var : this.f25170.m13769(ty3Var2.f22640)) {
                if (vy4Var.mo12142() == null) {
                    int m13719 = m13719(vy4Var.f24561);
                    if (m13719 != this.f25172.size()) {
                        C9472 c94722 = this.f25172.get(m13719);
                        if (c94722 != c9472) {
                            c9472.f25177 = false;
                            c9472.f25173.or(c94722.f25173);
                            if (C6241.m14988(c9472.f25174, c94722.f25174) < 0) {
                                c9472.f25174 = c94722.f25174;
                            }
                            Iterator<C9472> it = c94722.f25176.iterator();
                            while (it.hasNext()) {
                                C9472 next = it.next();
                                next.f25175.remove(c94722);
                                next.f25175.add(c9472);
                                c9472.f25176.add(next);
                            }
                            Iterator<C9472> it2 = c94722.f25175.iterator();
                            while (it2.hasNext()) {
                                C9472 next2 = it2.next();
                                next2.f25176.remove(c94722);
                                next2.f25176.add(c9472);
                                c9472.f25175.add(next2);
                            }
                            this.f25172.remove(m13719);
                        }
                    } else {
                        c9472.f25173.set(vy4Var.f24561.f22640);
                        arrayList.add(vy4Var.f24561);
                    }
                } else {
                    int i2 = vy4Var.mo12142().f15777;
                    if (i2 != 2) {
                        if (i2 != 33 && i2 != 35) {
                            if (i2 == 43 || i2 == 7 || i2 == 8) {
                                if (C6241.m14988(c9472.f25174, 3) < 0) {
                                    c9472.f25174 = 3;
                                }
                            } else if (i2 != 38) {
                                if (i2 != 39) {
                                    switch (i2) {
                                        case 48:
                                            i = 5;
                                            c9472.f25174 = i;
                                            break;
                                    }
                                } else if (!vy4Var.mo12144().m13019(2).f22641.mo9370()) {
                                    c9472.f25177 = false;
                                }
                                if (vy4Var.mo12144().m13019(0).f22641.mo9369() == 9) {
                                    c9472.f25177 = false;
                                    uy3 mo12144 = vy4Var.mo12144();
                                    if (mo12144.m13019(0).f22640 == ty3Var2.f22640) {
                                        int m137192 = m13719(mo12144.m13019(1));
                                        if (m137192 != this.f25172.size()) {
                                            C9472 c94723 = this.f25172.get(m137192);
                                            m13718(c94723, c9472);
                                            if (C6241.m14988(c9472.f25174, c94723.f25174) < 0) {
                                                i = c94723.f25174;
                                                c9472.f25174 = i;
                                            }
                                        }
                                    } else {
                                        int m137193 = m13719(mo12144.m13019(0));
                                        if (m137193 != this.f25172.size()) {
                                            C9472 c94724 = this.f25172.get(m137193);
                                            m13718(c9472, c94724);
                                            if (C6241.m14988(c94724.f25174, c9472.f25174) < 0) {
                                                c94724.f25174 = c9472.f25174;
                                            }
                                        }
                                    }
                                }
                            } else if (!vy4Var.mo12144().m13019(1).f22641.mo9370()) {
                                c9472.f25177 = false;
                            }
                        }
                        i = 4;
                        c9472.f25174 = i;
                    } else {
                        c9472.f25173.set(vy4Var.f24561.f22640);
                        arrayList.add(vy4Var.f24561);
                    }
                }
            }
        }
    }
}
