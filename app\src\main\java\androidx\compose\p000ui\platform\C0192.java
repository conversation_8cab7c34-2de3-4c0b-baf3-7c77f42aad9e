package androidx.compose.p000ui.platform;

import android.view.ViewGroup;
import okhttp3.internal.p042io.zu2;

/* renamed from: androidx.compose.ui.platform.ހ */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class C0192 {

    /* renamed from: Ϳ */
    @zu2
    public static final ViewGroup.LayoutParams f194 = new ViewGroup.LayoutParams(-2, -2);

    /* JADX WARN: Removed duplicated region for block: B:19:0x0060  */
    /* JADX WARN: Removed duplicated region for block: B:26:0x008d  */
    /* JADX WARN: Removed duplicated region for block: B:33:0x00ce  */
    /* JADX WARN: Removed duplicated region for block: B:35:0x00d3  */
    /* JADX WARN: Type inference failed for: r5v3, types: [java.util.ArrayList, java.util.List<okhttp3.internal.io.ph0<java.lang.Object, okhttp3.internal.io.lx5>>] */
    @okhttp3.internal.p042io.zu2
    @okhttp3.internal.p042io.InterfaceC6721
    /* renamed from: Ϳ */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public static final okhttp3.internal.p042io.InterfaceC7474 m59(@okhttp3.internal.p042io.zu2 androidx.compose.p000ui.platform.AbstractComposeView r7, @okhttp3.internal.p042io.zu2 okhttp3.internal.p042io.AbstractC6779 r8, @okhttp3.internal.p042io.zu2 okhttp3.internal.p042io.di0<? super okhttp3.internal.p042io.InterfaceC6968, ? super java.lang.Integer, okhttp3.internal.p042io.lx5> r9) {
        /*
            java.lang.String r0 = "<this>"
            okhttp3.internal.p042io.fa1.m6826(r7, r0)
            java.lang.String r0 = "parent"
            okhttp3.internal.p042io.fa1.m6826(r8, r0)
            okhttp3.internal.io.jo0 r0 = okhttp3.internal.p042io.jo0.f13276
            java.util.concurrent.atomic.AtomicBoolean r0 = okhttp3.internal.p042io.jo0.f13277
            r1 = 0
            r2 = 1
            boolean r0 = r0.compareAndSet(r1, r2)
            r3 = 0
            if (r0 == 0) goto L49
            r0 = -1
            r4 = 6
            okhttp3.internal.io.ڨ r0 = okhttp3.internal.p042io.qw3.m11406(r0, r3, r4)
            okhttp3.internal.io.ל$Ԫ r4 = okhttp3.internal.p042io.C6604.f29384
            okhttp3.internal.io.wx1<okhttp3.internal.io.ڛ> r4 = okhttp3.internal.p042io.C6604.f29385
            java.lang.Object r4 = r4.getValue()
            okhttp3.internal.io.ڛ r4 = (okhttp3.internal.p042io.InterfaceC6710) r4
            okhttp3.internal.io.ღ r4 = okhttp3.internal.p042io.C6422.m15327(r4)
            okhttp3.internal.io.ho0 r5 = new okhttp3.internal.io.ho0
            r5.<init>(r0, r3)
            r6 = 3
            okhttp3.internal.p042io.C6814.m16042(r4, r3, r1, r5, r6)
            okhttp3.internal.io.io0 r4 = new okhttp3.internal.io.io0
            r4.<init>(r0)
            java.lang.Object r0 = okhttp3.internal.p042io.ev4.f9534
            monitor-enter(r0)
            java.util.List<okhttp3.internal.io.ph0<java.lang.Object, okhttp3.internal.io.lx5>> r5 = okhttp3.internal.p042io.ev4.f9539     // Catch: java.lang.Throwable -> L46
            r5.add(r4)     // Catch: java.lang.Throwable -> L46
            monitor-exit(r0)
            okhttp3.internal.p042io.ev4.m6614()
            goto L49
        L46:
            r7 = move-exception
            monitor-exit(r0)
            throw r7
        L49:
            int r0 = r7.getChildCount()
            if (r0 <= 0) goto L5a
            android.view.View r0 = r7.getChildAt(r1)
            boolean r4 = r0 instanceof androidx.compose.p000ui.platform.AndroidComposeView
            if (r4 == 0) goto L5d
            androidx.compose.ui.platform.AndroidComposeView r0 = (androidx.compose.p000ui.platform.AndroidComposeView) r0
            goto L5e
        L5a:
            r7.removeAllViews()
        L5d:
            r0 = r3
        L5e:
            if (r0 != 0) goto L77
            androidx.compose.ui.platform.AndroidComposeView r0 = new androidx.compose.ui.platform.AndroidComposeView
            android.content.Context r4 = r7.getContext()
            java.lang.String r5 = "context"
            okhttp3.internal.p042io.fa1.m6825(r4, r5)
            r0.<init>(r4)
            android.view.View r4 = r0.getView()
            android.view.ViewGroup$LayoutParams r5 = androidx.compose.p000ui.platform.C0192.f194
            r7.addView(r4, r5)
        L77:
            int r7 = android.os.Build.VERSION.SDK_INT
            r4 = 29
            if (r7 < r4) goto L8b
            okhttp3.internal.io.ld6 r7 = okhttp3.internal.p042io.ld6.f14513
            java.util.Map r7 = r7.m9238(r0)
            boolean r7 = r7.isEmpty()
            r7 = r7 ^ r2
            if (r7 == 0) goto L8b
            r1 = 1
        L8b:
            if (r1 == 0) goto Lb3
            int r7 = okhttp3.internal.p042io.op3.inspection_slot_table_set
            java.util.WeakHashMap r1 = new java.util.WeakHashMap
            r1.<init>()
            java.util.Set r1 = java.util.Collections.newSetFromMap(r1)
            r0.setTag(r7, r1)
            okhttp3.internal.io.ph0<okhttp3.internal.io.g51, okhttp3.internal.io.lx5> r7 = okhttp3.internal.p042io.e51.f9040
            java.lang.Class<okhttp3.internal.io.e51> r7 = okhttp3.internal.p042io.e51.class
            java.lang.String r1 = "Ԩ"
            java.lang.reflect.Field r7 = r7.getDeclaredField(r1)     // Catch: java.lang.Exception -> Lac
            r7.setAccessible(r2)     // Catch: java.lang.Exception -> Lac
            r7.setBoolean(r3, r2)     // Catch: java.lang.Exception -> Lac
            goto Lb3
        Lac:
            java.lang.String r7 = "Wrapper"
            java.lang.String r1 = "Could not access isDebugInspectorInfoEnabled. Please set explicitly."
            android.util.Log.w(r7, r1)
        Lb3:
            okhttp3.internal.io.zv5 r7 = new okhttp3.internal.io.zv5
            okhttp3.internal.io.gx1 r1 = r0.getF99()
            r7.<init>(r1)
            okhttp3.internal.io.ഉ r7 = okhttp3.internal.p042io.C6237.m14985(r7, r8)
            android.view.View r8 = r0.getView()
            int r1 = okhttp3.internal.p042io.op3.wrapped_composition_tag
            java.lang.Object r8 = r8.getTag(r1)
            boolean r2 = r8 instanceof androidx.compose.p000ui.platform.WrappedComposition
            if (r2 == 0) goto Ld1
            r3 = r8
            androidx.compose.ui.platform.WrappedComposition r3 = (androidx.compose.p000ui.platform.WrappedComposition) r3
        Ld1:
            if (r3 != 0) goto Ldf
            androidx.compose.ui.platform.WrappedComposition r3 = new androidx.compose.ui.platform.WrappedComposition
            r3.<init>(r0, r7)
            android.view.View r7 = r0.getView()
            r7.setTag(r1, r3)
        Ldf:
            r3.mo55(r9)
            return r3
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.compose.p000ui.platform.C0192.m59(androidx.compose.ui.platform.AbstractComposeView, okhttp3.internal.io.ܔ, okhttp3.internal.io.di0):okhttp3.internal.io.ഉ");
    }
}
