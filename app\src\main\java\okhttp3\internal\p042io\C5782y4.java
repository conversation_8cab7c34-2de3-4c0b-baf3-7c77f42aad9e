package okhttp3.internal.p042io;

import java.util.Map;

/* renamed from: okhttp3.internal.io.y4 */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5782y4 implements InterfaceC7124 {

    /* renamed from: Ϳ */
    public final Map<String, InterfaceC7839> f26382;

    public C5782y4(Map<String, InterfaceC7839> map) {
        this.f26382 = map;
    }

    @Override // okhttp3.internal.p042io.InterfaceC7124
    /* renamed from: Ϳ */
    public final InterfaceC7839 mo8092(String str) {
        if (str == null || str.equals("")) {
            return null;
        }
        return this.f26382.get(str.toUpperCase());
    }
}
