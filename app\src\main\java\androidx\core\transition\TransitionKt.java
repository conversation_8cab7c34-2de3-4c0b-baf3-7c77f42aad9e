package androidx.core.transition;

import android.annotation.SuppressLint;
import android.transition.Transition;
import androidx.annotation.RequiresApi;
import androidx.autofill.HintConstants;
import kotlin.Metadata;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.ph0;
import okhttp3.internal.p042io.zu2;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\f\u001a5\u0010\b\u001a\u00020\u0007*\u00020\u00002#\b\u0004\u0010\u0006\u001a\u001d\u0012\u0013\u0012\u00110\u0000¢\u0006\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0004\u0012\u0004\u0012\u00020\u00050\u0001H\u0087\bø\u0001\u0000\u001a5\u0010\t\u001a\u00020\u0007*\u00020\u00002#\b\u0004\u0010\u0006\u001a\u001d\u0012\u0013\u0012\u00110\u0000¢\u0006\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0004\u0012\u0004\u0012\u00020\u00050\u0001H\u0087\bø\u0001\u0000\u001a5\u0010\n\u001a\u00020\u0007*\u00020\u00002#\b\u0004\u0010\u0006\u001a\u001d\u0012\u0013\u0012\u00110\u0000¢\u0006\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0004\u0012\u0004\u0012\u00020\u00050\u0001H\u0087\bø\u0001\u0000\u001a5\u0010\u000b\u001a\u00020\u0007*\u00020\u00002#\b\u0004\u0010\u0006\u001a\u001d\u0012\u0013\u0012\u00110\u0000¢\u0006\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0004\u0012\u0004\u0012\u00020\u00050\u0001H\u0087\bø\u0001\u0000\u001a5\u0010\f\u001a\u00020\u0007*\u00020\u00002#\b\u0004\u0010\u0006\u001a\u001d\u0012\u0013\u0012\u00110\u0000¢\u0006\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0004\u0012\u0004\u0012\u00020\u00050\u0001H\u0087\bø\u0001\u0000\u001aÉ\u0001\u0010\u0012\u001a\u00020\u0007*\u00020\u00002#\b\u0006\u0010\r\u001a\u001d\u0012\u0013\u0012\u00110\u0000¢\u0006\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0004\u0012\u0004\u0012\u00020\u00050\u00012#\b\u0006\u0010\u000e\u001a\u001d\u0012\u0013\u0012\u00110\u0000¢\u0006\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0004\u0012\u0004\u0012\u00020\u00050\u00012#\b\u0006\u0010\u000f\u001a\u001d\u0012\u0013\u0012\u00110\u0000¢\u0006\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0004\u0012\u0004\u0012\u00020\u00050\u00012#\b\u0006\u0010\u0010\u001a\u001d\u0012\u0013\u0012\u00110\u0000¢\u0006\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0004\u0012\u0004\u0012\u00020\u00050\u00012#\b\u0006\u0010\u0011\u001a\u001d\u0012\u0013\u0012\u00110\u0000¢\u0006\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0004\u0012\u0004\u0012\u00020\u00050\u0001H\u0087\bø\u0001\u0000\u0082\u0002\u0007\n\u0005\b\u009920\u0001¨\u0006\u0013"}, m4115d2 = {"Landroid/transition/Transition;", "Lkotlin/Function1;", "Lokhttp3/internal/io/o73;", HintConstants.AUTOFILL_HINT_NAME, "transition", "Lokhttp3/internal/io/lx5;", "action", "Landroid/transition/Transition$TransitionListener;", "doOnEnd", "doOnStart", "doOnCancel", "doOnResume", "doOnPause", "onEnd", "onStart", "onCancel", "onResume", "onPause", "addListener", "core-ktx_release"}, m4116k = 2, m4117mv = {1, 6, 0})
@SuppressLint({"ClassVerificationFailure"})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class TransitionKt {
    @RequiresApi(19)
    @zu2
    public static final Transition.TransitionListener addListener(@zu2 Transition transition, @zu2 ph0<? super Transition, lx5> ph0Var, @zu2 ph0<? super Transition, lx5> ph0Var2, @zu2 ph0<? super Transition, lx5> ph0Var3, @zu2 ph0<? super Transition, lx5> ph0Var4, @zu2 ph0<? super Transition, lx5> ph0Var5) {
        fa1.m6826(transition, "<this>");
        fa1.m6826(ph0Var, "onEnd");
        fa1.m6826(ph0Var2, "onStart");
        fa1.m6826(ph0Var3, "onCancel");
        fa1.m6826(ph0Var4, "onResume");
        fa1.m6826(ph0Var5, "onPause");
        TransitionKt$addListener$listener$1 transitionKt$addListener$listener$1 = new TransitionKt$addListener$listener$1(ph0Var, ph0Var4, ph0Var5, ph0Var3, ph0Var2);
        transition.addListener(transitionKt$addListener$listener$1);
        return transitionKt$addListener$listener$1;
    }

    public static /* synthetic */ Transition.TransitionListener addListener$default(Transition transition, ph0 ph0Var, ph0 ph0Var2, ph0 ph0Var3, ph0 ph0Var4, ph0 ph0Var5, int i, Object obj) {
        if ((i & 1) != 0) {
            ph0Var = TransitionKt$addListener$1.INSTANCE;
        }
        if ((i & 2) != 0) {
            ph0Var2 = TransitionKt$addListener$2.INSTANCE;
        }
        ph0 ph0Var6 = ph0Var2;
        if ((i & 4) != 0) {
            ph0Var3 = TransitionKt$addListener$3.INSTANCE;
        }
        ph0 ph0Var7 = ph0Var3;
        if ((i & 8) != 0) {
            ph0Var4 = TransitionKt$addListener$4.INSTANCE;
        }
        if ((i & 16) != 0) {
            ph0Var5 = TransitionKt$addListener$5.INSTANCE;
        }
        fa1.m6826(transition, "<this>");
        fa1.m6826(ph0Var, "onEnd");
        fa1.m6826(ph0Var6, "onStart");
        fa1.m6826(ph0Var7, "onCancel");
        fa1.m6826(ph0Var4, "onResume");
        fa1.m6826(ph0Var5, "onPause");
        TransitionKt$addListener$listener$1 transitionKt$addListener$listener$1 = new TransitionKt$addListener$listener$1(ph0Var, ph0Var4, ph0Var5, ph0Var7, ph0Var6);
        transition.addListener(transitionKt$addListener$listener$1);
        return transitionKt$addListener$listener$1;
    }

    @RequiresApi(19)
    @zu2
    public static final Transition.TransitionListener doOnCancel(@zu2 Transition transition, @zu2 final ph0<? super Transition, lx5> ph0Var) {
        fa1.m6826(transition, "<this>");
        fa1.m6826(ph0Var, "action");
        Transition.TransitionListener transitionListener = new Transition.TransitionListener() { // from class: androidx.core.transition.TransitionKt$doOnCancel$$inlined$addListener$default$1
            @Override // android.transition.Transition.TransitionListener
            public void onTransitionCancel(@zu2 Transition transition2) {
                fa1.m6826(transition2, "transition");
                ph0.this.invoke(transition2);
            }

            @Override // android.transition.Transition.TransitionListener
            public void onTransitionEnd(@zu2 Transition transition2) {
                fa1.m6826(transition2, "transition");
            }

            @Override // android.transition.Transition.TransitionListener
            public void onTransitionPause(@zu2 Transition transition2) {
                fa1.m6826(transition2, "transition");
            }

            @Override // android.transition.Transition.TransitionListener
            public void onTransitionResume(@zu2 Transition transition2) {
                fa1.m6826(transition2, "transition");
            }

            @Override // android.transition.Transition.TransitionListener
            public void onTransitionStart(@zu2 Transition transition2) {
                fa1.m6826(transition2, "transition");
            }
        };
        transition.addListener(transitionListener);
        return transitionListener;
    }

    @RequiresApi(19)
    @zu2
    public static final Transition.TransitionListener doOnEnd(@zu2 Transition transition, @zu2 final ph0<? super Transition, lx5> ph0Var) {
        fa1.m6826(transition, "<this>");
        fa1.m6826(ph0Var, "action");
        Transition.TransitionListener transitionListener = new Transition.TransitionListener() { // from class: androidx.core.transition.TransitionKt$doOnEnd$$inlined$addListener$default$1
            @Override // android.transition.Transition.TransitionListener
            public void onTransitionCancel(@zu2 Transition transition2) {
                fa1.m6826(transition2, "transition");
            }

            @Override // android.transition.Transition.TransitionListener
            public void onTransitionEnd(@zu2 Transition transition2) {
                fa1.m6826(transition2, "transition");
                ph0.this.invoke(transition2);
            }

            @Override // android.transition.Transition.TransitionListener
            public void onTransitionPause(@zu2 Transition transition2) {
                fa1.m6826(transition2, "transition");
            }

            @Override // android.transition.Transition.TransitionListener
            public void onTransitionResume(@zu2 Transition transition2) {
                fa1.m6826(transition2, "transition");
            }

            @Override // android.transition.Transition.TransitionListener
            public void onTransitionStart(@zu2 Transition transition2) {
                fa1.m6826(transition2, "transition");
            }
        };
        transition.addListener(transitionListener);
        return transitionListener;
    }

    @RequiresApi(19)
    @zu2
    public static final Transition.TransitionListener doOnPause(@zu2 Transition transition, @zu2 final ph0<? super Transition, lx5> ph0Var) {
        fa1.m6826(transition, "<this>");
        fa1.m6826(ph0Var, "action");
        Transition.TransitionListener transitionListener = new Transition.TransitionListener() { // from class: androidx.core.transition.TransitionKt$doOnPause$$inlined$addListener$default$1
            @Override // android.transition.Transition.TransitionListener
            public void onTransitionCancel(@zu2 Transition transition2) {
                fa1.m6826(transition2, "transition");
            }

            @Override // android.transition.Transition.TransitionListener
            public void onTransitionEnd(@zu2 Transition transition2) {
                fa1.m6826(transition2, "transition");
            }

            @Override // android.transition.Transition.TransitionListener
            public void onTransitionPause(@zu2 Transition transition2) {
                fa1.m6826(transition2, "transition");
                ph0.this.invoke(transition2);
            }

            @Override // android.transition.Transition.TransitionListener
            public void onTransitionResume(@zu2 Transition transition2) {
                fa1.m6826(transition2, "transition");
            }

            @Override // android.transition.Transition.TransitionListener
            public void onTransitionStart(@zu2 Transition transition2) {
                fa1.m6826(transition2, "transition");
            }
        };
        transition.addListener(transitionListener);
        return transitionListener;
    }

    @RequiresApi(19)
    @zu2
    public static final Transition.TransitionListener doOnResume(@zu2 Transition transition, @zu2 final ph0<? super Transition, lx5> ph0Var) {
        fa1.m6826(transition, "<this>");
        fa1.m6826(ph0Var, "action");
        Transition.TransitionListener transitionListener = new Transition.TransitionListener() { // from class: androidx.core.transition.TransitionKt$doOnResume$$inlined$addListener$default$1
            @Override // android.transition.Transition.TransitionListener
            public void onTransitionCancel(@zu2 Transition transition2) {
                fa1.m6826(transition2, "transition");
            }

            @Override // android.transition.Transition.TransitionListener
            public void onTransitionEnd(@zu2 Transition transition2) {
                fa1.m6826(transition2, "transition");
            }

            @Override // android.transition.Transition.TransitionListener
            public void onTransitionPause(@zu2 Transition transition2) {
                fa1.m6826(transition2, "transition");
            }

            @Override // android.transition.Transition.TransitionListener
            public void onTransitionResume(@zu2 Transition transition2) {
                fa1.m6826(transition2, "transition");
                ph0.this.invoke(transition2);
            }

            @Override // android.transition.Transition.TransitionListener
            public void onTransitionStart(@zu2 Transition transition2) {
                fa1.m6826(transition2, "transition");
            }
        };
        transition.addListener(transitionListener);
        return transitionListener;
    }

    @RequiresApi(19)
    @zu2
    public static final Transition.TransitionListener doOnStart(@zu2 Transition transition, @zu2 final ph0<? super Transition, lx5> ph0Var) {
        fa1.m6826(transition, "<this>");
        fa1.m6826(ph0Var, "action");
        Transition.TransitionListener transitionListener = new Transition.TransitionListener() { // from class: androidx.core.transition.TransitionKt$doOnStart$$inlined$addListener$default$1
            @Override // android.transition.Transition.TransitionListener
            public void onTransitionCancel(@zu2 Transition transition2) {
                fa1.m6826(transition2, "transition");
            }

            @Override // android.transition.Transition.TransitionListener
            public void onTransitionEnd(@zu2 Transition transition2) {
                fa1.m6826(transition2, "transition");
            }

            @Override // android.transition.Transition.TransitionListener
            public void onTransitionPause(@zu2 Transition transition2) {
                fa1.m6826(transition2, "transition");
            }

            @Override // android.transition.Transition.TransitionListener
            public void onTransitionResume(@zu2 Transition transition2) {
                fa1.m6826(transition2, "transition");
            }

            @Override // android.transition.Transition.TransitionListener
            public void onTransitionStart(@zu2 Transition transition2) {
                fa1.m6826(transition2, "transition");
                ph0.this.invoke(transition2);
            }
        };
        transition.addListener(transitionListener);
        return transitionListener;
    }
}
