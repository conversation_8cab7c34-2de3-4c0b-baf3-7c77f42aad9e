package okhttp3.internal.p042io;

import okhttp3.internal.p042io.InterfaceC6038;
import okhttp3.internal.p042io.oh2;

/* renamed from: okhttp3.internal.io.x7 */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final /* synthetic */ class C5678x7 {
    /* renamed from: Ϳ */
    public static void m13885(StringBuilder sb, String str, String str2, String str3, String str4) {
        sb.append(str);
        sb.append(str2);
        sb.append(str3);
        sb.append(str4);
    }

    /* renamed from: Ԩ */
    public static void m13886(InterfaceC6038 interfaceC6038, oh2.InterfaceC4390 interfaceC4390) {
        InterfaceC6038.InterfaceC9560 makeBackgroundTaskQueue = interfaceC6038.makeBackgroundTaskQueue();
        oh2.C4391 c4391 = oh2.C4391.f16774;
        C7784 c7784 = new C7784(interfaceC6038, "dev.flutter.pigeon.PathProviderApi.getTemporaryPath", c4391, makeBackgroundTaskQueue);
        int i = 4;
        if (interfaceC4390 != null) {
            c7784.m17413(new C6204(interfaceC4390, i));
        } else {
            c7784.m17413(null);
        }
        C7784 c77842 = new C7784(interfaceC6038, "dev.flutter.pigeon.PathProviderApi.getApplicationSupportPath", c4391, interfaceC6038.makeBackgroundTaskQueue());
        int i2 = 3;
        if (interfaceC4390 != null) {
            c77842.m17413(new s00(interfaceC4390, i2));
        } else {
            c77842.m17413(null);
        }
        C7784 c77843 = new C7784(interfaceC6038, "dev.flutter.pigeon.PathProviderApi.getApplicationDocumentsPath", c4391, interfaceC6038.makeBackgroundTaskQueue());
        if (interfaceC4390 != null) {
            c77843.m17413(new ck2(interfaceC4390, i));
        } else {
            c77843.m17413(null);
        }
        C7784 c77844 = new C7784(interfaceC6038, "dev.flutter.pigeon.PathProviderApi.getExternalStoragePath", c4391, interfaceC6038.makeBackgroundTaskQueue());
        if (interfaceC4390 != null) {
            c77844.m17413(new u00(interfaceC4390, i2));
        } else {
            c77844.m17413(null);
        }
        C7784 c77845 = new C7784(interfaceC6038, "dev.flutter.pigeon.PathProviderApi.getExternalCachePaths", c4391, interfaceC6038.makeBackgroundTaskQueue());
        if (interfaceC4390 != null) {
            c77845.m17413(new nh2(interfaceC4390, 0));
        } else {
            c77845.m17413(null);
        }
        C7784 c77846 = new C7784(interfaceC6038, "dev.flutter.pigeon.PathProviderApi.getExternalStoragePaths", c4391, interfaceC6038.makeBackgroundTaskQueue());
        if (interfaceC4390 != null) {
            c77846.m17413(new v00(interfaceC4390, 2));
        } else {
            c77846.m17413(null);
        }
    }
}
