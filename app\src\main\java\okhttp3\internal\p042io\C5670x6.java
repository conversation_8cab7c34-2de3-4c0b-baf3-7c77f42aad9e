package okhttp3.internal.p042io;

/* renamed from: okhttp3.internal.io.x6 */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public class C5670x6<T> extends AbstractC7012<T> implements InterfaceC5280u6<T>, qi4<T> {
    public C5670x6(@zu2 InterfaceC6710 interfaceC6710, boolean z) {
        super(interfaceC6710, z);
    }

    @Override // okhttp3.internal.p042io.InterfaceC5280u6
    @wv2
    /* renamed from: ޕ */
    public final Object mo12752(@zu2 InterfaceC7155<? super T> interfaceC7155) {
        return m6047(interfaceC7155);
    }
}
