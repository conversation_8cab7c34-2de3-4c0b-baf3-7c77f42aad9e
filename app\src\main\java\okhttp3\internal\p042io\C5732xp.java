package okhttp3.internal.p042io;

import java.math.BigInteger;

/* renamed from: okhttp3.internal.io.xp */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5732xp implements InterfaceC6218 {

    /* renamed from: Ϳ */
    public BigInteger f26128;

    /* renamed from: Ԩ */
    public BigInteger f26129;

    /* renamed from: ԩ */
    public int f26130;

    public C5732xp(BigInteger bigInteger, BigInteger bigInteger2, int i) {
        this.f26128 = bigInteger2;
        this.f26129 = bigInteger;
        this.f26130 = i;
    }

    public final boolean equals(Object obj) {
        if (!(obj instanceof C5732xp)) {
            return false;
        }
        C5732xp c5732xp = (C5732xp) obj;
        return c5732xp.f26129.equals(this.f26129) && c5732xp.f26128.equals(this.f26128) && c5732xp.f26130 == this.f26130;
    }

    public final int hashCode() {
        return (this.f26129.hashCode() ^ this.f26128.hashCode()) + this.f26130;
    }
}
