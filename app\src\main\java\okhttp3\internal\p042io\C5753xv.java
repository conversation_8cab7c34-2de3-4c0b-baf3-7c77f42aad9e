package okhttp3.internal.p042io;

/* renamed from: okhttp3.internal.io.xv */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5753xv {

    /* renamed from: Ϳ */
    public static final C9493 f26198 = new C9493();

    /* renamed from: okhttp3.internal.io.xv$Ϳ, reason: contains not printable characters */
    public static final class C9493 extends Throwable {
        private static final long serialVersionUID = -4649703670690200604L;

        public C9493() {
            super("No further exceptions");
        }

        @Override // java.lang.Throwable
        public final Throwable fillInStackTrace() {
            return this;
        }
    }

    /* renamed from: Ϳ */
    public static RuntimeException m14070(Throwable th) {
        if (th instanceof Error) {
            throw ((Error) th);
        }
        return th instanceof RuntimeException ? (RuntimeException) th : new RuntimeException(th);
    }
}
