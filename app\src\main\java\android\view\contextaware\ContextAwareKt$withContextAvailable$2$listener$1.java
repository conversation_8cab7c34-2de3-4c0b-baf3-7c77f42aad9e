package android.view.contextaware;

import android.content.Context;
import kotlin.Metadata;
import okhttp3.internal.p042io.C4350o9;
import okhttp3.internal.p042io.InterfaceC7155;
import okhttp3.internal.p042io.InterfaceC7300;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.ph0;
import okhttp3.internal.p042io.zu2;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000\u0017\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002*\u0001\u0000\b\n\u0018\u00002\u00020\u0001J\u0010\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H\u0016¨\u0006\u0006"}, m4115d2 = {"androidx/activity/contextaware/ContextAwareKt$withContextAvailable$2$listener$1", "Landroidx/activity/contextaware/OnContextAvailableListener;", "Landroid/content/Context;", "context", "Lokhttp3/internal/io/lx5;", "onContextAvailable", "activity-ktx_release"}, m4116k = 1, m4117mv = {1, 6, 0})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class ContextAwareKt$withContextAvailable$2$listener$1 implements OnContextAvailableListener {
    public final /* synthetic */ InterfaceC7300<R> $co;
    public final /* synthetic */ ph0<Context, R> $onContextAvailable;

    /* JADX WARN: Multi-variable type inference failed */
    public ContextAwareKt$withContextAvailable$2$listener$1(InterfaceC7300<? super R> interfaceC7300, ph0<? super Context, ? extends R> ph0Var) {
        this.$co = interfaceC7300;
        this.$onContextAvailable = ph0Var;
    }

    @Override // android.view.contextaware.OnContextAvailableListener
    public void onContextAvailable(@zu2 Context context) {
        Object m10267;
        fa1.m6826(context, "context");
        InterfaceC7155 interfaceC7155 = this.$co;
        try {
            m10267 = this.$onContextAvailable.invoke(context);
        } catch (Throwable th) {
            m10267 = C4350o9.m10267(th);
        }
        interfaceC7155.resumeWith(m10267);
    }
}
