package okhttp3.internal.p042io;

import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.UnknownHostException;

/* renamed from: okhttp3.internal.io.vn */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5489vn extends AbstractC7011 {

    /* renamed from: Ϳ */
    public final n82 f24288 = p82.m10674(C5489vn.class);

    @Override // okhttp3.internal.p042io.InterfaceC7839
    /* renamed from: Ϳ */
    public final void mo4509(xg0 xg0Var, eh0 eh0Var, ah0 ah0Var) {
        a62 a62Var;
        xg0Var.m13960();
        String str = ((C5016s5) ah0Var).f20881;
        if (str == null) {
            a62Var = new a62(501, n71.m9944(xg0Var, ah0Var, eh0Var, 501, "EPRT", null));
        } else {
            InterfaceC5879z mo5326 = xg0Var.m13956().mo5326();
            if (mo5326.mo5520()) {
                try {
                    int indexOf = str.indexOf(str.charAt(0), 3);
                    String substring = str.substring(3, indexOf);
                    String substring2 = str.substring(indexOf + 1, str.length() - 1);
                    try {
                        InetAddress byName = InetAddress.getByName(substring);
                        if (mo5326.mo5525() && (xg0Var.mo7760() instanceof InetSocketAddress) && !byName.equals(((InetSocketAddress) xg0Var.mo7760()).getAddress())) {
                            a62Var = new a62(501, n71.m9944(xg0Var, ah0Var, eh0Var, 501, "EPRT.mismatch", null));
                        } else {
                            try {
                                xg0Var.m13954().mo11355(new InetSocketAddress(byName, Integer.parseInt(substring2)));
                                a62Var = new a62(200, n71.m9944(xg0Var, ah0Var, eh0Var, 200, "EPRT", null));
                            } catch (NumberFormatException e) {
                                this.f24288.mo7592("Invalid port: " + substring2, e);
                                a62Var = new a62(501, n71.m9944(xg0Var, ah0Var, eh0Var, 501, "EPRT.invalid", null));
                            }
                        }
                    } catch (UnknownHostException e2) {
                        this.f24288.mo7592("Unknown host: " + substring, e2);
                        a62Var = new a62(501, n71.m9944(xg0Var, ah0Var, eh0Var, 501, "EPRT.host", null));
                    }
                } catch (Exception e3) {
                    this.f24288.mo7592("Exception parsing host and port: " + str, e3);
                    a62Var = new a62(501, n71.m9944(xg0Var, ah0Var, eh0Var, 501, "EPRT", null));
                }
            } else {
                a62Var = new a62(501, n71.m9944(xg0Var, ah0Var, eh0Var, 501, "EPRT.disabled", null));
            }
        }
        xg0Var.mo7737(a62Var);
    }
}
