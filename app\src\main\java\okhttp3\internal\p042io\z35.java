package okhttp3.internal.p042io;

import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import kotlin.reflect.jvm.internal.impl.builtins.AbstractC2633;
import okhttp3.internal.p042io.C2782aj;
import okhttp3.internal.p042io.C5940zi;
import okhttp3.internal.p042io.rk2;

@kp0
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes3.dex */
public class z35 {

    /* renamed from: Ϳ */
    @zu2
    public static final z35 f27094 = new z35();

    @gz4
    /* renamed from: Ϳ */
    public static final long m14428(float f, float f2) {
        long floatToIntBits = (Float.floatToIntBits(f2) & 4294967295L) | (Float.floatToIntBits(f) << 32);
        C5940zi.C9527 c9527 = C5940zi.f27423;
        return floatToIntBits;
    }

    @gz4
    /* renamed from: Ԩ */
    public static final long m14429(float f, float f2) {
        long floatToIntBits = (Float.floatToIntBits(f2) & 4294967295L) | (Float.floatToIntBits(f) << 32);
        C2782aj.C8932 c8932 = C2782aj.f6449;
        return floatToIntBits;
    }

    /* renamed from: Ԫ */
    public static boolean m14430(@zv2 Object obj, @zv2 Object obj2) {
        return obj == obj2 || (obj != null && obj.equals(obj2));
    }

    /* renamed from: ԫ */
    public static final int m14431(@zu2 CharSequence charSequence, int i) {
        fa1.m6826(charSequence, "<this>");
        int length = charSequence.length();
        for (int i2 = i + 1; i2 < length; i2++) {
            if (charSequence.charAt(i2) == '\n') {
                return i2;
            }
        }
        return charSequence.length();
    }

    /* renamed from: Ԭ */
    public static final int m14432(@zu2 CharSequence charSequence, int i) {
        fa1.m6826(charSequence, "<this>");
        for (int i2 = i - 1; i2 > 0; i2--) {
            if (charSequence.charAt(i2 - 1) == '\n') {
                return i2;
            }
        }
        return 0;
    }

    /* JADX WARN: Type inference failed for: r1v2, types: [java.util.ArrayList, java.util.List<okhttp3.internal.io.z63>] */
    /* renamed from: ԭ */
    public static final float m14433(@zu2 pf5 pf5Var, int i, boolean z, boolean z2) {
        boolean z3 = pf5Var.m10747(((!z || z2) && (z || !z2)) ? Math.max(i + (-1), 0) : i) == pf5Var.m10759(i);
        wm2 wm2Var = pf5Var.f17375;
        wm2Var.m13680(i);
        z63 z63Var = (z63) wm2Var.f25087.get(i == wm2Var.f25080.f26060.length() ? fa1.m6834(wm2Var.f25087) : ym2.m14320(wm2Var.f25087, i));
        return z63Var.f27140.mo14177(z63Var.m14479(i), z3);
    }

    @oo3
    /* renamed from: ԯ */
    public static final int m14434(int i) {
        if (i < 0) {
            return i;
        }
        if (i < 3) {
            return i + 1;
        }
        if (i < 1073741824) {
            return (int) ((i / 0.75f) + 1.0f);
        }
        return Integer.MAX_VALUE;
    }

    /* renamed from: ֏ */
    public static InterfaceC7250 m14435(z35 z35Var, ig0 ig0Var, AbstractC2633 abstractC2633) {
        Objects.requireNonNull(z35Var);
        fa1.m6826(abstractC2633, "builtIns");
        C7349 m9678 = mg1.f15311.m9678(ig0Var);
        if (m9678 != null) {
            return abstractC2633.m4158(m9678.m16914());
        }
        return null;
    }

    @zu2
    /* renamed from: ؠ */
    public static final Map m14436(@zu2 v63 v63Var) {
        fa1.m6826(v63Var, "pair");
        Map singletonMap = Collections.singletonMap(v63Var.f23750, v63Var.f23751);
        fa1.m6825(singletonMap, "singletonMap(pair.first, pair.second)");
        return singletonMap;
    }

    /* renamed from: ހ */
    public static rk2 m14437(rk2 rk2Var, float f, co4 co4Var) {
        long j = to0.f22396;
        fa1.m6826(rk2Var, "$this$shadow");
        fa1.m6826(co4Var, "shape");
        if (Float.compare(f, 0) <= 0) {
            return rk2Var;
        }
        ph0<g51, lx5> ph0Var = e51.f9040;
        ph0<g51, lx5> ph0Var2 = e51.f9040;
        return e51.m6332(rk2Var, C7451.m17038(rk2.C4938.f20511, new ao4(f, co4Var, false, j, j)));
    }

    @zu2
    /* renamed from: ށ */
    public static final Map m14438(@zu2 Map map) {
        Map.Entry entry = (Map.Entry) map.entrySet().iterator().next();
        Map singletonMap = Collections.singletonMap(entry.getKey(), entry.getValue());
        fa1.m6825(singletonMap, "with(entries.iterator().…ingletonMap(key, value) }");
        return singletonMap;
    }

    @zu2
    /* renamed from: ԩ */
    public InterfaceC7250 m14439(@zu2 InterfaceC7250 interfaceC7250) {
        jg0 m6378 = C3203e9.m6378(interfaceC7250);
        mg1 mg1Var = mg1.f15311;
        ig0 ig0Var = mg1.f15322.get(m6378);
        if (ig0Var != null) {
            return C3306f9.m6795(interfaceC7250).m4158(ig0Var);
        }
        throw new IllegalArgumentException("Given class " + interfaceC7250 + " is not a read-only collection");
    }

    /* renamed from: Ԯ */
    public boolean m14440(@zu2 InterfaceC7250 interfaceC7250) {
        fa1.m6826(interfaceC7250, "mutable");
        mg1 mg1Var = mg1.f15311;
        return mg1.f15321.containsKey(C3203e9.m6378(interfaceC7250));
    }
}
