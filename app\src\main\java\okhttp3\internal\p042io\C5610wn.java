package okhttp3.internal.p042io;

import com.stardust.autojs.core.inputevent.InputEventCodes;

/* renamed from: okhttp3.internal.io.wn */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5610wn extends AbstractC7011 {
    @Override // okhttp3.internal.p042io.InterfaceC7839
    /* renamed from: Ϳ */
    public final void mo4509(xg0 xg0Var, eh0 eh0Var, ah0 ah0Var) {
        xg0Var.m13960();
        try {
            xg0Var.mo7737(new a62(InputEventCodes.KEY_KBDILLUMDOWN, n71.m9944(xg0Var, ah0Var, eh0Var, InputEventCodes.KEY_KBDILLUMDOWN, "EPSV", "|||" + xg0Var.m13954().mo11356().getPort() + '|')));
        } catch (C2820b0 unused) {
            C6547.m15550(425, n71.m9944(xg0Var, ah0Var, eh0Var, 425, "EPSV", null), xg0Var);
        }
    }
}
