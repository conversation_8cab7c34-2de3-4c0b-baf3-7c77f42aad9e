package okhttp3.internal.p042io;

import java.io.BufferedOutputStream;
import java.io.OutputStream;

/* renamed from: okhttp3.internal.io.we */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5583we extends BufferedOutputStream {

    /* renamed from: ၯ */
    public static final /* synthetic */ int f24918 = 0;

    /* renamed from: ၥ */
    public int f24919;

    /* renamed from: ၦ */
    public byte[] f24920;

    /* renamed from: ၮ */
    public byte[] f24921;

    public C5583we(@pu2 OutputStream outputStream, int i) {
        super(outputStream, 262144);
        this.f24920 = new byte[8];
        this.f24921 = new byte[3];
        this.f24919 = i;
    }

    /* renamed from: Ԯ */
    public static void m13559(OutputStream outputStream, int i) {
        if (i >= 0) {
            while (i > 63) {
                outputStream.write((i & 127) | 128);
                i >>>= 7;
            }
        } else {
            while (i < -64) {
                outputStream.write((i & 127) | 128);
                i >>= 7;
            }
        }
        outputStream.write(i & 127);
    }

    /* renamed from: ֏ */
    public static void m13560(OutputStream outputStream, int i) {
        while ((i & 4294967295L) > 127) {
            outputStream.write((i & 127) | 128);
            i >>>= 7;
        }
        outputStream.write(i);
    }

    @Override // java.io.BufferedOutputStream, java.io.FilterOutputStream, java.io.OutputStream
    public final void write(int i) {
        this.f24919++;
        super.write(i);
    }

    @Override // java.io.FilterOutputStream, java.io.OutputStream
    public final void write(byte[] bArr) {
        write(bArr, 0, bArr.length);
    }

    @Override // java.io.BufferedOutputStream, java.io.FilterOutputStream, java.io.OutputStream
    public final void write(byte[] bArr, int i, int i2) {
        this.f24919 += i2;
        super.write(bArr, i, i2);
    }

    /* renamed from: Ϳ */
    public final void m13561() {
        int i = (-this.f24919) & 3;
        if (i > 0) {
            write(this.f24921, 0, i);
        }
    }

    /* renamed from: Ԩ */
    public final void m13562(int i, int i2) {
        int i3 = 0;
        if (i2 >= 0) {
            while (i2 > 127) {
                this.f24920[i3] = (byte) i2;
                i2 >>= 8;
                i3++;
            }
        } else {
            while (i2 < -128) {
                this.f24920[i3] = (byte) i2;
                i2 >>= 8;
                i3++;
            }
        }
        int i4 = i3 + 1;
        this.f24920[i3] = (byte) i2;
        m13564(i, i4 - 1);
        write(this.f24920, 0, i4);
    }

    /* renamed from: ԩ */
    public final void m13563(int i, int i2) {
        int i3 = 0;
        while (true) {
            int i4 = i3 + 1;
            this.f24920[i3] = (byte) i2;
            i2 >>>= 8;
            if (i2 == 0) {
                m13564(i, i4 - 1);
                write(this.f24920, 0, i4);
                return;
            }
            i3 = i4;
        }
    }

    /* renamed from: Ԫ */
    public final void m13564(int i, int i2) {
        write(i | (i2 << 5));
    }

    /* renamed from: ԫ */
    public final void m13565(int i) {
        write(i);
        write(i >> 8);
        write(i >> 16);
        write(i >> 24);
    }

    /* renamed from: Ԭ */
    public final void m13566(int i) {
        if (i < -32768 || i > 32767) {
            throw new C3261ew(null, "Short value out of range: %d", Integer.valueOf(i));
        }
        write(i);
        write(i >> 8);
    }

    /* renamed from: ԯ */
    public final void m13567(int i) {
        if (i < 0 || i > 255) {
            throw new C3261ew(null, "Unsigned byte value out of range: %d", Integer.valueOf(i));
        }
        write(i);
    }

    /* renamed from: ؠ */
    public final void m13568(int i) {
        if (i < 0 || i > 65535) {
            throw new C3261ew(null, "Unsigned short value out of range: %d", Integer.valueOf(i));
        }
        write(i);
        write(i >> 8);
    }
}
