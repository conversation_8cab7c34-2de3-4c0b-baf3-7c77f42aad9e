package okhttp3.internal.p042io;

import java.io.IOException;
import java.io.InputStream;

/* renamed from: okhttp3.internal.io.vm */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5483vm implements pt1 {

    /* renamed from: Ϳ */
    public C4526pm f24272;

    public C5483vm(C4526pm c4526pm) {
        this.f24272 = c4526pm;
    }

    @Override // okhttp3.internal.p042io.pt1
    /* renamed from: Ϳ */
    public final C6565 mo10891(InputStream inputStream) {
        int mo4704;
        int read = inputStream.read();
        if (read == 0) {
            throw new IOException("Sender's public key invalid.");
        }
        if (read == 2 || read == 3) {
            mo4704 = (this.f24272.f17741.mo4704() + 7) / 8;
        } else {
            if (read != 4 && read != 6 && read != 7) {
                StringBuilder m9240 = lf2.m9240("Sender's public key has invalid point encoding 0x");
                m9240.append(Integer.toString(read, 16));
                throw new IOException(m9240.toString());
            }
            mo4704 = ((this.f24272.f17741.mo4704() + 7) / 8) * 2;
        }
        byte[] bArr = new byte[mo4704 + 1];
        bArr[0] = (byte) read;
        ox2.m10536(inputStream, bArr, 1, bArr.length - 1);
        return new C5218tn(this.f24272.f17741.m8991(bArr), this.f24272);
    }
}
