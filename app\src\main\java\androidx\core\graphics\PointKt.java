package androidx.core.graphics;

import android.graphics.Point;
import android.graphics.PointF;
import kotlin.Metadata;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.zu2;

@Metadata(m4114d1 = {"\u0000\u0016\n\u0000\n\u0002\u0010\b\n\u0002\u0018\u0002\n\u0002\u0010\u0007\n\u0002\u0018\u0002\n\u0002\b\t\u001a\r\u0010\u0000\u001a\u00020\u0001*\u00020\u0002H\u0086\n\u001a\r\u0010\u0000\u001a\u00020\u0003*\u00020\u0004H\u0086\n\u001a\r\u0010\u0005\u001a\u00020\u0001*\u00020\u0002H\u0086\n\u001a\r\u0010\u0005\u001a\u00020\u0003*\u00020\u0004H\u0086\n\u001a\u0015\u0010\u0006\u001a\u00020\u0002*\u00020\u00022\u0006\u0010\u0007\u001a\u00020\u0002H\u0086\n\u001a\u0015\u0010\u0006\u001a\u00020\u0002*\u00020\u00022\u0006\u0010\b\u001a\u00020\u0001H\u0086\n\u001a\u0015\u0010\u0006\u001a\u00020\u0004*\u00020\u00042\u0006\u0010\u0007\u001a\u00020\u0004H\u0086\n\u001a\u0015\u0010\u0006\u001a\u00020\u0004*\u00020\u00042\u0006\u0010\b\u001a\u00020\u0003H\u0086\n\u001a\u0015\u0010\t\u001a\u00020\u0002*\u00020\u00022\u0006\u0010\u0007\u001a\u00020\u0002H\u0086\n\u001a\u0015\u0010\t\u001a\u00020\u0002*\u00020\u00022\u0006\u0010\b\u001a\u00020\u0001H\u0086\n\u001a\u0015\u0010\t\u001a\u00020\u0004*\u00020\u00042\u0006\u0010\u0007\u001a\u00020\u0004H\u0086\n\u001a\u0015\u0010\t\u001a\u00020\u0004*\u00020\u00042\u0006\u0010\b\u001a\u00020\u0003H\u0086\n\u001a\r\u0010\n\u001a\u00020\u0002*\u00020\u0004H\u0086\b\u001a\r\u0010\u000b\u001a\u00020\u0004*\u00020\u0002H\u0086\b\u001a\r\u0010\f\u001a\u00020\u0002*\u00020\u0002H\u0086\n\u001a\r\u0010\f\u001a\u00020\u0004*\u00020\u0004H\u0086\n¨\u0006\r"}, m4115d2 = {"component1", "", "Landroid/graphics/Point;", "", "Landroid/graphics/PointF;", "component2", "minus", "p", "xy", "plus", "toPoint", "toPointF", "unaryMinus", "core-ktx_release"}, m4116k = 2, m4117mv = {1, 6, 0}, m4119xi = 48)
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class PointKt {
    public static final float component1(@zu2 PointF pointF) {
        fa1.m6826(pointF, "<this>");
        return pointF.x;
    }

    public static final int component1(@zu2 Point point) {
        fa1.m6826(point, "<this>");
        return point.x;
    }

    public static final float component2(@zu2 PointF pointF) {
        fa1.m6826(pointF, "<this>");
        return pointF.y;
    }

    public static final int component2(@zu2 Point point) {
        fa1.m6826(point, "<this>");
        return point.y;
    }

    @zu2
    public static final Point minus(@zu2 Point point, int i) {
        fa1.m6826(point, "<this>");
        Point point2 = new Point(point.x, point.y);
        int i2 = -i;
        point2.offset(i2, i2);
        return point2;
    }

    @zu2
    public static final Point minus(@zu2 Point point, @zu2 Point point2) {
        fa1.m6826(point, "<this>");
        fa1.m6826(point2, "p");
        Point point3 = new Point(point.x, point.y);
        point3.offset(-point2.x, -point2.y);
        return point3;
    }

    @zu2
    public static final PointF minus(@zu2 PointF pointF, float f) {
        fa1.m6826(pointF, "<this>");
        PointF pointF2 = new PointF(pointF.x, pointF.y);
        float f2 = -f;
        pointF2.offset(f2, f2);
        return pointF2;
    }

    @zu2
    public static final PointF minus(@zu2 PointF pointF, @zu2 PointF pointF2) {
        fa1.m6826(pointF, "<this>");
        fa1.m6826(pointF2, "p");
        PointF pointF3 = new PointF(pointF.x, pointF.y);
        pointF3.offset(-pointF2.x, -pointF2.y);
        return pointF3;
    }

    @zu2
    public static final Point plus(@zu2 Point point, int i) {
        fa1.m6826(point, "<this>");
        Point point2 = new Point(point.x, point.y);
        point2.offset(i, i);
        return point2;
    }

    @zu2
    public static final Point plus(@zu2 Point point, @zu2 Point point2) {
        fa1.m6826(point, "<this>");
        fa1.m6826(point2, "p");
        Point point3 = new Point(point.x, point.y);
        point3.offset(point2.x, point2.y);
        return point3;
    }

    @zu2
    public static final PointF plus(@zu2 PointF pointF, float f) {
        fa1.m6826(pointF, "<this>");
        PointF pointF2 = new PointF(pointF.x, pointF.y);
        pointF2.offset(f, f);
        return pointF2;
    }

    @zu2
    public static final PointF plus(@zu2 PointF pointF, @zu2 PointF pointF2) {
        fa1.m6826(pointF, "<this>");
        fa1.m6826(pointF2, "p");
        PointF pointF3 = new PointF(pointF.x, pointF.y);
        pointF3.offset(pointF2.x, pointF2.y);
        return pointF3;
    }

    @zu2
    public static final Point toPoint(@zu2 PointF pointF) {
        fa1.m6826(pointF, "<this>");
        return new Point((int) pointF.x, (int) pointF.y);
    }

    @zu2
    public static final PointF toPointF(@zu2 Point point) {
        fa1.m6826(point, "<this>");
        return new PointF(point);
    }

    @zu2
    public static final Point unaryMinus(@zu2 Point point) {
        fa1.m6826(point, "<this>");
        return new Point(-point.x, -point.y);
    }

    @zu2
    public static final PointF unaryMinus(@zu2 PointF pointF) {
        fa1.m6826(pointF, "<this>");
        return new PointF(-pointF.x, -pointF.y);
    }
}
