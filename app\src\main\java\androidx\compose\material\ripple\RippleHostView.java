package androidx.compose.material.ripple;

import android.R;
import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.RippleDrawable;
import android.os.Build;
import android.view.View;
import android.view.animation.AnimationUtils;
import androidx.constraintlayout.core.motion.utils.TypedValues;
import com.stardust.autojs.engine.RhinoJavaScriptEngine;
import java.lang.reflect.Method;
import kotlin.Metadata;
import okhttp3.internal.p042io.C4350o9;
import okhttp3.internal.p042io.C7162;
import okhttp3.internal.p042io.du1;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.g03;
import okhttp3.internal.p042io.gi3;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.nh0;
import okhttp3.internal.p042io.wv2;
import okhttp3.internal.p042io.xx5;
import okhttp3.internal.p042io.y54;
import okhttp3.internal.p042io.yg3;
import okhttp3.internal.p042io.zu2;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000N\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0000\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010 \u001a\u00020\u001f¢\u0006\u0004\b!\u0010\"J\u0010\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H\u0002J\b\u0010\u0006\u001a\u00020\u0004H\u0016J\u0010\u0010\t\u001a\u00020\u00042\u0006\u0010\b\u001a\u00020\u0007H\u0016JQ\u0010\u0019\u001a\u00020\u00042\u0006\u0010\u000b\u001a\u00020\n2\u0006\u0010\f\u001a\u00020\u00022\u0006\u0010\u000e\u001a\u00020\r2\u0006\u0010\u0010\u001a\u00020\u000f2\u0006\u0010\u0012\u001a\u00020\u00112\u0006\u0010\u0014\u001a\u00020\u00132\f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00040\u0015ø\u0001\u0000ø\u0001\u0001¢\u0006\u0004\b\u0017\u0010\u0018J\u0006\u0010\u001a\u001a\u00020\u0004J3\u0010\u001d\u001a\u00020\u00042\u0006\u0010\u000e\u001a\u00020\r2\u0006\u0010\u0010\u001a\u00020\u000f2\u0006\u0010\u0012\u001a\u00020\u00112\u0006\u0010\u0014\u001a\u00020\u0013ø\u0001\u0000ø\u0001\u0001¢\u0006\u0004\b\u001b\u0010\u001cJ\u0006\u0010\u001e\u001a\u00020\u0004\u0082\u0002\u000b\n\u0005\b¡\u001e0\u0001\n\u0002\b\u0019¨\u0006#"}, m4115d2 = {"Landroidx/compose/material/ripple/RippleHostView;", "Landroid/view/View;", "", "pressed", "Lokhttp3/internal/io/lx5;", "setRippleState", "refreshDrawableState", "Landroid/graphics/drawable/Drawable;", "who", "invalidateDrawable", "Lokhttp3/internal/io/gi3$Ԩ;", "interaction", "bounded", "Lokhttp3/internal/io/ft4;", "size", "", "radius", "Lokhttp3/internal/io/ਅ;", TypedValues.Custom.S_COLOR, "", "alpha", "Lkotlin/Function0;", "onInvalidateRipple", "addRipple-KOepWvA", "(Lokhttp3/internal/io/gi3$Ԩ;ZJIJFLokhttp3/internal/io/nh0;)V", "addRipple", "removeRipple", "updateRippleProperties-biQXAtU", "(JIJF)V", "updateRippleProperties", "disposeRipple", "Landroid/content/Context;", "context", RhinoJavaScriptEngine.SOURCE_NAME_INIT, "(Landroid/content/Context;)V", "material-ripple_release"}, m4116k = 1, m4117mv = {1, 7, 1})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class RippleHostView extends View {

    /* renamed from: ၵ */
    @zu2
    public static final int[] f30 = {R.attr.state_pressed, R.attr.state_enabled};

    /* renamed from: ၶ */
    @zu2
    public static final int[] f31 = new int[0];

    /* renamed from: ၥ */
    @wv2
    public xx5 f32;

    /* renamed from: ၦ */
    @wv2
    public Boolean f33;

    /* renamed from: ၮ */
    @wv2
    public Long f34;

    /* renamed from: ၯ */
    @wv2
    public y54 f35;

    /* renamed from: ၰ */
    @wv2
    public nh0<lx5> f36;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public RippleHostView(@zu2 Context context) {
        super(context);
        fa1.m6826(context, "context");
    }

    private final void setRippleState(boolean z) {
        long currentAnimationTimeMillis = AnimationUtils.currentAnimationTimeMillis();
        Runnable runnable = this.f35;
        if (runnable != null) {
            removeCallbacks(runnable);
            runnable.run();
        }
        Long l = this.f34;
        long longValue = currentAnimationTimeMillis - (l != null ? l.longValue() : 0L);
        if (z || longValue >= 5) {
            int[] iArr = z ? f30 : f31;
            xx5 xx5Var = this.f32;
            if (xx5Var != null) {
                xx5Var.setState(iArr);
            }
        } else {
            y54 y54Var = new y54(this, 0);
            this.f35 = y54Var;
            postDelayed(y54Var, 50L);
        }
        this.f34 = Long.valueOf(currentAnimationTimeMillis);
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: setRippleState$lambda-2, reason: not valid java name */
    public static final void m18340setRippleState$lambda2(RippleHostView rippleHostView) {
        fa1.m6826(rippleHostView, "this$0");
        xx5 xx5Var = rippleHostView.f32;
        if (xx5Var != null) {
            xx5Var.setState(f31);
        }
        rippleHostView.f35 = null;
    }

    /* renamed from: addRipple-KOepWvA, reason: not valid java name */
    public final void m18341addRippleKOepWvA(@zu2 gi3.C3448 interaction, boolean bounded, long size, int radius, long color, float alpha, @zu2 nh0<lx5> onInvalidateRipple) {
        float centerX;
        float centerY;
        fa1.m6826(interaction, "interaction");
        fa1.m6826(onInvalidateRipple, "onInvalidateRipple");
        if (this.f32 == null || !fa1.m6818(Boolean.valueOf(bounded), this.f33)) {
            xx5 xx5Var = new xx5(bounded);
            setBackground(xx5Var);
            this.f32 = xx5Var;
            this.f33 = Boolean.valueOf(bounded);
        }
        xx5 xx5Var2 = this.f32;
        fa1.m6823(xx5Var2);
        this.f36 = onInvalidateRipple;
        m18342updateRipplePropertiesbiQXAtU(size, radius, color, alpha);
        if (bounded) {
            centerX = g03.m7139(interaction.f10834);
            centerY = g03.m7140(interaction.f10834);
        } else {
            centerX = xx5Var2.getBounds().centerX();
            centerY = xx5Var2.getBounds().centerY();
        }
        xx5Var2.setHotspot(centerX, centerY);
        setRippleState(true);
    }

    public final void disposeRipple() {
        this.f36 = null;
        y54 y54Var = this.f35;
        if (y54Var != null) {
            removeCallbacks(y54Var);
            y54 y54Var2 = this.f35;
            fa1.m6823(y54Var2);
            y54Var2.run();
        } else {
            xx5 xx5Var = this.f32;
            if (xx5Var != null) {
                xx5Var.setState(f31);
            }
        }
        xx5 xx5Var2 = this.f32;
        if (xx5Var2 == null) {
            return;
        }
        xx5Var2.setVisible(false, false);
        unscheduleDrawable(xx5Var2);
    }

    @Override // android.view.View, android.graphics.drawable.Drawable.Callback
    public void invalidateDrawable(@zu2 Drawable drawable) {
        fa1.m6826(drawable, "who");
        nh0<lx5> nh0Var = this.f36;
        if (nh0Var != null) {
            nh0Var.invoke();
        }
    }

    @Override // android.view.View
    public final void onLayout(boolean z, int i, int i2, int i3, int i4) {
    }

    @Override // android.view.View
    public final void onMeasure(int i, int i2) {
        setMeasuredDimension(0, 0);
    }

    @Override // android.view.View
    public void refreshDrawableState() {
    }

    public final void removeRipple() {
        setRippleState(false);
    }

    /* renamed from: updateRippleProperties-biQXAtU, reason: not valid java name */
    public final void m18342updateRipplePropertiesbiQXAtU(long size, int radius, long color, float alpha) {
        xx5 xx5Var = this.f32;
        if (xx5Var == null) {
            return;
        }
        Integer num = xx5Var.f26256;
        if (num == null || num.intValue() != radius) {
            xx5Var.f26256 = Integer.valueOf(radius);
            if (Build.VERSION.SDK_INT < 23) {
                try {
                    if (!xx5.f26253) {
                        xx5.f26253 = true;
                        xx5.f26252 = RippleDrawable.class.getDeclaredMethod("setMaxRadius", Integer.TYPE);
                    }
                    Method method = xx5.f26252;
                    if (method != null) {
                        method.invoke(xx5Var, Integer.valueOf(radius));
                    }
                } catch (Exception unused) {
                }
            } else {
                xx5.C5761.f26258.m14093(xx5Var, radius);
            }
        }
        if (Build.VERSION.SDK_INT < 28) {
            alpha *= 2;
        }
        if (alpha > 1.0f) {
            alpha = 1.0f;
        }
        long m16572 = C7162.m16572(color, alpha);
        C7162 c7162 = xx5Var.f26255;
        if (!(c7162 != null ? C7162.m16573(c7162.f31091, m16572) : false)) {
            xx5Var.f26255 = new C7162(m16572);
            xx5Var.setColor(ColorStateList.valueOf(du1.m6210(m16572)));
        }
        Rect m14276 = yg3.m14276(C4350o9.m10271(size));
        setLeft(m14276.left);
        setTop(m14276.top);
        setRight(m14276.right);
        setBottom(m14276.bottom);
        xx5Var.setBounds(m14276);
    }
}
