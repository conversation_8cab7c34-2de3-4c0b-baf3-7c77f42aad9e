package android.view.compose;

import kotlin.Metadata;
import okhttp3.internal.p042io.InterfaceC6968;
import okhttp3.internal.p042io.di0;
import okhttp3.internal.p042io.lv1;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.nh0;
import okhttp3.internal.p042io.wv2;

@Metadata(m4116k = 3, m4117mv = {1, 6, 0}, m4119xi = 48)
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class BackHandlerKt$BackHandler$3 extends lv1 implements di0<InterfaceC6968, Integer, lx5> {
    public final /* synthetic */ int $$changed;
    public final /* synthetic */ int $$default;
    public final /* synthetic */ boolean $enabled;
    public final /* synthetic */ nh0<lx5> $onBack;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public BackHandlerKt$BackHandler$3(boolean z, nh0<lx5> nh0Var, int i, int i2) {
        super(2);
        this.$enabled = z;
        this.$onBack = nh0Var;
        this.$$changed = i;
        this.$$default = i2;
    }

    @Override // okhttp3.internal.p042io.di0
    /* renamed from: invoke */
    public /* bridge */ /* synthetic */ lx5 mo18338invoke(InterfaceC6968 interfaceC6968, Integer num) {
        invoke(interfaceC6968, num.intValue());
        return lx5.f14876;
    }

    public final void invoke(@wv2 InterfaceC6968 interfaceC6968, int i) {
        BackHandlerKt.BackHandler(this.$enabled, this.$onBack, interfaceC6968, this.$$changed | 1, this.$$default);
    }
}
