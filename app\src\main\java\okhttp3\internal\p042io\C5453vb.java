package okhttp3.internal.p042io;

/* renamed from: okhttp3.internal.io.vb */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5453vb extends AbstractC3820jc {

    /* renamed from: ၥ */
    public final int f23962;

    public C5453vb(@pu2 C4694qf c4694qf) {
        this.f23962 = c4694qf.m11120(false);
    }

    @Override // okhttp3.internal.p042io.InterfaceC5361uv
    @vv2
    /* renamed from: ށ */
    public final String mo10331() {
        return null;
    }

    @Override // okhttp3.internal.p042io.InterfaceC5361uv
    /* renamed from: ޒ */
    public final int mo10332() {
        return this.f23962;
    }
}
