package okhttp3.internal.p042io;

import java.io.File;
import okhttp3.internal.p042io.InterfaceC5195tg;

/* renamed from: okhttp3.internal.io.ug */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes3.dex */
public final class C5324ug implements InterfaceC5195tg {
    @Override // okhttp3.internal.p042io.InterfaceC5195tg
    public final void clear() {
    }

    @Override // okhttp3.internal.p042io.InterfaceC5195tg
    /* renamed from: Ϳ */
    public final void mo12500(ps1 ps1Var, InterfaceC5195tg.InterfaceC9358 interfaceC9358) {
    }

    @Override // okhttp3.internal.p042io.InterfaceC5195tg
    /* renamed from: Ԩ */
    public final File mo12501(ps1 ps1Var) {
        return null;
    }
}
