package okhttp3.internal.p042io;

import java.util.Iterator;
import java.util.Map;
import p041j$.util.concurrent.ConcurrentHashMap;

/* renamed from: okhttp3.internal.io.x5 */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5668x5 implements jh0 {

    /* renamed from: Ϳ */
    public final n82 f25386;

    /* renamed from: Ԩ */
    public final Map<String, ih0> f25387;

    public C5668x5() {
        ConcurrentHashMap concurrentHashMap = new ConcurrentHashMap();
        this.f25386 = p82.m10674(C5668x5.class);
        this.f25387 = concurrentHashMap;
    }

    @Override // okhttp3.internal.p042io.ih0
    /* renamed from: Ϳ */
    public final int mo8257(gh0 gh0Var) {
        Iterator<Map.Entry<String, ih0>> it = this.f25387.entrySet().iterator();
        int i = 1;
        while (it.hasNext()) {
            i = it.next().getValue().mo8257(gh0Var);
            if (i == 0) {
                i = 1;
            }
            if (i != 1) {
                break;
            }
        }
        return i;
    }

    @Override // okhttp3.internal.p042io.ih0
    /* renamed from: Ԩ */
    public final int mo8258(gh0 gh0Var) {
        Iterator<Map.Entry<String, ih0>> it = this.f25387.entrySet().iterator();
        int i = 1;
        while (it.hasNext()) {
            i = it.next().getValue().mo8258(gh0Var);
            if (i == 0) {
                i = 1;
            }
            if (i != 1) {
                break;
            }
        }
        return i;
    }

    @Override // okhttp3.internal.p042io.ih0
    /* renamed from: ԩ */
    public final int mo8259(gh0 gh0Var, ah0 ah0Var, zg0 zg0Var) {
        Iterator<Map.Entry<String, ih0>> it = this.f25387.entrySet().iterator();
        int i = 1;
        while (it.hasNext()) {
            i = it.next().getValue().mo8259(gh0Var, ah0Var, zg0Var);
            if (i == 0) {
                i = 1;
            }
            if (i != 1) {
                break;
            }
        }
        return i;
    }

    @Override // okhttp3.internal.p042io.ih0
    /* renamed from: Ԫ */
    public final synchronized void mo8260(kh0 kh0Var) {
        Iterator<Map.Entry<String, ih0>> it = this.f25387.entrySet().iterator();
        while (it.hasNext()) {
            it.next().getValue().mo8260(kh0Var);
        }
    }

    @Override // okhttp3.internal.p042io.ih0
    /* renamed from: ԫ */
    public final int mo8261(gh0 gh0Var, ah0 ah0Var) {
        Iterator<Map.Entry<String, ih0>> it = this.f25387.entrySet().iterator();
        int i = 1;
        while (it.hasNext()) {
            i = it.next().getValue().mo8261(gh0Var, ah0Var);
            if (i == 0) {
                i = 1;
            }
            if (i != 1) {
                break;
            }
        }
        return i;
    }
}
