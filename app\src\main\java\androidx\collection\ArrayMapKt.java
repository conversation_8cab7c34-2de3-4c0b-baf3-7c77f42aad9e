package androidx.collection;

import androidx.exifinterface.media.ExifInterface;
import kotlin.Metadata;
import okhttp3.internal.p042io.C7314;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.v63;
import okhttp3.internal.p042io.zu2;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000\u0016\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0011\n\u0002\u0018\u0002\n\u0002\b\u0003\u001a!\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00028\u0000\u0012\u0004\u0012\u00028\u00010\u0002\"\u0004\b\u0000\u0010\u0000\"\u0004\b\u0001\u0010\u0001H\u0086\b\u001aQ\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00028\u0000\u0012\u0004\u0012\u00028\u00010\u0002\"\u0004\b\u0000\u0010\u0000\"\u0004\b\u0001\u0010\u00012*\u0010\u0006\u001a\u0016\u0012\u0012\b\u0001\u0012\u000e\u0012\u0004\u0012\u00028\u0000\u0012\u0004\u0012\u00028\u00010\u00050\u0004\"\u000e\u0012\u0004\u0012\u00028\u0000\u0012\u0004\u0012\u00028\u00010\u0005¢\u0006\u0004\b\u0003\u0010\u0007¨\u0006\b"}, m4115d2 = {"K", ExifInterface.GPS_MEASUREMENT_INTERRUPTED, "Landroidx/collection/ArrayMap;", "arrayMapOf", "", "Lokhttp3/internal/io/v63;", "pairs", "([Lokhttp3/internal/io/v63;)Landroidx/collection/ArrayMap;", "collection-ktx"}, m4116k = 2, m4117mv = {1, 4, 0})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class ArrayMapKt {
    @zu2
    public static final <K, V> ArrayMap<K, V> arrayMapOf() {
        return new ArrayMap<>();
    }

    @zu2
    public static final <K, V> ArrayMap<K, V> arrayMapOf(@zu2 v63<? extends K, ? extends V>... v63VarArr) {
        fa1.m6827(v63VarArr, "pairs");
        C7314 c7314 = (ArrayMap<K, V>) new ArrayMap(v63VarArr.length);
        for (v63<? extends K, ? extends V> v63Var : v63VarArr) {
            c7314.put(v63Var.f23750, v63Var.f23751);
        }
        return c7314;
    }
}
