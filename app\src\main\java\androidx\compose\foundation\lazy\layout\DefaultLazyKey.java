package androidx.compose.foundation.lazy.layout;

import android.annotation.SuppressLint;
import android.os.Parcel;
import android.os.Parcelable;
import kotlin.Metadata;
import okhttp3.internal.p042io.C6350;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.lf2;
import okhttp3.internal.p042io.ro1;
import okhttp3.internal.p042io.wv2;
import okhttp3.internal.p042io.zu2;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000\n\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b\u0083\b\u0018\u00002\u00020\u0001¨\u0006\u0002"}, m4115d2 = {"Landroidx/compose/foundation/lazy/layout/DefaultLazyKey;", "Landroid/os/Parcelable;", "foundation_release"}, m4116k = 1, m4117mv = {1, 6, 0})
@SuppressLint({"BanParcelableUsage"})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
final /* data */ class DefaultLazyKey implements Parcelable {

    @ro1
    @zu2
    public static final Parcelable.Creator<DefaultLazyKey> CREATOR = new C0161();

    /* renamed from: ၥ */
    public final int f24;

    /* renamed from: androidx.compose.foundation.lazy.layout.DefaultLazyKey$Ϳ */
    public static final class C0161 implements Parcelable.Creator<DefaultLazyKey> {
        @Override // android.os.Parcelable.Creator
        public final DefaultLazyKey createFromParcel(Parcel parcel) {
            fa1.m6826(parcel, "parcel");
            return new DefaultLazyKey(parcel.readInt());
        }

        @Override // android.os.Parcelable.Creator
        public final DefaultLazyKey[] newArray(int i) {
            return new DefaultLazyKey[i];
        }
    }

    public DefaultLazyKey(int i) {
        this.f24 = i;
    }

    @Override // android.os.Parcelable
    public final int describeContents() {
        return 0;
    }

    public final boolean equals(@wv2 Object obj) {
        if (this == obj) {
            return true;
        }
        return (obj instanceof DefaultLazyKey) && this.f24 == ((DefaultLazyKey) obj).f24;
    }

    /* renamed from: hashCode, reason: from getter */
    public final int getF24() {
        return this.f24;
    }

    @zu2
    public final String toString() {
        return C6350.m15228(lf2.m9240("DefaultLazyKey(index="), this.f24, ')');
    }

    @Override // android.os.Parcelable
    public final void writeToParcel(@zu2 Parcel parcel, int i) {
        fa1.m6826(parcel, "parcel");
        parcel.writeInt(this.f24);
    }
}
