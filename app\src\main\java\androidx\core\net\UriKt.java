package androidx.core.net;

import android.net.Uri;
import java.io.File;
import kotlin.Metadata;
import okhttp3.internal.p042io.C7798;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.zu2;

@Metadata(m4114d1 = {"\u0000\u0012\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\u001a\n\u0010\u0000\u001a\u00020\u0001*\u00020\u0002\u001a\r\u0010\u0003\u001a\u00020\u0002*\u00020\u0001H\u0086\b\u001a\r\u0010\u0003\u001a\u00020\u0002*\u00020\u0004H\u0086\b¨\u0006\u0005"}, m4115d2 = {"toFile", "Ljava/io/File;", "Landroid/net/Uri;", "toUri", "", "core-ktx_release"}, m4116k = 2, m4117mv = {1, 6, 0}, m4119xi = 48)
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class UriKt {
    @zu2
    public static final File toFile(@zu2 Uri uri) {
        fa1.m6826(uri, "<this>");
        if (!fa1.m6818(uri.getScheme(), "file")) {
            throw new IllegalArgumentException(C7798.m17420("Uri lacks 'file' scheme: ", uri).toString());
        }
        String path = uri.getPath();
        if (path != null) {
            return new File(path);
        }
        throw new IllegalArgumentException(C7798.m17420("Uri path is null: ", uri).toString());
    }

    @zu2
    public static final Uri toUri(@zu2 File file) {
        fa1.m6826(file, "<this>");
        Uri fromFile = Uri.fromFile(file);
        fa1.m6825(fromFile, "fromFile(this)");
        return fromFile;
    }

    @zu2
    public static final Uri toUri(@zu2 String str) {
        fa1.m6826(str, "<this>");
        Uri parse = Uri.parse(str);
        fa1.m6825(parse, "parse(this)");
        return parse;
    }
}
