package okhttp3.internal.p042io;

import java.util.List;

@fz4
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public abstract class c56<T> {

    @fz4
    /* renamed from: okhttp3.internal.io.c56$Ϳ */
    public static final class C2965 extends c56<AbstractC6961> {

        /* renamed from: Ϳ */
        @zu2
        public static final C2965 f7603 = new C2965();
    }

    @fz4
    /* renamed from: okhttp3.internal.io.c56$Ԩ */
    public static final class C2966 extends c56<Float> {

        /* renamed from: Ϳ */
        @zu2
        public static final C2966 f7604 = new C2966();
    }

    @fz4
    /* renamed from: okhttp3.internal.io.c56$Ԫ */
    public static final class C2967 extends c56<List<? extends h93>> {

        /* renamed from: Ϳ */
        @zu2
        public static final C2967 f7605 = new C2967();
    }

    @fz4
    /* renamed from: okhttp3.internal.io.c56$Ԭ */
    public static final class C2968 extends c56<Float> {

        /* renamed from: Ϳ */
        @zu2
        public static final C2968 f7606 = new C2968();
    }

    @fz4
    /* renamed from: okhttp3.internal.io.c56$Ԯ */
    public static final class C2969 extends c56<Float> {

        /* renamed from: Ϳ */
        @zu2
        public static final C2969 f7607 = new C2969();
    }

    @fz4
    /* renamed from: okhttp3.internal.io.c56$֏ */
    public static final class C2970 extends c56<Float> {

        /* renamed from: Ϳ */
        @zu2
        public static final C2970 f7608 = new C2970();
    }

    @fz4
    /* renamed from: okhttp3.internal.io.c56$ؠ */
    public static final class C2971 extends c56<Float> {

        /* renamed from: Ϳ */
        @zu2
        public static final C2971 f7609 = new C2971();
    }

    @fz4
    /* renamed from: okhttp3.internal.io.c56$ހ */
    public static final class C2972 extends c56<Float> {

        /* renamed from: Ϳ */
        @zu2
        public static final C2972 f7610 = new C2972();
    }

    @fz4
    /* renamed from: okhttp3.internal.io.c56$ށ */
    public static final class C2973 extends c56<AbstractC6961> {

        /* renamed from: Ϳ */
        @zu2
        public static final C2973 f7611 = new C2973();
    }

    @fz4
    /* renamed from: okhttp3.internal.io.c56$ނ */
    public static final class C2974 extends c56<Float> {

        /* renamed from: Ϳ */
        @zu2
        public static final C2974 f7612 = new C2974();
    }

    @fz4
    /* renamed from: okhttp3.internal.io.c56$ރ */
    public static final class C2975 extends c56<Float> {

        /* renamed from: Ϳ */
        @zu2
        public static final C2975 f7613 = new C2975();
    }

    @fz4
    /* renamed from: okhttp3.internal.io.c56$ބ */
    public static final class C2976 extends c56<Float> {

        /* renamed from: Ϳ */
        @zu2
        public static final C2976 f7614 = new C2976();
    }

    @fz4
    /* renamed from: okhttp3.internal.io.c56$ޅ */
    public static final class C2977 extends c56<Float> {

        /* renamed from: Ϳ */
        @zu2
        public static final C2977 f7615 = new C2977();
    }

    @fz4
    /* renamed from: okhttp3.internal.io.c56$ކ */
    public static final class C2978 extends c56<Float> {

        /* renamed from: Ϳ */
        @zu2
        public static final C2978 f7616 = new C2978();
    }

    @fz4
    /* renamed from: okhttp3.internal.io.c56$އ */
    public static final class C2979 extends c56<Float> {

        /* renamed from: Ϳ */
        @zu2
        public static final C2979 f7617 = new C2979();
    }

    @fz4
    /* renamed from: okhttp3.internal.io.c56$ވ */
    public static final class C2980 extends c56<Float> {

        /* renamed from: Ϳ */
        @zu2
        public static final C2980 f7618 = new C2980();
    }
}
