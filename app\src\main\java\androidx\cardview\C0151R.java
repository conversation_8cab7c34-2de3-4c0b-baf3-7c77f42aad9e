package androidx.cardview;

/* renamed from: androidx.cardview.R */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class C0151R {

    /* renamed from: androidx.cardview.R$attr */
    public static final class attr {
        public static final int cardBackgroundColor = 2130968732;
        public static final int cardCornerRadius = 2130968733;
        public static final int cardElevation = 2130968734;
        public static final int cardMaxElevation = 2130968736;
        public static final int cardPreventCornerOverlap = 2130968737;
        public static final int cardUseCompatPadding = 2130968738;
        public static final int cardViewStyle = 2130968739;
        public static final int contentPadding = 2130968894;
        public static final int contentPaddingBottom = 2130968895;
        public static final int contentPaddingLeft = 2130968897;
        public static final int contentPaddingRight = 2130968898;
        public static final int contentPaddingTop = 2130968900;

        private attr() {
        }
    }

    /* renamed from: androidx.cardview.R$color */
    public static final class color {
        public static final int cardview_dark_background = 2131099698;
        public static final int cardview_light_background = 2131099699;
        public static final int cardview_shadow_end_color = 2131099700;
        public static final int cardview_shadow_start_color = 2131099701;

        private color() {
        }
    }

    /* renamed from: androidx.cardview.R$dimen */
    public static final class dimen {
        public static final int cardview_compat_inset_shadow = 2131165270;
        public static final int cardview_default_elevation = 2131165271;
        public static final int cardview_default_radius = 2131165272;

        private dimen() {
        }
    }

    /* renamed from: androidx.cardview.R$style */
    public static final class style {
        public static final int Base_CardView = 2131886098;
        public static final int CardView = 2131886357;
        public static final int CardView_Dark = 2131886358;
        public static final int CardView_Light = 2131886359;

        private style() {
        }
    }

    /* renamed from: androidx.cardview.R$styleable */
    public static final class styleable {
        public static final int[] CardView = {android.R.attr.minWidth, android.R.attr.minHeight, 2130968732, 2130968733, 2130968734, 2130968736, 2130968737, 2130968738, 2130968894, 2130968895, 2130968897, 2130968898, 2130968900};
        public static final int CardView_android_minHeight = 1;
        public static final int CardView_android_minWidth = 0;
        public static final int CardView_cardBackgroundColor = 2;
        public static final int CardView_cardCornerRadius = 3;
        public static final int CardView_cardElevation = 4;
        public static final int CardView_cardMaxElevation = 5;
        public static final int CardView_cardPreventCornerOverlap = 6;
        public static final int CardView_cardUseCompatPadding = 7;
        public static final int CardView_contentPadding = 8;
        public static final int CardView_contentPaddingBottom = 9;
        public static final int CardView_contentPaddingLeft = 10;
        public static final int CardView_contentPaddingRight = 11;
        public static final int CardView_contentPaddingTop = 12;

        private styleable() {
        }
    }

    private C0151R() {
    }
}
