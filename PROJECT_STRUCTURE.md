# AutoJS9 兼容服务器项目结构

## 文件说明

```
autojs9-server/
├── autojs_server.py          # 核心服务器实现
├── gui_auth_server.py        # 带GUI授权界面的服务器
├── start_server.py           # 服务器启动脚本
├── test_client.py            # 基础测试客户端
├── advanced_client.py        # 高级功能演示客户端
├── config.json               # 服务器配置文件
├── requirements.txt          # Python依赖
├── README.md                 # 项目说明
├── PROJECT_STRUCTURE.md      # 项目结构说明
├── auth.db                   # SQLite数据库（运行时生成）
└── autojs_server.log         # 日志文件（运行时生成）
```

## 核心组件

### 1. AutoJSServer (autojs_server.py)
- **功能**: 主要的WebSocket服务器实现
- **特性**: 
  - WebSocket连接管理
  - RPC调用处理
  - Token认证
  - 文件系统操作
  - 调试命令执行

### 2. TokenManager (autojs_server.py)
- **功能**: Token和设备授权管理
- **特性**:
  - 设备授权流程
  - Token生成和验证
  - 持久化存储
  - 授权状态管理

### 3. GUIAutoJSServer (gui_auth_server.py)
- **功能**: 带图形界面的服务器版本
- **特性**:
  - tkinter授权对话框
  - 可视化设备管理
  - 系统通知支持

## 支持的RPC方法

### 认证类
- `debug.authorize` - 设备授权请求

### 基础功能类
- `ping` - 心跳检测
- `getDeviceInfo` - 获取设备信息

### 调试功能类
- `execDebugCommand` - 执行调试命令
  - `type: "eval"` - JavaScript代码执行
  - `type: "log"` - 日志输出

### 文件系统类
- `enableVfs` - 启用虚拟文件系统
- `listFiles` - 文件列表
- `readFile` - 文件读取
- `writeFile` - 文件写入

### 服务功能类
- `enableFtpServer` - FTP服务器启用

## 数据库结构

### authorized_devices 表
```sql
CREATE TABLE authorized_devices (
    device_id TEXT PRIMARY KEY,      -- 设备唯一标识
    token TEXT UNIQUE NOT NULL,      -- 授权token
    authorized_at REAL NOT NULL,     -- 授权时间戳
    last_access REAL NOT NULL,       -- 最后访问时间
    device_name TEXT                 -- 设备名称
);
```

## 配置文件结构

```json
{
  "server": {
    "host": "0.0.0.0",              // 服务器绑定地址
    "port": 9317,                   // 服务器端口
    "max_connections": 100          // 最大连接数
  },
  "auth": {
    "database_path": "auth.db",     // 数据库文件路径
    "authorization_timeout": 30,    // 授权超时时间(秒)
    "token_expiry_days": 30,        // Token过期天数
    "require_authorization": true   // 是否需要授权
  },
  "features": {
    "enable_vfs": true,             // 启用虚拟文件系统
    "enable_ftp": true,             // 启用FTP功能
    "ftp_port": 2121,               // FTP端口
    "script_root": "./scripts",     // 脚本根目录
    "allow_file_operations": true   // 允许文件操作
  },
  "logging": {
    "level": "INFO",                // 日志级别
    "file": "autojs_server.log",    // 日志文件
    "max_size_mb": 10,              // 日志文件最大大小
    "backup_count": 5               // 日志备份数量
  }
}
```

## 启动方式

### 1. 基础服务器
```bash
python autojs_server.py
```

### 2. GUI服务器
```bash
python gui_auth_server.py
```

### 3. 配置启动
```bash
python start_server.py --config config.json --port 9317
```

## 测试方式

### 1. 基础测试
```bash
python test_client.py
```

### 2. 高级功能测试
```bash
python advanced_client.py
```

## 扩展开发

### 添加新的RPC方法
1. 在 `AutoJSServer.handle_authorized_rpc()` 中添加方法处理
2. 实现具体的处理函数
3. 在客户端添加对应的调用方法

### 自定义认证逻辑
1. 继承 `TokenManager` 类
2. 重写 `request_authorization()` 方法
3. 在服务器中使用自定义的TokenManager

### 添加新的存储后端
1. 实现存储接口
2. 修改TokenManager的数据库操作方法
3. 更新配置文件结构

## 安全考虑

1. **网络安全**: 建议在内网环境使用
2. **Token管理**: 定期清理过期token
3. **文件访问**: 限制文件系统访问范围
4. **日志审计**: 记录所有重要操作
5. **连接限制**: 设置最大连接数限制

## 性能优化

1. **连接池**: 使用连接池管理WebSocket连接
2. **异步处理**: 所有I/O操作使用异步模式
3. **缓存机制**: 对频繁访问的数据进行缓存
4. **资源清理**: 及时清理无用的连接和资源

## 故障排除

### 常见问题
1. **端口占用**: 检查端口是否被其他程序占用
2. **权限问题**: 确保有足够的文件系统权限
3. **防火墙**: 检查防火墙设置
4. **依赖缺失**: 确保所有Python依赖已安装

### 调试方法
1. 启用调试日志: `--debug`
2. 查看日志文件: `tail -f autojs_server.log`
3. 使用测试客户端验证功能
4. 检查数据库内容
