package okhttp3.internal.p042io;

import okhttp3.internal.p042io.InterfaceC6968;

/* renamed from: okhttp3.internal.io.wj */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes3.dex */
public final class C5594wj extends lv1 implements di0<InterfaceC6968, Integer, bf3> {

    /* renamed from: ၥ */
    public final /* synthetic */ InterfaceC3453gk f25005;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public C5594wj(InterfaceC3453gk interfaceC3453gk) {
        super(2);
        this.f25005 = interfaceC3453gk;
    }

    @Override // okhttp3.internal.p042io.di0
    /* renamed from: invoke */
    public final bf3 mo18338invoke(InterfaceC6968 interfaceC6968, Integer num) {
        InterfaceC6968 interfaceC69682 = interfaceC6968;
        num.intValue();
        interfaceC69682.mo16268(830271906);
        InterfaceC3453gk interfaceC3453gk = this.f25005;
        interfaceC69682.mo16268(1157296644);
        boolean mo16303 = interfaceC69682.mo16303(interfaceC3453gk);
        Object mo16269 = interfaceC69682.mo16269();
        if (mo16303 || mo16269 == InterfaceC6968.C9945.f30561) {
            mo16269 = new xy0(interfaceC3453gk);
            interfaceC69682.mo16296(mo16269);
        }
        interfaceC69682.mo16300();
        xy0 xy0Var = (xy0) mo16269;
        interfaceC69682.mo16300();
        return xy0Var;
    }
}
