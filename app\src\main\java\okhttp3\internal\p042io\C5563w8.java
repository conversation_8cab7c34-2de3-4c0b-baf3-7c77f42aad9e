package okhttp3.internal.p042io;

import kotlin.reflect.jvm.internal.impl.builtins.C2634;

/* renamed from: okhttp3.internal.io.w8 */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5563w8 extends lv1 implements ph0<InterfaceC2727a9, lx5> {

    /* renamed from: ၥ */
    public static final C5563w8 f24709 = new C5563w8();

    public C5563w8() {
        super(1);
    }

    @Override // okhttp3.internal.p042io.ph0
    public final lx5 invoke(InterfaceC2727a9 interfaceC2727a9) {
        InterfaceC2727a9 interfaceC2727a92 = interfaceC2727a9;
        fa1.m6826(interfaceC2727a92, "$this$withOptions");
        interfaceC2727a92.mo4483(wm4.m13689(interfaceC2727a92.mo4490(), fa1.m6838(C2634.C8868.f5853, C2634.C8868.f5854)));
        return lx5.f14876;
    }
}
