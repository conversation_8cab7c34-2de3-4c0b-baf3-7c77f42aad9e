package androidx.compose.p000ui.text.android;

import android.text.StaticLayout;
import androidx.annotation.DoNotInline;
import kotlin.Metadata;
import okhttp3.internal.p042io.f15;
import okhttp3.internal.p042io.zu2;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\bb\u0018\u00002\u00020\u0001J\u0010\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H'ø\u0001\u0000\u0082\u0002\u0006\n\u0004\b!0\u0001¨\u0006\u0006À\u0006\u0001"}, m4115d2 = {"Landroidx/compose/ui/text/android/Ԭ;", "", "Lokhttp3/internal/io/f15;", "params", "Landroid/text/StaticLayout;", "Ϳ", "ui-text_release"}, m4116k = 1, m4117mv = {1, 7, 1})
/* renamed from: androidx.compose.ui.text.android.Ԭ */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
interface InterfaceC0196 {
    @DoNotInline
    @zu2
    /* renamed from: Ϳ */
    StaticLayout mo60(@zu2 f15 params);

    /* renamed from: Ԩ */
    boolean mo61(@zu2 StaticLayout staticLayout, boolean z);
}
