package androidx.compose.p000ui.platform;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.Configuration;
import android.graphics.Canvas;
import android.graphics.Rect;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.os.SystemClock;
import android.os.Trace;
import android.util.SparseArray;
import android.view.C10501ViewTreeSavedStateRegistryOwner;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.SavedStateRegistryOwner;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.view.ViewStructure;
import android.view.ViewTreeObserver;
import android.view.animation.AnimationUtils;
import android.view.autofill.AutofillId;
import android.view.autofill.AutofillValue;
import androidx.annotation.VisibleForTesting;
import androidx.compose.p000ui.platform.AndroidComposeView;
import androidx.compose.p000ui.platform.ViewLayer;
import androidx.compose.p000ui.viewinterop.AndroidViewHolder;
import androidx.compose.runtime.ParcelableSnapshotMutableState;
import androidx.core.app.NotificationCompat;
import androidx.core.view.AccessibilityDelegateCompat;
import androidx.core.view.ViewCompat;
import androidx.core.view.ViewConfigurationCompat;
import androidx.core.view.accessibility.AccessibilityNodeInfoCompat;
import androidx.lifecycle.DefaultLifecycleObserver;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.ViewTreeLifecycleOwner;
import com.stardust.autojs.engine.RhinoJavaScriptEngine;
import com.stardust.autojs.project.ProjectConfig;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.ListIterator;
import java.util.Map;
import java.util.Objects;
import kotlin.Metadata;
import okhttp3.internal.p042io.C2878bi;
import okhttp3.internal.p042io.C3299f6;
import okhttp3.internal.p042io.C4350o9;
import okhttp3.internal.p042io.C5436v7;
import okhttp3.internal.p042io.C6052;
import okhttp3.internal.p042io.C6059;
import okhttp3.internal.p042io.C6267;
import okhttp3.internal.p042io.C6492;
import okhttp3.internal.p042io.C6579;
import okhttp3.internal.p042io.C6584;
import okhttp3.internal.p042io.C6616;
import okhttp3.internal.p042io.C6757;
import okhttp3.internal.p042io.C6784;
import okhttp3.internal.p042io.C6899;
import okhttp3.internal.p042io.C7067;
import okhttp3.internal.p042io.C7149;
import okhttp3.internal.p042io.C7152;
import okhttp3.internal.p042io.C7261;
import okhttp3.internal.p042io.C7303;
import okhttp3.internal.p042io.C7366;
import okhttp3.internal.p042io.C7409;
import okhttp3.internal.p042io.C7459;
import okhttp3.internal.p042io.C7682;
import okhttp3.internal.p042io.C7687;
import okhttp3.internal.p042io.EnumC7329;
import okhttp3.internal.p042io.InterfaceC5282u7;
import okhttp3.internal.p042io.InterfaceC5795y7;
import okhttp3.internal.p042io.InterfaceC6831;
import okhttp3.internal.p042io.InterfaceC7155;
import okhttp3.internal.p042io.InterfaceC7598;
import okhttp3.internal.p042io.RunnableC6638;
import okhttp3.internal.p042io.RunnableC6906;
import okhttp3.internal.p042io.ViewTreeObserverOnGlobalLayoutListenerC6964;
import okhttp3.internal.p042io.ViewTreeObserverOnScrollChangedListenerC6662;
import okhttp3.internal.p042io.ViewTreeObserverOnTouchModeChangeListenerC6767;
import okhttp3.internal.p042io.a43;
import okhttp3.internal.p042io.a74;
import okhttp3.internal.p042io.ak4;
import okhttp3.internal.p042io.at1;
import okhttp3.internal.p042io.b43;
import okhttp3.internal.p042io.b74;
import okhttp3.internal.p042io.b86;
import okhttp3.internal.p042io.co2;
import okhttp3.internal.p042io.ct1;
import okhttp3.internal.p042io.cw1;
import okhttp3.internal.p042io.dr0;
import okhttp3.internal.p042io.e51;
import okhttp3.internal.p042io.ef3;
import okhttp3.internal.p042io.ef5;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.fc0;
import okhttp3.internal.p042io.ff3;
import okhttp3.internal.p042io.ff5;
import okhttp3.internal.p042io.fj3;
import okhttp3.internal.p042io.fz4;
import okhttp3.internal.p042io.g03;
import okhttp3.internal.p042io.g51;
import okhttp3.internal.p042io.g64;
import okhttp3.internal.p042io.g71;
import okhttp3.internal.p042io.gx1;
import okhttp3.internal.p042io.h64;
import okhttp3.internal.p042io.he0;
import okhttp3.internal.p042io.he2;
import okhttp3.internal.p042io.hk4;
import okhttp3.internal.p042io.ht2;
import okhttp3.internal.p042io.ie0;
import okhttp3.internal.p042io.io3;
import okhttp3.internal.p042io.it1;
import okhttp3.internal.p042io.iw3;
import okhttp3.internal.p042io.ix1;
import okhttp3.internal.p042io.j04;
import okhttp3.internal.p042io.jc0;
import okhttp3.internal.p042io.jf3;
import okhttp3.internal.p042io.js1;
import okhttp3.internal.p042io.kd0;
import okhttp3.internal.p042io.kf3;
import okhttp3.internal.p042io.ks5;
import okhttp3.internal.p042io.lb6;
import okhttp3.internal.p042io.ld0;
import okhttp3.internal.p042io.lf3;
import okhttp3.internal.p042io.lv1;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.ly3;
import okhttp3.internal.p042io.mb6;
import okhttp3.internal.p042io.md3;
import okhttp3.internal.p042io.nh0;
import okhttp3.internal.p042io.nt2;
import okhttp3.internal.p042io.o41;
import okhttp3.internal.p042io.og3;
import okhttp3.internal.p042io.og5;
import okhttp3.internal.p042io.oq0;
import okhttp3.internal.p042io.os1;
import okhttp3.internal.p042io.ov4;
import okhttp3.internal.p042io.p41;
import okhttp3.internal.p042io.ph0;
import okhttp3.internal.p042io.pv3;
import okhttp3.internal.p042io.q41;
import okhttp3.internal.p042io.qk2;
import okhttp3.internal.p042io.qm5;
import okhttp3.internal.p042io.rc0;
import okhttp3.internal.p042io.rg0;
import okhttp3.internal.p042io.rk2;
import okhttp3.internal.p042io.sc0;
import okhttp3.internal.p042io.sf3;
import okhttp3.internal.p042io.t86;
import okhttp3.internal.p042io.tc0;
import okhttp3.internal.p042io.te0;
import okhttp3.internal.p042io.uc3;
import okhttp3.internal.p042io.uj4;
import okhttp3.internal.p042io.v63;
import okhttp3.internal.p042io.vb6;
import okhttp3.internal.p042io.ve2;
import okhttp3.internal.p042io.vj4;
import okhttp3.internal.p042io.vk2;
import okhttp3.internal.p042io.w33;
import okhttp3.internal.p042io.wv2;
import okhttp3.internal.p042io.x33;
import okhttp3.internal.p042io.xl2;
import okhttp3.internal.p042io.xu4;
import okhttp3.internal.p042io.xv4;
import okhttp3.internal.p042io.y33;
import okhttp3.internal.p042io.yj4;
import okhttp3.internal.p042io.yu2;
import okhttp3.internal.p042io.z64;
import okhttp3.internal.p042io.z96;
import okhttp3.internal.p042io.zu2;
import org.mozilla.javascript.optimizer.Codegen;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000\u009e\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0018\u0002\n\u0002\b\u000b\n\u0002\u0010\t\n\u0002\b\u0010\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0006\b\u0001\u0018\u00002\u00020\u00012\u00020\u00022\u00020\u00032\u00020\u00042\u00020\u0005:\u0004\u0098\u0002\u0099\u0002B\u0013\u0012\b\u0010\u0095\u0002\u001a\u00030\u0094\u0002¢\u0006\u0006\b\u0096\u0002\u0010\u0097\u0002J\u0010\u0010\t\u001a\u00020\b2\u0006\u0010\u0007\u001a\u00020\u0006H\u0016J\u0010\u0010\f\u001a\u00020\b2\u0006\u0010\u000b\u001a\u00020\nH\u0016J\u0010\u0010\u000f\u001a\u00020\b2\u0006\u0010\u000e\u001a\u00020\rH\u0016J\u001d\u0010\u0014\u001a\u00020\r2\u0006\u0010\u0011\u001a\u00020\u0010H\u0016ø\u0001\u0000ø\u0001\u0001¢\u0006\u0004\b\u0012\u0010\u0013J\u0010\u0010\u0017\u001a\u00020\r2\u0006\u0010\u0016\u001a\u00020\u0015H\u0016J\u0010\u0010\u001a\u001a\u00020\b2\u0006\u0010\u0019\u001a\u00020\u0018H\u0016J\u0010\u0010\u001b\u001a\u00020\b2\u0006\u0010\u0019\u001a\u00020\u0018H\u0016J\u0006\u0010\u001c\u001a\u00020\bJ\b\u0010\u001d\u001a\u00020\bH\u0016J\u0016\u0010 \u001a\u00020\b2\f\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\b0\u001eH\u0016J\u0016\u0010$\u001a\u00020\b2\u0006\u0010\"\u001a\u00020!2\u0006\u0010#\u001a\u00020\u0018J\u000e\u0010%\u001a\u00020\b2\u0006\u0010\"\u001a\u00020!J\u0016\u0010(\u001a\u00020\b2\u0006\u0010\"\u001a\u00020!2\u0006\u0010'\u001a\u00020&J\u0010\u0010*\u001a\u00020\b2\u0006\u0010)\u001a\u00020\rH\u0016J%\u0010*\u001a\u00020\b2\u0006\u0010#\u001a\u00020\u00182\u0006\u0010,\u001a\u00020+H\u0016ø\u0001\u0000ø\u0001\u0001¢\u0006\u0004\b-\u0010.J\u0010\u0010/\u001a\u00020\b2\u0006\u0010#\u001a\u00020\u0018H\u0016J \u00102\u001a\u00020\b2\u0006\u0010#\u001a\u00020\u00182\u0006\u00100\u001a\u00020\r2\u0006\u00101\u001a\u00020\rH\u0016J \u00103\u001a\u00020\b2\u0006\u0010#\u001a\u00020\u00182\u0006\u00100\u001a\u00020\r2\u0006\u00101\u001a\u00020\rH\u0016J\u0010\u00104\u001a\u00020\b2\u0006\u0010#\u001a\u00020\u0018H\u0016J*\u0010:\u001a\u0002092\u0012\u00107\u001a\u000e\u0012\u0004\u0012\u000206\u0012\u0004\u0012\u00020\b052\f\u00108\u001a\b\u0012\u0004\u0012\u00020\b0\u001eH\u0016J\u0017\u0010>\u001a\u00020\r2\u0006\u0010;\u001a\u000209H\u0000¢\u0006\u0004\b<\u0010=J\b\u0010?\u001a\u00020\bH\u0016J\u0010\u0010@\u001a\u00020\b2\u0006\u0010#\u001a\u00020\u0018H\u0016J\u0010\u0010B\u001a\u00020\b2\u0006\u0010\u001f\u001a\u00020AH\u0016J\u001f\u0010F\u001a\u0004\u0018\u00010C2\u0006\u0010\u0011\u001a\u00020\u0010H\u0016ø\u0001\u0000ø\u0001\u0001¢\u0006\u0004\bD\u0010EJ\u001f\u0010J\u001a\u00020\b2\u0006\u0010;\u001a\u0002092\u0006\u0010G\u001a\u00020\rH\u0000¢\u0006\u0004\bH\u0010IJ\u001a\u0010M\u001a\u00020\b2\u0012\u0010L\u001a\u000e\u0012\u0004\u0012\u00020K\u0012\u0004\u0012\u00020\b05J\u0013\u0010N\u001a\u00020\bH\u0086@ø\u0001\u0001¢\u0006\u0004\bN\u0010OJ\u0013\u0010P\u001a\u00020\bH\u0086@ø\u0001\u0001¢\u0006\u0004\bP\u0010OJ\b\u0010Q\u001a\u00020\bH\u0016J\u001a\u0010V\u001a\u00020\b2\b\u0010S\u001a\u0004\u0018\u00010R2\u0006\u0010U\u001a\u00020TH\u0016J\u0016\u0010Z\u001a\u00020\b2\f\u0010Y\u001a\b\u0012\u0004\u0012\u00020X0WH\u0016J\u0010\u0010\\\u001a\u00020\r2\u0006\u0010\u0016\u001a\u00020[H\u0016J\u0010\u0010^\u001a\u00020\r2\u0006\u0010]\u001a\u00020[H\u0016J\u0010\u0010`\u001a\u00020\r2\u0006\u0010_\u001a\u00020TH\u0016J\u0010\u0010a\u001a\u00020\r2\u0006\u0010_\u001a\u00020TH\u0016J\u001d\u0010f\u001a\u00020b2\u0006\u0010c\u001a\u00020bH\u0016ø\u0001\u0000ø\u0001\u0001¢\u0006\u0004\bd\u0010eJ\u001d\u0010i\u001a\u00020b2\u0006\u0010g\u001a\u00020bH\u0016ø\u0001\u0000ø\u0001\u0001¢\u0006\u0004\bh\u0010eJ\b\u0010j\u001a\u00020\rH\u0016J\u0012\u0010n\u001a\u0004\u0018\u00010m2\u0006\u0010l\u001a\u00020kH\u0016J\u001d\u0010q\u001a\u00020b2\u0006\u0010o\u001a\u00020bH\u0016ø\u0001\u0000ø\u0001\u0001¢\u0006\u0004\bp\u0010eJ\u001d\u0010s\u001a\u00020b2\u0006\u0010c\u001a\u00020bH\u0016ø\u0001\u0000ø\u0001\u0001¢\u0006\u0004\br\u0010eJ\u0010\u0010u\u001a\u00020\b2\u0006\u0010t\u001a\u00020TH\u0016J\u0010\u0010v\u001a\u00020\r2\u0006\u0010\u0016\u001a\u00020[H\u0016J\u0010\u0010y\u001a\u0004\u0018\u00010x2\u0006\u0010w\u001a\u00020TJ\b\u0010z\u001a\u00020\rH\u0016R,\u0010\u0083\u0001\u001a\u00020\r8\u0016@\u0016X\u0096\u000e¢\u0006\u001b\n\u0004\b{\u0010|\u0012\u0006\b\u0081\u0001\u0010\u0082\u0001\u001a\u0004\b}\u0010~\"\u0005\b\u007f\u0010\u0080\u0001R2\u0010\u008c\u0001\u001a\u00030\u0084\u00018\u0000@\u0000X\u0081\u000e¢\u0006 \n\u0006\b\u0085\u0001\u0010\u0086\u0001\u0012\u0006\b\u008b\u0001\u0010\u0082\u0001\u001a\u0006\b\u0087\u0001\u0010\u0088\u0001\"\u0006\b\u0089\u0001\u0010\u008a\u0001R7\u0010\u0094\u0001\u001a\u0004\u0018\u00010K2\t\u0010\u008d\u0001\u001a\u0004\u0018\u00010K8F@BX\u0086\u008e\u0002¢\u0006\u0018\n\u0006\b\u008e\u0001\u0010\u008f\u0001\u001a\u0006\b\u0090\u0001\u0010\u0091\u0001\"\u0006\b\u0092\u0001\u0010\u0093\u0001R5\u0010\u009b\u0001\u001a\u00030\u0095\u00012\b\u0010\u008d\u0001\u001a\u00030\u0095\u00018V@RX\u0096\u008e\u0002¢\u0006\u0018\n\u0006\b\u0096\u0001\u0010\u008f\u0001\u001a\u0006\b\u0097\u0001\u0010\u0098\u0001\"\u0006\b\u0099\u0001\u0010\u009a\u0001R4\u0010t\u001a\u00030\u009c\u00012\b\u0010\u008d\u0001\u001a\u00030\u009c\u00018V@RX\u0096\u008e\u0002¢\u0006\u0018\n\u0006\b\u009d\u0001\u0010\u008f\u0001\u001a\u0006\b\u009e\u0001\u0010\u009f\u0001\"\u0006\b \u0001\u0010¡\u0001R \u0010£\u0001\u001a\u00030¢\u00018\u0016X\u0096\u0004¢\u0006\u0010\n\u0006\b£\u0001\u0010¤\u0001\u001a\u0006\b¥\u0001\u0010¦\u0001R\u0016\u0010\"\u001a\u00020x8VX\u0096\u0004¢\u0006\b\u001a\u0006\b§\u0001\u0010¨\u0001R,\u0010ª\u0001\u001a\u00030©\u00012\b\u0010\u008d\u0001\u001a\u00030©\u00018\u0016@RX\u0096\u000e¢\u0006\u0010\n\u0006\bª\u0001\u0010«\u0001\u001a\u0006\b¬\u0001\u0010\u00ad\u0001R\u0018\u0010±\u0001\u001a\u00030®\u00018VX\u0096\u0004¢\u0006\b\u001a\u0006\b¯\u0001\u0010°\u0001R\u0018\u0010µ\u0001\u001a\u00030²\u00018VX\u0096\u0004¢\u0006\b\u001a\u0006\b³\u0001\u0010´\u0001R\u001f\u0010¶\u0001\u001a\u00020\u00188\u0016X\u0096\u0004¢\u0006\u0010\n\u0006\b¶\u0001\u0010·\u0001\u001a\u0006\b¸\u0001\u0010¹\u0001R \u0010»\u0001\u001a\u00030º\u00018\u0016X\u0096\u0004¢\u0006\u0010\n\u0006\b»\u0001\u0010¼\u0001\u001a\u0006\b½\u0001\u0010¾\u0001R \u0010À\u0001\u001a\u00030¿\u00018\u0016X\u0096\u0004¢\u0006\u0010\n\u0006\bÀ\u0001\u0010Á\u0001\u001a\u0006\bÂ\u0001\u0010Ã\u0001R \u0010Å\u0001\u001a\u00030Ä\u00018\u0016X\u0096\u0004¢\u0006\u0010\n\u0006\bÅ\u0001\u0010Æ\u0001\u001a\u0006\bÇ\u0001\u0010È\u0001R6\u0010Ê\u0001\u001a\u000f\u0012\u0005\u0012\u00030É\u0001\u0012\u0004\u0012\u00020\b058\u0006@\u0006X\u0086\u000e¢\u0006\u0018\n\u0006\bÊ\u0001\u0010Ë\u0001\u001a\u0006\bÌ\u0001\u0010Í\u0001\"\u0006\bÎ\u0001\u0010Ï\u0001R\u0019\u0010Z\u001a\u0005\u0018\u00010Ð\u00018VX\u0096\u0004¢\u0006\b\u001a\u0006\bÑ\u0001\u0010Ò\u0001R \u0010Ô\u0001\u001a\u00030Ó\u00018\u0016X\u0096\u0004¢\u0006\u0010\n\u0006\bÔ\u0001\u0010Õ\u0001\u001a\u0006\bÖ\u0001\u0010×\u0001R \u0010Ù\u0001\u001a\u00030Ø\u00018\u0016X\u0096\u0004¢\u0006\u0010\n\u0006\bÙ\u0001\u0010Ú\u0001\u001a\u0006\bÛ\u0001\u0010Ü\u0001R \u0010Þ\u0001\u001a\u00030Ý\u00018\u0016X\u0096\u0004¢\u0006\u0010\n\u0006\bÞ\u0001\u0010ß\u0001\u001a\u0006\bà\u0001\u0010á\u0001R\u0018\u0010å\u0001\u001a\u00030â\u00018@X\u0080\u0004¢\u0006\b\u001a\u0006\bã\u0001\u0010ä\u0001R\u0018\u0010ç\u0001\u001a\u00030\u0084\u00018VX\u0096\u0004¢\u0006\b\u001a\u0006\bæ\u0001\u0010\u0088\u0001R \u0010é\u0001\u001a\u00030è\u00018\u0016X\u0096\u0004¢\u0006\u0010\n\u0006\bé\u0001\u0010ê\u0001\u001a\u0006\bë\u0001\u0010ì\u0001R\u0016\u0010î\u0001\u001a\u00020\r8VX\u0096\u0004¢\u0006\u0007\u001a\u0005\bí\u0001\u0010~R(\u0010ð\u0001\u001a\u00030ï\u00018\u0016X\u0096\u0004¢\u0006\u0018\n\u0006\bð\u0001\u0010ñ\u0001\u0012\u0006\bô\u0001\u0010\u0082\u0001\u001a\u0006\bò\u0001\u0010ó\u0001R(\u0010ö\u0001\u001a\u00030õ\u00018\u0016X\u0097\u0004¢\u0006\u0018\n\u0006\bö\u0001\u0010÷\u0001\u0012\u0006\bú\u0001\u0010\u0082\u0001\u001a\u0006\bø\u0001\u0010ù\u0001R \u0010ü\u0001\u001a\u00030û\u00018\u0016X\u0096\u0004¢\u0006\u0010\n\u0006\bü\u0001\u0010ý\u0001\u001a\u0006\bþ\u0001\u0010ÿ\u0001R\u0018\u0010\u0083\u0002\u001a\u00030\u0080\u00028VX\u0096\u0004¢\u0006\b\u001a\u0006\b\u0081\u0002\u0010\u0082\u0002R \u0010\u0085\u0002\u001a\u00030\u0084\u00028\u0016X\u0096\u0004¢\u0006\u0010\n\u0006\b\u0085\u0002\u0010\u0086\u0002\u001a\u0006\b\u0087\u0002\u0010\u0088\u0002R \u0010\u008a\u0002\u001a\u00030\u0089\u00028\u0016X\u0096\u0004¢\u0006\u0010\n\u0006\b\u008a\u0002\u0010\u008b\u0002\u001a\u0006\b\u008c\u0002\u0010\u008d\u0002R \u0010\u008f\u0002\u001a\u00030\u008e\u00028\u0016X\u0096\u0004¢\u0006\u0010\n\u0006\b\u008f\u0002\u0010\u0090\u0002\u001a\u0006\b\u0091\u0002\u0010\u0092\u0002R\u0016\u0010\u0093\u0002\u001a\u00020\r8VX\u0096\u0004¢\u0006\u0007\u001a\u0005\b\u0093\u0002\u0010~\u0082\u0002\u000b\n\u0005\b¡\u001e0\u0001\n\u0002\b\u0019¨\u0006\u009a\u0002"}, m4115d2 = {"Landroidx/compose/ui/platform/AndroidComposeView;", "Landroid/view/ViewGroup;", "Lokhttp3/internal/io/y33;", "Lokhttp3/internal/io/t86;", "Lokhttp3/internal/io/og3;", "Landroidx/lifecycle/DefaultLifecycleObserver;", "Landroid/graphics/Rect;", "rect", "Lokhttp3/internal/io/lx5;", "getFocusedRect", "Landroidx/lifecycle/LifecycleOwner;", "owner", "onResume", "", "hasWindowFocus", "onWindowFocusChanged", "Lokhttp3/internal/io/at1;", "keyEvent", "sendKeyEvent-ZmokQxo", "(Landroid/view/KeyEvent;)Z", "sendKeyEvent", "Landroid/view/KeyEvent;", NotificationCompat.CATEGORY_EVENT, "dispatchKeyEvent", "Lokhttp3/internal/io/gx1;", ProjectConfig.PROJECT_TYPE_NODE, "onAttach", "onDetach", "requestClearInvalidObservations", "onEndApplyChanges", "Lkotlin/Function0;", "listener", "registerOnEndApplyChangesListener", "Landroidx/compose/ui/viewinterop/AndroidViewHolder;", "view", "layoutNode", "addAndroidView", "removeAndroidView", "Landroid/graphics/Canvas;", "canvas", "drawAndroidView", "sendPointerUpdate", "measureAndLayout", "Lokhttp3/internal/io/ౘ;", "constraints", "measureAndLayout-0kLqBqw", "(Lokhttp3/internal/io/gx1;J)V", "forceMeasureTheSubtree", "affectsLookahead", "forceRequest", "onRequestMeasure", "onRequestRelayout", "requestOnPositionedCallback", "Lkotlin/Function1;", "Lokhttp3/internal/io/ค;", "drawBlock", "invalidateParentLayer", "Lokhttp3/internal/io/w33;", "createLayer", "layer", "recycle$ui_release", "(Lokhttp3/internal/io/w33;)Z", "recycle", "onSemanticsChange", "onLayoutChange", "Lokhttp3/internal/io/y33$Ϳ;", "registerOnLayoutCompletedListener", "Lokhttp3/internal/io/jc0;", "getFocusDirection-P8AzH3I", "(Landroid/view/KeyEvent;)Lokhttp3/internal/io/jc0;", "getFocusDirection", "isDirty", "notifyLayerIsDirty$ui_release", "(Lokhttp3/internal/io/w33;Z)V", "notifyLayerIsDirty", "Landroidx/compose/ui/platform/AndroidComposeView$Ԩ;", "callback", "setOnViewTreeOwnersAvailable", "boundsUpdatesEventLoop", "(Lokhttp3/internal/io/ৡ;)Ljava/lang/Object;", "keyboardVisibilityEventLoop", "invalidateDescendants", "Landroid/view/ViewStructure;", "structure", "", "flags", "onProvideAutofillVirtualStructure", "Landroid/util/SparseArray;", "Landroid/view/autofill/AutofillValue;", "values", "autofill", "Landroid/view/MotionEvent;", "dispatchGenericMotionEvent", "motionEvent", "dispatchTouchEvent", "direction", "canScrollHorizontally", "canScrollVertically", "Lokhttp3/internal/io/g03;", "localPosition", "localToScreen-MK-Hz9U", "(J)J", "localToScreen", "positionOnScreen", "screenToLocal-MK-Hz9U", "screenToLocal", "onCheckIsTextEditor", "Landroid/view/inputmethod/EditorInfo;", "outAttrs", "Landroid/view/inputmethod/InputConnection;", "onCreateInputConnection", "positionInWindow", "calculateLocalPosition-MK-Hz9U", "calculateLocalPosition", "calculatePositionInWindow-MK-Hz9U", "calculatePositionInWindow", "layoutDirection", "onRtlPropertiesChanged", "dispatchHoverEvent", "accessibilityId", "Landroid/view/View;", "findViewByAccessibilityIdTraversal", "shouldDelayChildPressedState", "ˊ", "Z", "getShowLayoutBounds", "()Z", "setShowLayoutBounds", "(Z)V", "getShowLayoutBounds$annotations", Codegen.TEMPLATE_LITERAL_INIT_METHOD_SIGNATURE, "showLayoutBounds", "", "ˈ", "J", "getLastMatrixRecalculationAnimationTime$ui_release", "()J", "setLastMatrixRecalculationAnimationTime$ui_release", "(J)V", "getLastMatrixRecalculationAnimationTime$ui_release$annotations", "lastMatrixRecalculationAnimationTime", "<set-?>", "viewTreeOwners$delegate", "Lokhttp3/internal/io/yn2;", "getViewTreeOwners", "()Landroidx/compose/ui/platform/AndroidComposeView$Ԩ;", "setViewTreeOwners", "(Landroidx/compose/ui/platform/AndroidComposeView$Ԩ;)V", "viewTreeOwners", "Lokhttp3/internal/io/ie0$Ԩ;", "fontFamilyResolver$delegate", "getFontFamilyResolver", "()Lokhttp3/internal/io/ie0$Ԩ;", "setFontFamilyResolver", "(Lokhttp3/internal/io/ie0$Ԩ;)V", "fontFamilyResolver", "Lokhttp3/internal/io/cw1;", "layoutDirection$delegate", "getLayoutDirection", "()Lokhttp3/internal/io/cw1;", "setLayoutDirection", "(Lokhttp3/internal/io/cw1;)V", "Lokhttp3/internal/io/ix1;", "sharedDrawScope", "Lokhttp3/internal/io/ix1;", "getSharedDrawScope", "()Lokhttp3/internal/io/ix1;", "getView", "()Landroid/view/View;", "Lokhttp3/internal/io/u7;", "density", "Lokhttp3/internal/io/u7;", "getDensity", "()Lokhttp3/internal/io/u7;", "Lokhttp3/internal/io/rc0;", "getFocusManager", "()Lokhttp3/internal/io/rc0;", "focusManager", "Lokhttp3/internal/io/lb6;", "getWindowInfo", "()Lokhttp3/internal/io/lb6;", "windowInfo", "root", "Lokhttp3/internal/io/gx1;", "getRoot", "()Lokhttp3/internal/io/gx1;", "Lokhttp3/internal/io/g64;", "rootForTest", "Lokhttp3/internal/io/g64;", "getRootForTest", "()Lokhttp3/internal/io/g64;", "Lokhttp3/internal/io/ak4;", "semanticsOwner", "Lokhttp3/internal/io/ak4;", "getSemanticsOwner", "()Lokhttp3/internal/io/ak4;", "Lokhttp3/internal/io/ମ;", "autofillTree", "Lokhttp3/internal/io/ମ;", "getAutofillTree", "()Lokhttp3/internal/io/ମ;", "Landroid/content/res/Configuration;", "configurationChangeObserver", "Lokhttp3/internal/io/ph0;", "getConfigurationChangeObserver", "()Lokhttp3/internal/io/ph0;", "setConfigurationChangeObserver", "(Lokhttp3/internal/io/ph0;)V", "Lokhttp3/internal/io/ݥ;", "getAutofill", "()Lokhttp3/internal/io/ݥ;", "Lokhttp3/internal/io/ר;", "clipboardManager", "Lokhttp3/internal/io/ר;", "getClipboardManager", "()Lokhttp3/internal/io/ר;", "Lokhttp3/internal/io/ܙ;", "accessibilityManager", "Lokhttp3/internal/io/ܙ;", "getAccessibilityManager", "()Lokhttp3/internal/io/ܙ;", "Lokhttp3/internal/io/b43;", "snapshotObserver", "Lokhttp3/internal/io/b43;", "getSnapshotObserver", "()Lokhttp3/internal/io/b43;", "Landroidx/compose/ui/platform/AndroidViewsHandler;", "getAndroidViewsHandler$ui_release", "()Landroidx/compose/ui/platform/AndroidViewsHandler;", "androidViewsHandler", "getMeasureIteration", "measureIteration", "Lokhttp3/internal/io/b86;", "viewConfiguration", "Lokhttp3/internal/io/b86;", "getViewConfiguration", "()Lokhttp3/internal/io/b86;", "getHasPendingMeasureOrLayout", "hasPendingMeasureOrLayout", "Lokhttp3/internal/io/ef5;", "textInputService", "Lokhttp3/internal/io/ef5;", "getTextInputService", "()Lokhttp3/internal/io/ef5;", "getTextInputService$annotations", "Lokhttp3/internal/io/he0$Ϳ;", "fontLoader", "Lokhttp3/internal/io/he0$Ϳ;", "getFontLoader", "()Lokhttp3/internal/io/he0$Ϳ;", "getFontLoader$annotations", "Lokhttp3/internal/io/oq0;", "hapticFeedBack", "Lokhttp3/internal/io/oq0;", "getHapticFeedBack", "()Lokhttp3/internal/io/oq0;", "Lokhttp3/internal/io/p41;", "getInputModeManager", "()Lokhttp3/internal/io/p41;", "inputModeManager", "Lokhttp3/internal/io/vk2;", "modifierLocalManager", "Lokhttp3/internal/io/vk2;", "getModifierLocalManager", "()Lokhttp3/internal/io/vk2;", "Lokhttp3/internal/io/og5;", "textToolbar", "Lokhttp3/internal/io/og5;", "getTextToolbar", "()Lokhttp3/internal/io/og5;", "Lokhttp3/internal/io/ff3;", "pointerIconService", "Lokhttp3/internal/io/ff3;", "getPointerIconService", "()Lokhttp3/internal/io/ff3;", "isLifecycleInResumedState", "Landroid/content/Context;", "context", RhinoJavaScriptEngine.SOURCE_NAME_INIT, "(Landroid/content/Context;)V", "Ϳ", "Ԩ", "ui_release"}, m4116k = 1, m4117mv = {1, 7, 1})
@SuppressLint({"ViewConstructor", "VisibleForTests"})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class AndroidComposeView extends ViewGroup implements y33, t86, og3, DefaultLifecycleObserver {

    /* renamed from: ء */
    @wv2
    public static Class<?> f45;

    /* renamed from: ا */
    @zu2
    public static final C0165 f46 = new C0165();

    /* renamed from: ـ */
    @wv2
    public static Method f47;

    /* renamed from: ʰ */
    @zu2
    public final z96<w33> f48;

    /* renamed from: ʱ */
    @zu2
    public final RunnableC6638 f49;

    /* renamed from: ʲ */
    @wv2
    public MotionEvent f50;

    /* renamed from: ʳ */
    @zu2
    public final ParcelableSnapshotMutableState f51;

    /* renamed from: ʴ */
    @zu2
    public final ParcelableSnapshotMutableState f52;

    /* renamed from: ʵ */
    public boolean f53;

    /* renamed from: ʶ */
    @zu2
    public final co2<nh0<lx5>> f54;

    /* renamed from: ʷ */
    @wv2
    public ef3 f55;

    /* renamed from: ʸ */
    @zu2
    public final RunnableC0174 f56;

    /* renamed from: ʹ */
    @zu2
    public final ViewTreeObserverOnScrollChangedListenerC6662 f57;

    /* renamed from: ʺ */
    public long f58;

    /* renamed from: ʻ */
    @zu2
    public final float[] f59;

    /* renamed from: ʼ */
    @zu2
    public final ff5 f60;

    /* renamed from: ʽ */
    @zu2
    public final int[] f61;

    /* renamed from: ʾ */
    @wv2
    public ph0<? super C0166, lx5> f62;

    /* renamed from: ʿ */
    @zu2
    public final ViewTreeObserverOnGlobalLayoutListenerC6964 f63;

    /* renamed from: ˀ */
    public boolean f64;

    /* renamed from: ˁ */
    public long f65;

    /* renamed from: ˆ */
    @wv2
    public C7409 f66;

    /* renamed from: ˇ */
    public boolean f67;

    /* renamed from: ˈ, reason: from kotlin metadata */
    public long lastMatrixRecalculationAnimationTime;

    /* renamed from: ˉ */
    @zu2
    public final ve2 f69;

    /* renamed from: ˊ, reason: from kotlin metadata */
    public boolean showLayoutBounds;

    /* renamed from: ˋ */
    @wv2
    public AndroidViewsHandler f71;

    /* renamed from: ˏ */
    @zu2
    public final C0171 f72;

    /* renamed from: ˑ */
    @zu2
    public final uc3 f73;

    /* renamed from: ˡ */
    @zu2
    public final C6059 f74;

    /* renamed from: ˢ */
    @zu2
    public final vk2 f75;

    /* renamed from: ˣ */
    @zu2
    public final C7261 f76;

    /* renamed from: ˤ */
    @zu2
    public final ef5 f77;

    /* renamed from: ˮ */
    @zu2
    public final q41 f78;

    /* renamed from: ʹ */
    @zu2
    public final ViewTreeObserverOnTouchModeChangeListenerC6767 f79;

    /* renamed from: ՙ */
    public boolean f80;

    /* renamed from: י */
    public boolean f81;

    /* renamed from: ٴ */
    @wv2
    public DrawChildContainer f82;

    /* renamed from: ۥ */
    @zu2
    public final InterfaceC0185 f83;

    /* renamed from: ߴ */
    public int f84;

    /* renamed from: ߵ */
    @zu2
    public final ParcelableSnapshotMutableState f85;

    /* renamed from: ࠚ */
    @zu2
    public final float[] f86;

    /* renamed from: ࠤ */
    public long f87;

    /* renamed from: ࠨ */
    @zu2
    public final C7149 f88;

    /* renamed from: ॱ */
    @zu2
    public final b43 f89;

    /* renamed from: ၥ */
    public long f90;

    /* renamed from: ၦ */
    public boolean f91;

    /* renamed from: ၮ */
    @zu2
    public final ix1 f92;

    /* renamed from: ၯ */
    @zu2
    public C5436v7 f93;

    /* renamed from: ၰ */
    @zu2
    public final sc0 f94;

    /* renamed from: ၵ */
    @zu2
    public final mb6 f95;

    /* renamed from: ၶ */
    @zu2
    public final it1 f96;

    /* renamed from: ၷ */
    @zu2
    public final rk2 f97;

    /* renamed from: ၸ */
    @zu2
    public final C7459 f98;

    /* renamed from: ၹ */
    @zu2
    public final gx1 f99;

    /* renamed from: ၺ */
    @zu2
    public final AndroidComposeView f100;

    /* renamed from: ၻ */
    @zu2
    public final ak4 f101;

    /* renamed from: ၼ */
    @zu2
    public final C7067 f102;

    /* renamed from: ၽ */
    @zu2
    public final C7303 f103;

    /* renamed from: ၾ */
    @zu2
    public final List<w33> f104;

    /* renamed from: ၿ */
    @wv2
    public List<w33> f105;

    /* renamed from: ႀ */
    public boolean f106;

    /* renamed from: ႁ */
    @zu2
    public final xl2 f107;

    /* renamed from: ႎ */
    @zu2
    public final lf3 f108;

    /* renamed from: Ⴧ */
    @zu2
    public ph0<? super Configuration, lx5> f109;

    /* renamed from: Ⴭ */
    @wv2
    public final C7682 f110;

    /* renamed from: ჼ */
    @zu2
    public final nh0<lx5> f111;

    /* renamed from: ჽ */
    public boolean f112;

    /* renamed from: ჾ */
    @zu2
    public final C6616 f113;

    /* renamed from: ჿ */
    @zu2
    public final C6784 f114;

    /* renamed from: androidx.compose.ui.platform.AndroidComposeView$Ϳ */
    public static final class C0165 {
        /* renamed from: Ϳ */
        public static final boolean m49() {
            C0165 c0165 = AndroidComposeView.f46;
            try {
                if (AndroidComposeView.f45 == null) {
                    AndroidComposeView.f45 = Class.forName("android.os.SystemProperties");
                    Class cls = AndroidComposeView.f45;
                    AndroidComposeView.f47 = cls != null ? cls.getDeclaredMethod("getBoolean", String.class, Boolean.TYPE) : null;
                }
                Method method = AndroidComposeView.f47;
                Object invoke = method != null ? method.invoke(null, "debug.layout", Boolean.FALSE) : null;
                Boolean bool = invoke instanceof Boolean ? (Boolean) invoke : null;
                if (bool != null) {
                    return bool.booleanValue();
                }
                return false;
            } catch (Exception unused) {
                return false;
            }
        }
    }

    @fz4
    /* renamed from: androidx.compose.ui.platform.AndroidComposeView$Ԩ */
    public static final class C0166 {

        /* renamed from: Ϳ */
        @zu2
        public final LifecycleOwner f115;

        /* renamed from: Ԩ */
        @zu2
        public final SavedStateRegistryOwner f116;

        public C0166(@zu2 LifecycleOwner lifecycleOwner, @zu2 SavedStateRegistryOwner savedStateRegistryOwner) {
            this.f115 = lifecycleOwner;
            this.f116 = savedStateRegistryOwner;
        }
    }

    /* renamed from: androidx.compose.ui.platform.AndroidComposeView$Ԫ */
    public static final class C0167 extends lv1 implements ph0<o41, Boolean> {
        public C0167() {
            super(1);
        }

        @Override // okhttp3.internal.p042io.ph0
        public final Boolean invoke(o41 o41Var) {
            int i = o41Var.f16510;
            boolean z = false;
            if (i == 1) {
                z = AndroidComposeView.this.isInTouchMode();
            } else {
                if (i == 2) {
                    z = AndroidComposeView.this.isInTouchMode() ? AndroidComposeView.this.requestFocusFromTouch() : true;
                }
            }
            return Boolean.valueOf(z);
        }
    }

    /* renamed from: androidx.compose.ui.platform.AndroidComposeView$Ԭ */
    public static final class C0168 extends AccessibilityDelegateCompat {

        /* renamed from: Ϳ */
        public final /* synthetic */ gx1 f118;

        /* renamed from: Ԩ */
        public final /* synthetic */ AndroidComposeView f119;

        /* renamed from: ԩ */
        public final /* synthetic */ AndroidComposeView f120;

        public C0168(gx1 gx1Var, AndroidComposeView androidComposeView, AndroidComposeView androidComposeView2) {
            this.f118 = gx1Var;
            this.f119 = androidComposeView;
            this.f120 = androidComposeView2;
        }

        @Override // androidx.core.view.AccessibilityDelegateCompat
        public final void onInitializeAccessibilityNodeInfo(@zu2 View view, @zu2 AccessibilityNodeInfoCompat accessibilityNodeInfoCompat) {
            fa1.m6826(view, "host");
            fa1.m6826(accessibilityNodeInfoCompat, "info");
            super.onInitializeAccessibilityNodeInfo(view, accessibilityNodeInfoCompat);
            vj4 m6157 = dr0.m6157(this.f118);
            fa1.m6823(m6157);
            gx1 m10150 = nt2.m10150(m6157);
            fa1.m6826(m10150, "layoutNode");
            C7366.m16958(m6157);
            gx1 m6153 = dr0.m6153(m10150, yj4.C5832.f26744);
            vj4 m61572 = m6153 != null ? dr0.m6157(m6153) : null;
            yj4 yj4Var = m61572 != null ? new yj4(m61572, false, nt2.m10150(m61572)) : null;
            fa1.m6823(yj4Var);
            int i = yj4Var.f26741;
            if (i == this.f119.getF101().m4773().f26741) {
                i = -1;
            }
            accessibilityNodeInfoCompat.setParent(this.f120, i);
        }
    }

    /* renamed from: androidx.compose.ui.platform.AndroidComposeView$Ԯ */
    public static final class C0169 extends lv1 implements ph0<Configuration, lx5> {

        /* renamed from: ၥ */
        public static final C0169 f121 = new C0169();

        public C0169() {
            super(1);
        }

        @Override // okhttp3.internal.p042io.ph0
        public final lx5 invoke(Configuration configuration) {
            fa1.m6826(configuration, "it");
            return lx5.f14876;
        }
    }

    /* renamed from: androidx.compose.ui.platform.AndroidComposeView$֏ */
    public static final class C0170 extends lv1 implements ph0<at1, Boolean> {
        public C0170() {
            super(1);
        }

        @Override // okhttp3.internal.p042io.ph0
        public final Boolean invoke(at1 at1Var) {
            KeyEvent keyEvent = at1Var.f6678;
            fa1.m6826(keyEvent, "it");
            jc0 m18345getFocusDirectionP8AzH3I = AndroidComposeView.this.m18345getFocusDirectionP8AzH3I(keyEvent);
            if (m18345getFocusDirectionP8AzH3I != null) {
                if (ct1.m5814(keyEvent) == 2) {
                    return Boolean.valueOf(AndroidComposeView.this.getFocusManager().mo11552(m18345getFocusDirectionP8AzH3I.f13047));
                }
            }
            return Boolean.FALSE;
        }
    }

    /* renamed from: androidx.compose.ui.platform.AndroidComposeView$ؠ */
    public static final class C0171 implements ff3 {
        public C0171() {
        }
    }

    /* renamed from: androidx.compose.ui.platform.AndroidComposeView$ހ */
    public static final class C0172 extends lv1 implements nh0<lx5> {

        /* renamed from: ၦ */
        public final /* synthetic */ AndroidViewHolder f125;

        /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
        public C0172(AndroidViewHolder androidViewHolder) {
            super(0);
            this.f125 = androidViewHolder;
        }

        @Override // okhttp3.internal.p042io.nh0
        public final lx5 invoke() {
            AndroidComposeView.this.getAndroidViewsHandler$ui_release().removeViewInLayout(this.f125);
            HashMap<gx1, AndroidViewHolder> layoutNodeToHolder = AndroidComposeView.this.getAndroidViewsHandler$ui_release().getLayoutNodeToHolder();
            ks5.m9029(layoutNodeToHolder).remove(AndroidComposeView.this.getAndroidViewsHandler$ui_release().getHolderToLayoutNode().remove(this.f125));
            ViewCompat.setImportantForAccessibility(this.f125, 0);
            return lx5.f14876;
        }
    }

    /* renamed from: androidx.compose.ui.platform.AndroidComposeView$ށ */
    public static final class C0173 extends lv1 implements nh0<lx5> {
        public C0173() {
            super(0);
        }

        @Override // okhttp3.internal.p042io.nh0
        public final lx5 invoke() {
            int actionMasked;
            MotionEvent motionEvent = AndroidComposeView.this.f50;
            if (motionEvent != null && ((actionMasked = motionEvent.getActionMasked()) == 7 || actionMasked == 9)) {
                AndroidComposeView.this.f58 = SystemClock.uptimeMillis();
                AndroidComposeView androidComposeView = AndroidComposeView.this;
                androidComposeView.post(androidComposeView.f56);
            }
            return lx5.f14876;
        }
    }

    /* renamed from: androidx.compose.ui.platform.AndroidComposeView$ނ */
    public static final class RunnableC0174 implements Runnable {
        public RunnableC0174() {
        }

        @Override // java.lang.Runnable
        public final void run() {
            AndroidComposeView.this.removeCallbacks(this);
            MotionEvent motionEvent = AndroidComposeView.this.f50;
            if (motionEvent != null) {
                boolean z = false;
                boolean z2 = motionEvent.getToolType(0) == 3;
                int actionMasked = motionEvent.getActionMasked();
                if (!z2 ? actionMasked != 1 : !(actionMasked == 10 || actionMasked == 1)) {
                    z = true;
                }
                if (z) {
                    int i = (actionMasked == 7 || actionMasked == 9) ? 7 : 2;
                    AndroidComposeView androidComposeView = AndroidComposeView.this;
                    androidComposeView.m47(motionEvent, i, androidComposeView.f58, false);
                }
            }
        }
    }

    /* renamed from: androidx.compose.ui.platform.AndroidComposeView$ރ */
    public static final class C0175 extends lv1 implements ph0<b74, Boolean> {

        /* renamed from: ၥ */
        public static final C0175 f128 = new C0175();

        public C0175() {
            super(1);
        }

        @Override // okhttp3.internal.p042io.ph0
        public final Boolean invoke(b74 b74Var) {
            fa1.m6826(b74Var, "it");
            return Boolean.FALSE;
        }
    }

    /* renamed from: androidx.compose.ui.platform.AndroidComposeView$ބ */
    public static final class C0176 extends lv1 implements ph0<hk4, lx5> {

        /* renamed from: ၥ */
        public static final C0176 f129 = new C0176();

        public C0176() {
            super(1);
        }

        @Override // okhttp3.internal.p042io.ph0
        public final lx5 invoke(hk4 hk4Var) {
            fa1.m6826(hk4Var, "$this$$receiver");
            return lx5.f14876;
        }
    }

    /* renamed from: androidx.compose.ui.platform.AndroidComposeView$ޅ */
    public static final class C0177 extends lv1 implements ph0<nh0<? extends lx5>, lx5> {
        public C0177() {
            super(1);
        }

        @Override // okhttp3.internal.p042io.ph0
        public final lx5 invoke(nh0<? extends lx5> nh0Var) {
            nh0<? extends lx5> nh0Var2 = nh0Var;
            fa1.m6826(nh0Var2, "command");
            Handler handler = AndroidComposeView.this.getHandler();
            if ((handler != null ? handler.getLooper() : null) == Looper.myLooper()) {
                nh0Var2.invoke();
            } else {
                Handler handler2 = AndroidComposeView.this.getHandler();
                if (handler2 != null) {
                    handler2.post(new RunnableC6906(nh0Var2, 0));
                }
            }
            return lx5.f14876;
        }
    }

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    /* JADX WARN: Type inference failed for: r3v13, types: [okhttp3.internal.io.ࡆ] */
    /* JADX WARN: Type inference failed for: r3v14, types: [okhttp3.internal.io.ه] */
    /* JADX WARN: Type inference failed for: r3v15, types: [okhttp3.internal.io.ە] */
    public AndroidComposeView(@zu2 Context context) {
        super(context);
        fa1.m6826(context, "context");
        g03.C3393 c3393 = g03.f10466;
        this.f90 = g03.f10469;
        this.f91 = true;
        this.f92 = new ix1();
        this.f93 = (C5436v7) dr0.m6152(context);
        C0176 c0176 = C0176.f129;
        ph0<g51, lx5> ph0Var = e51.f9040;
        uj4 uj4Var = new uj4(false, false, c0176, e51.f9040);
        sc0 sc0Var = new sc0();
        this.f94 = sc0Var;
        this.f95 = new mb6();
        it1 it1Var = new it1(new C0170(), null);
        this.f96 = it1Var;
        rk2.C4938 c4938 = rk2.C4938.f20511;
        C0175 c0175 = C0175.f128;
        io3<fc0<b74>> io3Var = z64.f27147;
        fa1.m6826(c0175, "onRotaryScrollEvent");
        rk2 m6332 = e51.m6332(c4938, new fc0(new a74(c0175), z64.f27147));
        this.f97 = m6332;
        this.f98 = new C7459();
        gx1 gx1Var = new gx1(false, 0, 3, null);
        gx1Var.mo7500(h64.f11314);
        gx1Var.mo7496(getDensity());
        gx1Var.mo7499(qk2.m11347(uj4Var, m6332).mo23(sc0Var.f21126).mo23(it1Var));
        this.f99 = gx1Var;
        this.f100 = this;
        this.f101 = new ak4(getF99());
        C7067 c7067 = new C7067(this);
        this.f102 = c7067;
        this.f103 = new C7303();
        this.f104 = new ArrayList();
        this.f107 = new xl2();
        this.f108 = new lf3(getF99());
        this.f109 = C0169.f121;
        this.f110 = m31() ? new C7682(this, getF103()) : null;
        this.f113 = new C6616(context);
        this.f114 = new C6784(context);
        this.f89 = new b43(new C0177());
        this.f69 = new ve2(getF99());
        ViewConfiguration viewConfiguration = ViewConfiguration.get(context);
        fa1.m6825(viewConfiguration, "get(context)");
        this.f88 = new C7149(viewConfiguration);
        this.f87 = rg0.m11587(Integer.MAX_VALUE, Integer.MAX_VALUE);
        this.f61 = new int[]{0, 0};
        this.f86 = he2.m7780();
        this.f59 = he2.m7780();
        this.lastMatrixRecalculationAnimationTime = -1L;
        this.f65 = g03.f10468;
        this.f64 = true;
        this.f51 = (ParcelableSnapshotMutableState) ov4.m10507(null);
        this.f63 = new ViewTreeObserver.OnGlobalLayoutListener() { // from class: okhttp3.internal.io.ࡆ
            @Override // android.view.ViewTreeObserver.OnGlobalLayoutListener
            public final void onGlobalLayout() {
                AndroidComposeView androidComposeView = AndroidComposeView.this;
                AndroidComposeView.C0165 c0165 = AndroidComposeView.f46;
                fa1.m6826(androidComposeView, "this$0");
                androidComposeView.m48();
            }
        };
        this.f57 = new ViewTreeObserver.OnScrollChangedListener() { // from class: okhttp3.internal.io.ه
            @Override // android.view.ViewTreeObserver.OnScrollChangedListener
            public final void onScrollChanged() {
                AndroidComposeView androidComposeView = AndroidComposeView.this;
                AndroidComposeView.C0165 c0165 = AndroidComposeView.f46;
                fa1.m6826(androidComposeView, "this$0");
                androidComposeView.m48();
            }
        };
        this.f79 = new ViewTreeObserver.OnTouchModeChangeListener() { // from class: okhttp3.internal.io.ە
            @Override // android.view.ViewTreeObserver.OnTouchModeChangeListener
            public final void onTouchModeChanged(boolean z) {
                AndroidComposeView androidComposeView = AndroidComposeView.this;
                AndroidComposeView.C0165 c0165 = AndroidComposeView.f46;
                fa1.m6826(androidComposeView, "this$0");
                androidComposeView.f78.f18248.setValue(new o41(z ? 1 : 2));
                qm5.m11377(androidComposeView.f94.f21125);
            }
        };
        ff5 ff5Var = new ff5(this);
        this.f60 = ff5Var;
        ph0<? super md3, ? extends ef5> ph0Var2 = C7687.f32624;
        this.f77 = (ef5) C7687.f32624.invoke(ff5Var);
        this.f74 = new C6059(context);
        this.f52 = (ParcelableSnapshotMutableState) ov4.m10506(te0.m12416(context), iw3.f12730);
        Configuration configuration = context.getResources().getConfiguration();
        fa1.m6825(configuration, "context.resources.configuration");
        this.f84 = m35(configuration);
        Configuration configuration2 = context.getResources().getConfiguration();
        fa1.m6825(configuration2, "context.resources.configuration");
        int layoutDirection = configuration2.getLayoutDirection();
        cw1 cw1Var = cw1.Ltr;
        if (layoutDirection != 0 && layoutDirection == 1) {
            cw1Var = cw1.Rtl;
        }
        this.f85 = (ParcelableSnapshotMutableState) ov4.m10507(cw1Var);
        this.f73 = new uc3(this);
        this.f78 = new q41(isInTouchMode() ? 1 : 2, new C0167(), null);
        this.f75 = new vk2(this);
        this.f76 = new C7261(this);
        this.f48 = new z96<>();
        this.f54 = new co2<>(new nh0[16]);
        this.f56 = new RunnableC0174();
        this.f49 = new RunnableC6638(this, 0);
        this.f111 = new C0173();
        int i = Build.VERSION.SDK_INT;
        this.f83 = i >= 29 ? new C0187() : new C0186();
        setWillNotDraw(false);
        setFocusable(true);
        if (i >= 26) {
            C6267.f28456.m15025(this, 1, false);
        }
        setFocusableInTouchMode(true);
        setClipChildren(false);
        setTransitionGroup(true);
        ViewCompat.setAccessibilityDelegate(this, c7067);
        getF99().m7502(this);
        if (i >= 29) {
            C6899.f30256.m16166(this);
        }
        this.f72 = new C0171();
    }

    @InterfaceC5795y7
    public static /* synthetic */ void getFontLoader$annotations() {
    }

    @VisibleForTesting
    public static /* synthetic */ void getLastMatrixRecalculationAnimationTime$ui_release$annotations() {
    }

    public static /* synthetic */ void getShowLayoutBounds$annotations() {
    }

    public static /* synthetic */ void getTextInputService$annotations() {
    }

    private void setFontFamilyResolver(ie0.InterfaceC3713 interfaceC3713) {
        this.f52.setValue(interfaceC3713);
    }

    private void setLayoutDirection(cw1 cw1Var) {
        this.f85.setValue(cw1Var);
    }

    private final void setViewTreeOwners(C0166 c0166) {
        this.f51.setValue(c0166);
    }

    public final void addAndroidView(@zu2 AndroidViewHolder androidViewHolder, @zu2 gx1 gx1Var) {
        fa1.m6826(androidViewHolder, "view");
        fa1.m6826(gx1Var, "layoutNode");
        getAndroidViewsHandler$ui_release().getHolderToLayoutNode().put(androidViewHolder, gx1Var);
        getAndroidViewsHandler$ui_release().addView(androidViewHolder);
        getAndroidViewsHandler$ui_release().getLayoutNodeToHolder().put(gx1Var, androidViewHolder);
        ViewCompat.setImportantForAccessibility(androidViewHolder, 1);
        ViewCompat.setAccessibilityDelegate(androidViewHolder, new C0168(gx1Var, this, this));
    }

    /* JADX WARN: Type inference failed for: r4v4, types: [java.util.LinkedHashMap, java.util.Map<java.lang.Integer, okhttp3.internal.io.ճ>] */
    @Override // android.view.View
    public void autofill(@zu2 SparseArray<AutofillValue> sparseArray) {
        C7682 c7682;
        fa1.m6826(sparseArray, "values");
        if (!m31() || (c7682 = this.f110) == null) {
            return;
        }
        int size = sparseArray.size();
        for (int i = 0; i < size; i++) {
            int keyAt = sparseArray.keyAt(i);
            AutofillValue autofillValue = sparseArray.get(keyAt);
            C7152 c7152 = C7152.f31069;
            fa1.m6825(autofillValue, "value");
            if (c7152.m16557(autofillValue)) {
                C7303 c7303 = c7682.f32619;
                String obj = c7152.m16562(autofillValue).toString();
                Objects.requireNonNull(c7303);
                fa1.m6826(obj, "value");
            } else {
                if (c7152.m16555(autofillValue)) {
                    throw new yu2("An operation is not implemented: b/138604541: Add onFill() callback for date");
                }
                if (c7152.m16556(autofillValue)) {
                    throw new yu2("An operation is not implemented: b/138604541: Add onFill() callback for list");
                }
                if (c7152.m16558(autofillValue)) {
                    throw new yu2("An operation is not implemented: b/138604541:  Add onFill() callback for toggle");
                }
            }
        }
    }

    @wv2
    public final Object boundsUpdatesEventLoop(@zu2 InterfaceC7155<? super lx5> interfaceC7155) {
        Object m16419 = this.f102.m16419(interfaceC7155);
        return m16419 == EnumC7329.COROUTINE_SUSPENDED ? m16419 : lx5.f14876;
    }

    @Override // okhttp3.internal.p042io.y33
    /* renamed from: calculateLocalPosition-MK-Hz9U, reason: not valid java name */
    public long mo18343calculateLocalPositionMKHz9U(long positionInWindow) {
        m43();
        return he2.m7783(this.f59, positionInWindow);
    }

    @Override // okhttp3.internal.p042io.y33
    /* renamed from: calculatePositionInWindow-MK-Hz9U, reason: not valid java name */
    public long mo18344calculatePositionInWindowMKHz9U(long localPosition) {
        m43();
        return he2.m7783(this.f86, localPosition);
    }

    @Override // android.view.View
    public boolean canScrollHorizontally(int direction) {
        return this.f102.m16420(false, direction, this.f90);
    }

    @Override // android.view.View
    public boolean canScrollVertically(int direction) {
        return this.f102.m16420(true, direction, this.f90);
    }

    @Override // okhttp3.internal.p042io.y33
    @zu2
    public w33 createLayer(@zu2 ph0<? super InterfaceC7598, lx5> ph0Var, @zu2 nh0<lx5> nh0Var) {
        w33 w33Var;
        boolean z;
        DrawChildContainer viewLayerContainer;
        fa1.m6826(ph0Var, "drawBlock");
        fa1.m6826(nh0Var, "invalidateParentLayer");
        z96<w33> z96Var = this.f48;
        z96Var.m14487();
        while (true) {
            if (!z96Var.f27198.m5776()) {
                w33Var = null;
                break;
            }
            w33Var = z96Var.f27198.m5779(r1.f8122 - 1).get();
            if (w33Var != null) {
                break;
            }
        }
        w33 w33Var2 = w33Var;
        if (w33Var2 != null) {
            w33Var2.reuseLayer(ph0Var, nh0Var);
            return w33Var2;
        }
        if (isHardwareAccelerated() && Build.VERSION.SDK_INT >= 23 && this.f64) {
            try {
                return new j04(this, ph0Var, nh0Var);
            } catch (Throwable unused) {
                this.f64 = false;
            }
        }
        if (this.f82 == null) {
            ViewLayer.C0181 c0181 = ViewLayer.f138;
            if (!ViewLayer.f143) {
                c0181.m51(new View(getContext()));
            }
            z = ViewLayer.f144;
            if (z) {
                Context context = getContext();
                fa1.m6825(context, "context");
                viewLayerContainer = new DrawChildContainer(context);
            } else {
                Context context2 = getContext();
                fa1.m6825(context2, "context");
                viewLayerContainer = new ViewLayerContainer(context2);
            }
            this.f82 = viewLayerContainer;
            addView(viewLayerContainer);
        }
        DrawChildContainer drawChildContainer = this.f82;
        fa1.m6823(drawChildContainer);
        return new ViewLayer(this, drawChildContainer, ph0Var, nh0Var);
    }

    /* JADX WARN: Type inference failed for: r0v11, types: [java.util.ArrayList, java.util.List<okhttp3.internal.io.w33>] */
    /* JADX WARN: Type inference failed for: r0v4, types: [java.util.ArrayList, java.util.List<okhttp3.internal.io.w33>] */
    /* JADX WARN: Type inference failed for: r0v9, types: [java.util.ArrayList, java.util.List<okhttp3.internal.io.w33>] */
    /* JADX WARN: Type inference failed for: r3v1, types: [java.util.ArrayList, java.util.List<okhttp3.internal.io.w33>] */
    /* JADX WARN: Type inference failed for: r7v1, types: [java.util.ArrayList, java.util.List<okhttp3.internal.io.w33>] */
    /* JADX WARN: Type inference failed for: r7v2, types: [java.util.ArrayList, java.util.Collection, java.util.List<okhttp3.internal.io.w33>] */
    @Override // android.view.ViewGroup, android.view.View
    public final void dispatchDraw(@zu2 Canvas canvas) {
        boolean z;
        fa1.m6826(canvas, "canvas");
        if (!isAttachedToWindow()) {
            m38(getF99());
        }
        int i = x33.f25343;
        measureAndLayout(true);
        this.f106 = true;
        C7459 c7459 = this.f98;
        C6052 c6052 = c7459.f31914;
        Canvas canvas2 = c6052.f27963;
        c6052.f27963 = canvas;
        gx1 f99 = getF99();
        Objects.requireNonNull(f99);
        fa1.m6826(c6052, "canvas");
        f99.f11129.f9491.m8006(c6052);
        c7459.f31914.m14765(canvas2);
        if (!this.f104.isEmpty()) {
            int size = this.f104.size();
            for (int i2 = 0; i2 < size; i2++) {
                ((w33) this.f104.get(i2)).updateDisplayList();
            }
        }
        ViewLayer.C0181 c0181 = ViewLayer.f138;
        z = ViewLayer.f144;
        if (z) {
            int save = canvas.save();
            canvas.clipRect(0.0f, 0.0f, 0.0f, 0.0f);
            super.dispatchDraw(canvas);
            canvas.restoreToCount(save);
        }
        this.f104.clear();
        this.f106 = false;
        ?? r7 = this.f105;
        if (r7 != 0) {
            this.f104.addAll(r7);
            r7.clear();
        }
    }

    @Override // android.view.View
    public boolean dispatchGenericMotionEvent(@zu2 MotionEvent event) {
        fc0<b74> fc0Var;
        fa1.m6826(event, NotificationCompat.CATEGORY_EVENT);
        if (event.getActionMasked() == 8) {
            if (event.isFromSource(4194304)) {
                ViewConfiguration viewConfiguration = ViewConfiguration.get(getContext());
                float f = -event.getAxisValue(26);
                b74 b74Var = new b74(ViewConfigurationCompat.getScaledVerticalScrollFactor(viewConfiguration, getContext()) * f, ViewConfigurationCompat.getScaledHorizontalScrollFactor(viewConfiguration, getContext()) * f, event.getEventTime());
                tc0 m11370 = qm5.m11370(this.f94.f21125);
                if (m11370 == null || (fc0Var = m11370.f22009) == null) {
                    return false;
                }
                return fc0Var.m6889(b74Var) || fc0Var.m6888(b74Var);
            }
            if (!m40(event) && isAttachedToWindow()) {
                return fj3.m6985(m36(event));
            }
        }
        return super.dispatchGenericMotionEvent(event);
    }

    /* JADX WARN: Code restructure failed: missing block: B:46:0x00f9, code lost:
    
        if (r1 != Integer.MIN_VALUE) goto L50;
     */
    @Override // android.view.ViewGroup, android.view.View
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public boolean dispatchHoverEvent(@okhttp3.internal.p042io.zu2 android.view.MotionEvent r10) {
        /*
            Method dump skipped, instructions count: 322
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.compose.p000ui.platform.AndroidComposeView.dispatchHoverEvent(android.view.MotionEvent):boolean");
    }

    @Override // android.view.ViewGroup, android.view.View
    public boolean dispatchKeyEvent(@zu2 KeyEvent event) {
        fa1.m6826(event, NotificationCompat.CATEGORY_EVENT);
        if (!isFocused()) {
            return super.dispatchKeyEvent(event);
        }
        mb6 mb6Var = this.f95;
        int metaState = event.getMetaState();
        Objects.requireNonNull(mb6Var);
        mb6.f15224.setValue(new sf3(metaState));
        return m18349sendKeyEventZmokQxo(event);
    }

    @Override // android.view.ViewGroup, android.view.View
    public boolean dispatchTouchEvent(@zu2 MotionEvent motionEvent) {
        fa1.m6826(motionEvent, "motionEvent");
        if (this.f53) {
            removeCallbacks(this.f49);
            MotionEvent motionEvent2 = this.f50;
            fa1.m6823(motionEvent2);
            if (motionEvent.getActionMasked() != 0 || m37(motionEvent, motionEvent2)) {
                this.f49.run();
            } else {
                this.f53 = false;
            }
        }
        if (m40(motionEvent) || !isAttachedToWindow()) {
            return false;
        }
        if (motionEvent.getActionMasked() == 2 && !m42(motionEvent)) {
            return false;
        }
        int m36 = m36(motionEvent);
        if ((m36 & 2) != 0) {
            getParent().requestDisallowInterceptTouchEvent(true);
        }
        return fj3.m6985(m36);
    }

    public final void drawAndroidView(@zu2 AndroidViewHolder androidViewHolder, @zu2 Canvas canvas) {
        fa1.m6826(androidViewHolder, "view");
        fa1.m6826(canvas, "canvas");
        getAndroidViewsHandler$ui_release().drawView(androidViewHolder, canvas);
    }

    @wv2
    public final View findViewByAccessibilityIdTraversal(int accessibilityId) {
        View view = null;
        try {
            if (Build.VERSION.SDK_INT >= 29) {
                Method declaredMethod = View.class.getDeclaredMethod("findViewByAccessibilityIdTraversal", Integer.TYPE);
                declaredMethod.setAccessible(true);
                Object invoke = declaredMethod.invoke(this, Integer.valueOf(accessibilityId));
                if (invoke instanceof View) {
                    view = (View) invoke;
                }
            } else {
                view = m34(accessibilityId, this);
            }
        } catch (NoSuchMethodException unused) {
        }
        return view;
    }

    @Override // okhttp3.internal.p042io.y33
    public void forceMeasureTheSubtree(@zu2 gx1 gx1Var) {
        fa1.m6826(gx1Var, "layoutNode");
        this.f69.m13207(gx1Var);
    }

    @Override // okhttp3.internal.p042io.y33
    @zu2
    /* renamed from: getAccessibilityManager, reason: from getter */
    public C6784 getF114() {
        return this.f114;
    }

    @zu2
    public final AndroidViewsHandler getAndroidViewsHandler$ui_release() {
        if (this.f71 == null) {
            Context context = getContext();
            fa1.m6825(context, "context");
            AndroidViewsHandler androidViewsHandler = new AndroidViewsHandler(context);
            this.f71 = androidViewsHandler;
            addView(androidViewsHandler);
        }
        AndroidViewsHandler androidViewsHandler2 = this.f71;
        fa1.m6823(androidViewsHandler2);
        return androidViewsHandler2;
    }

    @Override // okhttp3.internal.p042io.y33
    @wv2
    public InterfaceC6831 getAutofill() {
        return this.f110;
    }

    @Override // okhttp3.internal.p042io.y33
    @zu2
    /* renamed from: getAutofillTree, reason: from getter */
    public C7303 getF103() {
        return this.f103;
    }

    @Override // okhttp3.internal.p042io.y33
    @zu2
    /* renamed from: getClipboardManager, reason: from getter */
    public C6616 getF113() {
        return this.f113;
    }

    @zu2
    public final ph0<Configuration, lx5> getConfigurationChangeObserver() {
        return this.f109;
    }

    @Override // okhttp3.internal.p042io.y33
    @zu2
    public InterfaceC5282u7 getDensity() {
        return this.f93;
    }

    @wv2
    /* renamed from: getFocusDirection-P8AzH3I, reason: not valid java name */
    public jc0 m18345getFocusDirectionP8AzH3I(@zu2 KeyEvent keyEvent) {
        jc0 jc0Var;
        fa1.m6826(keyEvent, "keyEvent");
        long m5811 = ct1.m5811(keyEvent);
        os1.C4417 c4417 = os1.f16907;
        if (os1.m10458(m5811, os1.f16914)) {
            return new jc0(keyEvent.isShiftPressed() ? 2 : 1);
        }
        if (os1.m10458(m5811, os1.f16912)) {
            jc0Var = new jc0(4);
        } else if (os1.m10458(m5811, os1.f16911)) {
            jc0Var = new jc0(3);
        } else if (os1.m10458(m5811, os1.f16909)) {
            jc0Var = new jc0(5);
        } else if (os1.m10458(m5811, os1.f16910)) {
            jc0Var = new jc0(6);
        } else {
            if (os1.m10458(m5811, os1.f16913) ? true : os1.m10458(m5811, os1.f16915) ? true : os1.m10458(m5811, os1.f16917)) {
                jc0Var = new jc0(7);
            } else {
                if (!(os1.m10458(m5811, os1.f16908) ? true : os1.m10458(m5811, os1.f16916))) {
                    return null;
                }
                jc0Var = new jc0(8);
            }
        }
        return jc0Var;
    }

    @Override // okhttp3.internal.p042io.y33
    @zu2
    public rc0 getFocusManager() {
        return this.f94;
    }

    @Override // android.view.View
    public void getFocusedRect(@zu2 Rect rect) {
        lx5 lx5Var;
        fa1.m6826(rect, "rect");
        tc0 m11370 = qm5.m11370(this.f94.f21125);
        if (m11370 != null) {
            pv3 m13186 = vb6.m13186(m11370);
            rect.left = ly3.m9477(m13186.f18007);
            rect.top = ly3.m9477(m13186.f18008);
            rect.right = ly3.m9477(m13186.f18009);
            rect.bottom = ly3.m9477(m13186.f18010);
            lx5Var = lx5.f14876;
        } else {
            lx5Var = null;
        }
        if (lx5Var == null) {
            super.getFocusedRect(rect);
        }
    }

    @Override // okhttp3.internal.p042io.y33
    @zu2
    public ie0.InterfaceC3713 getFontFamilyResolver() {
        return (ie0.InterfaceC3713) this.f52.getValue();
    }

    @Override // okhttp3.internal.p042io.y33
    @zu2
    public he0.InterfaceC3576 getFontLoader() {
        return this.f74;
    }

    @Override // okhttp3.internal.p042io.y33
    @zu2
    public oq0 getHapticFeedBack() {
        return this.f73;
    }

    public boolean getHasPendingMeasureOrLayout() {
        return !this.f69.f24133.m6366();
    }

    @Override // okhttp3.internal.p042io.y33
    @zu2
    public p41 getInputModeManager() {
        return this.f78;
    }

    /* renamed from: getLastMatrixRecalculationAnimationTime$ui_release, reason: from getter */
    public final long getLastMatrixRecalculationAnimationTime() {
        return this.lastMatrixRecalculationAnimationTime;
    }

    /* JADX WARN: Multi-variable type inference failed */
    @Override // android.view.View, android.view.ViewParent, okhttp3.internal.p042io.y33
    @zu2
    public cw1 getLayoutDirection() {
        return (cw1) this.f85.getValue();
    }

    public long getMeasureIteration() {
        ve2 ve2Var = this.f69;
        if (ve2Var.f24134) {
            return ve2Var.f24137;
        }
        throw new IllegalArgumentException("measureIteration should be only used during the measure/layout pass".toString());
    }

    @Override // okhttp3.internal.p042io.y33
    @zu2
    /* renamed from: getModifierLocalManager, reason: from getter */
    public vk2 getF75() {
        return this.f75;
    }

    @Override // okhttp3.internal.p042io.y33
    @zu2
    public ff3 getPointerIconService() {
        return this.f72;
    }

    @zu2
    /* renamed from: getRoot, reason: from getter */
    public gx1 getF99() {
        return this.f99;
    }

    @zu2
    public g64 getRootForTest() {
        return this.f100;
    }

    @zu2
    /* renamed from: getSemanticsOwner, reason: from getter */
    public ak4 getF101() {
        return this.f101;
    }

    @Override // okhttp3.internal.p042io.y33
    @zu2
    /* renamed from: getSharedDrawScope, reason: from getter */
    public ix1 getF92() {
        return this.f92;
    }

    @Override // okhttp3.internal.p042io.y33
    public boolean getShowLayoutBounds() {
        return this.showLayoutBounds;
    }

    @Override // okhttp3.internal.p042io.y33
    @zu2
    /* renamed from: getSnapshotObserver, reason: from getter */
    public b43 getF89() {
        return this.f89;
    }

    @Override // okhttp3.internal.p042io.y33
    @zu2
    /* renamed from: getTextInputService, reason: from getter */
    public ef5 getF77() {
        return this.f77;
    }

    @Override // okhttp3.internal.p042io.y33
    @zu2
    public og5 getTextToolbar() {
        return this.f76;
    }

    @zu2
    public View getView() {
        return this;
    }

    @Override // okhttp3.internal.p042io.y33
    @zu2
    public b86 getViewConfiguration() {
        return this.f88;
    }

    /* JADX WARN: Multi-variable type inference failed */
    @wv2
    public final C0166 getViewTreeOwners() {
        return (C0166) this.f51.getValue();
    }

    @Override // okhttp3.internal.p042io.y33
    @zu2
    public lb6 getWindowInfo() {
        return this.f95;
    }

    public void invalidateDescendants() {
        m38(getF99());
    }

    public boolean isLifecycleInResumedState() {
        LifecycleOwner lifecycleOwner;
        Lifecycle lifecycle;
        C0166 viewTreeOwners = getViewTreeOwners();
        return ((viewTreeOwners == null || (lifecycleOwner = viewTreeOwners.f115) == null || (lifecycle = lifecycleOwner.getLifecycle()) == null) ? null : lifecycle.getCurrentState()) == Lifecycle.State.RESUMED;
    }

    @wv2
    public final Object keyboardVisibilityEventLoop(@zu2 InterfaceC7155<? super lx5> interfaceC7155) {
        Object m6930 = this.f60.m6930(interfaceC7155);
        return m6930 == EnumC7329.COROUTINE_SUSPENDED ? m6930 : lx5.f14876;
    }

    @Override // okhttp3.internal.p042io.og3
    /* renamed from: localToScreen-MK-Hz9U, reason: not valid java name */
    public long mo18346localToScreenMKHz9U(long localPosition) {
        m43();
        long m7783 = he2.m7783(this.f86, localPosition);
        return C2878bi.m5168(g03.m7139(this.f65) + g03.m7139(m7783), g03.m7140(this.f65) + g03.m7140(m7783));
    }

    @Override // okhttp3.internal.p042io.y33
    public void measureAndLayout(boolean z) {
        nh0<lx5> nh0Var;
        Trace.beginSection("AndroidOwner:measureAndLayout");
        if (z) {
            try {
                nh0Var = this.f111;
            } catch (Throwable th) {
                Trace.endSection();
                throw th;
            }
        } else {
            nh0Var = null;
        }
        if (this.f69.m13210(nh0Var)) {
            requestLayout();
        }
        this.f69.m13204(false);
        Trace.endSection();
    }

    @Override // okhttp3.internal.p042io.y33
    /* renamed from: measureAndLayout-0kLqBqw, reason: not valid java name */
    public void mo18347measureAndLayout0kLqBqw(@zu2 gx1 layoutNode, long constraints) {
        fa1.m6826(layoutNode, "layoutNode");
        Trace.beginSection("AndroidOwner:measureAndLayout");
        try {
            this.f69.m13211(layoutNode, constraints);
            this.f69.m13204(false);
        } finally {
            Trace.endSection();
        }
    }

    /* JADX WARN: Type inference failed for: r3v7, types: [java.util.ArrayList, java.util.List<okhttp3.internal.io.w33>] */
    public final void notifyLayerIsDirty$ui_release(@zu2 w33 layer, boolean isDirty) {
        List list;
        fa1.m6826(layer, "layer");
        if (!isDirty) {
            if (!this.f106 && !this.f104.remove(layer)) {
                throw new IllegalArgumentException("Failed requirement.".toString());
            }
            return;
        }
        if (this.f106) {
            list = this.f105;
            if (list == null) {
                list = new ArrayList();
                this.f105 = list;
            }
        } else {
            list = this.f104;
        }
        list.add(layer);
    }

    @Override // okhttp3.internal.p042io.y33
    public void onAttach(@zu2 gx1 gx1Var) {
        fa1.m6826(gx1Var, ProjectConfig.PROJECT_TYPE_NODE);
    }

    @Override // android.view.ViewGroup, android.view.View
    public final void onAttachedToWindow() {
        LifecycleOwner lifecycleOwner;
        Lifecycle lifecycle;
        LifecycleOwner lifecycleOwner2;
        C7682 c7682;
        super.onAttachedToWindow();
        m39(getF99());
        m38(getF99());
        getF89().f6888.m14077();
        if (m31() && (c7682 = this.f110) != null) {
            C6757.f29966.m15950(c7682);
        }
        LifecycleOwner lifecycleOwner3 = ViewTreeLifecycleOwner.get(this);
        SavedStateRegistryOwner savedStateRegistryOwner = C10501ViewTreeSavedStateRegistryOwner.get(this);
        C0166 viewTreeOwners = getViewTreeOwners();
        if (viewTreeOwners == null || !(lifecycleOwner3 == null || savedStateRegistryOwner == null || (lifecycleOwner3 == (lifecycleOwner2 = viewTreeOwners.f115) && savedStateRegistryOwner == lifecycleOwner2))) {
            if (lifecycleOwner3 == null) {
                throw new IllegalStateException("Composed into the View which doesn't propagate ViewTreeLifecycleOwner!");
            }
            if (savedStateRegistryOwner == null) {
                throw new IllegalStateException("Composed into the View which doesn't propagateViewTreeSavedStateRegistryOwner!");
            }
            if (viewTreeOwners != null && (lifecycleOwner = viewTreeOwners.f115) != null && (lifecycle = lifecycleOwner.getLifecycle()) != null) {
                lifecycle.removeObserver(this);
            }
            lifecycleOwner3.getLifecycle().addObserver(this);
            C0166 c0166 = new C0166(lifecycleOwner3, savedStateRegistryOwner);
            setViewTreeOwners(c0166);
            ph0<? super C0166, lx5> ph0Var = this.f62;
            if (ph0Var != null) {
                ph0Var.invoke(c0166);
            }
            this.f62 = null;
        }
        C0166 viewTreeOwners2 = getViewTreeOwners();
        fa1.m6823(viewTreeOwners2);
        viewTreeOwners2.f115.getLifecycle().addObserver(this);
        getViewTreeObserver().addOnGlobalLayoutListener(this.f63);
        getViewTreeObserver().addOnScrollChangedListener(this.f57);
        getViewTreeObserver().addOnTouchModeChangeListener(this.f79);
    }

    @Override // android.view.View
    public boolean onCheckIsTextEditor() {
        return this.f60.f10066;
    }

    @Override // android.view.View
    public final void onConfigurationChanged(@zu2 Configuration configuration) {
        fa1.m6826(configuration, "newConfig");
        super.onConfigurationChanged(configuration);
        Context context = getContext();
        fa1.m6825(context, "context");
        this.f93 = (C5436v7) dr0.m6152(context);
        if (m35(configuration) != this.f84) {
            this.f84 = m35(configuration);
            Context context2 = getContext();
            fa1.m6825(context2, "context");
            setFontFamilyResolver(te0.m12416(context2));
        }
        this.f109.invoke(configuration);
    }

    @Override // androidx.lifecycle.DefaultLifecycleObserver, androidx.lifecycle.FullLifecycleObserver
    public final /* synthetic */ void onCreate(LifecycleOwner lifecycleOwner) {
        C3299f6.m6765(this, lifecycleOwner);
    }

    /* JADX WARN: Removed duplicated region for block: B:43:0x0136  */
    /* JADX WARN: Type inference failed for: r0v4, types: [java.util.ArrayList, java.util.List<java.lang.ref.WeakReference<okhttp3.internal.io.nv3>>] */
    @Override // android.view.View
    @okhttp3.internal.p042io.wv2
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public android.view.inputmethod.InputConnection onCreateInputConnection(@okhttp3.internal.p042io.zu2 android.view.inputmethod.EditorInfo r18) {
        /*
            Method dump skipped, instructions count: 401
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.compose.p000ui.platform.AndroidComposeView.onCreateInputConnection(android.view.inputmethod.EditorInfo):android.view.inputmethod.InputConnection");
    }

    @Override // androidx.lifecycle.DefaultLifecycleObserver, androidx.lifecycle.FullLifecycleObserver
    public final /* synthetic */ void onDestroy(LifecycleOwner lifecycleOwner) {
        C3299f6.m6766(this, lifecycleOwner);
    }

    @Override // okhttp3.internal.p042io.y33
    public void onDetach(@zu2 gx1 gx1Var) {
        fa1.m6826(gx1Var, ProjectConfig.PROJECT_TYPE_NODE);
        ve2 ve2Var = this.f69;
        Objects.requireNonNull(ve2Var);
        ve2Var.f24133.m6367(gx1Var);
        requestClearInvalidObservations();
    }

    @Override // android.view.ViewGroup, android.view.View
    public final void onDetachedFromWindow() {
        C7682 c7682;
        LifecycleOwner lifecycleOwner;
        Lifecycle lifecycle;
        super.onDetachedFromWindow();
        b43 f89 = getF89();
        xu4 xu4Var = f89.f6888.f26204;
        if (xu4Var != null) {
            xu4Var.dispose();
        }
        f89.f6888.m14074();
        C0166 viewTreeOwners = getViewTreeOwners();
        if (viewTreeOwners != null && (lifecycleOwner = viewTreeOwners.f115) != null && (lifecycle = lifecycleOwner.getLifecycle()) != null) {
            lifecycle.removeObserver(this);
        }
        if (m31() && (c7682 = this.f110) != null) {
            C6757.f29966.m15951(c7682);
        }
        getViewTreeObserver().removeOnGlobalLayoutListener(this.f63);
        getViewTreeObserver().removeOnScrollChangedListener(this.f57);
        getViewTreeObserver().removeOnTouchModeChangeListener(this.f79);
    }

    @Override // android.view.View
    public final void onDraw(@zu2 Canvas canvas) {
        fa1.m6826(canvas, "canvas");
    }

    @Override // okhttp3.internal.p042io.y33
    public void onEndApplyChanges() {
        if (this.f112) {
            xv4 xv4Var = getF89().f6888;
            a43 a43Var = a43.f6108;
            Objects.requireNonNull(xv4Var);
            fa1.m6826(a43Var, "predicate");
            synchronized (xv4Var.f26203) {
                co2<xv4.C5754> co2Var = xv4Var.f26203;
                int i = co2Var.f8122;
                if (i > 0) {
                    xv4.C5754[] c5754Arr = co2Var.f8120;
                    fa1.m6824(c5754Arr, "null cannot be cast to non-null type kotlin.Array<T of androidx.compose.runtime.collection.MutableVector>");
                    int i2 = 0;
                    do {
                        c5754Arr[i2].m14082(a43Var);
                        i2++;
                    } while (i2 < i);
                }
            }
            this.f112 = false;
        }
        AndroidViewsHandler androidViewsHandler = this.f71;
        if (androidViewsHandler != null) {
            m32(androidViewsHandler);
        }
        while (this.f54.m5776()) {
            int i3 = this.f54.f8122;
            for (int i4 = 0; i4 < i3; i4++) {
                co2<nh0<lx5>> co2Var2 = this.f54;
                nh0<lx5> nh0Var = co2Var2.f8120[i4];
                co2Var2.m5781(i4, null);
                if (nh0Var != null) {
                    nh0Var.invoke();
                }
            }
            this.f54.m5780(0, i3);
        }
    }

    @Override // android.view.View
    public final void onFocusChanged(boolean z, int i, @wv2 Rect rect) {
        super.onFocusChanged(z, i, rect);
        sc0 sc0Var = this.f94;
        if (!z) {
            ld0.m9226(sc0Var.f21125, true);
            return;
        }
        tc0 tc0Var = sc0Var.f21125;
        if (tc0Var.f22006 == kd0.Inactive) {
            tc0Var.m12335(kd0.Active);
        }
    }

    @Override // android.view.ViewGroup, android.view.View
    public final void onLayout(boolean z, int i, int i2, int i3, int i4) {
        this.f69.m13210(this.f111);
        this.f66 = null;
        m48();
        if (this.f71 != null) {
            getAndroidViewsHandler$ui_release().layout(0, 0, i3 - i, i4 - i2);
        }
    }

    @Override // okhttp3.internal.p042io.y33
    public void onLayoutChange(@zu2 gx1 gx1Var) {
        fa1.m6826(gx1Var, "layoutNode");
        C7067 c7067 = this.f102;
        Objects.requireNonNull(c7067);
        c7067.f30829 = true;
        if (c7067.m16428()) {
            c7067.m16429(gx1Var);
        }
    }

    @Override // android.view.View
    public final void onMeasure(int i, int i2) {
        Trace.beginSection("AndroidOwner:onMeasure");
        try {
            if (!isAttachedToWindow()) {
                m39(getF99());
            }
            v63<Integer, Integer> m33 = m33(i);
            int intValue = m33.f23750.intValue();
            int intValue2 = m33.f23751.intValue();
            v63<Integer, Integer> m332 = m33(i2);
            long m15455 = C6492.m15455(intValue, intValue2, m332.f23750.intValue(), m332.f23751.intValue());
            C7409 c7409 = this.f66;
            boolean z = false;
            if (c7409 == null) {
                this.f66 = new C7409(m15455);
                this.f67 = false;
            } else {
                if (c7409 != null) {
                    z = C7409.m16997(c7409.f31784, m15455);
                }
                if (!z) {
                    this.f67 = true;
                }
            }
            this.f69.m13220(m15455);
            this.f69.m13212();
            setMeasuredDimension(getF99().f11122.f13445.f7055, getF99().f11122.f13445.f7056);
            if (this.f71 != null) {
                getAndroidViewsHandler$ui_release().measure(View.MeasureSpec.makeMeasureSpec(getF99().f11122.f13445.f7055, 1073741824), View.MeasureSpec.makeMeasureSpec(getF99().f11122.f13445.f7056, 1073741824));
            }
        } finally {
            Trace.endSection();
        }
    }

    @Override // androidx.lifecycle.DefaultLifecycleObserver, androidx.lifecycle.FullLifecycleObserver
    public final /* synthetic */ void onPause(LifecycleOwner lifecycleOwner) {
        C3299f6.m6767(this, lifecycleOwner);
    }

    /* JADX WARN: Type inference failed for: r1v4, types: [java.util.LinkedHashMap, java.util.Map<java.lang.Integer, okhttp3.internal.io.ճ>] */
    @Override // android.view.View
    public void onProvideAutofillVirtualStructure(@wv2 ViewStructure viewStructure, int i) {
        C7682 c7682;
        if (!m31() || viewStructure == null || (c7682 = this.f110) == null) {
            return;
        }
        int m15634 = C6584.f29323.m15634(viewStructure, c7682.f32619.f31525.size());
        for (Map.Entry entry : c7682.f32619.f31525.entrySet()) {
            int intValue = ((Number) entry.getKey()).intValue();
            C6579 c6579 = (C6579) entry.getValue();
            C6584 c6584 = C6584.f29323;
            ViewStructure m15635 = c6584.m15635(viewStructure, m15634);
            if (m15635 != null) {
                C7152 c7152 = C7152.f31069;
                AutofillId m16554 = c7152.m16554(viewStructure);
                fa1.m6823(m16554);
                c7152.m16560(m15635, m16554, intValue);
                c6584.m15637(m15635, intValue, c7682.f32618.getContext().getPackageName(), null, null);
                c7152.m16561(m15635, 1);
                Objects.requireNonNull(c6579);
                throw null;
            }
            m15634++;
        }
    }

    @Override // okhttp3.internal.p042io.y33
    public void onRequestMeasure(@zu2 gx1 gx1Var, boolean z, boolean z2) {
        fa1.m6826(gx1Var, "layoutNode");
        if (z) {
            if (!this.f69.m13217(gx1Var, z2)) {
                return;
            }
        } else if (!this.f69.m13219(gx1Var, z2)) {
            return;
        }
        m45(gx1Var);
    }

    @Override // okhttp3.internal.p042io.y33
    public void onRequestRelayout(@zu2 gx1 gx1Var, boolean z, boolean z2) {
        fa1.m6826(gx1Var, "layoutNode");
        if (z) {
            if (!this.f69.m13216(gx1Var, z2)) {
                return;
            }
        } else if (!this.f69.m13218(gx1Var, z2)) {
            return;
        }
        m45(null);
    }

    @Override // androidx.lifecycle.DefaultLifecycleObserver, androidx.lifecycle.FullLifecycleObserver
    public void onResume(@zu2 LifecycleOwner lifecycleOwner) {
        fa1.m6826(lifecycleOwner, "owner");
        setShowLayoutBounds(C0165.m49());
    }

    @Override // android.view.View
    public void onRtlPropertiesChanged(int i) {
        if (this.f91) {
            ph0<? super md3, ? extends ef5> ph0Var = C7687.f32624;
            cw1 cw1Var = cw1.Ltr;
            if (i != 0 && i == 1) {
                cw1Var = cw1.Rtl;
            }
            setLayoutDirection(cw1Var);
            sc0 sc0Var = this.f94;
            Objects.requireNonNull(sc0Var);
            sc0Var.f21127 = cw1Var;
        }
    }

    @Override // okhttp3.internal.p042io.y33
    public void onSemanticsChange() {
        C7067 c7067 = this.f102;
        c7067.f30829 = true;
        if (!c7067.m16428() || c7067.f30835) {
            return;
        }
        c7067.f30835 = true;
        c7067.f30820.post(c7067.f30836);
    }

    @Override // androidx.lifecycle.DefaultLifecycleObserver, androidx.lifecycle.FullLifecycleObserver
    public final /* synthetic */ void onStart(LifecycleOwner lifecycleOwner) {
        C3299f6.m6769(this, lifecycleOwner);
    }

    @Override // androidx.lifecycle.DefaultLifecycleObserver, androidx.lifecycle.FullLifecycleObserver
    public final /* synthetic */ void onStop(LifecycleOwner lifecycleOwner) {
        C3299f6.m6770(this, lifecycleOwner);
    }

    @Override // android.view.View
    public void onWindowFocusChanged(boolean z) {
        boolean m49;
        this.f95.f15225.setValue(Boolean.valueOf(z));
        this.f81 = true;
        super.onWindowFocusChanged(z);
        if (!z || getShowLayoutBounds() == (m49 = C0165.m49())) {
            return;
        }
        setShowLayoutBounds(m49);
        invalidateDescendants();
    }

    /* JADX WARN: Removed duplicated region for block: B:11:0x002a  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final boolean recycle$ui_release(@okhttp3.internal.p042io.zu2 okhttp3.internal.p042io.w33 r5) {
        /*
            r4 = this;
            java.lang.String r0 = "layer"
            okhttp3.internal.p042io.fa1.m6826(r5, r0)
            androidx.compose.ui.platform.DrawChildContainer r0 = r4.f82
            if (r0 == 0) goto L27
            androidx.compose.ui.platform.ViewLayer$Ԫ r0 = androidx.compose.p000ui.platform.ViewLayer.f138
            boolean r0 = androidx.compose.p000ui.platform.ViewLayer.access$getShouldUseDispatchDraw$cp()
            if (r0 != 0) goto L27
            int r0 = android.os.Build.VERSION.SDK_INT
            r1 = 23
            if (r0 >= r1) goto L27
            okhttp3.internal.io.z96<okhttp3.internal.io.w33> r0 = r4.f48
            r0.m14487()
            okhttp3.internal.io.co2<java.lang.ref.Reference<T>> r0 = r0.f27198
            int r0 = r0.f8122
            r1 = 10
            if (r0 >= r1) goto L25
            goto L27
        L25:
            r0 = 0
            goto L28
        L27:
            r0 = 1
        L28:
            if (r0 == 0) goto L3b
            okhttp3.internal.io.z96<okhttp3.internal.io.w33> r1 = r4.f48
            r1.m14487()
            okhttp3.internal.io.co2<java.lang.ref.Reference<T>> r2 = r1.f27198
            java.lang.ref.WeakReference r3 = new java.lang.ref.WeakReference
            java.lang.ref.ReferenceQueue<T> r1 = r1.f27199
            r3.<init>(r5, r1)
            r2.m5767(r3)
        L3b:
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.compose.p000ui.platform.AndroidComposeView.recycle$ui_release(okhttp3.internal.io.w33):boolean");
    }

    @Override // okhttp3.internal.p042io.y33
    public void registerOnEndApplyChangesListener(@zu2 nh0<lx5> nh0Var) {
        fa1.m6826(nh0Var, "listener");
        if (this.f54.m5772(nh0Var)) {
            return;
        }
        this.f54.m5767(nh0Var);
    }

    @Override // okhttp3.internal.p042io.y33
    public void registerOnLayoutCompletedListener(@zu2 y33.InterfaceC5780 interfaceC5780) {
        fa1.m6826(interfaceC5780, "listener");
        ve2 ve2Var = this.f69;
        Objects.requireNonNull(ve2Var);
        ve2Var.f24136.m5767(interfaceC5780);
        m45(null);
    }

    public final void removeAndroidView(@zu2 AndroidViewHolder androidViewHolder) {
        fa1.m6826(androidViewHolder, "view");
        registerOnEndApplyChangesListener(new C0172(androidViewHolder));
    }

    public final void requestClearInvalidObservations() {
        this.f112 = true;
    }

    @Override // okhttp3.internal.p042io.y33
    public void requestOnPositionedCallback(@zu2 gx1 gx1Var) {
        fa1.m6826(gx1Var, "layoutNode");
        ve2 ve2Var = this.f69;
        Objects.requireNonNull(ve2Var);
        ve2Var.f24135.m6295(gx1Var);
        m45(null);
    }

    @Override // okhttp3.internal.p042io.og3
    /* renamed from: screenToLocal-MK-Hz9U, reason: not valid java name */
    public long mo18348screenToLocalMKHz9U(long positionOnScreen) {
        m43();
        return he2.m7783(this.f59, C2878bi.m5168(g03.m7139(positionOnScreen) - g03.m7139(this.f65), g03.m7140(positionOnScreen) - g03.m7140(this.f65)));
    }

    /* renamed from: sendKeyEvent-ZmokQxo, reason: not valid java name */
    public boolean m18349sendKeyEventZmokQxo(@zu2 KeyEvent keyEvent) {
        tc0 m13184;
        gx1 gx1Var;
        fa1.m6826(keyEvent, "keyEvent");
        it1 it1Var = this.f96;
        Objects.requireNonNull(it1Var);
        tc0 tc0Var = it1Var.f12647;
        if (tc0Var != null && (m13184 = vb6.m13184(tc0Var)) != null) {
            ht2 ht2Var = m13184.f22015;
            it1 it1Var2 = null;
            if (ht2Var != null && (gx1Var = ht2Var.f11899) != null) {
                co2<it1> co2Var = m13184.f22018;
                int i = co2Var.f8122;
                if (i > 0) {
                    int i2 = 0;
                    it1[] it1VarArr = co2Var.f8120;
                    fa1.m6824(it1VarArr, "null cannot be cast to non-null type kotlin.Array<T of androidx.compose.runtime.collection.MutableVector>");
                    do {
                        it1 it1Var3 = it1VarArr[i2];
                        if (fa1.m6818(it1Var3.f12649, gx1Var)) {
                            if (it1Var2 != null) {
                                gx1 gx1Var2 = it1Var3.f12649;
                                it1 it1Var4 = it1Var2;
                                while (!fa1.m6818(it1Var4, it1Var3)) {
                                    it1Var4 = it1Var4.f12648;
                                    if (it1Var4 != null && fa1.m6818(it1Var4.f12649, gx1Var2)) {
                                    }
                                }
                            }
                            it1Var2 = it1Var3;
                            break;
                        }
                        i2++;
                    } while (i2 < i);
                }
                if (it1Var2 == null) {
                    it1Var2 = m13184.f22017;
                }
            }
            if (it1Var2 != null) {
                if (it1Var2.m8417(keyEvent)) {
                    return true;
                }
                return it1Var2.m8416(keyEvent);
            }
        }
        throw new IllegalStateException("KeyEvent can't be processed because this key input node is not active.".toString());
    }

    public final void setConfigurationChangeObserver(@zu2 ph0<? super Configuration, lx5> ph0Var) {
        fa1.m6826(ph0Var, "<set-?>");
        this.f109 = ph0Var;
    }

    public final void setLastMatrixRecalculationAnimationTime$ui_release(long j) {
        this.lastMatrixRecalculationAnimationTime = j;
    }

    public final void setOnViewTreeOwnersAvailable(@zu2 ph0<? super C0166, lx5> ph0Var) {
        fa1.m6826(ph0Var, "callback");
        C0166 viewTreeOwners = getViewTreeOwners();
        if (viewTreeOwners != null) {
            ph0Var.invoke(viewTreeOwners);
        }
        if (isAttachedToWindow()) {
            return;
        }
        this.f62 = ph0Var;
    }

    @Override // okhttp3.internal.p042io.y33
    public void setShowLayoutBounds(boolean z) {
        this.showLayoutBounds = z;
    }

    @Override // android.view.ViewGroup
    public boolean shouldDelayChildPressedState() {
        return false;
    }

    /* renamed from: Ϳ */
    public final boolean m31() {
        return Build.VERSION.SDK_INT >= 26;
    }

    /* renamed from: Ԩ */
    public final void m32(ViewGroup viewGroup) {
        int childCount = viewGroup.getChildCount();
        for (int i = 0; i < childCount; i++) {
            View childAt = viewGroup.getChildAt(i);
            if (childAt instanceof AndroidComposeView) {
                ((AndroidComposeView) childAt).onEndApplyChanges();
            } else if (childAt instanceof ViewGroup) {
                m32((ViewGroup) childAt);
            }
        }
    }

    /* renamed from: ԩ */
    public final v63<Integer, Integer> m33(int i) {
        int mode = View.MeasureSpec.getMode(i);
        int size = View.MeasureSpec.getSize(i);
        if (mode == Integer.MIN_VALUE) {
            return new v63<>(0, Integer.valueOf(size));
        }
        if (mode == 0) {
            return new v63<>(0, Integer.MAX_VALUE);
        }
        if (mode == 1073741824) {
            return new v63<>(Integer.valueOf(size), Integer.valueOf(size));
        }
        throw new IllegalStateException();
    }

    /* renamed from: Ԫ */
    public final View m34(int i, View view) {
        if (Build.VERSION.SDK_INT >= 29) {
            return null;
        }
        Method declaredMethod = View.class.getDeclaredMethod("getAccessibilityViewId", new Class[0]);
        declaredMethod.setAccessible(true);
        if (fa1.m6818(declaredMethod.invoke(view, new Object[0]), Integer.valueOf(i))) {
            return view;
        }
        if (!(view instanceof ViewGroup)) {
            return null;
        }
        ViewGroup viewGroup = (ViewGroup) view;
        int childCount = viewGroup.getChildCount();
        for (int i2 = 0; i2 < childCount; i2++) {
            View childAt = viewGroup.getChildAt(i2);
            fa1.m6825(childAt, "currentView.getChildAt(i)");
            View m34 = m34(i, childAt);
            if (m34 != null) {
                return m34;
            }
        }
        return null;
    }

    /* renamed from: ԫ */
    public final int m35(Configuration configuration) {
        if (Build.VERSION.SDK_INT >= 31) {
            return configuration.fontWeightAdjustment;
        }
        return 0;
    }

    /* JADX WARN: Removed duplicated region for block: B:18:0x0049 A[Catch: all -> 0x0066, TryCatch #1 {all -> 0x0066, blocks: (B:5:0x0017, B:7:0x0020, B:11:0x002b, B:13:0x0031, B:18:0x0049, B:19:0x004f, B:22:0x0059, B:23:0x0038, B:30:0x0068, B:38:0x007a, B:40:0x0080, B:42:0x008e, B:43:0x0091), top: B:4:0x0017, outer: #0 }] */
    /* JADX WARN: Removed duplicated region for block: B:19:0x004f A[Catch: all -> 0x0066, TryCatch #1 {all -> 0x0066, blocks: (B:5:0x0017, B:7:0x0020, B:11:0x002b, B:13:0x0031, B:18:0x0049, B:19:0x004f, B:22:0x0059, B:23:0x0038, B:30:0x0068, B:38:0x007a, B:40:0x0080, B:42:0x008e, B:43:0x0091), top: B:4:0x0017, outer: #0 }] */
    /* renamed from: Ԭ */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final int m36(android.view.MotionEvent r13) {
        /*
            r12 = this;
            androidx.compose.ui.platform.AndroidComposeView$ނ r0 = r12.f56
            r12.removeCallbacks(r0)
            r0 = 0
            r12.m44(r13)     // Catch: java.lang.Throwable -> Lb2
            r1 = 1
            r12.f80 = r1     // Catch: java.lang.Throwable -> Lb2
            r12.measureAndLayout(r0)     // Catch: java.lang.Throwable -> Lb2
            r2 = 0
            r12.f55 = r2     // Catch: java.lang.Throwable -> Lb2
            java.lang.String r2 = "AndroidOwner:onTouch"
            android.os.Trace.beginSection(r2)     // Catch: java.lang.Throwable -> Lb2
            int r2 = r13.getActionMasked()     // Catch: java.lang.Throwable -> L66
            android.view.MotionEvent r9 = r12.f50     // Catch: java.lang.Throwable -> L66
            r10 = 3
            if (r9 == 0) goto L28
            int r3 = r9.getToolType(r0)     // Catch: java.lang.Throwable -> L66
            if (r3 != r10) goto L28
            r11 = 1
            goto L29
        L28:
            r11 = 0
        L29:
            if (r9 == 0) goto L68
            boolean r3 = r12.m37(r13, r9)     // Catch: java.lang.Throwable -> L66
            if (r3 == 0) goto L68
            int r3 = r9.getButtonState()     // Catch: java.lang.Throwable -> L66
            if (r3 == 0) goto L38
            goto L46
        L38:
            int r3 = r9.getActionMasked()     // Catch: java.lang.Throwable -> L66
            if (r3 == 0) goto L46
            r4 = 2
            if (r3 == r4) goto L46
            r4 = 6
            if (r3 == r4) goto L46
            r3 = 0
            goto L47
        L46:
            r3 = 1
        L47:
            if (r3 == 0) goto L4f
            okhttp3.internal.io.lf3 r3 = r12.f108     // Catch: java.lang.Throwable -> L66
            r3.m9242()     // Catch: java.lang.Throwable -> L66
            goto L68
        L4f:
            int r3 = r9.getActionMasked()     // Catch: java.lang.Throwable -> L66
            r4 = 10
            if (r3 == r4) goto L68
            if (r11 == 0) goto L68
            r5 = 10
            long r6 = r9.getEventTime()     // Catch: java.lang.Throwable -> L66
            r8 = 1
            r3 = r12
            r4 = r9
            r3.m47(r4, r5, r6, r8)     // Catch: java.lang.Throwable -> L66
            goto L68
        L66:
            r13 = move-exception
            goto Lae
        L68:
            int r3 = r13.getToolType(r0)     // Catch: java.lang.Throwable -> L66
            if (r3 != r10) goto L6f
            goto L70
        L6f:
            r1 = 0
        L70:
            if (r11 != 0) goto L8c
            if (r1 == 0) goto L8c
            if (r2 == r10) goto L8c
            r1 = 9
            if (r2 == r1) goto L8c
            boolean r1 = r12.m41(r13)     // Catch: java.lang.Throwable -> L66
            if (r1 == 0) goto L8c
            r4 = 9
            long r5 = r13.getEventTime()     // Catch: java.lang.Throwable -> L66
            r7 = 1
            r2 = r12
            r3 = r13
            r2.m47(r3, r4, r5, r7)     // Catch: java.lang.Throwable -> L66
        L8c:
            if (r9 == 0) goto L91
            r9.recycle()     // Catch: java.lang.Throwable -> L66
        L91:
            android.view.MotionEvent r1 = android.view.MotionEvent.obtainNoHistory(r13)     // Catch: java.lang.Throwable -> L66
            r12.f50 = r1     // Catch: java.lang.Throwable -> L66
            int r13 = r12.m46(r13)     // Catch: java.lang.Throwable -> L66
            android.os.Trace.endSection()     // Catch: java.lang.Throwable -> Lb2
            int r1 = android.os.Build.VERSION.SDK_INT     // Catch: java.lang.Throwable -> Lb2
            r2 = 24
            if (r1 < r2) goto Lab
            okhttp3.internal.io.ߑ r1 = okhttp3.internal.p042io.C6904.f30275     // Catch: java.lang.Throwable -> Lb2
            okhttp3.internal.io.ef3 r2 = r12.f55     // Catch: java.lang.Throwable -> Lb2
            r1.m16175(r12, r2)     // Catch: java.lang.Throwable -> Lb2
        Lab:
            r12.f80 = r0
            return r13
        Lae:
            android.os.Trace.endSection()     // Catch: java.lang.Throwable -> Lb2
            throw r13     // Catch: java.lang.Throwable -> Lb2
        Lb2:
            r13 = move-exception
            r12.f80 = r0
            throw r13
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.compose.p000ui.platform.AndroidComposeView.m36(android.view.MotionEvent):int");
    }

    /* renamed from: ԭ */
    public final boolean m37(MotionEvent motionEvent, MotionEvent motionEvent2) {
        return (motionEvent2.getSource() == motionEvent.getSource() && motionEvent2.getToolType(0) == motionEvent.getToolType(0)) ? false : true;
    }

    /* renamed from: Ԯ */
    public final void m38(gx1 gx1Var) {
        gx1Var.m7517();
        co2<gx1> m7512 = gx1Var.m7512();
        int i = m7512.f8122;
        if (i > 0) {
            int i2 = 0;
            gx1[] gx1VarArr = m7512.f8120;
            fa1.m6824(gx1VarArr, "null cannot be cast to non-null type kotlin.Array<T of androidx.compose.runtime.collection.MutableVector>");
            do {
                m38(gx1VarArr[i2]);
                i2++;
            } while (i2 < i);
        }
    }

    /* renamed from: ԯ */
    public final void m39(gx1 gx1Var) {
        int i = 0;
        this.f69.m13219(gx1Var, false);
        co2<gx1> m7512 = gx1Var.m7512();
        int i2 = m7512.f8122;
        if (i2 > 0) {
            gx1[] gx1VarArr = m7512.f8120;
            fa1.m6824(gx1VarArr, "null cannot be cast to non-null type kotlin.Array<T of androidx.compose.runtime.collection.MutableVector>");
            do {
                m39(gx1VarArr[i]);
                i++;
            } while (i < i2);
        }
    }

    /* renamed from: ֏ */
    public final boolean m40(MotionEvent motionEvent) {
        float x = motionEvent.getX();
        if (!((Float.isInfinite(x) || Float.isNaN(x)) ? false : true)) {
            return true;
        }
        float y = motionEvent.getY();
        if (!((Float.isInfinite(y) || Float.isNaN(y)) ? false : true)) {
            return true;
        }
        float rawX = motionEvent.getRawX();
        if (!((Float.isInfinite(rawX) || Float.isNaN(rawX)) ? false : true)) {
            return true;
        }
        float rawY = motionEvent.getRawY();
        return !(!Float.isInfinite(rawY) && !Float.isNaN(rawY));
    }

    /* renamed from: ؠ */
    public final boolean m41(MotionEvent motionEvent) {
        float x = motionEvent.getX();
        float y = motionEvent.getY();
        if (0.0f <= x && x <= ((float) getWidth())) {
            if (0.0f <= y && y <= ((float) getHeight())) {
                return true;
            }
        }
        return false;
    }

    /* renamed from: ހ */
    public final boolean m42(MotionEvent motionEvent) {
        MotionEvent motionEvent2;
        if (motionEvent.getPointerCount() != 1 || (motionEvent2 = this.f50) == null) {
            return true;
        }
        if (motionEvent.getRawX() == motionEvent2.getRawX()) {
            return !((motionEvent.getRawY() > motionEvent2.getRawY() ? 1 : (motionEvent.getRawY() == motionEvent2.getRawY() ? 0 : -1)) == 0);
        }
        return true;
    }

    /* renamed from: ށ */
    public final void m43() {
        if (this.f80) {
            return;
        }
        long currentAnimationTimeMillis = AnimationUtils.currentAnimationTimeMillis();
        if (currentAnimationTimeMillis != this.lastMatrixRecalculationAnimationTime) {
            this.lastMatrixRecalculationAnimationTime = currentAnimationTimeMillis;
            this.f83.mo56(this, this.f86);
            js1.m8718(this.f86, this.f59);
            ViewParent parent = getParent();
            View view = this;
            while (parent instanceof ViewGroup) {
                view = (View) parent;
                parent = ((ViewGroup) view).getParent();
            }
            view.getLocationOnScreen(this.f61);
            int[] iArr = this.f61;
            float f = iArr[0];
            float f2 = iArr[1];
            view.getLocationInWindow(iArr);
            int[] iArr2 = this.f61;
            this.f65 = C2878bi.m5168(f - iArr2[0], f2 - iArr2[1]);
        }
    }

    /* renamed from: ނ */
    public final void m44(MotionEvent motionEvent) {
        this.lastMatrixRecalculationAnimationTime = AnimationUtils.currentAnimationTimeMillis();
        this.f83.mo56(this, this.f86);
        js1.m8718(this.f86, this.f59);
        long m7783 = he2.m7783(this.f86, C2878bi.m5168(motionEvent.getX(), motionEvent.getY()));
        this.f65 = C2878bi.m5168(motionEvent.getRawX() - g03.m7139(m7783), motionEvent.getRawY() - g03.m7140(m7783));
    }

    /* renamed from: ރ */
    public final void m45(gx1 gx1Var) {
        if (isLayoutRequested() || !isAttachedToWindow()) {
            return;
        }
        if (this.f67 && gx1Var != null) {
            while (gx1Var != null && gx1Var.f11156 == 1) {
                gx1Var = gx1Var.m7510();
            }
            if (gx1Var == getF99()) {
                requestLayout();
                return;
            }
        }
        if (getWidth() == 0 || getHeight() == 0) {
            requestLayout();
        } else {
            invalidate();
        }
    }

    /* renamed from: ބ */
    public final int m46(MotionEvent motionEvent) {
        kf3 kf3Var;
        if (this.f81) {
            this.f81 = false;
            mb6 mb6Var = this.f95;
            int metaState = motionEvent.getMetaState();
            Objects.requireNonNull(mb6Var);
            mb6.f15224.setValue(new sf3(metaState));
        }
        jf3 m14010 = this.f107.m14010(motionEvent, this);
        if (m14010 == null) {
            this.f108.m9242();
            return C4350o9.m10265(false, false);
        }
        List<kf3> list = m14010.f13112;
        ListIterator<kf3> listIterator = list.listIterator(list.size());
        while (true) {
            if (!listIterator.hasPrevious()) {
                kf3Var = null;
                break;
            }
            kf3Var = listIterator.previous();
            if (kf3Var.f13783) {
                break;
            }
        }
        kf3 kf3Var2 = kf3Var;
        if (kf3Var2 != null) {
            this.f90 = kf3Var2.f13782;
        }
        int m9241 = this.f108.m9241(m14010, this, m41(motionEvent));
        int actionMasked = motionEvent.getActionMasked();
        if ((actionMasked != 0 && actionMasked != 5) || fj3.m6985(m9241)) {
            return m9241;
        }
        xl2 xl2Var = this.f107;
        int pointerId = motionEvent.getPointerId(motionEvent.getActionIndex());
        xl2Var.f26009.delete(pointerId);
        xl2Var.f26008.delete(pointerId);
        return m9241;
    }

    /* renamed from: ޅ */
    public final void m47(MotionEvent motionEvent, int i, long j, boolean z) {
        int actionMasked = motionEvent.getActionMasked();
        int i2 = -1;
        if (actionMasked != 1) {
            if (actionMasked == 6) {
                i2 = motionEvent.getActionIndex();
            }
        } else if (i != 9 && i != 10) {
            i2 = 0;
        }
        int pointerCount = motionEvent.getPointerCount() - (i2 >= 0 ? 1 : 0);
        if (pointerCount == 0) {
            return;
        }
        MotionEvent.PointerProperties[] pointerPropertiesArr = new MotionEvent.PointerProperties[pointerCount];
        for (int i3 = 0; i3 < pointerCount; i3++) {
            pointerPropertiesArr[i3] = new MotionEvent.PointerProperties();
        }
        MotionEvent.PointerCoords[] pointerCoordsArr = new MotionEvent.PointerCoords[pointerCount];
        for (int i4 = 0; i4 < pointerCount; i4++) {
            pointerCoordsArr[i4] = new MotionEvent.PointerCoords();
        }
        int i5 = 0;
        while (i5 < pointerCount) {
            int i6 = ((i2 < 0 || i5 < i2) ? 0 : 1) + i5;
            motionEvent.getPointerProperties(i6, pointerPropertiesArr[i5]);
            MotionEvent.PointerCoords pointerCoords = pointerCoordsArr[i5];
            motionEvent.getPointerCoords(i6, pointerCoords);
            long mo18346localToScreenMKHz9U = mo18346localToScreenMKHz9U(C2878bi.m5168(pointerCoords.x, pointerCoords.y));
            pointerCoords.x = g03.m7139(mo18346localToScreenMKHz9U);
            pointerCoords.y = g03.m7140(mo18346localToScreenMKHz9U);
            i5++;
        }
        MotionEvent obtain = MotionEvent.obtain(motionEvent.getDownTime() == motionEvent.getEventTime() ? j : motionEvent.getDownTime(), j, i, pointerCount, pointerPropertiesArr, pointerCoordsArr, motionEvent.getMetaState(), z ? 0 : motionEvent.getButtonState(), motionEvent.getXPrecision(), motionEvent.getYPrecision(), motionEvent.getDeviceId(), motionEvent.getEdgeFlags(), motionEvent.getSource(), motionEvent.getFlags());
        xl2 xl2Var = this.f107;
        fa1.m6825(obtain, NotificationCompat.CATEGORY_EVENT);
        jf3 m14010 = xl2Var.m14010(obtain, this);
        fa1.m6823(m14010);
        this.f108.m9241(m14010, this, true);
        obtain.recycle();
    }

    /* renamed from: ކ */
    public final void m48() {
        getLocationOnScreen(this.f61);
        long j = this.f87;
        g71.C3412 c3412 = g71.f10637;
        int i = (int) (j >> 32);
        int m7254 = g71.m7254(j);
        int[] iArr = this.f61;
        boolean z = false;
        if (i != iArr[0] || m7254 != iArr[1]) {
            this.f87 = rg0.m11587(iArr[0], iArr[1]);
            if (i != Integer.MAX_VALUE && m7254 != Integer.MAX_VALUE) {
                getF99().f11122.f13445.m8779();
                z = true;
            }
        }
        this.f69.m13204(z);
    }
}
