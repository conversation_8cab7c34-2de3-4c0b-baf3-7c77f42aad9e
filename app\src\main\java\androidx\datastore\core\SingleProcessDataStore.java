package androidx.datastore.core;

import androidx.annotation.GuardedBy;
import androidx.autofill.HintConstants;
import androidx.exifinterface.media.ExifInterface;
import com.stardust.autojs.engine.RhinoJavaScriptEngine;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import kotlin.Metadata;
import okhttp3.internal.p042io.C2849b5;
import okhttp3.internal.p042io.C6090;
import okhttp3.internal.p042io.C6698;
import okhttp3.internal.p042io.C6717;
import okhttp3.internal.p042io.C6802;
import okhttp3.internal.p042io.EnumC7329;
import okhttp3.internal.p042io.InterfaceC6260;
import okhttp3.internal.p042io.InterfaceC6710;
import okhttp3.internal.p042io.InterfaceC7155;
import okhttp3.internal.p042io.InterfaceC7881;
import okhttp3.internal.p042io.di0;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.nh0;
import okhttp3.internal.p042io.q80;
import okhttp3.internal.p042io.w94;
import okhttp3.internal.p042io.wv2;
import okhttp3.internal.p042io.wx1;
import okhttp3.internal.p042io.yg3;
import okhttp3.internal.p042io.zn2;
import okhttp3.internal.p042io.zu2;
import org.mozilla.javascript.optimizer.Codegen;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000\u008e\u0001\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\b\u0000\u0018\u0000 H*\u0004\b\u0000\u0010\u00012\b\u0012\u0004\u0012\u00028\u00000\u0002:\u0003HIJB\u0081\u0001\u0012\f\u0010B\u001a\b\u0012\u0004\u0012\u00020\u001d0A\u0012\f\u0010&\u001a\b\u0012\u0004\u0012\u00028\u00000%\u0012?\b\u0002\u0010C\u001a9\u00125\u00123\b\u0001\u0012\u0019\u0012\u0017\u0012\u0004\u0012\u00028\u00000/¢\u0006\f\b\u0013\u0012\b\b\u0014\u0012\u0004\b\b(0\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u0016\u0012\u0006\u0012\u0004\u0018\u00010\u00170\u00120.\u0012\u000e\b\u0002\u0010)\u001a\b\u0012\u0004\u0012\u00028\u00000(\u0012\b\b\u0002\u0010E\u001a\u00020Dø\u0001\u0000¢\u0006\u0004\bF\u0010GJ!\u0010\u0006\u001a\u00020\u00052\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00028\u00000\u0003H\u0082@ø\u0001\u0000¢\u0006\u0004\b\u0006\u0010\u0007J!\u0010\n\u001a\u00020\u00052\f\u0010\t\u001a\b\u0012\u0004\u0012\u00028\u00000\bH\u0082@ø\u0001\u0000¢\u0006\u0004\b\n\u0010\u000bJ\u0013\u0010\f\u001a\u00020\u0005H\u0082@ø\u0001\u0000¢\u0006\u0004\b\f\u0010\rJ\u0013\u0010\u000e\u001a\u00020\u0005H\u0082@ø\u0001\u0000¢\u0006\u0004\b\u000e\u0010\rJ\u0013\u0010\u000f\u001a\u00020\u0005H\u0082@ø\u0001\u0000¢\u0006\u0004\b\u000f\u0010\rJ\u0013\u0010\u0010\u001a\u00028\u0000H\u0082@ø\u0001\u0000¢\u0006\u0004\b\u0010\u0010\rJ\u0013\u0010\u0011\u001a\u00028\u0000H\u0082@ø\u0001\u0000¢\u0006\u0004\b\u0011\u0010\rJN\u0010\u001b\u001a\u00028\u000021\u0010\u0018\u001a-\b\u0001\u0012\u0013\u0012\u00118\u0000¢\u0006\f\b\u0013\u0012\b\b\u0014\u0012\u0004\b\b(\u0015\u0012\n\u0012\b\u0012\u0004\u0012\u00028\u00000\u0016\u0012\u0006\u0012\u0004\u0018\u00010\u00170\u00122\u0006\u0010\u001a\u001a\u00020\u0019H\u0082@ø\u0001\u0000¢\u0006\u0004\b\u001b\u0010\u001cJ\f\u0010\u001e\u001a\u00020\u0005*\u00020\u001dH\u0002JF\u0010\u001f\u001a\u00028\u000021\u0010\u0018\u001a-\b\u0001\u0012\u0013\u0012\u00118\u0000¢\u0006\f\b\u0013\u0012\b\b\u0014\u0012\u0004\b\b(\u0015\u0012\n\u0012\b\u0012\u0004\u0012\u00028\u00000\u0016\u0012\u0006\u0012\u0004\u0018\u00010\u00170\u0012H\u0096@ø\u0001\u0000¢\u0006\u0004\b\u001f\u0010 J\u001b\u0010$\u001a\u00020\u00052\u0006\u0010!\u001a\u00028\u0000H\u0080@ø\u0001\u0000¢\u0006\u0004\b\"\u0010#R\u001a\u0010&\u001a\b\u0012\u0004\u0012\u00028\u00000%8\u0002X\u0082\u0004¢\u0006\u0006\n\u0004\b&\u0010'R\u001a\u0010)\u001a\b\u0012\u0004\u0012\u00028\u00000(8\u0002X\u0082\u0004¢\u0006\u0006\n\u0004\b)\u0010*R\u0014\u0010,\u001a\u00020+8\u0002X\u0082D¢\u0006\u0006\n\u0004\b,\u0010-RR\u00101\u001a;\u00125\u00123\b\u0001\u0012\u0019\u0012\u0017\u0012\u0004\u0012\u00028\u00000/¢\u0006\f\b\u0013\u0012\b\b\u0014\u0012\u0004\b\b(0\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u0016\u0012\u0006\u0012\u0004\u0018\u00010\u00170\u0012\u0018\u00010.8\u0002@\u0002X\u0082\u000eø\u0001\u0000¢\u0006\u0006\n\u0004\b1\u00102R \u00105\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00028\u000004038\u0002X\u0082\u0004¢\u0006\u0006\n\u0004\b5\u00106R\u001b\u0010;\u001a\u00020\u001d8BX\u0082\u0084\u0002¢\u0006\f\n\u0004\b7\u00108\u001a\u0004\b9\u0010:R \u0010=\u001a\b\u0012\u0004\u0012\u00028\u00000<8\u0016X\u0096\u0004¢\u0006\f\n\u0004\b=\u0010>\u001a\u0004\b?\u0010@\u0082\u0002\u0004\n\u0002\b\u0019¨\u0006K"}, m4115d2 = {"Landroidx/datastore/core/SingleProcessDataStore;", ExifInterface.GPS_DIRECTION_TRUE, "Landroidx/datastore/core/DataStore;", "Landroidx/datastore/core/SingleProcessDataStore$Message$Read;", "read", "Lokhttp3/internal/io/lx5;", "handleRead", "(Landroidx/datastore/core/SingleProcessDataStore$Message$Read;Lokhttp3/internal/io/ৡ;)Ljava/lang/Object;", "Landroidx/datastore/core/SingleProcessDataStore$Message$Update;", "update", "handleUpdate", "(Landroidx/datastore/core/SingleProcessDataStore$Message$Update;Lokhttp3/internal/io/ৡ;)Ljava/lang/Object;", "readAndInitOrPropagateAndThrowFailure", "(Lokhttp3/internal/io/ৡ;)Ljava/lang/Object;", "readAndInitOrPropagateFailure", "readAndInit", "readDataOrHandleCorruption", "readData", "Lkotlin/Function2;", "Lokhttp3/internal/io/o73;", HintConstants.AUTOFILL_HINT_NAME, "t", "Lokhttp3/internal/io/ৡ;", "", "transform", "Lokhttp3/internal/io/ڛ;", "callerContext", "transformAndWrite", "(Lokhttp3/internal/io/di0;Lokhttp3/internal/io/ڛ;Lokhttp3/internal/io/ৡ;)Ljava/lang/Object;", "Ljava/io/File;", "createParentDirectories", "updateData", "(Lokhttp3/internal/io/di0;Lokhttp3/internal/io/ৡ;)Ljava/lang/Object;", "newData", "writeData$datastore_core", "(Ljava/lang/Object;Lokhttp3/internal/io/ৡ;)Ljava/lang/Object;", "writeData", "Landroidx/datastore/core/Serializer;", "serializer", "Landroidx/datastore/core/Serializer;", "Landroidx/datastore/core/CorruptionHandler;", "corruptionHandler", "Landroidx/datastore/core/CorruptionHandler;", "", "SCRATCH_SUFFIX", "Ljava/lang/String;", "", "Landroidx/datastore/core/InitializerApi;", "api", "initTasks", "Ljava/util/List;", "Landroidx/datastore/core/SimpleActor;", "Landroidx/datastore/core/SingleProcessDataStore$Message;", "actor", "Landroidx/datastore/core/SimpleActor;", "file$delegate", "Lokhttp3/internal/io/wx1;", "getFile", "()Ljava/io/File;", "file", "Lokhttp3/internal/io/q80;", "data", "Lokhttp3/internal/io/q80;", "getData", "()Lokhttp3/internal/io/q80;", "Lkotlin/Function0;", "produceFile", "initTasksList", "Lokhttp3/internal/io/ღ;", "scope", RhinoJavaScriptEngine.SOURCE_NAME_INIT, "(Lokhttp3/internal/io/nh0;Landroidx/datastore/core/Serializer;Ljava/util/List;Landroidx/datastore/core/CorruptionHandler;Lokhttp3/internal/io/ღ;)V", "Companion", "Message", "UncloseableOutputStream", "datastore-core"}, m4116k = 1, m4117mv = {1, 5, 1})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class SingleProcessDataStore<T> implements DataStore<T> {

    /* renamed from: Companion, reason: from kotlin metadata */
    @zu2
    public static final Companion INSTANCE = new Companion(null);

    @GuardedBy("activeFilesLock")
    @zu2
    private static final Set<String> activeFiles = new LinkedHashSet();

    @zu2
    private static final Object activeFilesLock = new Object();

    @zu2
    private final String SCRATCH_SUFFIX;

    @zu2
    private final SimpleActor<Message<T>> actor;

    @zu2
    private final CorruptionHandler<T> corruptionHandler;

    @zu2
    private final q80<T> data;

    @zu2
    private final zn2<State<T>> downstreamFlow;

    /* renamed from: file$delegate, reason: from kotlin metadata */
    @zu2
    private final wx1 file;

    @wv2
    private List<? extends di0<? super InitializerApi<T>, ? super InterfaceC7155<? super lx5>, ? extends Object>> initTasks;

    @zu2
    private final nh0<File> produceFile;

    @zu2
    private final InterfaceC7881 scope;

    @zu2
    private final Serializer<T> serializer;

    @Metadata(m4114d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010#\n\u0002\u0010\u000e\n\u0002\b\u0006\b\u0080\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002¢\u0006\u0002\u0010\u0002R\u001c\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u00048\u0000X\u0081\u0004¢\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007R\u0014\u0010\b\u001a\u00020\u0001X\u0080\u0004¢\u0006\b\n\u0000\u001a\u0004\b\t\u0010\n¨\u0006\u000b"}, m4115d2 = {"Landroidx/datastore/core/SingleProcessDataStore$Companion;", "", Codegen.TEMPLATE_LITERAL_INIT_METHOD_SIGNATURE, "activeFiles", "", "", "getActiveFiles$datastore_core", "()Ljava/util/Set;", "activeFilesLock", "getActiveFilesLock$datastore_core", "()Ljava/lang/Object;", "datastore-core"}, m4116k = 1, m4117mv = {1, 5, 1}, m4119xi = 48)
    public static final class Companion {
        private Companion() {
        }

        public /* synthetic */ Companion(C2849b5 c2849b5) {
            this();
        }

        @zu2
        public final Set<String> getActiveFiles$datastore_core() {
            return SingleProcessDataStore.activeFiles;
        }

        @zu2
        public final Object getActiveFilesLock$datastore_core() {
            return SingleProcessDataStore.activeFilesLock;
        }
    }

    @Metadata(m4114d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b2\u0018\u0000*\u0004\b\u0001\u0010\u00012\u00020\u0002:\u0002\b\tB\u0007\b\u0004¢\u0006\u0002\u0010\u0003R\u001a\u0010\u0004\u001a\n\u0012\u0004\u0012\u00028\u0001\u0018\u00010\u0005X¦\u0004¢\u0006\u0006\u001a\u0004\b\u0006\u0010\u0007\u0082\u0001\u0002\n\u000b¨\u0006\f"}, m4115d2 = {"Landroidx/datastore/core/SingleProcessDataStore$Message;", ExifInterface.GPS_DIRECTION_TRUE, "", Codegen.TEMPLATE_LITERAL_INIT_METHOD_SIGNATURE, "lastState", "Landroidx/datastore/core/State;", "getLastState", "()Landroidx/datastore/core/State;", "Read", "Update", "Landroidx/datastore/core/SingleProcessDataStore$Message$Read;", "Landroidx/datastore/core/SingleProcessDataStore$Message$Update;", "datastore-core"}, m4116k = 1, m4117mv = {1, 5, 1}, m4119xi = 48)
    public static abstract class Message<T> {

        @Metadata(m4114d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\u0018\u0000*\u0004\b\u0002\u0010\u00012\b\u0012\u0004\u0012\u0002H\u00010\u0002B\u0015\u0012\u000e\u0010\u0003\u001a\n\u0012\u0004\u0012\u00028\u0002\u0018\u00010\u0004¢\u0006\u0002\u0010\u0005R\u001c\u0010\u0003\u001a\n\u0012\u0004\u0012\u00028\u0002\u0018\u00010\u0004X\u0096\u0004¢\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007¨\u0006\b"}, m4115d2 = {"Landroidx/datastore/core/SingleProcessDataStore$Message$Read;", ExifInterface.GPS_DIRECTION_TRUE, "Landroidx/datastore/core/SingleProcessDataStore$Message;", "lastState", "Landroidx/datastore/core/State;", "(Landroidx/datastore/core/State;)V", "getLastState", "()Landroidx/datastore/core/State;", "datastore-core"}, m4116k = 1, m4117mv = {1, 5, 1}, m4119xi = 48)
        public static final class Read<T> extends Message<T> {

            @wv2
            private final State<T> lastState;

            public Read(@wv2 State<T> state) {
                super(null);
                this.lastState = state;
            }

            @Override // androidx.datastore.core.SingleProcessDataStore.Message
            @wv2
            public State<T> getLastState() {
                return this.lastState;
            }
        }

        @Metadata(m4113bv = {}, m4114d1 = {"\u0000:\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0007\u0018\u0000*\u0004\b\u0002\u0010\u00012\b\u0012\u0004\u0012\u00028\u00020\u0002Bc\u00121\u0010\u000e\u001a-\b\u0001\u0012\u0013\u0012\u00118\u0002¢\u0006\f\b\t\u0012\b\b\n\u0012\u0004\b\b(\u000b\u0012\n\u0012\b\u0012\u0004\u0012\u00028\u00020\f\u0012\u0006\u0012\u0004\u0018\u00010\r0\b\u0012\f\u0010\u0013\u001a\b\u0012\u0004\u0012\u00028\u00020\u0012\u0012\u000e\u0010\u0004\u001a\n\u0012\u0004\u0012\u00028\u0002\u0018\u00010\u0003\u0012\u0006\u0010\u0018\u001a\u00020\u0017ø\u0001\u0000¢\u0006\u0004\b\u001c\u0010\u001dR\"\u0010\u0004\u001a\n\u0012\u0004\u0012\u00028\u0002\u0018\u00010\u00038\u0016X\u0096\u0004¢\u0006\f\n\u0004\b\u0004\u0010\u0005\u001a\u0004\b\u0006\u0010\u0007RE\u0010\u000e\u001a-\b\u0001\u0012\u0013\u0012\u00118\u0002¢\u0006\f\b\t\u0012\b\b\n\u0012\u0004\b\b(\u000b\u0012\n\u0012\b\u0012\u0004\u0012\u00028\u00020\f\u0012\u0006\u0012\u0004\u0018\u00010\r0\b8\u0006ø\u0001\u0000¢\u0006\f\n\u0004\b\u000e\u0010\u000f\u001a\u0004\b\u0010\u0010\u0011R\u001d\u0010\u0013\u001a\b\u0012\u0004\u0012\u00028\u00020\u00128\u0006¢\u0006\f\n\u0004\b\u0013\u0010\u0014\u001a\u0004\b\u0015\u0010\u0016R\u0017\u0010\u0018\u001a\u00020\u00178\u0006¢\u0006\f\n\u0004\b\u0018\u0010\u0019\u001a\u0004\b\u001a\u0010\u001b\u0082\u0002\u0004\n\u0002\b\u0019¨\u0006\u001e"}, m4115d2 = {"Landroidx/datastore/core/SingleProcessDataStore$Message$Update;", ExifInterface.GPS_DIRECTION_TRUE, "Landroidx/datastore/core/SingleProcessDataStore$Message;", "Landroidx/datastore/core/State;", "lastState", "Landroidx/datastore/core/State;", "getLastState", "()Landroidx/datastore/core/State;", "Lkotlin/Function2;", "Lokhttp3/internal/io/o73;", HintConstants.AUTOFILL_HINT_NAME, "t", "Lokhttp3/internal/io/ৡ;", "", "transform", "Lokhttp3/internal/io/di0;", "getTransform", "()Lokhttp3/internal/io/di0;", "Lokhttp3/internal/io/ʍ;", "ack", "Lokhttp3/internal/io/ʍ;", "getAck", "()Lokhttp3/internal/io/ʍ;", "Lokhttp3/internal/io/ڛ;", "callerContext", "Lokhttp3/internal/io/ڛ;", "getCallerContext", "()Lokhttp3/internal/io/ڛ;", RhinoJavaScriptEngine.SOURCE_NAME_INIT, "(Lokhttp3/internal/io/di0;Lokhttp3/internal/io/ʍ;Landroidx/datastore/core/State;Lokhttp3/internal/io/ڛ;)V", "datastore-core"}, m4116k = 1, m4117mv = {1, 5, 1})
        public static final class Update<T> extends Message<T> {

            @zu2
            private final InterfaceC6260<T> ack;

            @zu2
            private final InterfaceC6710 callerContext;

            @wv2
            private final State<T> lastState;

            @zu2
            private final di0<T, InterfaceC7155<? super T>, Object> transform;

            /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
            /* JADX WARN: Multi-variable type inference failed */
            public Update(@zu2 di0<? super T, ? super InterfaceC7155<? super T>, ? extends Object> di0Var, @zu2 InterfaceC6260<T> interfaceC6260, @wv2 State<T> state, @zu2 InterfaceC6710 interfaceC6710) {
                super(null);
                fa1.m6826(di0Var, "transform");
                fa1.m6826(interfaceC6260, "ack");
                fa1.m6826(interfaceC6710, "callerContext");
                this.transform = di0Var;
                this.ack = interfaceC6260;
                this.lastState = state;
                this.callerContext = interfaceC6710;
            }

            @zu2
            public final InterfaceC6260<T> getAck() {
                return this.ack;
            }

            @zu2
            public final InterfaceC6710 getCallerContext() {
                return this.callerContext;
            }

            @Override // androidx.datastore.core.SingleProcessDataStore.Message
            @wv2
            public State<T> getLastState() {
                return this.lastState;
            }

            @zu2
            public final di0<T, InterfaceC7155<? super T>, Object> getTransform() {
                return this.transform;
            }
        }

        private Message() {
        }

        public /* synthetic */ Message(C2849b5 c2849b5) {
            this();
        }

        @wv2
        public abstract State<T> getLastState();
    }

    @Metadata(m4113bv = {}, m4114d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0012\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0007\b\u0002\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\r\u001a\u00020\f¢\u0006\u0004\b\u0011\u0010\u0012J\u0010\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H\u0016J\u0010\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0006H\u0016J \u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\b\u001a\u00020\u00022\u0006\u0010\t\u001a\u00020\u0002H\u0016J\b\u0010\n\u001a\u00020\u0004H\u0016J\b\u0010\u000b\u001a\u00020\u0004H\u0016R\u0017\u0010\r\u001a\u00020\f8\u0006¢\u0006\f\n\u0004\b\r\u0010\u000e\u001a\u0004\b\u000f\u0010\u0010¨\u0006\u0013"}, m4115d2 = {"Landroidx/datastore/core/SingleProcessDataStore$UncloseableOutputStream;", "Ljava/io/OutputStream;", "", "b", "Lokhttp3/internal/io/lx5;", "write", "", "bytes", "off", "len", "close", "flush", "Ljava/io/FileOutputStream;", "fileOutputStream", "Ljava/io/FileOutputStream;", "getFileOutputStream", "()Ljava/io/FileOutputStream;", RhinoJavaScriptEngine.SOURCE_NAME_INIT, "(Ljava/io/FileOutputStream;)V", "datastore-core"}, m4116k = 1, m4117mv = {1, 5, 1})
    public static final class UncloseableOutputStream extends OutputStream {

        @zu2
        private final FileOutputStream fileOutputStream;

        public UncloseableOutputStream(@zu2 FileOutputStream fileOutputStream) {
            fa1.m6826(fileOutputStream, "fileOutputStream");
            this.fileOutputStream = fileOutputStream;
        }

        @Override // java.io.OutputStream, java.io.Closeable, java.lang.AutoCloseable
        public void close() {
        }

        @Override // java.io.OutputStream, java.io.Flushable
        public void flush() {
            this.fileOutputStream.flush();
        }

        @zu2
        public final FileOutputStream getFileOutputStream() {
            return this.fileOutputStream;
        }

        @Override // java.io.OutputStream
        public void write(int i) {
            this.fileOutputStream.write(i);
        }

        @Override // java.io.OutputStream
        public void write(@zu2 byte[] bArr) {
            fa1.m6826(bArr, "b");
            this.fileOutputStream.write(bArr);
        }

        @Override // java.io.OutputStream
        public void write(@zu2 byte[] bArr, int i, int i2) {
            fa1.m6826(bArr, "bytes");
            this.fileOutputStream.write(bArr, i, i2);
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    public SingleProcessDataStore(@zu2 nh0<? extends File> nh0Var, @zu2 Serializer<T> serializer, @zu2 List<? extends di0<? super InitializerApi<T>, ? super InterfaceC7155<? super lx5>, ? extends Object>> list, @zu2 CorruptionHandler<T> corruptionHandler, @zu2 InterfaceC7881 interfaceC7881) {
        fa1.m6826(nh0Var, "produceFile");
        fa1.m6826(serializer, "serializer");
        fa1.m6826(list, "initTasksList");
        fa1.m6826(corruptionHandler, "corruptionHandler");
        fa1.m6826(interfaceC7881, "scope");
        this.produceFile = nh0Var;
        this.serializer = serializer;
        this.corruptionHandler = corruptionHandler;
        this.scope = interfaceC7881;
        this.data = new w94(new SingleProcessDataStore$data$1(this, null));
        this.SCRATCH_SUFFIX = ".tmp";
        this.file = yg3.m14275(new SingleProcessDataStore$file$2(this));
        this.downstreamFlow = C6698.m15833(UnInitialized.INSTANCE);
        this.initTasks = C6802.m16030(list);
        this.actor = new SimpleActor<>(interfaceC7881, new SingleProcessDataStore$actor$1(this), SingleProcessDataStore$actor$2.INSTANCE, new SingleProcessDataStore$actor$3(this, null));
    }

    private final void createParentDirectories(File file) {
        File parentFile = file.getCanonicalFile().getParentFile();
        if (parentFile == null) {
            return;
        }
        parentFile.mkdirs();
        if (!parentFile.isDirectory()) {
            throw new IOException(fa1.m6846("Unable to create parent directories of ", file));
        }
    }

    private static /* synthetic */ void getDownstreamFlow$annotations() {
    }

    /* JADX INFO: Access modifiers changed from: private */
    public final File getFile() {
        return (File) this.file.getValue();
    }

    /* JADX INFO: Access modifiers changed from: private */
    public final Object handleRead(Message.Read<T> read, InterfaceC7155<? super lx5> interfaceC7155) {
        EnumC7329 enumC7329 = EnumC7329.COROUTINE_SUSPENDED;
        State<T> value = this.downstreamFlow.getValue();
        if (!(value instanceof Data)) {
            if (value instanceof ReadException) {
                if (value == read.getLastState()) {
                    Object readAndInitOrPropagateFailure = readAndInitOrPropagateFailure(interfaceC7155);
                    return readAndInitOrPropagateFailure == enumC7329 ? readAndInitOrPropagateFailure : lx5.f14876;
                }
            } else {
                if (fa1.m6818(value, UnInitialized.INSTANCE)) {
                    Object readAndInitOrPropagateFailure2 = readAndInitOrPropagateFailure(interfaceC7155);
                    return readAndInitOrPropagateFailure2 == enumC7329 ? readAndInitOrPropagateFailure2 : lx5.f14876;
                }
                if (value instanceof Final) {
                    throw new IllegalStateException("Can't read in final state.".toString());
                }
            }
        }
        return lx5.f14876;
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:19:0x00db  */
    /* JADX WARN: Removed duplicated region for block: B:22:0x00df  */
    /* JADX WARN: Removed duplicated region for block: B:35:0x00b4 A[RETURN] */
    /* JADX WARN: Removed duplicated region for block: B:48:0x0023 A[EXC_TOP_SPLITTER, SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:8:0x0054  */
    /* JADX WARN: Type inference failed for: r8v0, types: [androidx.datastore.core.SingleProcessDataStore, androidx.datastore.core.SingleProcessDataStore<T>, java.lang.Object] */
    /* JADX WARN: Type inference failed for: r9v14, types: [okhttp3.internal.io.ʍ] */
    /* JADX WARN: Type inference failed for: r9v17 */
    /* JADX WARN: Type inference failed for: r9v28 */
    /* JADX WARN: Type inference failed for: r9v29 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final java.lang.Object handleUpdate(androidx.datastore.core.SingleProcessDataStore.Message.Update<T> r9, okhttp3.internal.p042io.InterfaceC7155<? super okhttp3.internal.p042io.lx5> r10) {
        /*
            Method dump skipped, instructions count: 229
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.datastore.core.SingleProcessDataStore.handleUpdate(androidx.datastore.core.SingleProcessDataStore$Message$Update, okhttp3.internal.io.ৡ):java.lang.Object");
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* JADX WARN: Removed duplicated region for block: B:16:0x011b  */
    /* JADX WARN: Removed duplicated region for block: B:30:0x00d7  */
    /* JADX WARN: Removed duplicated region for block: B:39:0x0108 A[RETURN] */
    /* JADX WARN: Removed duplicated region for block: B:40:0x0109  */
    /* JADX WARN: Removed duplicated region for block: B:44:0x00cd  */
    /* JADX WARN: Removed duplicated region for block: B:45:0x007c  */
    /* JADX WARN: Removed duplicated region for block: B:8:0x0025  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final java.lang.Object readAndInit(okhttp3.internal.p042io.InterfaceC7155<? super okhttp3.internal.p042io.lx5> r14) {
        /*
            Method dump skipped, instructions count: 311
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.datastore.core.SingleProcessDataStore.readAndInit(okhttp3.internal.io.ৡ):java.lang.Object");
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* JADX WARN: Removed duplicated region for block: B:21:0x0035  */
    /* JADX WARN: Removed duplicated region for block: B:8:0x0021  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final java.lang.Object readAndInitOrPropagateAndThrowFailure(okhttp3.internal.p042io.InterfaceC7155<? super okhttp3.internal.p042io.lx5> r5) {
        /*
            r4 = this;
            boolean r0 = r5 instanceof androidx.datastore.core.SingleProcessDataStore$readAndInitOrPropagateAndThrowFailure$1
            if (r0 == 0) goto L13
            r0 = r5
            androidx.datastore.core.SingleProcessDataStore$readAndInitOrPropagateAndThrowFailure$1 r0 = (androidx.datastore.core.SingleProcessDataStore$readAndInitOrPropagateAndThrowFailure$1) r0
            int r1 = r0.label
            r2 = -2147483648(0xffffffff80000000, float:-0.0)
            r3 = r1 & r2
            if (r3 == 0) goto L13
            int r1 = r1 - r2
            r0.label = r1
            goto L18
        L13:
            androidx.datastore.core.SingleProcessDataStore$readAndInitOrPropagateAndThrowFailure$1 r0 = new androidx.datastore.core.SingleProcessDataStore$readAndInitOrPropagateAndThrowFailure$1
            r0.<init>(r4, r5)
        L18:
            java.lang.Object r5 = r0.result
            okhttp3.internal.io.ஐ r1 = okhttp3.internal.p042io.EnumC7329.COROUTINE_SUSPENDED
            int r2 = r0.label
            r3 = 1
            if (r2 == 0) goto L35
            if (r2 != r3) goto L2d
            java.lang.Object r0 = r0.L$0
            androidx.datastore.core.SingleProcessDataStore r0 = (androidx.datastore.core.SingleProcessDataStore) r0
            okhttp3.internal.p042io.C4350o9.m10270(r5)     // Catch: java.lang.Throwable -> L2b
            goto L43
        L2b:
            r5 = move-exception
            goto L48
        L2d:
            java.lang.IllegalStateException r5 = new java.lang.IllegalStateException
            java.lang.String r0 = "call to 'resume' before 'invoke' with coroutine"
            r5.<init>(r0)
            throw r5
        L35:
            okhttp3.internal.p042io.C4350o9.m10270(r5)
            r0.L$0 = r4     // Catch: java.lang.Throwable -> L46
            r0.label = r3     // Catch: java.lang.Throwable -> L46
            java.lang.Object r5 = r4.readAndInit(r0)     // Catch: java.lang.Throwable -> L46
            if (r5 != r1) goto L43
            return r1
        L43:
            okhttp3.internal.io.lx5 r5 = okhttp3.internal.p042io.lx5.f14876
            return r5
        L46:
            r5 = move-exception
            r0 = r4
        L48:
            okhttp3.internal.io.zn2<androidx.datastore.core.State<T>> r0 = r0.downstreamFlow
            androidx.datastore.core.ReadException r1 = new androidx.datastore.core.ReadException
            r1.<init>(r5)
            r0.setValue(r1)
            throw r5
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.datastore.core.SingleProcessDataStore.readAndInitOrPropagateAndThrowFailure(okhttp3.internal.io.ৡ):java.lang.Object");
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* JADX WARN: Removed duplicated region for block: B:20:0x0035  */
    /* JADX WARN: Removed duplicated region for block: B:8:0x0021  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final java.lang.Object readAndInitOrPropagateFailure(okhttp3.internal.p042io.InterfaceC7155<? super okhttp3.internal.p042io.lx5> r5) {
        /*
            r4 = this;
            boolean r0 = r5 instanceof androidx.datastore.core.SingleProcessDataStore$readAndInitOrPropagateFailure$1
            if (r0 == 0) goto L13
            r0 = r5
            androidx.datastore.core.SingleProcessDataStore$readAndInitOrPropagateFailure$1 r0 = (androidx.datastore.core.SingleProcessDataStore$readAndInitOrPropagateFailure$1) r0
            int r1 = r0.label
            r2 = -2147483648(0xffffffff80000000, float:-0.0)
            r3 = r1 & r2
            if (r3 == 0) goto L13
            int r1 = r1 - r2
            r0.label = r1
            goto L18
        L13:
            androidx.datastore.core.SingleProcessDataStore$readAndInitOrPropagateFailure$1 r0 = new androidx.datastore.core.SingleProcessDataStore$readAndInitOrPropagateFailure$1
            r0.<init>(r4, r5)
        L18:
            java.lang.Object r5 = r0.result
            okhttp3.internal.io.ஐ r1 = okhttp3.internal.p042io.EnumC7329.COROUTINE_SUSPENDED
            int r2 = r0.label
            r3 = 1
            if (r2 == 0) goto L35
            if (r2 != r3) goto L2d
            java.lang.Object r0 = r0.L$0
            androidx.datastore.core.SingleProcessDataStore r0 = (androidx.datastore.core.SingleProcessDataStore) r0
            okhttp3.internal.p042io.C4350o9.m10270(r5)     // Catch: java.lang.Throwable -> L2b
            goto L4f
        L2b:
            r5 = move-exception
            goto L45
        L2d:
            java.lang.IllegalStateException r5 = new java.lang.IllegalStateException
            java.lang.String r0 = "call to 'resume' before 'invoke' with coroutine"
            r5.<init>(r0)
            throw r5
        L35:
            okhttp3.internal.p042io.C4350o9.m10270(r5)
            r0.L$0 = r4     // Catch: java.lang.Throwable -> L43
            r0.label = r3     // Catch: java.lang.Throwable -> L43
            java.lang.Object r5 = r4.readAndInit(r0)     // Catch: java.lang.Throwable -> L43
            if (r5 != r1) goto L4f
            return r1
        L43:
            r5 = move-exception
            r0 = r4
        L45:
            okhttp3.internal.io.zn2<androidx.datastore.core.State<T>> r0 = r0.downstreamFlow
            androidx.datastore.core.ReadException r1 = new androidx.datastore.core.ReadException
            r1.<init>(r5)
            r0.setValue(r1)
        L4f:
            okhttp3.internal.io.lx5 r5 = okhttp3.internal.p042io.lx5.f14876
            return r5
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.datastore.core.SingleProcessDataStore.readAndInitOrPropagateFailure(okhttp3.internal.io.ৡ):java.lang.Object");
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:26:0x003d  */
    /* JADX WARN: Removed duplicated region for block: B:9:0x0021  */
    /* JADX WARN: Type inference failed for: r0v12, types: [androidx.datastore.core.SingleProcessDataStore] */
    /* JADX WARN: Type inference failed for: r0v15 */
    /* JADX WARN: Type inference failed for: r0v16 */
    /* JADX WARN: Type inference failed for: r0v2, types: [androidx.datastore.core.SingleProcessDataStore$readData$1, okhttp3.internal.io.ৡ] */
    /* JADX WARN: Type inference failed for: r0v3 */
    /* JADX WARN: Type inference failed for: r0v4, types: [androidx.datastore.core.SingleProcessDataStore] */
    /* JADX WARN: Type inference failed for: r0v5 */
    /* JADX WARN: Type inference failed for: r0v6 */
    /* JADX WARN: Type inference failed for: r0v9 */
    /* JADX WARN: Type inference failed for: r2v1, types: [java.io.FileInputStream, java.io.InputStream, java.lang.Object] */
    /* JADX WARN: Type inference failed for: r2v2, types: [java.io.Closeable] */
    /* JADX WARN: Type inference failed for: r2v5, types: [java.io.Closeable] */
    /* JADX WARN: Type inference failed for: r4v0, types: [androidx.datastore.core.Serializer, androidx.datastore.core.Serializer<T>] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final java.lang.Object readData(okhttp3.internal.p042io.InterfaceC7155<? super T> r6) {
        /*
            r5 = this;
            boolean r0 = r6 instanceof androidx.datastore.core.SingleProcessDataStore$readData$1
            if (r0 == 0) goto L13
            r0 = r6
            androidx.datastore.core.SingleProcessDataStore$readData$1 r0 = (androidx.datastore.core.SingleProcessDataStore$readData$1) r0
            int r1 = r0.label
            r2 = -2147483648(0xffffffff80000000, float:-0.0)
            r3 = r1 & r2
            if (r3 == 0) goto L13
            int r1 = r1 - r2
            r0.label = r1
            goto L18
        L13:
            androidx.datastore.core.SingleProcessDataStore$readData$1 r0 = new androidx.datastore.core.SingleProcessDataStore$readData$1
            r0.<init>(r5, r6)
        L18:
            java.lang.Object r6 = r0.result
            okhttp3.internal.io.ஐ r1 = okhttp3.internal.p042io.EnumC7329.COROUTINE_SUSPENDED
            int r2 = r0.label
            r3 = 1
            if (r2 == 0) goto L3d
            if (r2 != r3) goto L35
            java.lang.Object r1 = r0.L$2
            java.lang.Throwable r1 = (java.lang.Throwable) r1
            java.lang.Object r2 = r0.L$1
            java.io.Closeable r2 = (java.io.Closeable) r2
            java.lang.Object r0 = r0.L$0
            androidx.datastore.core.SingleProcessDataStore r0 = (androidx.datastore.core.SingleProcessDataStore) r0
            okhttp3.internal.p042io.C4350o9.m10270(r6)     // Catch: java.lang.Throwable -> L33
            goto L5e
        L33:
            r6 = move-exception
            goto L66
        L35:
            java.lang.IllegalStateException r6 = new java.lang.IllegalStateException
            java.lang.String r0 = "call to 'resume' before 'invoke' with coroutine"
            r6.<init>(r0)
            throw r6
        L3d:
            okhttp3.internal.p042io.C4350o9.m10270(r6)
            java.io.FileInputStream r2 = new java.io.FileInputStream     // Catch: java.io.FileNotFoundException -> L6c
            java.io.File r6 = r5.getFile()     // Catch: java.io.FileNotFoundException -> L6c
            r2.<init>(r6)     // Catch: java.io.FileNotFoundException -> L6c
            r6 = 0
            androidx.datastore.core.Serializer<T> r4 = r5.serializer     // Catch: java.lang.Throwable -> L64
            r0.L$0 = r5     // Catch: java.lang.Throwable -> L64
            r0.L$1 = r2     // Catch: java.lang.Throwable -> L64
            r0.L$2 = r6     // Catch: java.lang.Throwable -> L64
            r0.label = r3     // Catch: java.lang.Throwable -> L64
            java.lang.Object r0 = r4.readFrom(r2, r0)     // Catch: java.lang.Throwable -> L64
            if (r0 != r1) goto L5b
            return r1
        L5b:
            r1 = r6
            r6 = r0
            r0 = r5
        L5e:
            okhttp3.internal.p042io.C7208.m16733(r2, r1)     // Catch: java.io.FileNotFoundException -> L62
            return r6
        L62:
            r6 = move-exception
            goto L6e
        L64:
            r6 = move-exception
            r0 = r5
        L66:
            throw r6     // Catch: java.lang.Throwable -> L67
        L67:
            r1 = move-exception
            okhttp3.internal.p042io.C7208.m16733(r2, r6)     // Catch: java.io.FileNotFoundException -> L62
            throw r1     // Catch: java.io.FileNotFoundException -> L62
        L6c:
            r6 = move-exception
            r0 = r5
        L6e:
            java.io.File r1 = r0.getFile()
            boolean r1 = r1.exists()
            if (r1 != 0) goto L7f
            androidx.datastore.core.Serializer<T> r6 = r0.serializer
            java.lang.Object r6 = r6.getDefaultValue()
            return r6
        L7f:
            throw r6
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.datastore.core.SingleProcessDataStore.readData(okhttp3.internal.io.ৡ):java.lang.Object");
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* JADX WARN: Removed duplicated region for block: B:26:0x0083 A[RETURN] */
    /* JADX WARN: Removed duplicated region for block: B:27:0x0084  */
    /* JADX WARN: Removed duplicated region for block: B:40:0x0072 A[RETURN] */
    /* JADX WARN: Removed duplicated region for block: B:41:0x0073  */
    /* JADX WARN: Removed duplicated region for block: B:42:0x0053  */
    /* JADX WARN: Removed duplicated region for block: B:8:0x0023  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final java.lang.Object readDataOrHandleCorruption(okhttp3.internal.p042io.InterfaceC7155<? super T> r8) {
        /*
            r7 = this;
            boolean r0 = r8 instanceof androidx.datastore.core.SingleProcessDataStore$readDataOrHandleCorruption$1
            if (r0 == 0) goto L13
            r0 = r8
            androidx.datastore.core.SingleProcessDataStore$readDataOrHandleCorruption$1 r0 = (androidx.datastore.core.SingleProcessDataStore$readDataOrHandleCorruption$1) r0
            int r1 = r0.label
            r2 = -2147483648(0xffffffff80000000, float:-0.0)
            r3 = r1 & r2
            if (r3 == 0) goto L13
            int r1 = r1 - r2
            r0.label = r1
            goto L18
        L13:
            androidx.datastore.core.SingleProcessDataStore$readDataOrHandleCorruption$1 r0 = new androidx.datastore.core.SingleProcessDataStore$readDataOrHandleCorruption$1
            r0.<init>(r7, r8)
        L18:
            java.lang.Object r8 = r0.result
            okhttp3.internal.io.ஐ r1 = okhttp3.internal.p042io.EnumC7329.COROUTINE_SUSPENDED
            int r2 = r0.label
            r3 = 3
            r4 = 2
            r5 = 1
            if (r2 == 0) goto L53
            if (r2 == r5) goto L49
            if (r2 == r4) goto L3d
            if (r2 != r3) goto L35
            java.lang.Object r1 = r0.L$1
            java.lang.Object r0 = r0.L$0
            androidx.datastore.core.CorruptionException r0 = (androidx.datastore.core.CorruptionException) r0
            okhttp3.internal.p042io.C4350o9.m10270(r8)     // Catch: java.io.IOException -> L33
            goto L85
        L33:
            r8 = move-exception
            goto L88
        L35:
            java.lang.IllegalStateException r8 = new java.lang.IllegalStateException
            java.lang.String r0 = "call to 'resume' before 'invoke' with coroutine"
            r8.<init>(r0)
            throw r8
        L3d:
            java.lang.Object r2 = r0.L$1
            androidx.datastore.core.CorruptionException r2 = (androidx.datastore.core.CorruptionException) r2
            java.lang.Object r4 = r0.L$0
            androidx.datastore.core.SingleProcessDataStore r4 = (androidx.datastore.core.SingleProcessDataStore) r4
            okhttp3.internal.p042io.C4350o9.m10270(r8)
            goto L77
        L49:
            java.lang.Object r2 = r0.L$0
            androidx.datastore.core.SingleProcessDataStore r2 = (androidx.datastore.core.SingleProcessDataStore) r2
            okhttp3.internal.p042io.C4350o9.m10270(r8)     // Catch: androidx.datastore.core.CorruptionException -> L51
            goto L61
        L51:
            r8 = move-exception
            goto L64
        L53:
            okhttp3.internal.p042io.C4350o9.m10270(r8)
            r0.L$0 = r7     // Catch: androidx.datastore.core.CorruptionException -> L62
            r0.label = r5     // Catch: androidx.datastore.core.CorruptionException -> L62
            java.lang.Object r8 = r7.readData(r0)     // Catch: androidx.datastore.core.CorruptionException -> L62
            if (r8 != r1) goto L61
            return r1
        L61:
            return r8
        L62:
            r8 = move-exception
            r2 = r7
        L64:
            androidx.datastore.core.CorruptionHandler<T> r5 = r2.corruptionHandler
            r0.L$0 = r2
            r0.L$1 = r8
            r0.label = r4
            java.lang.Object r4 = r5.handleCorruption(r8, r0)
            if (r4 != r1) goto L73
            return r1
        L73:
            r6 = r2
            r2 = r8
            r8 = r4
            r4 = r6
        L77:
            r0.L$0 = r2     // Catch: java.io.IOException -> L86
            r0.L$1 = r8     // Catch: java.io.IOException -> L86
            r0.label = r3     // Catch: java.io.IOException -> L86
            java.lang.Object r0 = r4.writeData$datastore_core(r8, r0)     // Catch: java.io.IOException -> L86
            if (r0 != r1) goto L84
            return r1
        L84:
            r1 = r8
        L85:
            return r1
        L86:
            r8 = move-exception
            r0 = r2
        L88:
            okhttp3.internal.p042io.ly3.m9475(r0, r8)
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.datastore.core.SingleProcessDataStore.readDataOrHandleCorruption(okhttp3.internal.io.ৡ):java.lang.Object");
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* JADX WARN: Removed duplicated region for block: B:13:0x0092  */
    /* JADX WARN: Removed duplicated region for block: B:17:0x0097  */
    /* JADX WARN: Removed duplicated region for block: B:23:0x007b  */
    /* JADX WARN: Removed duplicated region for block: B:27:0x0047  */
    /* JADX WARN: Removed duplicated region for block: B:8:0x0023  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final java.lang.Object transformAndWrite(okhttp3.internal.p042io.di0<? super T, ? super okhttp3.internal.p042io.InterfaceC7155<? super T>, ? extends java.lang.Object> r8, okhttp3.internal.p042io.InterfaceC6710 r9, okhttp3.internal.p042io.InterfaceC7155<? super T> r10) {
        /*
            r7 = this;
            boolean r0 = r10 instanceof androidx.datastore.core.SingleProcessDataStore$transformAndWrite$1
            if (r0 == 0) goto L13
            r0 = r10
            androidx.datastore.core.SingleProcessDataStore$transformAndWrite$1 r0 = (androidx.datastore.core.SingleProcessDataStore$transformAndWrite$1) r0
            int r1 = r0.label
            r2 = -2147483648(0xffffffff80000000, float:-0.0)
            r3 = r1 & r2
            if (r3 == 0) goto L13
            int r1 = r1 - r2
            r0.label = r1
            goto L18
        L13:
            androidx.datastore.core.SingleProcessDataStore$transformAndWrite$1 r0 = new androidx.datastore.core.SingleProcessDataStore$transformAndWrite$1
            r0.<init>(r7, r10)
        L18:
            java.lang.Object r10 = r0.result
            okhttp3.internal.io.ஐ r1 = okhttp3.internal.p042io.EnumC7329.COROUTINE_SUSPENDED
            int r2 = r0.label
            r3 = 0
            r4 = 2
            r5 = 1
            if (r2 == 0) goto L47
            if (r2 == r5) goto L39
            if (r2 != r4) goto L31
            java.lang.Object r8 = r0.L$1
            java.lang.Object r9 = r0.L$0
            androidx.datastore.core.SingleProcessDataStore r9 = (androidx.datastore.core.SingleProcessDataStore) r9
            okhttp3.internal.p042io.C4350o9.m10270(r10)
            goto L8c
        L31:
            java.lang.IllegalStateException r8 = new java.lang.IllegalStateException
            java.lang.String r9 = "call to 'resume' before 'invoke' with coroutine"
            r8.<init>(r9)
            throw r8
        L39:
            java.lang.Object r8 = r0.L$2
            java.lang.Object r9 = r0.L$1
            androidx.datastore.core.Data r9 = (androidx.datastore.core.Data) r9
            java.lang.Object r2 = r0.L$0
            androidx.datastore.core.SingleProcessDataStore r2 = (androidx.datastore.core.SingleProcessDataStore) r2
            okhttp3.internal.p042io.C4350o9.m10270(r10)
            goto L71
        L47:
            okhttp3.internal.p042io.C4350o9.m10270(r10)
            okhttp3.internal.io.zn2<androidx.datastore.core.State<T>> r10 = r7.downstreamFlow
            java.lang.Object r10 = r10.getValue()
            androidx.datastore.core.Data r10 = (androidx.datastore.core.Data) r10
            r10.checkHashCode()
            java.lang.Object r2 = r10.getValue()
            androidx.datastore.core.SingleProcessDataStore$transformAndWrite$newData$1 r6 = new androidx.datastore.core.SingleProcessDataStore$transformAndWrite$newData$1
            r6.<init>(r8, r2, r3)
            r0.L$0 = r7
            r0.L$1 = r10
            r0.L$2 = r2
            r0.label = r5
            java.lang.Object r8 = okhttp3.internal.p042io.C6814.m16052(r9, r6, r0)
            if (r8 != r1) goto L6d
            return r1
        L6d:
            r9 = r10
            r10 = r8
            r8 = r2
            r2 = r7
        L71:
            r9.checkHashCode()
            boolean r9 = okhttp3.internal.p042io.fa1.m6818(r8, r10)
            if (r9 == 0) goto L7b
            goto L9e
        L7b:
            r0.L$0 = r2
            r0.L$1 = r10
            r0.L$2 = r3
            r0.label = r4
            java.lang.Object r8 = r2.writeData$datastore_core(r10, r0)
            if (r8 != r1) goto L8a
            return r1
        L8a:
            r8 = r10
            r9 = r2
        L8c:
            okhttp3.internal.io.zn2<androidx.datastore.core.State<T>> r9 = r9.downstreamFlow
            androidx.datastore.core.Data r10 = new androidx.datastore.core.Data
            if (r8 == 0) goto L97
            int r0 = r8.hashCode()
            goto L98
        L97:
            r0 = 0
        L98:
            r10.<init>(r8, r0)
            r9.setValue(r10)
        L9e:
            return r8
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.datastore.core.SingleProcessDataStore.transformAndWrite(okhttp3.internal.io.di0, okhttp3.internal.io.ڛ, okhttp3.internal.io.ৡ):java.lang.Object");
    }

    @Override // androidx.datastore.core.DataStore
    @zu2
    public q80<T> getData() {
        return this.data;
    }

    @Override // androidx.datastore.core.DataStore
    @wv2
    public Object updateData(@zu2 di0<? super T, ? super InterfaceC7155<? super T>, ? extends Object> di0Var, @zu2 InterfaceC7155<? super T> interfaceC7155) {
        InterfaceC6260 m14839 = C6090.m14839();
        this.actor.offer(new Message.Update(di0Var, m14839, this.downstreamFlow.getValue(), interfaceC7155.getContext()));
        return ((C6717) m14839).m6047(interfaceC7155);
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:16:0x009d A[RETURN] */
    /* JADX WARN: Removed duplicated region for block: B:18:0x009e A[Catch: IOException -> 0x00c2, TryCatch #1 {IOException -> 0x00c2, blocks: (B:14:0x0090, B:18:0x009e, B:19:0x00b9, B:26:0x00be, B:27:0x00c1, B:23:0x00bc), top: B:7:0x001f, inners: #4 }] */
    /* JADX WARN: Removed duplicated region for block: B:30:0x0046  */
    /* JADX WARN: Removed duplicated region for block: B:9:0x0021  */
    /* JADX WARN: Type inference failed for: r2v10 */
    /* JADX WARN: Type inference failed for: r2v11 */
    /* JADX WARN: Type inference failed for: r2v4, types: [java.io.FileOutputStream, java.lang.Object] */
    /* JADX WARN: Type inference failed for: r2v5, types: [java.io.Closeable] */
    /* JADX WARN: Type inference failed for: r2v6 */
    /* JADX WARN: Type inference failed for: r2v7, types: [java.io.Closeable] */
    /* JADX WARN: Type inference failed for: r2v9, types: [java.io.Closeable] */
    /* JADX WARN: Type inference failed for: r3v0 */
    /* JADX WARN: Type inference failed for: r3v1 */
    /* JADX WARN: Type inference failed for: r3v10 */
    /* JADX WARN: Type inference failed for: r3v2 */
    /* JADX WARN: Type inference failed for: r3v3 */
    /* JADX WARN: Type inference failed for: r3v4 */
    /* JADX WARN: Type inference failed for: r3v5 */
    /* JADX WARN: Type inference failed for: r3v6, types: [java.io.File, java.lang.Object] */
    /* JADX WARN: Type inference failed for: r3v8, types: [java.io.File] */
    /* JADX WARN: Type inference failed for: r9v9, types: [java.lang.StringBuilder] */
    @okhttp3.internal.p042io.wv2
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final java.lang.Object writeData$datastore_core(T r8, @okhttp3.internal.p042io.zu2 okhttp3.internal.p042io.InterfaceC7155<? super okhttp3.internal.p042io.lx5> r9) {
        /*
            r7 = this;
            boolean r0 = r9 instanceof androidx.datastore.core.SingleProcessDataStore$writeData$1
            if (r0 == 0) goto L13
            r0 = r9
            androidx.datastore.core.SingleProcessDataStore$writeData$1 r0 = (androidx.datastore.core.SingleProcessDataStore$writeData$1) r0
            int r1 = r0.label
            r2 = -2147483648(0xffffffff80000000, float:-0.0)
            r3 = r1 & r2
            if (r3 == 0) goto L13
            int r1 = r1 - r2
            r0.label = r1
            goto L18
        L13:
            androidx.datastore.core.SingleProcessDataStore$writeData$1 r0 = new androidx.datastore.core.SingleProcessDataStore$writeData$1
            r0.<init>(r7, r9)
        L18:
            java.lang.Object r9 = r0.result
            okhttp3.internal.io.ஐ r1 = okhttp3.internal.p042io.EnumC7329.COROUTINE_SUSPENDED
            int r2 = r0.label
            r3 = 1
            if (r2 == 0) goto L46
            if (r2 != r3) goto L3e
            java.lang.Object r8 = r0.L$4
            java.io.FileOutputStream r8 = (java.io.FileOutputStream) r8
            java.lang.Object r1 = r0.L$3
            java.lang.Throwable r1 = (java.lang.Throwable) r1
            java.lang.Object r2 = r0.L$2
            java.io.Closeable r2 = (java.io.Closeable) r2
            java.lang.Object r3 = r0.L$1
            java.io.File r3 = (java.io.File) r3
            java.lang.Object r0 = r0.L$0
            androidx.datastore.core.SingleProcessDataStore r0 = (androidx.datastore.core.SingleProcessDataStore) r0
            okhttp3.internal.p042io.C4350o9.m10270(r9)     // Catch: java.lang.Throwable -> L3b
            goto L87
        L3b:
            r8 = move-exception
            goto Lbc
        L3e:
            java.lang.IllegalStateException r8 = new java.lang.IllegalStateException
            java.lang.String r9 = "call to 'resume' before 'invoke' with coroutine"
            r8.<init>(r9)
            throw r8
        L46:
            okhttp3.internal.p042io.C4350o9.m10270(r9)
            java.io.File r9 = r7.getFile()
            r7.createParentDirectories(r9)
            java.io.File r9 = new java.io.File
            java.io.File r2 = r7.getFile()
            java.lang.String r2 = r2.getAbsolutePath()
            java.lang.String r4 = r7.SCRATCH_SUFFIX
            java.lang.String r2 = okhttp3.internal.p042io.fa1.m6846(r2, r4)
            r9.<init>(r2)
            java.io.FileOutputStream r2 = new java.io.FileOutputStream     // Catch: java.io.IOException -> Lc5
            r2.<init>(r9)     // Catch: java.io.IOException -> Lc5
            r4 = 0
            androidx.datastore.core.Serializer<T> r5 = r7.serializer     // Catch: java.lang.Throwable -> Lba
            androidx.datastore.core.SingleProcessDataStore$UncloseableOutputStream r6 = new androidx.datastore.core.SingleProcessDataStore$UncloseableOutputStream     // Catch: java.lang.Throwable -> Lba
            r6.<init>(r2)     // Catch: java.lang.Throwable -> Lba
            r0.L$0 = r7     // Catch: java.lang.Throwable -> Lba
            r0.L$1 = r9     // Catch: java.lang.Throwable -> Lba
            r0.L$2 = r2     // Catch: java.lang.Throwable -> Lba
            r0.L$3 = r4     // Catch: java.lang.Throwable -> Lba
            r0.L$4 = r2     // Catch: java.lang.Throwable -> Lba
            r0.label = r3     // Catch: java.lang.Throwable -> Lba
            java.lang.Object r8 = r5.writeTo(r8, r6, r0)     // Catch: java.lang.Throwable -> Lba
            if (r8 != r1) goto L83
            return r1
        L83:
            r0 = r7
            r3 = r9
            r8 = r2
            r1 = r4
        L87:
            java.io.FileDescriptor r8 = r8.getFD()     // Catch: java.lang.Throwable -> L3b
            r8.sync()     // Catch: java.lang.Throwable -> L3b
            okhttp3.internal.io.lx5 r8 = okhttp3.internal.p042io.lx5.f14876     // Catch: java.lang.Throwable -> L3b
            okhttp3.internal.p042io.C7208.m16733(r2, r1)     // Catch: java.io.IOException -> Lc2
            java.io.File r9 = r0.getFile()     // Catch: java.io.IOException -> Lc2
            boolean r9 = r3.renameTo(r9)     // Catch: java.io.IOException -> Lc2
            if (r9 == 0) goto L9e
            return r8
        L9e:
            java.io.IOException r8 = new java.io.IOException     // Catch: java.io.IOException -> Lc2
            java.lang.StringBuilder r9 = new java.lang.StringBuilder     // Catch: java.io.IOException -> Lc2
            r9.<init>()     // Catch: java.io.IOException -> Lc2
            java.lang.String r0 = "Unable to rename "
            r9.append(r0)     // Catch: java.io.IOException -> Lc2
            r9.append(r3)     // Catch: java.io.IOException -> Lc2
            java.lang.String r0 = ".This likely means that there are multiple instances of DataStore for this file. Ensure that you are only creating a single instance of datastore for this file."
            r9.append(r0)     // Catch: java.io.IOException -> Lc2
            java.lang.String r9 = r9.toString()     // Catch: java.io.IOException -> Lc2
            r8.<init>(r9)     // Catch: java.io.IOException -> Lc2
            throw r8     // Catch: java.io.IOException -> Lc2
        Lba:
            r8 = move-exception
            r3 = r9
        Lbc:
            throw r8     // Catch: java.lang.Throwable -> Lbd
        Lbd:
            r9 = move-exception
            okhttp3.internal.p042io.C7208.m16733(r2, r8)     // Catch: java.io.IOException -> Lc2
            throw r9     // Catch: java.io.IOException -> Lc2
        Lc2:
            r8 = move-exception
            r9 = r3
            goto Lc6
        Lc5:
            r8 = move-exception
        Lc6:
            boolean r0 = r9.exists()
            if (r0 == 0) goto Lcf
            r9.delete()
        Lcf:
            throw r8
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.datastore.core.SingleProcessDataStore.writeData$datastore_core(java.lang.Object, okhttp3.internal.io.ৡ):java.lang.Object");
    }

    /* JADX WARN: Illegal instructions before constructor call */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public SingleProcessDataStore(okhttp3.internal.p042io.nh0 r7, androidx.datastore.core.Serializer r8, java.util.List r9, androidx.datastore.core.CorruptionHandler r10, okhttp3.internal.p042io.InterfaceC7881 r11, int r12, okhttp3.internal.p042io.C2849b5 r13) {
        /*
            r6 = this;
            r13 = r12 & 4
            if (r13 == 0) goto L6
            okhttp3.internal.io.xq r9 = okhttp3.internal.p042io.C5737xq.f26149
        L6:
            r3 = r9
            r9 = r12 & 8
            if (r9 == 0) goto L10
            androidx.datastore.core.handlers.NoOpCorruptionHandler r10 = new androidx.datastore.core.handlers.NoOpCorruptionHandler
            r10.<init>()
        L10:
            r4 = r10
            r9 = r12 & 16
            if (r9 == 0) goto L25
            okhttp3.internal.io.hh r9 = okhttp3.internal.p042io.C3580hh.f11573
            okhttp3.internal.io.d6 r9 = okhttp3.internal.p042io.C3580hh.f11575
            okhttp3.internal.io.ܩ r10 = okhttp3.internal.p042io.a75.m4467()
            okhttp3.internal.io.ڛ r9 = r9.plus(r10)
            okhttp3.internal.io.ღ r11 = okhttp3.internal.p042io.C6422.m15327(r9)
        L25:
            r5 = r11
            r0 = r6
            r1 = r7
            r2 = r8
            r0.<init>(r1, r2, r3, r4, r5)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.datastore.core.SingleProcessDataStore.<init>(okhttp3.internal.io.nh0, androidx.datastore.core.Serializer, java.util.List, androidx.datastore.core.CorruptionHandler, okhttp3.internal.io.ღ, int, okhttp3.internal.io.b5):void");
    }
}
