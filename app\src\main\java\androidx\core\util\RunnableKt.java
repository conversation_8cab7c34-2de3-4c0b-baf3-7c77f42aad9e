package androidx.core.util;

import kotlin.Metadata;
import okhttp3.internal.p042io.InterfaceC7155;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.zu2;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a\u0010\u0010\u0003\u001a\u00020\u0002*\b\u0012\u0004\u0012\u00020\u00010\u0000¨\u0006\u0004"}, m4115d2 = {"Lokhttp3/internal/io/ৡ;", "Lokhttp3/internal/io/lx5;", "Ljava/lang/Runnable;", "asRunnable", "core-ktx_release"}, m4116k = 2, m4117mv = {1, 6, 0})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class RunnableKt {
    @zu2
    public static final Runnable asRunnable(@zu2 InterfaceC7155<? super lx5> interfaceC7155) {
        fa1.m6826(interfaceC7155, "<this>");
        return new ContinuationRunnable(interfaceC7155);
    }
}
