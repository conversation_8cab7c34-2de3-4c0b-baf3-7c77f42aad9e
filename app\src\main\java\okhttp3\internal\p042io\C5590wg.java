package okhttp3.internal.p042io;

import java.util.ArrayDeque;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Queue;
import java.util.concurrent.locks.ReentrantLock;

/* renamed from: okhttp3.internal.io.wg */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes3.dex */
public final class C5590wg {

    /* renamed from: Ϳ */
    public final Map<String, C9470> f24958 = new HashMap();

    /* renamed from: Ԩ */
    public final C9471 f24959 = new C9471();

    /* renamed from: okhttp3.internal.io.wg$Ϳ, reason: contains not printable characters */
    public static class C9470 {

        /* renamed from: Ϳ */
        public final ReentrantLock f24960 = new ReentrantLock();

        /* renamed from: Ԩ */
        public int f24961;
    }

    /* renamed from: okhttp3.internal.io.wg$Ԩ, reason: contains not printable characters */
    public static class C9471 {

        /* renamed from: Ϳ */
        public final Queue<C9470> f24962 = new ArrayDeque();
    }

    /* JADX WARN: Type inference failed for: r0v0, types: [java.util.HashMap, java.util.Map<java.lang.String, okhttp3.internal.io.wg$Ϳ>] */
    /* JADX WARN: Type inference failed for: r1v4, types: [java.util.HashMap, java.util.Map<java.lang.String, okhttp3.internal.io.wg$Ϳ>] */
    /* JADX WARN: Type inference failed for: r3v2, types: [java.util.ArrayDeque, java.util.Queue<okhttp3.internal.io.wg$Ϳ>] */
    /* JADX WARN: Type inference failed for: r6v9, types: [java.util.ArrayDeque, java.util.Queue<okhttp3.internal.io.wg$Ϳ>] */
    /* renamed from: Ϳ */
    public final void m13572(String str) {
        C9470 c9470;
        synchronized (this) {
            Object obj = this.f24958.get(str);
            Objects.requireNonNull(obj, "Argument must not be null");
            c9470 = (C9470) obj;
            int i = c9470.f24961;
            if (i < 1) {
                throw new IllegalStateException("Cannot release a lock that is not held, safeKey: " + str + ", interestedThreads: " + c9470.f24961);
            }
            int i2 = i - 1;
            c9470.f24961 = i2;
            if (i2 == 0) {
                C9470 c94702 = (C9470) this.f24958.remove(str);
                if (!c94702.equals(c9470)) {
                    throw new IllegalStateException("Removed the wrong lock, expected to remove: " + c9470 + ", but actually removed: " + c94702 + ", safeKey: " + str);
                }
                C9471 c9471 = this.f24959;
                synchronized (c9471.f24962) {
                    if (c9471.f24962.size() < 10) {
                        c9471.f24962.offer(c94702);
                    }
                }
            }
        }
        c9470.f24960.unlock();
    }
}
