#!/usr/bin/env python3
"""
AutoJS9服务器测试客户端
"""

import asyncio
import json
import websockets
import uuid
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AutoJSClient:
    """AutoJS客户端"""
    
    def __init__(self, server_url: str = "ws://localhost:9317"):
        self.server_url = server_url
        self.websocket = None
        self.token = None
        self.device_id = str(uuid.uuid4())
        self.request_id = 0
    
    async def connect(self):
        """连接到服务器"""
        logger.info(f"连接到服务器: {self.server_url}")
        self.websocket = await websockets.connect(self.server_url)
        logger.info("连接成功")
    
    async def disconnect(self):
        """断开连接"""
        if self.websocket:
            await self.websocket.close()
            logger.info("连接已断开")
    
    def get_next_id(self) -> int:
        """获取下一个请求ID"""
        self.request_id += 1
        return self.request_id
    
    async def send_rpc(self, method: str, params: dict = None) -> dict:
        """发送RPC请求"""
        if not self.websocket:
            raise Exception("未连接到服务器")
        
        request = {
            "jsonrpc": "2.0",
            "method": method,
            "params": params or {},
            "id": self.get_next_id()
        }
        
        logger.info(f"发送RPC请求: {method}")
        await self.websocket.send(json.dumps(request))
        
        # 等待响应
        response_str = await self.websocket.recv()
        response = json.loads(response_str)
        
        if "error" in response:
            logger.error(f"RPC错误: {response['error']}")
            raise Exception(f"RPC错误: {response['error']['message']}")
        
        return response.get("result", {})
    
    async def authorize(self, device_name: str = "Test Device") -> bool:
        """请求授权"""
        logger.info("请求设备授权...")
        
        result = await self.send_rpc("debug.authorize", {
            "device_id": self.device_id,
            "device_name": device_name
        })
        
        if result.get("authorized"):
            self.token = result.get("token")
            logger.info(f"授权成功，获得token: {self.token[:8]}...")
            return True
        else:
            logger.error("授权失败")
            return False
    
    async def ping(self) -> dict:
        """发送ping请求"""
        if not self.token:
            raise Exception("未授权，请先调用authorize()")
        
        return await self.send_rpc("ping", {"token": self.token})
    
    async def get_device_info(self) -> dict:
        """获取设备信息"""
        if not self.token:
            raise Exception("未授权，请先调用authorize()")
        
        return await self.send_rpc("getDeviceInfo", {"token": self.token})

async def test_client():
    """测试客户端功能"""
    client = AutoJSClient()
    
    try:
        # 连接到服务器
        await client.connect()
        
        # 请求授权
        if await client.authorize("Python测试客户端"):
            print("✓ 授权成功")
            
            # 测试ping
            ping_result = await client.ping()
            print(f"✓ Ping响应: {ping_result}")
            
            # 测试获取设备信息
            device_info = await client.get_device_info()
            print(f"✓ 设备信息: {device_info}")
            
        else:
            print("✗ 授权失败")
    
    except Exception as e:
        logger.error(f"测试失败: {e}")
    
    finally:
        await client.disconnect()

async def test_unauthorized_access():
    """测试未授权访问"""
    client = AutoJSClient()
    
    try:
        await client.connect()
        
        # 尝试不授权直接调用API
        try:
            await client.send_rpc("ping", {"token": "invalid_token"})
            print("✗ 未授权访问应该失败")
        except Exception as e:
            print(f"✓ 未授权访问被正确拒绝: {e}")
    
    except Exception as e:
        logger.error(f"测试失败: {e}")
    
    finally:
        await client.disconnect()

if __name__ == "__main__":
    print("=== AutoJS9服务器测试 ===\n")
    
    print("1. 测试正常授权流程:")
    asyncio.run(test_client())
    
    print("\n2. 测试未授权访问:")
    asyncio.run(test_unauthorized_access())
