package androidx.core.util;

import android.annotation.SuppressLint;
import android.util.Size;
import android.util.SizeF;
import androidx.annotation.RequiresApi;
import kotlin.Metadata;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.zu2;

@Metadata(m4114d1 = {"\u0000\u001a\n\u0000\n\u0002\u0010\b\n\u0002\u0018\u0002\n\u0002\u0010\u0007\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a\r\u0010\u0000\u001a\u00020\u0001*\u00020\u0002H\u0087\n\u001a\r\u0010\u0000\u001a\u00020\u0003*\u00020\u0004H\u0087\n\u001a\r\u0010\u0000\u001a\u00020\u0003*\u00020\u0005H\u0086\n\u001a\r\u0010\u0006\u001a\u00020\u0001*\u00020\u0002H\u0087\n\u001a\r\u0010\u0006\u001a\u00020\u0003*\u00020\u0004H\u0087\n\u001a\r\u0010\u0006\u001a\u00020\u0003*\u00020\u0005H\u0086\n¨\u0006\u0007"}, m4115d2 = {"component1", "", "Landroid/util/Size;", "", "Landroid/util/SizeF;", "Landroidx/core/util/SizeFCompat;", "component2", "core-ktx_release"}, m4116k = 2, m4117mv = {1, 6, 0}, m4119xi = 48)
@SuppressLint({"ClassVerificationFailure"})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class SizeKt {
    @RequiresApi(21)
    public static final float component1(@zu2 SizeF sizeF) {
        fa1.m6826(sizeF, "<this>");
        return sizeF.getWidth();
    }

    public static final float component1(@zu2 SizeFCompat sizeFCompat) {
        fa1.m6826(sizeFCompat, "<this>");
        return sizeFCompat.getWidth();
    }

    @RequiresApi(21)
    public static final int component1(@zu2 Size size) {
        fa1.m6826(size, "<this>");
        return size.getWidth();
    }

    @RequiresApi(21)
    public static final float component2(@zu2 SizeF sizeF) {
        fa1.m6826(sizeF, "<this>");
        return sizeF.getHeight();
    }

    public static final float component2(@zu2 SizeFCompat sizeFCompat) {
        fa1.m6826(sizeFCompat, "<this>");
        return sizeFCompat.getHeight();
    }

    @RequiresApi(21)
    public static final int component2(@zu2 Size size) {
        fa1.m6826(size, "<this>");
        return size.getHeight();
    }
}
