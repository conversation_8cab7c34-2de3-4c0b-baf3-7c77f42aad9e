package android.view.result;

import android.view.result.contract.ActivityResultContract;
import androidx.core.app.ActivityOptionsCompat;
import com.stardust.autojs.engine.RhinoJavaScriptEngine;
import kotlin.Metadata;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.wv2;
import okhttp3.internal.p042io.wx1;
import okhttp3.internal.p042io.yg3;
import okhttp3.internal.p042io.zu2;
import org.mozilla.javascript.optimizer.OptRuntime;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0014\b\u0000\u0018\u0000*\u0004\b\u0000\u0010\u0001*\u0004\b\u0001\u0010\u00022\b\u0012\u0004\u0012\u00020\u00040\u0003B1\u0012\f\u0010\r\u001a\b\u0012\u0004\u0012\u00028\u00000\u0003\u0012\u0012\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u00028\u0000\u0012\u0004\u0012\u00028\u00010\u000b\u0012\u0006\u0010\u0015\u001a\u00028\u0000¢\u0006\u0004\b\u001d\u0010\u001eJ!\u0010\b\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00042\b\u0010\u0007\u001a\u0004\u0018\u00010\u0006H\u0016¢\u0006\u0004\b\b\u0010\tJ\b\u0010\n\u001a\u00020\u0004H\u0016J\u0014\u0010\f\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00028\u00010\u000bH\u0016R\u001d\u0010\r\u001a\b\u0012\u0004\u0012\u00028\u00000\u00038\u0006¢\u0006\f\n\u0004\b\r\u0010\u000e\u001a\u0004\b\u000f\u0010\u0010R#\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u00028\u0000\u0012\u0004\u0012\u00028\u00010\u000b8\u0006¢\u0006\f\n\u0004\b\u0011\u0010\u0012\u001a\u0004\b\u0013\u0010\u0014R\u0017\u0010\u0015\u001a\u00028\u00008\u0006¢\u0006\f\n\u0004\b\u0015\u0010\u0016\u001a\u0004\b\u0017\u0010\u0018R'\u0010\u001c\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00028\u00010\u000b8FX\u0086\u0084\u0002¢\u0006\f\n\u0004\b\u0019\u0010\u001a\u001a\u0004\b\u001b\u0010\u0014¨\u0006\u001f"}, m4115d2 = {"Landroidx/activity/result/ActivityResultCallerLauncher;", OptRuntime.GeneratorState.resumptionPoint_TYPE, "O", "Landroidx/activity/result/ActivityResultLauncher;", "Lokhttp3/internal/io/lx5;", "input", "Landroidx/core/app/ActivityOptionsCompat;", "options", "launch", "(Lokhttp3/internal/io/lx5;Landroidx/core/app/ActivityOptionsCompat;)V", "unregister", "Landroidx/activity/result/contract/ActivityResultContract;", "getContract", "launcher", "Landroidx/activity/result/ActivityResultLauncher;", "getLauncher", "()Landroidx/activity/result/ActivityResultLauncher;", "callerContract", "Landroidx/activity/result/contract/ActivityResultContract;", "getCallerContract", "()Landroidx/activity/result/contract/ActivityResultContract;", "callerInput", "Ljava/lang/Object;", "getCallerInput", "()Ljava/lang/Object;", "resultContract$delegate", "Lokhttp3/internal/io/wx1;", "getResultContract", "resultContract", RhinoJavaScriptEngine.SOURCE_NAME_INIT, "(Landroidx/activity/result/ActivityResultLauncher;Landroidx/activity/result/contract/ActivityResultContract;Ljava/lang/Object;)V", "activity-ktx_release"}, m4116k = 1, m4117mv = {1, 6, 0})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class ActivityResultCallerLauncher<I, O> extends ActivityResultLauncher<lx5> {

    @zu2
    private final ActivityResultContract<I, O> callerContract;
    private final I callerInput;

    @zu2
    private final ActivityResultLauncher<I> launcher;

    /* renamed from: resultContract$delegate, reason: from kotlin metadata */
    @zu2
    private final wx1 resultContract;

    public ActivityResultCallerLauncher(@zu2 ActivityResultLauncher<I> activityResultLauncher, @zu2 ActivityResultContract<I, O> activityResultContract, I i) {
        fa1.m6826(activityResultLauncher, "launcher");
        fa1.m6826(activityResultContract, "callerContract");
        this.launcher = activityResultLauncher;
        this.callerContract = activityResultContract;
        this.callerInput = i;
        this.resultContract = yg3.m14275(new ActivityResultCallerLauncher$resultContract$2(this));
    }

    @zu2
    public final ActivityResultContract<I, O> getCallerContract() {
        return this.callerContract;
    }

    public final I getCallerInput() {
        return this.callerInput;
    }

    @Override // android.view.result.ActivityResultLauncher
    @zu2
    public ActivityResultContract<lx5, ?> getContract() {
        return getResultContract();
    }

    @zu2
    public final ActivityResultLauncher<I> getLauncher() {
        return this.launcher;
    }

    @zu2
    public final ActivityResultContract<lx5, O> getResultContract() {
        return (ActivityResultContract) this.resultContract.getValue();
    }

    @Override // android.view.result.ActivityResultLauncher
    public void launch(@zu2 lx5 input, @wv2 ActivityOptionsCompat options) {
        fa1.m6826(input, "input");
        this.launcher.launch(this.callerInput, options);
    }

    @Override // android.view.result.ActivityResultLauncher
    public void unregister() {
        this.launcher.unregister();
    }
}
