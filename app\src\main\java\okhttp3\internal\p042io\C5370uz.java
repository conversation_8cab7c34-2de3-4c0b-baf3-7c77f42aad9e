package okhttp3.internal.p042io;

import java.io.ByteArrayOutputStream;

/* renamed from: okhttp3.internal.io.uz */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5370uz extends ByteArrayOutputStream {
    public C5370uz() {
        super(8193);
    }

    @zu2
    /* renamed from: Ϳ */
    public final byte[] m13026() {
        byte[] bArr = ((ByteArrayOutputStream) this).buf;
        fa1.m6825(bArr, "buf");
        return bArr;
    }
}
