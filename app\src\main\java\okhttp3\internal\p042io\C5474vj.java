package okhttp3.internal.p042io;

@InterfaceC4988s2(m11868c = "androidx.compose.foundation.gestures.DraggableKt$draggable$2", m11869f = "Draggable.kt", m11870l = {}, m11871m = "invokeSuspend")
/* renamed from: okhttp3.internal.io.vj */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes3.dex */
public final class C5474vj extends u75 implements fi0<InterfaceC7881, Float, InterfaceC7155<? super lx5>, Object> {
    public C5474vj(InterfaceC7155<? super C5474vj> interfaceC7155) {
        super(3, interfaceC7155);
    }

    @Override // okhttp3.internal.p042io.fi0
    public final Object invoke(InterfaceC7881 interfaceC7881, Float f, InterfaceC7155<? super lx5> interfaceC7155) {
        f.floatValue();
        C5474vj c5474vj = new C5474vj(interfaceC7155);
        lx5 lx5Var = lx5.f14876;
        c5474vj.invokeSuspend(lx5Var);
        return lx5Var;
    }

    @Override // okhttp3.internal.p042io.AbstractC7853
    @wv2
    public final Object invokeSuspend(@zu2 Object obj) {
        C4350o9.m10270(obj);
        return lx5.f14876;
    }
}
