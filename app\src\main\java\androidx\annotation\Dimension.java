package androidx.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import kotlin.Metadata;
import okhttp3.internal.p042io.dn2;
import okhttp3.internal.p042io.l44;
import okhttp3.internal.p042io.u95;
import okhttp3.internal.p042io.zu2;
import org.mozilla.javascript.optimizer.Codegen;

@Target({ElementType.FIELD, ElementType.METHOD, ElementType.PARAMETER, ElementType.LOCAL_VARIABLE, ElementType.ANNOTATION_TYPE})
@dn2
@u95
@Retention(RetentionPolicy.CLASS)
@Metadata(m4114d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u001b\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\b\u0087\u0002\u0018\u0000 \u00052\u00020\u0001:\u0001\u0005B\n\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003R\u000f\u0010\u0002\u001a\u00020\u0003¢\u0006\u0006\u001a\u0004\b\u0002\u0010\u0004¨\u0006\u0006"}, m4115d2 = {"Landroidx/annotation/Dimension;", "", "unit", "", "()I", "Companion", "annotation"}, m4116k = 1, m4117mv = {1, 7, 1}, m4119xi = 48)
@l44
@Documented
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public @interface Dimension {

    /* renamed from: Companion, reason: from kotlin metadata */
    @zu2
    public static final Companion INSTANCE = Companion.$$INSTANCE;

    /* renamed from: DP */
    public static final int f4DP = 0;

    /* renamed from: PX */
    public static final int f5PX = 1;

    /* renamed from: SP */
    public static final int f6SP = 2;

    @Metadata(m4114d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002¢\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T¢\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T¢\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T¢\u0006\u0002\n\u0000¨\u0006\u0007"}, m4115d2 = {"Landroidx/annotation/Dimension$Companion;", "", Codegen.TEMPLATE_LITERAL_INIT_METHOD_SIGNATURE, "DP", "", "PX", "SP", "annotation"}, m4116k = 1, m4117mv = {1, 7, 1}, m4119xi = 48)
    public static final class Companion {
        public static final /* synthetic */ Companion $$INSTANCE = new Companion();
        public static final int DP = 0;
        public static final int PX = 1;
        public static final int SP = 2;

        private Companion() {
        }
    }

    int unit() default 1;
}
