package okhttp3.internal.p042io;

import android.app.Activity;
import android.content.DialogInterface;
import com.google.android.material.dialog.MaterialAlertDialogBuilder;

/* renamed from: okhttp3.internal.io.y1 */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5776y1 extends lv1 implements nh0<lx5> {

    /* renamed from: ၥ */
    public final /* synthetic */ Activity f26321;

    /* renamed from: ၦ */
    public final /* synthetic */ String f26322;

    /* renamed from: ၮ */
    public final /* synthetic */ InterfaceC7300<Boolean> f26323;

    /* renamed from: ၯ */
    public final /* synthetic */ String f26324;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    /* JADX WARN: Multi-variable type inference failed */
    public C5776y1(Activity activity, String str, InterfaceC7300<? super Boolean> interfaceC7300, String str2) {
        super(0);
        this.f26321 = activity;
        this.f26322 = str;
        this.f26323 = interfaceC7300;
        this.f26324 = str2;
    }

    @Override // okhttp3.internal.p042io.nh0
    public final lx5 invoke() {
        this.f26323.mo15598(new C5264u1(new MaterialAlertDialogBuilder(this.f26321).setTitle(2131821550).setMessage((CharSequence) this.f26321.getString(2131820674, this.f26322)).setPositiveButton(2131820651, (DialogInterface.OnClickListener) new DialogInterfaceOnClickListenerC5380v1(this.f26323)).setNegativeButton(2131820653, (DialogInterface.OnClickListener) new DialogInterfaceOnClickListenerC5545w1(this.f26323)).setNeutralButton(2131820652, (DialogInterface.OnClickListener) new DialogInterfaceOnClickListenerC5648x1(this.f26323, this.f26324)).show()));
        return lx5.f14876;
    }
}
