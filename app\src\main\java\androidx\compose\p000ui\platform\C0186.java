package androidx.compose.p000ui.platform;

import android.graphics.Matrix;
import android.view.View;
import okhttp3.internal.p042io.C7464;
import okhttp3.internal.p042io.C7687;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.he2;
import okhttp3.internal.p042io.zu2;

/* renamed from: androidx.compose.ui.platform.Ԩ */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class C0186 implements InterfaceC0185 {

    /* renamed from: Ϳ */
    @zu2
    public final int[] f182 = new int[2];

    /* renamed from: Ԩ */
    @zu2
    public final float[] f183 = he2.m7780();

    @Override // androidx.compose.p000ui.platform.InterfaceC0185
    /* renamed from: Ϳ */
    public final void mo56(@zu2 View view, @zu2 float[] fArr) {
        fa1.m6826(view, "view");
        fa1.m6826(fArr, "matrix");
        he2.m7786(fArr);
        m58(view, fArr);
    }

    /* renamed from: Ԩ */
    public final void m57(float[] fArr, float f, float f2) {
        he2.m7786(this.f183);
        he2.m7790(this.f183, f, f2);
        C7687.m17300(fArr, this.f183);
    }

    /* renamed from: ԩ */
    public final void m58(View view, float[] fArr) {
        Object parent = view.getParent();
        if (parent instanceof View) {
            m58((View) parent, fArr);
            m57(fArr, -view.getScrollX(), -view.getScrollY());
            m57(fArr, view.getLeft(), view.getTop());
        } else {
            view.getLocationInWindow(this.f182);
            m57(fArr, -view.getScrollX(), -view.getScrollY());
            m57(fArr, r0[0], r0[1]);
        }
        Matrix matrix = view.getMatrix();
        if (matrix.isIdentity()) {
            return;
        }
        C7464.m17057(this.f183, matrix);
        C7687.m17300(fArr, this.f183);
    }
}
