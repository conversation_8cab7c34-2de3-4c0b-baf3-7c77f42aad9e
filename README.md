# AutoJS9 兼容服务器

这是一个与AutoJS9客户端兼容的调试服务器实现，支持WebSocket连接、token认证和RPC调用。

## 功能特性

- ✅ WebSocket服务器
- ✅ Token认证机制
- ✅ 设备授权管理
- ✅ RPC调用支持
- ✅ 文件系统操作
- ✅ 调试命令执行
- ✅ 持久化存储
- ✅ 日志记录

## 安装依赖

```bash
pip install -r requirements.txt
```

## 快速开始

### 1. 启动服务器

```bash
python autojs_server.py
```

服务器将在 `ws://localhost:9317` 启动。

### 2. 测试连接

```bash
python test_client.py
```

## 认证流程

1. 客户端连接到WebSocket服务器
2. 发送 `debug.authorize` 请求，包含设备ID和设备名称
3. 服务器显示授权请求，等待用户确认
4. 用户确认后，服务器返回授权token
5. 后续所有RPC调用都需要携带此token

## 支持的RPC方法

### 认证相关
- `debug.authorize` - 请求设备授权

### 基础功能
- `ping` - 心跳检测
- `getDeviceInfo` - 获取设备信息

### 调试功能
- `execDebugCommand` - 执行调试命令
  - `type: "eval"` - 执行JavaScript代码
  - `type: "log"` - 输出日志

### 文件系统
- `enableVfs` - 启用虚拟文件系统
- `listFiles` - 列出文件
- `readFile` - 读取文件
- `writeFile` - 写入文件

### 服务功能
- `enableFtpServer` - 启用FTP服务器

## 配置文件

编辑 `config.json` 来自定义服务器配置：

```json
{
  "server": {
    "host": "0.0.0.0",
    "port": 9317
  },
  "auth": {
    "authorization_timeout": 30,
    "require_authorization": true
  },
  "features": {
    "enable_vfs": true,
    "enable_ftp": true,
    "script_root": "./scripts"
  }
}
```

## 数据库

服务器使用SQLite数据库存储授权设备信息：

- 数据库文件：`auth.db`
- 表：`authorized_devices`
- 字段：device_id, token, authorized_at, last_access, device_name

## 安全注意事项

1. **网络安全**：建议在受信任的网络环境中使用
2. **Token管理**：定期清理过期的授权token
3. **文件访问**：限制文件系统访问权限
4. **日志记录**：监控异常访问行为

## 扩展开发

### 添加新的RPC方法

在 `AutoJSServer.handle_authorized_rpc()` 方法中添加新的处理逻辑：

```python
elif method == "your_method":
    result = await self.handle_your_method(params)
```

### 自定义认证逻辑

继承 `TokenManager` 类并重写相关方法：

```python
class CustomTokenManager(TokenManager):
    async def request_authorization(self, device_id: str, device_name: str = None) -> bool:
        # 自定义授权逻辑
        pass
```

## 故障排除

### 常见问题

1. **连接失败**
   - 检查端口是否被占用
   - 确认防火墙设置

2. **授权超时**
   - 增加 `authorization_timeout` 配置
   - 检查控制台输入

3. **Token无效**
   - 重新授权设备
   - 检查数据库文件权限

### 日志查看

```bash
tail -f autojs_server.log
```

## 许可证

MIT License
