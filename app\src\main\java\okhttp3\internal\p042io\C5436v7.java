package okhttp3.internal.p042io;

/* renamed from: okhttp3.internal.io.v7 */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes3.dex */
public final class C5436v7 implements InterfaceC5282u7 {

    /* renamed from: ၥ */
    public final float f23785;

    /* renamed from: ၦ */
    public final float f23786;

    public C5436v7(float f, float f2) {
        this.f23785 = f;
        this.f23786 = f2;
    }

    public final boolean equals(@wv2 Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof C5436v7)) {
            return false;
        }
        C5436v7 c5436v7 = (C5436v7) obj;
        return fa1.m6818(Float.valueOf(this.f23785), Float.valueOf(c5436v7.f23785)) && fa1.m6818(Float.valueOf(this.f23786), Float.valueOf(c5436v7.f23786));
    }

    @Override // okhttp3.internal.p042io.InterfaceC5282u7
    public final float getDensity() {
        return this.f23785;
    }

    public final int hashCode() {
        return Float.floatToIntBits(this.f23786) + (Float.floatToIntBits(this.f23785) * 31);
    }

    @zu2
    public final String toString() {
        StringBuilder m9240 = lf2.m9240("DensityImpl(density=");
        m9240.append(this.f23785);
        m9240.append(", fontScale=");
        return rn5.m11780(m9240, this.f23786, ')');
    }

    @Override // okhttp3.internal.p042io.InterfaceC5282u7
    /* renamed from: ރ */
    public final float mo6960(int i) {
        return i / this.f23785;
    }

    @Override // okhttp3.internal.p042io.InterfaceC5282u7
    /* renamed from: ޓ */
    public final /* synthetic */ long mo6961(long j) {
        return C5157t7.m12298(this, j);
    }

    @Override // okhttp3.internal.p042io.InterfaceC5282u7
    /* renamed from: ޣ */
    public final float mo6962(float f) {
        return f / getDensity();
    }

    @Override // okhttp3.internal.p042io.InterfaceC5282u7
    /* renamed from: ޱ */
    public final float mo6963() {
        return this.f23786;
    }

    @Override // okhttp3.internal.p042io.InterfaceC5282u7
    /* renamed from: ࡧ */
    public final float mo6964(float f) {
        return getDensity() * f;
    }

    @Override // okhttp3.internal.p042io.InterfaceC5282u7
    /* renamed from: ࢣ */
    public final /* synthetic */ int mo6965(float f) {
        return C5157t7.m12297(this, f);
    }

    @Override // okhttp3.internal.p042io.InterfaceC5282u7
    /* renamed from: ࢩ */
    public final /* synthetic */ long mo6966(long j) {
        return C5157t7.m12300(this, j);
    }

    @Override // okhttp3.internal.p042io.InterfaceC5282u7
    /* renamed from: ࢫ */
    public final /* synthetic */ float mo6967(long j) {
        return C5157t7.m12299(this, j);
    }
}
