package androidx.datastore.preferences.core;

import kotlin.Metadata;
import okhttp3.internal.p042io.C4350o9;
import okhttp3.internal.p042io.EnumC7329;
import okhttp3.internal.p042io.InterfaceC4988s2;
import okhttp3.internal.p042io.InterfaceC7155;
import okhttp3.internal.p042io.di0;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.u75;
import okhttp3.internal.p042io.wv2;
import okhttp3.internal.p042io.zu2;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000\b\n\u0002\u0018\u0002\n\u0002\b\u0002\u0010\u0002\u001a\u00020\u00002\u0006\u0010\u0001\u001a\u00020\u0000H\u008a@"}, m4115d2 = {"Landroidx/datastore/preferences/core/Preferences;", "it", "<anonymous>"}, m4116k = 3, m4117mv = {1, 5, 1})
@InterfaceC4988s2(m11868c = "androidx.datastore.preferences.core.PreferencesKt$edit$2", m11869f = "Preferences.kt", m11870l = {329}, m11871m = "invokeSuspend")
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class PreferencesKt$edit$2 extends u75 implements di0<Preferences, InterfaceC7155<? super Preferences>, Object> {
    public final /* synthetic */ di0<MutablePreferences, InterfaceC7155<? super lx5>, Object> $transform;
    public /* synthetic */ Object L$0;
    public int label;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    /* JADX WARN: Multi-variable type inference failed */
    public PreferencesKt$edit$2(di0<? super MutablePreferences, ? super InterfaceC7155<? super lx5>, ? extends Object> di0Var, InterfaceC7155<? super PreferencesKt$edit$2> interfaceC7155) {
        super(2, interfaceC7155);
        this.$transform = di0Var;
    }

    @Override // okhttp3.internal.p042io.AbstractC7853
    @zu2
    public final InterfaceC7155<lx5> create(@wv2 Object obj, @zu2 InterfaceC7155<?> interfaceC7155) {
        PreferencesKt$edit$2 preferencesKt$edit$2 = new PreferencesKt$edit$2(this.$transform, interfaceC7155);
        preferencesKt$edit$2.L$0 = obj;
        return preferencesKt$edit$2;
    }

    @Override // okhttp3.internal.p042io.di0
    @wv2
    /* renamed from: invoke, reason: avoid collision after fix types in other method and merged with bridge method [inline-methods] */
    public final Object mo18338invoke(@zu2 Preferences preferences, @wv2 InterfaceC7155<? super Preferences> interfaceC7155) {
        return ((PreferencesKt$edit$2) create(preferences, interfaceC7155)).invokeSuspend(lx5.f14876);
    }

    @Override // okhttp3.internal.p042io.AbstractC7853
    @wv2
    public final Object invokeSuspend(@zu2 Object obj) {
        EnumC7329 enumC7329 = EnumC7329.COROUTINE_SUSPENDED;
        int i = this.label;
        if (i != 0) {
            if (i != 1) {
                throw new IllegalStateException("call to 'resume' before 'invoke' with coroutine");
            }
            MutablePreferences mutablePreferences = (MutablePreferences) this.L$0;
            C4350o9.m10270(obj);
            return mutablePreferences;
        }
        C4350o9.m10270(obj);
        MutablePreferences mutablePreferences2 = ((Preferences) this.L$0).toMutablePreferences();
        di0<MutablePreferences, InterfaceC7155<? super lx5>, Object> di0Var = this.$transform;
        this.L$0 = mutablePreferences2;
        this.label = 1;
        return di0Var.mo18338invoke(mutablePreferences2, this) == enumC7329 ? enumC7329 : mutablePreferences2;
    }
}
