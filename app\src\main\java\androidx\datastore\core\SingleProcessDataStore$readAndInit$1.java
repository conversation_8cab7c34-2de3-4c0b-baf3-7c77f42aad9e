package androidx.datastore.core;

import com.stardust.autojs.core.inputevent.InputEventCodes;
import kotlin.Metadata;
import okhttp3.internal.p042io.AbstractC6644;
import okhttp3.internal.p042io.InterfaceC4988s2;
import okhttp3.internal.p042io.InterfaceC7155;
import okhttp3.internal.p042io.wv2;
import okhttp3.internal.p042io.zu2;

@InterfaceC4988s2(m11868c = "androidx.datastore.core.SingleProcessDataStore", m11869f = "SingleProcessDataStore.kt", m11870l = {InputEventCodes.BTN_TOOL_BRUSH, 348, 505}, m11871m = "readAndInit")
@Metadata(m4116k = 3, m4117mv = {1, 5, 1}, m4119xi = 48)
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class SingleProcessDataStore$readAndInit$1 extends AbstractC6644 {
    public Object L$0;
    public Object L$1;
    public Object L$2;
    public Object L$3;
    public Object L$4;
    public Object L$5;
    public int label;
    public /* synthetic */ Object result;
    public final /* synthetic */ SingleProcessDataStore<T> this$0;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public SingleProcessDataStore$readAndInit$1(SingleProcessDataStore<T> singleProcessDataStore, InterfaceC7155<? super SingleProcessDataStore$readAndInit$1> interfaceC7155) {
        super(interfaceC7155);
        this.this$0 = singleProcessDataStore;
    }

    @Override // okhttp3.internal.p042io.AbstractC7853
    @wv2
    public final Object invokeSuspend(@zu2 Object obj) {
        Object readAndInit;
        this.result = obj;
        this.label |= Integer.MIN_VALUE;
        readAndInit = this.this$0.readAndInit(this);
        return readAndInit;
    }
}
