package okhttp3.internal.p042io;

/* renamed from: okhttp3.internal.io.uu */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes3.dex */
public final class C5359uu extends AbstractC3887k0 {

    /* renamed from: Ԩ */
    public final /* synthetic */ ty3 f23507;

    /* renamed from: ԩ */
    public final /* synthetic */ ty3 f23508;

    /* renamed from: Ԫ */
    public final /* synthetic */ C5625wu f23509;

    public C5359uu(C5625wu c5625wu, ty3 ty3Var, ty3 ty3Var2) {
        this.f23509 = c5625wu;
        this.f23507 = ty3Var;
        this.f23508 = ty3Var2;
    }

    @Override // okhttp3.internal.p042io.AbstractC3887k0
    /* renamed from: Ԩ */
    public final ty3 mo7912(ty3 ty3Var) {
        return ty3Var.f22640 == this.f23507.f22640 ? this.f23508 : ty3Var;
    }
}
