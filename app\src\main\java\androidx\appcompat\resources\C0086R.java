package androidx.appcompat.resources;

/* renamed from: androidx.appcompat.resources.R */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class C0086R {

    /* renamed from: androidx.appcompat.resources.R$drawable */
    public static final class drawable {
        public static final int abc_vector_test = 2131230832;

        private drawable() {
        }
    }

    /* renamed from: androidx.appcompat.resources.R$styleable */
    public static final class styleable {
        public static final int AnimatedStateListDrawableCompat_android_constantSize = 3;
        public static final int AnimatedStateListDrawableCompat_android_dither = 0;
        public static final int AnimatedStateListDrawableCompat_android_enterFadeDuration = 4;
        public static final int AnimatedStateListDrawableCompat_android_exitFadeDuration = 5;
        public static final int AnimatedStateListDrawableCompat_android_variablePadding = 2;
        public static final int AnimatedStateListDrawableCompat_android_visible = 1;
        public static final int AnimatedStateListDrawableItem_android_drawable = 1;
        public static final int AnimatedStateListDrawableItem_android_id = 0;
        public static final int AnimatedStateListDrawableTransition_android_drawable = 0;
        public static final int AnimatedStateListDrawableTransition_android_fromId = 2;
        public static final int AnimatedStateListDrawableTransition_android_reversible = 3;
        public static final int AnimatedStateListDrawableTransition_android_toId = 1;
        public static final int StateListDrawableItem_android_drawable = 0;
        public static final int StateListDrawable_android_constantSize = 3;
        public static final int StateListDrawable_android_dither = 0;
        public static final int StateListDrawable_android_enterFadeDuration = 4;
        public static final int StateListDrawable_android_exitFadeDuration = 5;
        public static final int StateListDrawable_android_variablePadding = 2;
        public static final int StateListDrawable_android_visible = 1;
        public static final int[] AnimatedStateListDrawableCompat = {android.R.attr.dither, android.R.attr.visible, android.R.attr.variablePadding, android.R.attr.constantSize, android.R.attr.enterFadeDuration, android.R.attr.exitFadeDuration};
        public static final int[] AnimatedStateListDrawableItem = {android.R.attr.id, android.R.attr.drawable};
        public static final int[] AnimatedStateListDrawableTransition = {android.R.attr.drawable, android.R.attr.toId, android.R.attr.fromId, android.R.attr.reversible};
        public static final int[] StateListDrawable = {android.R.attr.dither, android.R.attr.visible, android.R.attr.variablePadding, android.R.attr.constantSize, android.R.attr.enterFadeDuration, android.R.attr.exitFadeDuration};
        public static final int[] StateListDrawableItem = {android.R.attr.drawable};

        private styleable() {
        }
    }

    private C0086R() {
    }
}
