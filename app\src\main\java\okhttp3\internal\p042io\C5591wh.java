package okhttp3.internal.p042io;

import android.os.Binder;
import android.os.Parcelable;
import android.util.Size;
import android.util.SizeF;
import android.util.SparseArray;
import java.io.Serializable;

/* renamed from: okhttp3.internal.io.wh */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes3.dex */
public final class C5591wh {

    /* renamed from: Ϳ */
    @zu2
    public static final Class<? extends Object>[] f24983 = {Serializable.class, Parcelable.class, String.class, SparseArray.class, Binder.class, Size.class, SizeF.class};

    /* renamed from: Ϳ */
    public static final boolean m13575(Object obj) {
        if (obj instanceof lv4) {
            lv4 lv4Var = (lv4) obj;
            if (lv4Var.mo9452() != qr2.f19477 && lv4Var.mo9452() != k55.f13609 && lv4Var.mo9452() != iw3.f12730) {
                return false;
            }
            T value = lv4Var.getValue();
            if (value == 0) {
                return true;
            }
            return m13575(value);
        }
        if ((obj instanceof ei0) && (obj instanceof Serializable)) {
            return false;
        }
        Class<? extends Object>[] clsArr = f24983;
        for (int i = 0; i < 7; i++) {
            if (clsArr[i].isInstance(obj)) {
                return true;
            }
        }
        return false;
    }
}
