package androidx.compose.p000ui.platform;

import android.content.Context;
import android.util.AttributeSet;
import androidx.compose.runtime.ParcelableSnapshotMutableState;
import com.stardust.autojs.engine.RhinoJavaScriptEngine;
import kotlin.Metadata;
import okhttp3.internal.p042io.C2849b5;
import okhttp3.internal.p042io.C7171;
import okhttp3.internal.p042io.InterfaceC6575;
import okhttp3.internal.p042io.InterfaceC6721;
import okhttp3.internal.p042io.InterfaceC6968;
import okhttp3.internal.p042io.InterfaceC7452;
import okhttp3.internal.p042io.bp1;
import okhttp3.internal.p042io.di0;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.fi0;
import okhttp3.internal.p042io.fz4;
import okhttp3.internal.p042io.hz3;
import okhttp3.internal.p042io.lu4;
import okhttp3.internal.p042io.lv1;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.ov4;
import okhttp3.internal.p042io.qb4;
import okhttp3.internal.p042io.wv2;
import okhttp3.internal.p042io.yn2;
import okhttp3.internal.p042io.zu2;
import org.mozilla.javascript.optimizer.Codegen;

@fz4
@Metadata(m4113bv = {}, m4114d1 = {"\u0000>\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\r\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\b\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0004\b\u0007\u0018\u00002\u00020\u0001B'\b\u0007\u0012\u0006\u0010\u0016\u001a\u00020\u0015\u0012\n\b\u0002\u0010\u0018\u001a\u0004\u0018\u00010\u0017\u0012\b\b\u0002\u0010\u001a\u001a\u00020\u0019¢\u0006\u0004\b\u001b\u0010\u001cJ\u000f\u0010\u0003\u001a\u00020\u0002H\u0017¢\u0006\u0004\b\u0003\u0010\u0004J\b\u0010\u0006\u001a\u00020\u0005H\u0016J \u0010\n\u001a\u00020\u00022\u0011\u0010\t\u001a\r\u0012\u0004\u0012\u00020\u00020\u0007¢\u0006\u0002\b\b¢\u0006\u0004\b\n\u0010\u000bR*\u0010\u0014\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\f8\u0014@RX\u0094\u000e¢\u0006\u0012\n\u0004\b\u000e\u0010\u000f\u0012\u0004\b\u0012\u0010\u0013\u001a\u0004\b\u0010\u0010\u0011¨\u0006\u001d"}, m4115d2 = {"Landroidx/compose/ui/platform/ComposeView;", "Landroidx/compose/ui/platform/AbstractComposeView;", "Lokhttp3/internal/io/lx5;", "Content", "(Lokhttp3/internal/io/ࡊ;I)V", "", "getAccessibilityClassName", "Lkotlin/Function0;", "Lokhttp3/internal/io/ಭ;", "content", "setContent", "(Lokhttp3/internal/io/di0;)V", "", "<set-?>", "ၸ", "Z", "getShouldCreateCompositionOnAttachedToWindow", "()Z", "getShouldCreateCompositionOnAttachedToWindow$annotations", Codegen.TEMPLATE_LITERAL_INIT_METHOD_SIGNATURE, "shouldCreateCompositionOnAttachedToWindow", "Landroid/content/Context;", "context", "Landroid/util/AttributeSet;", "attrs", "", "defStyleAttr", RhinoJavaScriptEngine.SOURCE_NAME_INIT, "(Landroid/content/Context;Landroid/util/AttributeSet;I)V", "ui_release"}, m4116k = 1, m4117mv = {1, 7, 1})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class ComposeView extends AbstractComposeView {

    /* renamed from: ၷ */
    @zu2
    public final yn2<di0<InterfaceC6968, Integer, lx5>> f133;

    /* renamed from: ၸ, reason: from kotlin metadata */
    public boolean shouldCreateCompositionOnAttachedToWindow;

    /* renamed from: androidx.compose.ui.platform.ComposeView$Ϳ */
    public static final class C0178 extends lv1 implements di0<InterfaceC6968, Integer, lx5> {

        /* renamed from: ၦ */
        public final /* synthetic */ int f136;

        /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
        public C0178(int i) {
            super(2);
            this.f136 = i;
        }

        @Override // okhttp3.internal.p042io.di0
        /* renamed from: invoke */
        public final lx5 mo18338invoke(InterfaceC6968 interfaceC6968, Integer num) {
            num.intValue();
            ComposeView.this.Content(interfaceC6968, this.f136 | 1);
            return lx5.f14876;
        }
    }

    /* JADX WARN: 'this' call moved to the top of the method (can break code semantics) */
    @bp1
    public ComposeView(@zu2 Context context) {
        this(context, null, 0, 6, null);
        fa1.m6826(context, "context");
    }

    /* JADX WARN: 'this' call moved to the top of the method (can break code semantics) */
    @bp1
    public ComposeView(@zu2 Context context, @wv2 AttributeSet attributeSet) {
        this(context, attributeSet, 0, 4, null);
        fa1.m6826(context, "context");
    }

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    @bp1
    public ComposeView(@zu2 Context context, @wv2 AttributeSet attributeSet, int i) {
        super(context, attributeSet, i);
        fa1.m6826(context, "context");
        this.f133 = (ParcelableSnapshotMutableState) ov4.m10507(null);
    }

    public /* synthetic */ ComposeView(Context context, AttributeSet attributeSet, int i, int i2, C2849b5 c2849b5) {
        this(context, (i2 & 2) != 0 ? null : attributeSet, (i2 & 4) != 0 ? 0 : i);
    }

    public static /* synthetic */ void getShouldCreateCompositionOnAttachedToWindow$annotations() {
    }

    @Override // androidx.compose.p000ui.platform.AbstractComposeView
    @InterfaceC7452
    public void Content(@wv2 InterfaceC6968 interfaceC6968, int i) {
        InterfaceC6968 mo16280 = interfaceC6968.mo16280(420213850);
        fi0<InterfaceC6575<?>, lu4, hz3, lx5> fi0Var = C7171.f31108;
        di0<InterfaceC6968, Integer, lx5> value = this.f133.getValue();
        if (value != null) {
            value.mo18338invoke(mo16280, 0);
        }
        qb4 mo16286 = mo16280.mo16286();
        if (mo16286 == null) {
            return;
        }
        mo16286.mo5841(new C0178(i));
    }

    @Override // android.view.ViewGroup, android.view.View
    @zu2
    public CharSequence getAccessibilityClassName() {
        return "androidx.compose.ui.platform.ComposeView";
    }

    @Override // androidx.compose.p000ui.platform.AbstractComposeView
    public boolean getShouldCreateCompositionOnAttachedToWindow() {
        return this.shouldCreateCompositionOnAttachedToWindow;
    }

    @InterfaceC6721
    public final void setContent(@zu2 di0<? super InterfaceC6968, ? super Integer, lx5> content) {
        fa1.m6826(content, "content");
        this.shouldCreateCompositionOnAttachedToWindow = true;
        this.f133.setValue(content);
        if (isAttachedToWindow()) {
            createComposition();
        }
    }
}
