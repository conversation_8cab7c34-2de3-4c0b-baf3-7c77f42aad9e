package okhttp3.internal.p042io;

/* renamed from: okhttp3.internal.io.wd */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5577wd extends AbstractC6526 {

    /* renamed from: ၥ */
    @pu2
    public final C3318fc f24879;

    /* renamed from: ၦ */
    public final int f24880;

    public C5577wd(@pu2 C3318fc c3318fc, @pu2 C4694qf c4694qf, int i) {
        this.f24879 = c3318fc;
        this.f24880 = c4694qf.m11116(i + 1);
    }

    @Override // okhttp3.internal.p042io.di2
    @pu2
    public final xi2 getValue() {
        return new C2869be(this.f24879, this.f24880);
    }
}
