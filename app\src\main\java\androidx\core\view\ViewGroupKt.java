package androidx.core.view;

import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.Px;
import androidx.annotation.RequiresApi;
import androidx.autofill.HintConstants;
import java.util.Iterator;
import kotlin.Metadata;
import okhttp3.internal.p042io.C6489;
import okhttp3.internal.p042io.C6668;
import okhttp3.internal.p042io.di0;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.j71;
import okhttp3.internal.p042io.ka1;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.ph0;
import okhttp3.internal.p042io.pk4;
import okhttp3.internal.p042io.zu2;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000P\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010)\n\u0000\n\u0002\u0018\u0002\n\u0002\b\f\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0006\u001a\u0015\u0010\u0004\u001a\u00020\u0003*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u0001H\u0086\u0002\u001a\u0015\u0010\u0007\u001a\u00020\u0006*\u00020\u00002\u0006\u0010\u0005\u001a\u00020\u0003H\u0086\n\u001a\u0015\u0010\t\u001a\u00020\b*\u00020\u00002\u0006\u0010\u0005\u001a\u00020\u0003H\u0086\n\u001a\u0015\u0010\n\u001a\u00020\b*\u00020\u00002\u0006\u0010\u0005\u001a\u00020\u0003H\u0086\n\u001a\r\u0010\u000b\u001a\u00020\u0006*\u00020\u0000H\u0086\b\u001a\r\u0010\f\u001a\u00020\u0006*\u00020\u0000H\u0086\b\u001a3\u0010\u0011\u001a\u00020\b*\u00020\u00002!\u0010\u0010\u001a\u001d\u0012\u0013\u0012\u00110\u0003¢\u0006\f\b\u000e\u0012\b\b\u000f\u0012\u0004\b\b(\u0005\u0012\u0004\u0012\u00020\b0\rH\u0086\bø\u0001\u0000\u001aH\u0010\u0013\u001a\u00020\b*\u00020\u000026\u0010\u0010\u001a2\u0012\u0013\u0012\u00110\u0001¢\u0006\f\b\u000e\u0012\b\b\u000f\u0012\u0004\b\b(\u0002\u0012\u0013\u0012\u00110\u0003¢\u0006\f\b\u000e\u0012\b\b\u000f\u0012\u0004\b\b(\u0005\u0012\u0004\u0012\u00020\b0\u0012H\u0086\bø\u0001\u0000\u001a\u0013\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00030\u0014*\u00020\u0000H\u0086\u0002\u001a\u0017\u0010\u0018\u001a\u00020\b*\u00020\u00162\b\b\u0001\u0010\u0017\u001a\u00020\u0001H\u0086\b\u001a5\u0010\u001d\u001a\u00020\b*\u00020\u00162\b\b\u0003\u0010\u0019\u001a\u00020\u00012\b\b\u0003\u0010\u001a\u001a\u00020\u00012\b\b\u0003\u0010\u001b\u001a\u00020\u00012\b\b\u0003\u0010\u001c\u001a\u00020\u0001H\u0086\b\u001a5\u0010 \u001a\u00020\b*\u00020\u00162\b\b\u0003\u0010\u001e\u001a\u00020\u00012\b\b\u0003\u0010\u001a\u001a\u00020\u00012\b\b\u0003\u0010\u001f\u001a\u00020\u00012\b\b\u0003\u0010\u001c\u001a\u00020\u0001H\u0087\b\"\u0016\u0010\u0017\u001a\u00020\u0001*\u00020\u00008Æ\u0002¢\u0006\u0006\u001a\u0004\b!\u0010\"\"\u0016\u0010&\u001a\u00020#*\u00020\u00008Æ\u0002¢\u0006\u0006\u001a\u0004\b$\u0010%\"\u001b\u0010*\u001a\b\u0012\u0004\u0012\u00020\u00030'*\u00020\u00008F¢\u0006\u0006\u001a\u0004\b(\u0010)\"\u001b\u0010,\u001a\b\u0012\u0004\u0012\u00020\u00030'*\u00020\u00008F¢\u0006\u0006\u001a\u0004\b+\u0010)\u0082\u0002\u0007\n\u0005\b\u009920\u0001¨\u0006-"}, m4115d2 = {"Landroid/view/ViewGroup;", "", "index", "Landroid/view/View;", "get", "view", "", "contains", "Lokhttp3/internal/io/lx5;", "plusAssign", "minusAssign", "isEmpty", "isNotEmpty", "Lkotlin/Function1;", "Lokhttp3/internal/io/o73;", HintConstants.AUTOFILL_HINT_NAME, "action", "forEach", "Lkotlin/Function2;", "forEachIndexed", "", "iterator", "Landroid/view/ViewGroup$MarginLayoutParams;", "size", "setMargins", "left", "top", "right", "bottom", "updateMargins", "start", "end", "updateMarginsRelative", "getSize", "(Landroid/view/ViewGroup;)I", "Lokhttp3/internal/io/j71;", "getIndices", "(Landroid/view/ViewGroup;)Lokhttp3/internal/io/j71;", "indices", "Lokhttp3/internal/io/pk4;", "getChildren", "(Landroid/view/ViewGroup;)Lokhttp3/internal/io/pk4;", "children", "getDescendants", "descendants", "core-ktx_release"}, m4116k = 2, m4117mv = {1, 6, 0})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class ViewGroupKt {
    public static final boolean contains(@zu2 ViewGroup viewGroup, @zu2 View view) {
        fa1.m6826(viewGroup, "<this>");
        fa1.m6826(view, "view");
        return viewGroup.indexOfChild(view) != -1;
    }

    public static final void forEach(@zu2 ViewGroup viewGroup, @zu2 ph0<? super View, lx5> ph0Var) {
        fa1.m6826(viewGroup, "<this>");
        fa1.m6826(ph0Var, "action");
        int childCount = viewGroup.getChildCount();
        for (int i = 0; i < childCount; i++) {
            View childAt = viewGroup.getChildAt(i);
            fa1.m6825(childAt, "getChildAt(index)");
            ph0Var.invoke(childAt);
        }
    }

    public static final void forEachIndexed(@zu2 ViewGroup viewGroup, @zu2 di0<? super Integer, ? super View, lx5> di0Var) {
        fa1.m6826(viewGroup, "<this>");
        fa1.m6826(di0Var, "action");
        int childCount = viewGroup.getChildCount();
        for (int i = 0; i < childCount; i++) {
            Integer valueOf = Integer.valueOf(i);
            View childAt = viewGroup.getChildAt(i);
            fa1.m6825(childAt, "getChildAt(index)");
            di0Var.mo18338invoke(valueOf, childAt);
        }
    }

    @zu2
    public static final View get(@zu2 ViewGroup viewGroup, int i) {
        fa1.m6826(viewGroup, "<this>");
        View childAt = viewGroup.getChildAt(i);
        if (childAt != null) {
            return childAt;
        }
        StringBuilder m15808 = C6668.m15808("Index: ", i, ", Size: ");
        m15808.append(viewGroup.getChildCount());
        throw new IndexOutOfBoundsException(m15808.toString());
    }

    @zu2
    public static final pk4<View> getChildren(@zu2 final ViewGroup viewGroup) {
        fa1.m6826(viewGroup, "<this>");
        return new pk4<View>() { // from class: androidx.core.view.ViewGroupKt$children$1
            @Override // okhttp3.internal.p042io.pk4
            @zu2
            public Iterator<View> iterator() {
                return ViewGroupKt.iterator(viewGroup);
            }
        };
    }

    @zu2
    public static final pk4<View> getDescendants(@zu2 ViewGroup viewGroup) {
        fa1.m6826(viewGroup, "<this>");
        return ka1.m8870(new ViewGroupKt$descendants$1(viewGroup, null));
    }

    @zu2
    public static final j71 getIndices(@zu2 ViewGroup viewGroup) {
        fa1.m6826(viewGroup, "<this>");
        return C6489.m15450(0, viewGroup.getChildCount());
    }

    public static final int getSize(@zu2 ViewGroup viewGroup) {
        fa1.m6826(viewGroup, "<this>");
        return viewGroup.getChildCount();
    }

    public static final boolean isEmpty(@zu2 ViewGroup viewGroup) {
        fa1.m6826(viewGroup, "<this>");
        return viewGroup.getChildCount() == 0;
    }

    public static final boolean isNotEmpty(@zu2 ViewGroup viewGroup) {
        fa1.m6826(viewGroup, "<this>");
        return viewGroup.getChildCount() != 0;
    }

    @zu2
    public static final Iterator<View> iterator(@zu2 ViewGroup viewGroup) {
        fa1.m6826(viewGroup, "<this>");
        return new ViewGroupKt$iterator$1(viewGroup);
    }

    public static final void minusAssign(@zu2 ViewGroup viewGroup, @zu2 View view) {
        fa1.m6826(viewGroup, "<this>");
        fa1.m6826(view, "view");
        viewGroup.removeView(view);
    }

    public static final void plusAssign(@zu2 ViewGroup viewGroup, @zu2 View view) {
        fa1.m6826(viewGroup, "<this>");
        fa1.m6826(view, "view");
        viewGroup.addView(view);
    }

    public static final void setMargins(@zu2 ViewGroup.MarginLayoutParams marginLayoutParams, @Px int i) {
        fa1.m6826(marginLayoutParams, "<this>");
        marginLayoutParams.setMargins(i, i, i, i);
    }

    public static final void updateMargins(@zu2 ViewGroup.MarginLayoutParams marginLayoutParams, @Px int i, @Px int i2, @Px int i3, @Px int i4) {
        fa1.m6826(marginLayoutParams, "<this>");
        marginLayoutParams.setMargins(i, i2, i3, i4);
    }

    public static /* synthetic */ void updateMargins$default(ViewGroup.MarginLayoutParams marginLayoutParams, int i, int i2, int i3, int i4, int i5, Object obj) {
        if ((i5 & 1) != 0) {
            i = marginLayoutParams.leftMargin;
        }
        if ((i5 & 2) != 0) {
            i2 = marginLayoutParams.topMargin;
        }
        if ((i5 & 4) != 0) {
            i3 = marginLayoutParams.rightMargin;
        }
        if ((i5 & 8) != 0) {
            i4 = marginLayoutParams.bottomMargin;
        }
        fa1.m6826(marginLayoutParams, "<this>");
        marginLayoutParams.setMargins(i, i2, i3, i4);
    }

    @RequiresApi(17)
    public static final void updateMarginsRelative(@zu2 ViewGroup.MarginLayoutParams marginLayoutParams, @Px int i, @Px int i2, @Px int i3, @Px int i4) {
        fa1.m6826(marginLayoutParams, "<this>");
        marginLayoutParams.setMarginStart(i);
        marginLayoutParams.topMargin = i2;
        marginLayoutParams.setMarginEnd(i3);
        marginLayoutParams.bottomMargin = i4;
    }

    public static /* synthetic */ void updateMarginsRelative$default(ViewGroup.MarginLayoutParams marginLayoutParams, int i, int i2, int i3, int i4, int i5, Object obj) {
        if ((i5 & 1) != 0) {
            i = marginLayoutParams.getMarginStart();
        }
        if ((i5 & 2) != 0) {
            i2 = marginLayoutParams.topMargin;
        }
        if ((i5 & 4) != 0) {
            i3 = marginLayoutParams.getMarginEnd();
        }
        if ((i5 & 8) != 0) {
            i4 = marginLayoutParams.bottomMargin;
        }
        fa1.m6826(marginLayoutParams, "<this>");
        marginLayoutParams.setMarginStart(i);
        marginLayoutParams.topMargin = i2;
        marginLayoutParams.setMarginEnd(i3);
        marginLayoutParams.bottomMargin = i4;
    }
}
