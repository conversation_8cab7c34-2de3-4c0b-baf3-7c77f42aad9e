package okhttp3.internal.p042io;

/* renamed from: okhttp3.internal.io.v5 */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5392v5 implements gh0 {

    /* renamed from: Ϳ */
    public final xg0 f23704;

    public C5392v5(xg0 xg0Var) {
        this.f23704 = xg0Var;
    }

    /* renamed from: Ϳ */
    public final EnumC4307o0 m13084() {
        return (EnumC4307o0) this.f23704.mo7738("org.apache.ftpserver.data-type", EnumC4307o0.ASCII);
    }

    /* renamed from: Ԩ */
    public final lz5 m13085() {
        return this.f23704.m13957();
    }

    /* renamed from: ԩ */
    public final void m13086(int i) {
        hc1 hc1Var = this.f23704.f25930;
        if (hc1Var instanceof AbstractC7521) {
            ((AbstractC7521) hc1Var).m17165(i, System.currentTimeMillis());
        }
    }

    /* renamed from: Ԫ */
    public final void m13087(int i) {
        xg0 xg0Var = this.f23704;
        hc1 hc1Var = xg0Var.f25930;
        if (hc1Var instanceof AbstractC7521) {
            ((AbstractC7521) hc1Var).m17166(i);
            ((AbstractC7521) xg0Var.f25930).m17167(i, System.currentTimeMillis());
        }
    }
}
