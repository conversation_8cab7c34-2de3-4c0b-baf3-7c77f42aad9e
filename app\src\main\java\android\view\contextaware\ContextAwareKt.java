package android.view.contextaware;

import android.content.Context;
import kotlin.Metadata;
import okhttp3.internal.p042io.C6570;
import okhttp3.internal.p042io.C6698;
import okhttp3.internal.p042io.InterfaceC7155;
import okhttp3.internal.p042io.ph0;
import okhttp3.internal.p042io.wv2;
import okhttp3.internal.p042io.zu2;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000\u0012\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\u001a3\u0010\u0005\u001a\u00028\u0000\"\u0004\b\u0000\u0010\u0000*\u00020\u00012\u0014\b\u0004\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00028\u00000\u0002H\u0086Hø\u0001\u0000¢\u0006\u0004\b\u0005\u0010\u0006\u0082\u0002\u0004\n\u0002\b\u0019¨\u0006\u0007"}, m4115d2 = {"R", "Landroidx/activity/contextaware/ContextAware;", "Lkotlin/Function1;", "Landroid/content/Context;", "onContextAvailable", "withContextAvailable", "(Landroidx/activity/contextaware/ContextAware;Lokhttp3/internal/io/ph0;Lokhttp3/internal/io/ৡ;)Ljava/lang/Object;", "activity-ktx_release"}, m4116k = 2, m4117mv = {1, 6, 0})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class ContextAwareKt {
    @wv2
    public static final <R> Object withContextAvailable(@zu2 ContextAware contextAware, @zu2 ph0<? super Context, ? extends R> ph0Var, @zu2 InterfaceC7155<? super R> interfaceC7155) {
        Context peekAvailableContext = contextAware.peekAvailableContext();
        if (peekAvailableContext != null) {
            return ph0Var.invoke(peekAvailableContext);
        }
        C6570 c6570 = new C6570(C6698.m15836(interfaceC7155), 1);
        c6570.m15594();
        ContextAwareKt$withContextAvailable$2$listener$1 contextAwareKt$withContextAvailable$2$listener$1 = new ContextAwareKt$withContextAvailable$2$listener$1(c6570, ph0Var);
        contextAware.addOnContextAvailableListener(contextAwareKt$withContextAvailable$2$listener$1);
        c6570.mo15598(new ContextAwareKt$withContextAvailable$2$1(contextAware, contextAwareKt$withContextAvailable$2$listener$1));
        return c6570.m15593();
    }

    private static final <R> Object withContextAvailable$$forInline(ContextAware contextAware, ph0<? super Context, ? extends R> ph0Var, InterfaceC7155<? super R> interfaceC7155) {
        Context peekAvailableContext = contextAware.peekAvailableContext();
        if (peekAvailableContext != null) {
            return ph0Var.invoke(peekAvailableContext);
        }
        C6570 c6570 = new C6570(C6698.m15836(interfaceC7155), 1);
        c6570.m15594();
        ContextAwareKt$withContextAvailable$2$listener$1 contextAwareKt$withContextAvailable$2$listener$1 = new ContextAwareKt$withContextAvailable$2$listener$1(c6570, ph0Var);
        contextAware.addOnContextAvailableListener(contextAwareKt$withContextAvailable$2$listener$1);
        c6570.mo15598(new ContextAwareKt$withContextAvailable$2$1(contextAware, contextAwareKt$withContextAvailable$2$listener$1));
        return c6570.m15593();
    }
}
