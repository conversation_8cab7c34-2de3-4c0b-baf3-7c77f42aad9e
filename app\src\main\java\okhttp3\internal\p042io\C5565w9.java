package okhttp3.internal.p042io;

import java.util.List;

/* renamed from: okhttp3.internal.io.w9 */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5565w9 extends C6125 {

    /* renamed from: ԩ */
    @zu2
    public final tu1 f24721;

    /* renamed from: okhttp3.internal.io.w9$Ϳ, reason: contains not printable characters */
    public static final class C9462 extends lv1 implements ph0<kl2, tu1> {

        /* renamed from: ၥ */
        public final /* synthetic */ tu1 f24722;

        /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
        public C9462(tu1 tu1Var) {
            super(1);
            this.f24722 = tu1Var;
        }

        @Override // okhttp3.internal.p042io.ph0
        public final tu1 invoke(kl2 kl2Var) {
            fa1.m6826(kl2Var, "it");
            return this.f24722;
        }
    }

    public C5565w9(@zu2 List<? extends AbstractC7507<?>> list, @zu2 tu1 tu1Var) {
        super(list, new C9462(tu1Var));
        this.f24721 = tu1Var;
    }
}
