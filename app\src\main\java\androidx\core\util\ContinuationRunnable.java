package androidx.core.util;

import com.stardust.autojs.engine.RhinoJavaScriptEngine;
import com.stardust.autojs.project.ProjectConfig;
import java.util.concurrent.atomic.AtomicBoolean;
import kotlin.Metadata;
import okhttp3.internal.p042io.InterfaceC7155;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.lf2;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.zu2;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0002\u0018\u00002\u00020\u00012\u00020\u0002B\u0015\u0012\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00030\u0007¢\u0006\u0004\b\t\u0010\nJ\b\u0010\u0004\u001a\u00020\u0003H\u0016J\b\u0010\u0006\u001a\u00020\u0005H\u0016¨\u0006\u000b"}, m4115d2 = {"Landroidx/core/util/ContinuationRunnable;", "Ljava/lang/Runnable;", "Ljava/util/concurrent/atomic/AtomicBoolean;", "Lokhttp3/internal/io/lx5;", "run", "", "toString", "Lokhttp3/internal/io/ৡ;", ProjectConfig.FEATURE_CONTINUATION, RhinoJavaScriptEngine.SOURCE_NAME_INIT, "(Lokhttp3/internal/io/ৡ;)V", "core-ktx_release"}, m4116k = 1, m4117mv = {1, 6, 0})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
final class ContinuationRunnable extends AtomicBoolean implements Runnable {

    @zu2
    private final InterfaceC7155<lx5> continuation;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    /* JADX WARN: Multi-variable type inference failed */
    public ContinuationRunnable(@zu2 InterfaceC7155<? super lx5> interfaceC7155) {
        super(false);
        fa1.m6826(interfaceC7155, ProjectConfig.FEATURE_CONTINUATION);
        this.continuation = interfaceC7155;
    }

    @Override // java.lang.Runnable
    public void run() {
        if (compareAndSet(false, true)) {
            this.continuation.resumeWith(lx5.f14876);
        }
    }

    @Override // java.util.concurrent.atomic.AtomicBoolean
    @zu2
    public String toString() {
        StringBuilder m9240 = lf2.m9240("ContinuationRunnable(ran = ");
        m9240.append(get());
        m9240.append(')');
        return m9240.toString();
    }
}
