package okhttp3.internal.p042io;

/* renamed from: okhttp3.internal.io.xs */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes3.dex */
public final class C5741xs extends lv1 implements ph0<m71, m71> {

    /* renamed from: ၥ */
    public final /* synthetic */ ph0<Integer, Integer> f26160;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    /* JADX WARN: Multi-variable type inference failed */
    public C5741xs(ph0<? super Integer, Integer> ph0Var) {
        super(1);
        this.f26160 = ph0Var;
    }

    @Override // okhttp3.internal.p042io.ph0
    public final m71 invoke(m71 m71Var) {
        long j = m71Var.f15070;
        return new m71(n71.m9931(this.f26160.invoke(Integer.valueOf((int) (j >> 32))).intValue(), m71.m9614(j)));
    }
}
