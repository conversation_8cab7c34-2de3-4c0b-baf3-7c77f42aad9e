package okhttp3.internal.p042io;

import okhttp3.internal.p042io.InterfaceC6710;

/* renamed from: okhttp3.internal.io.xw */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5758xw extends lv1 implements ph0<InterfaceC6710.InterfaceC9868, AbstractC5864yw> {

    /* renamed from: ၥ */
    public static final C5758xw f26226 = new C5758xw();

    public C5758xw() {
        super(1);
    }

    @Override // okhttp3.internal.p042io.ph0
    public final AbstractC5864yw invoke(InterfaceC6710.InterfaceC9868 interfaceC9868) {
        InterfaceC6710.InterfaceC9868 interfaceC98682 = interfaceC9868;
        if (interfaceC98682 instanceof AbstractC5864yw) {
            return (AbstractC5864yw) interfaceC98682;
        }
        return null;
    }
}
