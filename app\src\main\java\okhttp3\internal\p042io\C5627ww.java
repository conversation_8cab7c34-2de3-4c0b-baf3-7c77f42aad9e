package okhttp3.internal.p042io;

import java.io.IOException;
import java.lang.annotation.Annotation;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Objects;
import java.util.concurrent.Executor;
import okhttp3.internal.p042io.InterfaceC7108;

/* renamed from: okhttp3.internal.io.ww */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5627ww extends InterfaceC7108.AbstractC10014 {

    /* renamed from: Ϳ */
    public final Executor f25194;

    /* renamed from: okhttp3.internal.io.ww$Ϳ, reason: contains not printable characters */
    public class C9474 implements InterfaceC7108<Object, InterfaceC7310<?>> {

        /* renamed from: Ϳ */
        public final /* synthetic */ Type f25195;

        public C9474(Type type) {
            this.f25195 = type;
        }

        @Override // okhttp3.internal.p042io.InterfaceC7108
        /* renamed from: Ϳ */
        public final InterfaceC7310<?> mo5955(InterfaceC7310<Object> interfaceC7310) {
            return new C9475(C5627ww.this.f25194, interfaceC7310);
        }

        @Override // okhttp3.internal.p042io.InterfaceC7108
        /* renamed from: Ԩ */
        public final Type mo5956() {
            return this.f25195;
        }
    }

    /* renamed from: okhttp3.internal.io.ww$Ԩ, reason: contains not printable characters */
    public static final class C9475<T> implements InterfaceC7310<T> {

        /* renamed from: ၥ */
        public final Executor f25197;

        /* renamed from: ၦ */
        public final InterfaceC7310<T> f25198;

        /* renamed from: okhttp3.internal.io.ww$Ԩ$Ϳ, reason: contains not printable characters */
        public class C9476 implements InterfaceC6927<T> {

            /* renamed from: Ϳ */
            public final /* synthetic */ InterfaceC6927 f25199;

            /* renamed from: okhttp3.internal.io.ww$Ԩ$Ϳ$Ϳ, reason: contains not printable characters */
            public class RunnableC9477 implements Runnable {

                /* renamed from: ၥ */
                public final /* synthetic */ t34 f25201;

                public RunnableC9477(t34 t34Var) {
                    this.f25201 = t34Var;
                }

                @Override // java.lang.Runnable
                public final void run() {
                    if (C9475.this.f25198.isCanceled()) {
                        C9476 c9476 = C9476.this;
                        c9476.f25199.mo13746(C9475.this, new IOException("Canceled"));
                    } else {
                        C9476 c94762 = C9476.this;
                        c94762.f25199.mo13747(C9475.this, this.f25201);
                    }
                }
            }

            /* renamed from: okhttp3.internal.io.ww$Ԩ$Ϳ$Ԩ, reason: contains not printable characters */
            public class RunnableC9478 implements Runnable {

                /* renamed from: ၥ */
                public final /* synthetic */ Throwable f25203;

                public RunnableC9478(Throwable th) {
                    this.f25203 = th;
                }

                @Override // java.lang.Runnable
                public final void run() {
                    C9476 c9476 = C9476.this;
                    c9476.f25199.mo13746(C9475.this, this.f25203);
                }
            }

            public C9476(InterfaceC6927 interfaceC6927) {
                this.f25199 = interfaceC6927;
            }

            @Override // okhttp3.internal.p042io.InterfaceC6927
            /* renamed from: Ϳ */
            public final void mo13746(InterfaceC7310<T> interfaceC7310, Throwable th) {
                C9475.this.f25197.execute(new RunnableC9478(th));
            }

            @Override // okhttp3.internal.p042io.InterfaceC6927
            /* renamed from: Ԩ */
            public final void mo13747(InterfaceC7310<T> interfaceC7310, t34<T> t34Var) {
                C9475.this.f25197.execute(new RunnableC9477(t34Var));
            }
        }

        public C9475(Executor executor, InterfaceC7310<T> interfaceC7310) {
            this.f25197 = executor;
            this.f25198 = interfaceC7310;
        }

        @Override // okhttp3.internal.p042io.InterfaceC7310
        public final void cancel() {
            this.f25198.cancel();
        }

        @Override // okhttp3.internal.p042io.InterfaceC7310
        public final InterfaceC7310<T> clone() {
            return new C9475(this.f25197, this.f25198.clone());
        }

        @Override // okhttp3.internal.p042io.InterfaceC7310
        public final t34<T> execute() {
            return this.f25198.execute();
        }

        @Override // okhttp3.internal.p042io.InterfaceC7310
        public final boolean isCanceled() {
            return this.f25198.isCanceled();
        }

        @Override // okhttp3.internal.p042io.InterfaceC7310
        /* renamed from: ޤ */
        public final void mo10550(InterfaceC6927<T> interfaceC6927) {
            Objects.requireNonNull(interfaceC6927, "callback == null");
            this.f25198.mo10550(new C9476(interfaceC6927));
        }
    }

    public C5627ww(Executor executor) {
        this.f25194 = executor;
    }

    @Override // okhttp3.internal.p042io.InterfaceC7108.AbstractC10014
    /* renamed from: Ϳ */
    public final InterfaceC7108<?, ?> mo6370(Type type, Annotation[] annotationArr, m44 m44Var) {
        if (c06.m5495(type) != InterfaceC7310.class) {
            return null;
        }
        if (type instanceof ParameterizedType) {
            return new C9474(c06.m5494(0, (ParameterizedType) type));
        }
        throw new IllegalArgumentException("Call return type must be parameterized as Call<Foo> or Call<? extends Foo>");
    }
}
