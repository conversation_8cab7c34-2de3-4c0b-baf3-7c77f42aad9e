package androidx.core.graphics;

import android.annotation.SuppressLint;
import android.graphics.Bitmap;
import android.graphics.ImageDecoder;
import android.graphics.drawable.Drawable;
import androidx.annotation.RequiresApi;
import androidx.autofill.HintConstants;
import com.stardust.autojs.engine.ScriptEngine;
import kotlin.Metadata;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.fi0;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.zu2;

@Metadata(m4113bv = {}, m4114d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\u001aU\u0010\f\u001a\u00020\u000b*\u00020\u00002C\b\u0004\u0010\n\u001a=\u0012\u0004\u0012\u00020\u0002\u0012\u0013\u0012\u00110\u0003¢\u0006\f\b\u0004\u0012\b\b\u0005\u0012\u0004\b\b(\u0006\u0012\u0013\u0012\u00110\u0000¢\u0006\f\b\u0004\u0012\b\b\u0005\u0012\u0004\b\b(\u0007\u0012\u0004\u0012\u00020\b0\u0001¢\u0006\u0002\b\tH\u0087\bø\u0001\u0000\u001aU\u0010\u000e\u001a\u00020\r*\u00020\u00002C\b\u0004\u0010\n\u001a=\u0012\u0004\u0012\u00020\u0002\u0012\u0013\u0012\u00110\u0003¢\u0006\f\b\u0004\u0012\b\b\u0005\u0012\u0004\b\b(\u0006\u0012\u0013\u0012\u00110\u0000¢\u0006\f\b\u0004\u0012\b\b\u0005\u0012\u0004\b\b(\u0007\u0012\u0004\u0012\u00020\b0\u0001¢\u0006\u0002\b\tH\u0087\bø\u0001\u0000\u0082\u0002\u0007\n\u0005\b\u009920\u0001¨\u0006\u000f"}, m4115d2 = {"Landroid/graphics/ImageDecoder$Source;", "Lkotlin/Function3;", "Landroid/graphics/ImageDecoder;", "Landroid/graphics/ImageDecoder$ImageInfo;", "Lokhttp3/internal/io/o73;", HintConstants.AUTOFILL_HINT_NAME, "info", ScriptEngine.TAG_SOURCE, "Lokhttp3/internal/io/lx5;", "Lokhttp3/internal/io/f00;", "action", "Landroid/graphics/Bitmap;", "decodeBitmap", "Landroid/graphics/drawable/Drawable;", "decodeDrawable", "core-ktx_release"}, m4116k = 2, m4117mv = {1, 6, 0})
@SuppressLint({"ClassVerificationFailure"})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class ImageDecoderKt {
    @RequiresApi(28)
    @zu2
    public static final Bitmap decodeBitmap(@zu2 ImageDecoder.Source source, @zu2 final fi0<? super ImageDecoder, ? super ImageDecoder.ImageInfo, ? super ImageDecoder.Source, lx5> fi0Var) {
        fa1.m6826(source, "<this>");
        fa1.m6826(fi0Var, "action");
        Bitmap decodeBitmap = ImageDecoder.decodeBitmap(source, new ImageDecoder.OnHeaderDecodedListener() { // from class: androidx.core.graphics.ImageDecoderKt$decodeBitmap$1
            @Override // android.graphics.ImageDecoder.OnHeaderDecodedListener
            public final void onHeaderDecoded(@zu2 ImageDecoder imageDecoder, @zu2 ImageDecoder.ImageInfo imageInfo, @zu2 ImageDecoder.Source source2) {
                fa1.m6826(imageDecoder, "decoder");
                fa1.m6826(imageInfo, "info");
                fa1.m6826(source2, ScriptEngine.TAG_SOURCE);
                fi0Var.invoke(imageDecoder, imageInfo, source2);
            }
        });
        fa1.m6825(decodeBitmap, "crossinline action: Imag…ction(info, source)\n    }");
        return decodeBitmap;
    }

    @RequiresApi(28)
    @zu2
    public static final Drawable decodeDrawable(@zu2 ImageDecoder.Source source, @zu2 final fi0<? super ImageDecoder, ? super ImageDecoder.ImageInfo, ? super ImageDecoder.Source, lx5> fi0Var) {
        fa1.m6826(source, "<this>");
        fa1.m6826(fi0Var, "action");
        Drawable decodeDrawable = ImageDecoder.decodeDrawable(source, new ImageDecoder.OnHeaderDecodedListener() { // from class: androidx.core.graphics.ImageDecoderKt$decodeDrawable$1
            @Override // android.graphics.ImageDecoder.OnHeaderDecodedListener
            public final void onHeaderDecoded(@zu2 ImageDecoder imageDecoder, @zu2 ImageDecoder.ImageInfo imageInfo, @zu2 ImageDecoder.Source source2) {
                fa1.m6826(imageDecoder, "decoder");
                fa1.m6826(imageInfo, "info");
                fa1.m6826(source2, ScriptEngine.TAG_SOURCE);
                fi0Var.invoke(imageDecoder, imageInfo, source2);
            }
        });
        fa1.m6825(decodeDrawable, "crossinline action: Imag…ction(info, source)\n    }");
        return decodeDrawable;
    }
}
