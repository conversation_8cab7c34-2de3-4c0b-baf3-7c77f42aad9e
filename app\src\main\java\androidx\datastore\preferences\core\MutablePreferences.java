package androidx.datastore.preferences.core;

import androidx.datastore.preferences.core.Preferences;
import androidx.exifinterface.media.ExifInterface;
import com.stardust.autojs.engine.RhinoJavaScriptEngine;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import kotlin.Metadata;
import okhttp3.internal.p042io.C2849b5;
import okhttp3.internal.p042io.C6802;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.wv2;
import okhttp3.internal.p042io.zu2;
import org.mozilla.javascript.optimizer.Codegen;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000V\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010$\n\u0002\u0010\u0000\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0011\n\u0002\b\u0007\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010%\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0006\u0018\u00002\u00020\u0001B-\b\u0000\u0012\u0018\b\u0002\u0010)\u001a\u0012\u0012\b\u0012\u0006\u0012\u0002\b\u00030\t\u0012\u0004\u0012\u00020\u00100(\u0012\b\b\u0002\u00100\u001a\u00020\u000b¢\u0006\u0004\b1\u00102J\u000f\u0010\u0005\u001a\u00020\u0002H\u0000¢\u0006\u0004\b\u0003\u0010\u0004J\u000f\u0010\u0007\u001a\u00020\u0002H\u0000¢\u0006\u0004\b\u0006\u0010\u0004J\u001d\u0010\f\u001a\u00020\u000b\"\u0004\b\u0000\u0010\b2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00028\u00000\tH\u0096\u0002J&\u0010\r\u001a\u0004\u0018\u00018\u0000\"\u0004\b\u0000\u0010\b2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00028\u00000\tH\u0096\u0002¢\u0006\u0004\b\r\u0010\u000eJ\u0018\u0010\u0011\u001a\u0012\u0012\b\u0012\u0006\u0012\u0002\b\u00030\t\u0012\u0004\u0012\u00020\u00100\u000fH\u0016J,\u0010\u0013\u001a\u00020\u0002\"\u0004\b\u0000\u0010\b2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00028\u00000\t2\u0006\u0010\u0012\u001a\u00028\u0000H\u0086\u0002¢\u0006\u0004\b\u0013\u0010\u0014J%\u0010\u0016\u001a\u00020\u00022\n\u0010\n\u001a\u0006\u0012\u0002\b\u00030\t2\b\u0010\u0012\u001a\u0004\u0018\u00010\u0010H\u0000¢\u0006\u0004\b\u0015\u0010\u0014J\u0011\u0010\u0018\u001a\u00020\u00022\u0006\u0010\u0017\u001a\u00020\u0001H\u0086\u0002J\u0015\u0010\u0018\u001a\u00020\u00022\n\u0010\u001a\u001a\u0006\u0012\u0002\b\u00030\u0019H\u0086\u0002J\u0015\u0010\u001b\u001a\u00020\u00022\n\u0010\n\u001a\u0006\u0012\u0002\b\u00030\tH\u0086\u0002J)\u0010\u001e\u001a\u00020\u00022\u001a\u0010\u001d\u001a\u000e\u0012\n\b\u0001\u0012\u0006\u0012\u0002\b\u00030\u00190\u001c\"\u0006\u0012\u0002\b\u00030\u0019¢\u0006\u0004\b\u001e\u0010\u001fJ!\u0010 \u001a\u00028\u0000\"\u0004\b\u0000\u0010\b2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00028\u00000\t¢\u0006\u0004\b \u0010\u000eJ\u0006\u0010!\u001a\u00020\u0002J\u0013\u0010#\u001a\u00020\u000b2\b\u0010\"\u001a\u0004\u0018\u00010\u0010H\u0096\u0002J\b\u0010%\u001a\u00020$H\u0016J\b\u0010'\u001a\u00020&H\u0016R*\u0010)\u001a\u0012\u0012\b\u0012\u0006\u0012\u0002\b\u00030\t\u0012\u0004\u0012\u00020\u00100(8\u0000X\u0080\u0004¢\u0006\f\n\u0004\b)\u0010*\u001a\u0004\b+\u0010,R\u0014\u0010.\u001a\u00020-8\u0002X\u0082\u0004¢\u0006\u0006\n\u0004\b.\u0010/¨\u00063"}, m4115d2 = {"Landroidx/datastore/preferences/core/MutablePreferences;", "Landroidx/datastore/preferences/core/Preferences;", "Lokhttp3/internal/io/lx5;", "checkNotFrozen$datastore_preferences_core", Codegen.TEMPLATE_LITERAL_INIT_METHOD_SIGNATURE, "checkNotFrozen", "freeze$datastore_preferences_core", "freeze", ExifInterface.GPS_DIRECTION_TRUE, "Landroidx/datastore/preferences/core/Preferences$Key;", "key", "", "contains", "get", "(Landroidx/datastore/preferences/core/Preferences$Key;)Ljava/lang/Object;", "", "", "asMap", "value", "set", "(Landroidx/datastore/preferences/core/Preferences$Key;Ljava/lang/Object;)V", "setUnchecked$datastore_preferences_core", "setUnchecked", "prefs", "plusAssign", "Landroidx/datastore/preferences/core/Preferences$Pair;", "pair", "minusAssign", "", "pairs", "putAll", "([Landroidx/datastore/preferences/core/Preferences$Pair;)V", "remove", "clear", "other", "equals", "", "hashCode", "", "toString", "", "preferencesMap", "Ljava/util/Map;", "getPreferencesMap$datastore_preferences_core", "()Ljava/util/Map;", "Ljava/util/concurrent/atomic/AtomicBoolean;", "frozen", "Ljava/util/concurrent/atomic/AtomicBoolean;", "startFrozen", RhinoJavaScriptEngine.SOURCE_NAME_INIT, "(Ljava/util/Map;Z)V", "datastore-preferences-core"}, m4116k = 1, m4117mv = {1, 5, 1})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class MutablePreferences extends Preferences {

    @zu2
    private final AtomicBoolean frozen;

    @zu2
    private final Map<Preferences.Key<?>, Object> preferencesMap;

    public MutablePreferences() {
        this(null, false, 3, 0 == true ? 1 : 0);
    }

    public MutablePreferences(@zu2 Map<Preferences.Key<?>, Object> map, boolean z) {
        fa1.m6826(map, "preferencesMap");
        this.preferencesMap = map;
        this.frozen = new AtomicBoolean(z);
    }

    public /* synthetic */ MutablePreferences(Map map, boolean z, int i, C2849b5 c2849b5) {
        this((i & 1) != 0 ? new LinkedHashMap() : map, (i & 2) != 0 ? true : z);
    }

    @Override // androidx.datastore.preferences.core.Preferences
    @zu2
    public Map<Preferences.Key<?>, Object> asMap() {
        Map<Preferences.Key<?>, Object> unmodifiableMap = Collections.unmodifiableMap(this.preferencesMap);
        fa1.m6825(unmodifiableMap, "unmodifiableMap(preferencesMap)");
        return unmodifiableMap;
    }

    public final void checkNotFrozen$datastore_preferences_core() {
        if (!(!this.frozen.get())) {
            throw new IllegalStateException("Do mutate preferences once returned to DataStore.".toString());
        }
    }

    public final void clear() {
        checkNotFrozen$datastore_preferences_core();
        this.preferencesMap.clear();
    }

    @Override // androidx.datastore.preferences.core.Preferences
    public <T> boolean contains(@zu2 Preferences.Key<T> key) {
        fa1.m6826(key, "key");
        return this.preferencesMap.containsKey(key);
    }

    public boolean equals(@wv2 Object other) {
        if (other instanceof MutablePreferences) {
            return fa1.m6818(this.preferencesMap, ((MutablePreferences) other).preferencesMap);
        }
        return false;
    }

    public final void freeze$datastore_preferences_core() {
        this.frozen.set(true);
    }

    @Override // androidx.datastore.preferences.core.Preferences
    @wv2
    public <T> T get(@zu2 Preferences.Key<T> key) {
        fa1.m6826(key, "key");
        return (T) this.preferencesMap.get(key);
    }

    @zu2
    public final Map<Preferences.Key<?>, Object> getPreferencesMap$datastore_preferences_core() {
        return this.preferencesMap;
    }

    public int hashCode() {
        return this.preferencesMap.hashCode();
    }

    public final void minusAssign(@zu2 Preferences.Key<?> key) {
        fa1.m6826(key, "key");
        checkNotFrozen$datastore_preferences_core();
        remove(key);
    }

    public final void plusAssign(@zu2 Preferences.Pair<?> pair) {
        fa1.m6826(pair, "pair");
        checkNotFrozen$datastore_preferences_core();
        putAll(pair);
    }

    public final void plusAssign(@zu2 Preferences preferences) {
        fa1.m6826(preferences, "prefs");
        checkNotFrozen$datastore_preferences_core();
        this.preferencesMap.putAll(preferences.asMap());
    }

    public final void putAll(@zu2 Preferences.Pair<?>... pairs) {
        fa1.m6826(pairs, "pairs");
        checkNotFrozen$datastore_preferences_core();
        for (Preferences.Pair<?> pair : pairs) {
            setUnchecked$datastore_preferences_core(pair.getKey$datastore_preferences_core(), pair.getValue$datastore_preferences_core());
        }
    }

    public final <T> T remove(@zu2 Preferences.Key<T> key) {
        fa1.m6826(key, "key");
        checkNotFrozen$datastore_preferences_core();
        return (T) this.preferencesMap.remove(key);
    }

    public final <T> void set(@zu2 Preferences.Key<T> key, T value) {
        fa1.m6826(key, "key");
        setUnchecked$datastore_preferences_core(key, value);
    }

    public final void setUnchecked$datastore_preferences_core(@zu2 Preferences.Key<?> key, @wv2 Object value) {
        Map<Preferences.Key<?>, Object> map;
        fa1.m6826(key, "key");
        checkNotFrozen$datastore_preferences_core();
        if (value == null) {
            remove(key);
            return;
        }
        if (value instanceof Set) {
            map = this.preferencesMap;
            value = Collections.unmodifiableSet(C6802.m16034((Iterable) value));
            fa1.m6825(value, "unmodifiableSet(value.toSet())");
        } else {
            map = this.preferencesMap;
        }
        map.put(key, value);
    }

    @zu2
    public String toString() {
        return C6802.m16011(this.preferencesMap.entrySet(), ",\n", "{\n", "\n}", MutablePreferences$toString$1.INSTANCE, 24);
    }
}
