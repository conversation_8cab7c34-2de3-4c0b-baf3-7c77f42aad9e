package androidx.core.util;

import android.annotation.SuppressLint;
import android.util.Half;
import androidx.annotation.RequiresApi;
import kotlin.Metadata;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.zu2;

@Metadata(m4114d1 = {"\u0000\u0018\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0006\n\u0002\u0010\u0007\n\u0002\u0010\n\n\u0002\u0010\u000e\n\u0000\u001a\r\u0010\u0000\u001a\u00020\u0001*\u00020\u0002H\u0087\b\u001a\r\u0010\u0000\u001a\u00020\u0001*\u00020\u0003H\u0087\b\u001a\r\u0010\u0000\u001a\u00020\u0001*\u00020\u0004H\u0087\b\u001a\r\u0010\u0000\u001a\u00020\u0001*\u00020\u0005H\u0087\b¨\u0006\u0006"}, m4115d2 = {"toHalf", "Landroid/util/Half;", "", "", "", "", "core-ktx_release"}, m4116k = 2, m4117mv = {1, 6, 0}, m4119xi = 48)
@SuppressLint({"ClassVerificationFailure"})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class HalfKt {
    @RequiresApi(26)
    @zu2
    public static final Half toHalf(double d) {
        Half valueOf = Half.valueOf((float) d);
        fa1.m6825(valueOf, "valueOf(this)");
        return valueOf;
    }

    @RequiresApi(26)
    @zu2
    public static final Half toHalf(float f) {
        Half valueOf = Half.valueOf(f);
        fa1.m6825(valueOf, "valueOf(this)");
        return valueOf;
    }

    @RequiresApi(26)
    @zu2
    public static final Half toHalf(@zu2 String str) {
        fa1.m6826(str, "<this>");
        Half valueOf = Half.valueOf(str);
        fa1.m6825(valueOf, "valueOf(this)");
        return valueOf;
    }

    @RequiresApi(26)
    @zu2
    public static final Half toHalf(short s) {
        Half valueOf = Half.valueOf(s);
        fa1.m6825(valueOf, "valueOf(this)");
        return valueOf;
    }
}
