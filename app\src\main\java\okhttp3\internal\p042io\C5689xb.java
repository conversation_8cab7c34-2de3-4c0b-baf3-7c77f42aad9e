package okhttp3.internal.p042io;

import java.util.Iterator;
import okhttp3.internal.p042io.AbstractC4102lr;
import okhttp3.internal.p042io.AbstractC6243;

/* renamed from: okhttp3.internal.io.xb */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5689xb implements Iterable<C3914kc> {

    /* renamed from: ၥ */
    public final /* synthetic */ AbstractC6243 f25767;

    /* renamed from: ၦ */
    public final /* synthetic */ int f25768;

    /* renamed from: ၮ */
    public final /* synthetic */ int f25769;

    /* renamed from: ၯ */
    public final /* synthetic */ Iterator f25770;

    /* renamed from: ၰ */
    public final /* synthetic */ boolean f25771 = true;

    /* renamed from: ၵ */
    public final /* synthetic */ C5575wb f25772;

    /* renamed from: okhttp3.internal.io.xb$Ϳ, reason: contains not printable characters */
    public class C9482 extends i46<C3914kc> {

        /* renamed from: ၯ */
        public int f25773;

        /* renamed from: ၰ */
        @vv2
        public a11 f25774;

        /* renamed from: ၵ */
        public int f25775;

        /* renamed from: ၶ */
        public final /* synthetic */ AbstractC4102lr f25776;

        /* renamed from: ၷ */
        public final /* synthetic */ AbstractC6243.InterfaceC9621 f25777;

        /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
        public C9482(C4670qe c4670qe, int i, AbstractC4102lr abstractC4102lr, AbstractC6243.InterfaceC9621 interfaceC9621) {
            super(c4670qe, i);
            this.f25776 = abstractC4102lr;
            this.f25777 = interfaceC9621;
        }

        @Override // okhttp3.internal.p042io.i46
        @vv2
        /* renamed from: ԩ */
        public final C3914kc mo4572(@pu2 C4694qf c4694qf) {
            C3914kc c3914kc;
            a11 a11Var;
            a11 m4322;
            do {
                int i = this.f25773 + 1;
                this.f25773 = i;
                C5689xb c5689xb = C5689xb.this;
                C5575wb c5575wb = c5689xb.f25772;
                if (i <= c5575wb.f24839) {
                    Iterator it = c5689xb.f25770;
                    int intValue = it != null ? ((Integer) it.next()).intValue() : 7;
                    C5575wb c5575wb2 = C5689xb.this.f25772;
                    c3914kc = new C3914kc(c5575wb2.f24832, c4694qf, c5575wb2, this.f25775, this.f25776, this.f25777, intValue);
                    a11Var = this.f25774;
                    m4322 = a11.m4322(c3914kc);
                    this.f25774 = m4322;
                    this.f25775 = c3914kc.f13712;
                    if (!C5689xb.this.f25771 || a11Var == null) {
                        break;
                    }
                } else {
                    c5575wb.f24836 = c4694qf.f18583;
                    this.f32222 = 3;
                    return null;
                }
            } while (a11Var.equals(m4322));
            return c3914kc;
        }
    }

    public C5689xb(C5575wb c5575wb, AbstractC6243 abstractC6243, int i, int i2, Iterator it) {
        this.f25772 = c5575wb;
        this.f25767 = abstractC6243;
        this.f25768 = i;
        this.f25769 = i2;
        this.f25770 = it;
    }

    @Override // java.lang.Iterable
    @pu2
    public final Iterator<C3914kc> iterator() {
        AbstractC6243.InterfaceC9621 mo14996 = this.f25767.mo14996();
        C3318fc c3318fc = this.f25772.f24832;
        int i = this.f25768;
        return new C9482(this.f25772.f24832.f9943, this.f25769, i == 0 ? AbstractC4102lr.f14743 : new AbstractC4102lr.C9187(c3318fc, i), mo14996);
    }
}
