package okhttp3.internal.p042io;

/* renamed from: okhttp3.internal.io.v8 */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5448v8 extends lv1 implements ph0<dt5, CharSequence> {

    /* renamed from: ၥ */
    public final /* synthetic */ C5287u8 f23850;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public C5448v8(C5287u8 c5287u8) {
        super(1);
        this.f23850 = c5287u8;
    }

    @Override // okhttp3.internal.p042io.ph0
    public final CharSequence invoke(dt5 dt5Var) {
        dt5 dt5Var2 = dt5Var;
        fa1.m6826(dt5Var2, "it");
        if (dt5Var2.mo6200()) {
            return "*";
        }
        C5287u8 c5287u8 = this.f23850;
        tu1 type = dt5Var2.getType();
        fa1.m6825(type, "it.type");
        String mo12305 = c5287u8.mo12305(type);
        if (dt5Var2.mo6199() == l46.INVARIANT) {
            return mo12305;
        }
        return dt5Var2.mo6199() + ' ' + mo12305;
    }
}
