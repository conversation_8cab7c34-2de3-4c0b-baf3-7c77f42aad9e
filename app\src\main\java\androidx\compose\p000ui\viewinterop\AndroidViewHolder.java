package androidx.compose.p000ui.viewinterop;

import android.content.Context;
import android.graphics.Rect;
import android.graphics.Region;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.view.C10501ViewTreeSavedStateRegistryOwner;
import android.view.SavedStateRegistryOwner;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import androidx.compose.p000ui.platform.AndroidComposeView;
import androidx.constraintlayout.core.motion.utils.TypedValues;
import androidx.core.view.NestedScrollingParent3;
import androidx.core.view.NestedScrollingParentHelper;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.ViewTreeLifecycleOwner;
import com.stardust.autojs.engine.RhinoJavaScriptEngine;
import java.util.List;
import java.util.Objects;
import kotlin.Metadata;
import okhttp3.internal.p042io.AbstractC6779;
import okhttp3.internal.p042io.C2878bi;
import okhttp3.internal.p042io.C4350o9;
import okhttp3.internal.p042io.C5849yq;
import okhttp3.internal.p042io.C6354;
import okhttp3.internal.p042io.C6489;
import okhttp3.internal.p042io.C6814;
import okhttp3.internal.p042io.C7024;
import okhttp3.internal.p042io.C7409;
import okhttp3.internal.p042io.C7847;
import okhttp3.internal.p042io.EnumC7329;
import okhttp3.internal.p042io.InterfaceC4988s2;
import okhttp3.internal.p042io.InterfaceC5066sk;
import okhttp3.internal.p042io.InterfaceC5282u7;
import okhttp3.internal.p042io.InterfaceC7155;
import okhttp3.internal.p042io.InterfaceC7598;
import okhttp3.internal.p042io.InterfaceC7881;
import okhttp3.internal.p042io.RunnableC7885;
import okhttp3.internal.p042io.bc3;
import okhttp3.internal.p042io.bf2;
import okhttp3.internal.p042io.bw1;
import okhttp3.internal.p042io.bz5;
import okhttp3.internal.p042io.di0;
import okhttp3.internal.p042io.ea1;
import okhttp3.internal.p042io.ec6;
import okhttp3.internal.p042io.er2;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.fr2;
import okhttp3.internal.p042io.g03;
import okhttp3.internal.p042io.gx1;
import okhttp3.internal.p042io.lv1;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.m91;
import okhttp3.internal.p042io.nh0;
import okhttp3.internal.p042io.p56;
import okhttp3.internal.p042io.ph0;
import okhttp3.internal.p042io.qf3;
import okhttp3.internal.p042io.rf3;
import okhttp3.internal.p042io.rk2;
import okhttp3.internal.p042io.u75;
import okhttp3.internal.p042io.ue2;
import okhttp3.internal.p042io.ve4;
import okhttp3.internal.p042io.wv2;
import okhttp3.internal.p042io.xu4;
import okhttp3.internal.p042io.xv3;
import okhttp3.internal.p042io.xv4;
import okhttp3.internal.p042io.y04;
import okhttp3.internal.p042io.y33;
import okhttp3.internal.p042io.ye2;
import okhttp3.internal.p042io.yg3;
import okhttp3.internal.p042io.ym2;
import okhttp3.internal.p042io.ze2;
import okhttp3.internal.p042io.zu2;
import org.opencv.videoio.Videoio;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000\u009e\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u0015\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u000f\n\u0002\u0010\u0007\n\u0002\b\r\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\f\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\b \u0018\u00002\u00020\u00012\u00020\u0002B!\u0012\u0006\u0010n\u001a\u00020m\u0012\b\u0010p\u001a\u0004\u0018\u00010o\u0012\u0006\u0010r\u001a\u00020q¢\u0006\u0004\bs\u0010tJ\u0006\u0010\u0004\u001a\u00020\u0003J\n\u0010\u0006\u001a\u0004\u0018\u00010\u0005H\u0016J\u0010\u0010\t\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\u0007H\u0016J\u001e\u0010\u000f\u001a\u0004\u0018\u00010\u000e2\b\u0010\u000b\u001a\u0004\u0018\u00010\n2\b\u0010\r\u001a\u0004\u0018\u00010\fH\u0016J\u0018\u0010\u0013\u001a\u00020\u00032\u0006\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u0012\u001a\u00020\u0010H\u0016J\u0012\u0010\u0016\u001a\u00020\u00072\b\u0010\u0015\u001a\u0004\u0018\u00010\u0014H\u0016J\b\u0010\u0017\u001a\u00020\u0007H\u0016J(\u0010\u001b\u001a\u00020\u00072\u0006\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u0012\u001a\u00020\u00102\u0006\u0010\u0019\u001a\u00020\u00182\u0006\u0010\u001a\u001a\u00020\u0018H\u0016J\b\u0010\u001c\u001a\u00020\u0018H\u0016J(\u0010\u001d\u001a\u00020\u00032\u0006\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u0012\u001a\u00020\u00102\u0006\u0010\u0019\u001a\u00020\u00182\u0006\u0010\u001a\u001a\u00020\u0018H\u0016J\u0018\u0010\u001e\u001a\u00020\u00032\u0006\u0010\u0012\u001a\u00020\u00102\u0006\u0010\u001a\u001a\u00020\u0018H\u0016J@\u0010$\u001a\u00020\u00032\u0006\u0010\u0012\u001a\u00020\u00102\u0006\u0010\u001f\u001a\u00020\u00182\u0006\u0010 \u001a\u00020\u00182\u0006\u0010!\u001a\u00020\u00182\u0006\u0010\"\u001a\u00020\u00182\u0006\u0010\u001a\u001a\u00020\u00182\u0006\u0010#\u001a\u00020\nH\u0016J8\u0010$\u001a\u00020\u00032\u0006\u0010\u0012\u001a\u00020\u00102\u0006\u0010\u001f\u001a\u00020\u00182\u0006\u0010 \u001a\u00020\u00182\u0006\u0010!\u001a\u00020\u00182\u0006\u0010\"\u001a\u00020\u00182\u0006\u0010\u001a\u001a\u00020\u0018H\u0016J0\u0010'\u001a\u00020\u00032\u0006\u0010\u0012\u001a\u00020\u00102\u0006\u0010%\u001a\u00020\u00182\u0006\u0010&\u001a\u00020\u00182\u0006\u0010#\u001a\u00020\n2\u0006\u0010\u001a\u001a\u00020\u0018H\u0016J(\u0010+\u001a\u00020\u00072\u0006\u0010\u0012\u001a\u00020\u00102\u0006\u0010)\u001a\u00020(2\u0006\u0010*\u001a\u00020(2\u0006\u0010#\u001a\u00020\u0007H\u0016J \u0010,\u001a\u00020\u00072\u0006\u0010\u0012\u001a\u00020\u00102\u0006\u0010)\u001a\u00020(2\u0006\u0010*\u001a\u00020(H\u0016J\b\u0010-\u001a\u00020\u0007H\u0016R.\u00105\u001a\u0004\u0018\u00010\u00102\b\u0010.\u001a\u0004\u0018\u00010\u00108\u0006@@X\u0086\u000e¢\u0006\u0012\n\u0004\b/\u00100\u001a\u0004\b1\u00102\"\u0004\b3\u00104R.\u0010=\u001a\u0004\u0018\u0001062\b\u0010.\u001a\u0004\u0018\u0001068\u0006@FX\u0086\u000e¢\u0006\u0012\n\u0004\b7\u00108\u001a\u0004\b9\u0010:\"\u0004\b;\u0010<R.\u0010E\u001a\u0004\u0018\u00010>2\b\u0010.\u001a\u0004\u0018\u00010>8\u0006@FX\u0086\u000e¢\u0006\u0012\n\u0004\b?\u0010@\u001a\u0004\bA\u0010B\"\u0004\bC\u0010DR6\u0010G\u001a\b\u0012\u0004\u0012\u00020\u00030F2\f\u0010.\u001a\b\u0012\u0004\u0012\u00020\u00030F8\u0006@DX\u0086\u000e¢\u0006\u0012\n\u0004\bG\u0010H\u001a\u0004\bI\u0010J\"\u0004\bK\u0010LR*\u0010N\u001a\u00020M2\u0006\u0010.\u001a\u00020M8\u0006@FX\u0086\u000e¢\u0006\u0012\n\u0004\bN\u0010O\u001a\u0004\bP\u0010Q\"\u0004\bR\u0010SR0\u0010U\u001a\u0010\u0012\u0004\u0012\u00020M\u0012\u0004\u0012\u00020\u0003\u0018\u00010T8\u0000@\u0000X\u0080\u000e¢\u0006\u0012\n\u0004\bU\u0010V\u001a\u0004\bW\u0010X\"\u0004\bY\u0010ZR*\u0010\\\u001a\u00020[2\u0006\u0010.\u001a\u00020[8\u0006@FX\u0086\u000e¢\u0006\u0012\n\u0004\b\\\u0010]\u001a\u0004\b^\u0010_\"\u0004\b`\u0010aR0\u0010b\u001a\u0010\u0012\u0004\u0012\u00020[\u0012\u0004\u0012\u00020\u0003\u0018\u00010T8\u0000@\u0000X\u0080\u000e¢\u0006\u0012\n\u0004\bb\u0010V\u001a\u0004\bc\u0010X\"\u0004\bd\u0010ZR0\u0010e\u001a\u0010\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u0003\u0018\u00010T8\u0000@\u0000X\u0080\u000e¢\u0006\u0012\n\u0004\be\u0010V\u001a\u0004\bf\u0010X\"\u0004\bg\u0010ZR\u0017\u0010i\u001a\u00020h8\u0006¢\u0006\f\n\u0004\bi\u0010j\u001a\u0004\bk\u0010l¨\u0006u"}, m4115d2 = {"Landroidx/compose/ui/viewinterop/AndroidViewHolder;", "Landroid/view/ViewGroup;", "Landroidx/core/view/NestedScrollingParent3;", "Lokhttp3/internal/io/lx5;", "remeasure", "Landroid/view/ViewGroup$LayoutParams;", "getLayoutParams", "", "disallowIntercept", "requestDisallowInterceptTouchEvent", "", "location", "Landroid/graphics/Rect;", "dirty", "Landroid/view/ViewParent;", "invalidateChildInParent", "Landroid/view/View;", "child", TypedValues.AttributesType.S_TARGET, "onDescendantInvalidated", "Landroid/graphics/Region;", "region", "gatherTransparentRegion", "shouldDelayChildPressedState", "", "axes", "type", "onStartNestedScroll", "getNestedScrollAxes", "onNestedScrollAccepted", "onStopNestedScroll", "dxConsumed", "dyConsumed", "dxUnconsumed", "dyUnconsumed", "consumed", "onNestedScroll", "dx", "dy", "onNestedPreScroll", "", "velocityX", "velocityY", "onNestedFling", "onNestedPreFling", "isNestedScrollingEnabled", "value", "ၦ", "Landroid/view/View;", "getView", "()Landroid/view/View;", "setView$ui_release", "(Landroid/view/View;)V", "view", "Landroidx/lifecycle/LifecycleOwner;", "ၸ", "Landroidx/lifecycle/LifecycleOwner;", "getLifecycleOwner", "()Landroidx/lifecycle/LifecycleOwner;", "setLifecycleOwner", "(Landroidx/lifecycle/LifecycleOwner;)V", "lifecycleOwner", "Landroidx/savedstate/SavedStateRegistryOwner;", "ၹ", "Landroidx/savedstate/SavedStateRegistryOwner;", "getSavedStateRegistryOwner", "()Landroidx/savedstate/SavedStateRegistryOwner;", "setSavedStateRegistryOwner", "(Landroidx/savedstate/SavedStateRegistryOwner;)V", "savedStateRegistryOwner", "Lkotlin/Function0;", "update", "Lokhttp3/internal/io/nh0;", "getUpdate", "()Lokhttp3/internal/io/nh0;", "setUpdate", "(Lokhttp3/internal/io/nh0;)V", "Lokhttp3/internal/io/rk2;", "modifier", "Lokhttp3/internal/io/rk2;", "getModifier", "()Lokhttp3/internal/io/rk2;", "setModifier", "(Lokhttp3/internal/io/rk2;)V", "Lkotlin/Function1;", "onModifierChanged", "Lokhttp3/internal/io/ph0;", "getOnModifierChanged$ui_release", "()Lokhttp3/internal/io/ph0;", "setOnModifierChanged$ui_release", "(Lokhttp3/internal/io/ph0;)V", "Lokhttp3/internal/io/u7;", "density", "Lokhttp3/internal/io/u7;", "getDensity", "()Lokhttp3/internal/io/u7;", "setDensity", "(Lokhttp3/internal/io/u7;)V", "onDensityChanged", "getOnDensityChanged$ui_release", "setOnDensityChanged$ui_release", "onRequestDisallowInterceptTouchEvent", "getOnRequestDisallowInterceptTouchEvent$ui_release", "setOnRequestDisallowInterceptTouchEvent$ui_release", "Lokhttp3/internal/io/gx1;", "layoutNode", "Lokhttp3/internal/io/gx1;", "getLayoutNode", "()Lokhttp3/internal/io/gx1;", "Landroid/content/Context;", "context", "Lokhttp3/internal/io/ܔ;", "parentContext", "Lokhttp3/internal/io/fr2;", "dispatcher", RhinoJavaScriptEngine.SOURCE_NAME_INIT, "(Landroid/content/Context;Lokhttp3/internal/io/ܔ;Lokhttp3/internal/io/fr2;)V", "ui_release"}, m4116k = 1, m4117mv = {1, 7, 1})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public abstract class AndroidViewHolder extends ViewGroup implements NestedScrollingParent3 {

    /* renamed from: ၥ */
    @zu2
    public final fr2 f214;

    /* renamed from: ၦ, reason: from kotlin metadata */
    @wv2
    public View view;

    /* renamed from: ၮ */
    @zu2
    public nh0<lx5> f216;

    /* renamed from: ၯ */
    public boolean f217;

    /* renamed from: ၰ */
    @zu2
    public rk2 f218;

    /* renamed from: ၵ */
    @wv2
    public ph0<? super rk2, lx5> f219;

    /* renamed from: ၶ */
    @zu2
    public InterfaceC5282u7 f220;

    /* renamed from: ၷ */
    @wv2
    public ph0<? super InterfaceC5282u7, lx5> f221;

    /* renamed from: ၸ, reason: from kotlin metadata */
    @wv2
    public LifecycleOwner lifecycleOwner;

    /* renamed from: ၹ, reason: from kotlin metadata */
    @wv2
    public SavedStateRegistryOwner savedStateRegistryOwner;

    /* renamed from: ၺ */
    @zu2
    public final xv4 f224;

    /* renamed from: ၻ */
    @zu2
    public final ph0<AndroidViewHolder, lx5> f225;

    /* renamed from: ၼ */
    @zu2
    public final nh0<lx5> f226;

    /* renamed from: ၽ */
    @wv2
    public ph0<? super Boolean, lx5> f227;

    /* renamed from: ၾ */
    @zu2
    public final int[] f228;

    /* renamed from: ၿ */
    public int f229;

    /* renamed from: ႀ */
    public int f230;

    /* renamed from: ႁ */
    @zu2
    public final NestedScrollingParentHelper f231;

    /* renamed from: ႎ */
    @zu2
    public final gx1 f232;

    /* renamed from: androidx.compose.ui.viewinterop.AndroidViewHolder$Ϳ */
    public static final class C0198 extends lv1 implements ph0<rk2, lx5> {

        /* renamed from: ၥ */
        public final /* synthetic */ gx1 f233;

        /* renamed from: ၦ */
        public final /* synthetic */ rk2 f234;

        /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
        public C0198(gx1 gx1Var, rk2 rk2Var) {
            super(1);
            this.f233 = gx1Var;
            this.f234 = rk2Var;
        }

        @Override // okhttp3.internal.p042io.ph0
        public final lx5 invoke(rk2 rk2Var) {
            rk2 rk2Var2 = rk2Var;
            fa1.m6826(rk2Var2, "it");
            this.f233.mo7499(rk2Var2.mo23(this.f234));
            return lx5.f14876;
        }
    }

    /* renamed from: androidx.compose.ui.viewinterop.AndroidViewHolder$Ԩ */
    public static final class C0199 extends lv1 implements ph0<InterfaceC5282u7, lx5> {

        /* renamed from: ၥ */
        public final /* synthetic */ gx1 f235;

        /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
        public C0199(gx1 gx1Var) {
            super(1);
            this.f235 = gx1Var;
        }

        @Override // okhttp3.internal.p042io.ph0
        public final lx5 invoke(InterfaceC5282u7 interfaceC5282u7) {
            InterfaceC5282u7 interfaceC5282u72 = interfaceC5282u7;
            fa1.m6826(interfaceC5282u72, "it");
            this.f235.mo7496(interfaceC5282u72);
            return lx5.f14876;
        }
    }

    /* renamed from: androidx.compose.ui.viewinterop.AndroidViewHolder$Ԫ */
    public static final class C0200 extends lv1 implements ph0<y33, lx5> {

        /* renamed from: ၦ */
        public final /* synthetic */ gx1 f237;

        /* renamed from: ၮ */
        public final /* synthetic */ xv3<View> f238;

        /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
        public C0200(gx1 gx1Var, xv3<View> xv3Var) {
            super(1);
            this.f237 = gx1Var;
            this.f238 = xv3Var;
        }

        @Override // okhttp3.internal.p042io.ph0
        public final lx5 invoke(y33 y33Var) {
            y33 y33Var2 = y33Var;
            fa1.m6826(y33Var2, "owner");
            AndroidComposeView androidComposeView = y33Var2 instanceof AndroidComposeView ? (AndroidComposeView) y33Var2 : null;
            if (androidComposeView != null) {
                androidComposeView.addAndroidView(AndroidViewHolder.this, this.f237);
            }
            View view = this.f238.f26199;
            if (view != null) {
                AndroidViewHolder.this.setView$ui_release(view);
            }
            return lx5.f14876;
        }
    }

    /* renamed from: androidx.compose.ui.viewinterop.AndroidViewHolder$Ԭ */
    public static final class C0201 extends lv1 implements ph0<y33, lx5> {

        /* renamed from: ၦ */
        public final /* synthetic */ xv3<View> f240;

        /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
        public C0201(xv3<View> xv3Var) {
            super(1);
            this.f240 = xv3Var;
        }

        /* JADX WARN: Type inference failed for: r0v3, types: [T, android.view.View] */
        @Override // okhttp3.internal.p042io.ph0
        public final lx5 invoke(y33 y33Var) {
            y33 y33Var2 = y33Var;
            fa1.m6826(y33Var2, "owner");
            AndroidComposeView androidComposeView = y33Var2 instanceof AndroidComposeView ? (AndroidComposeView) y33Var2 : null;
            if (androidComposeView != null) {
                androidComposeView.removeAndroidView(AndroidViewHolder.this);
            }
            this.f240.f26199 = AndroidViewHolder.this.getView();
            AndroidViewHolder.this.setView$ui_release(null);
            return lx5.f14876;
        }
    }

    /* renamed from: androidx.compose.ui.viewinterop.AndroidViewHolder$Ԯ */
    public static final class C0202 implements ye2 {

        /* renamed from: Ԩ */
        public final /* synthetic */ gx1 f242;

        /* renamed from: androidx.compose.ui.viewinterop.AndroidViewHolder$Ԯ$Ϳ, reason: contains not printable characters */
        public static final class C8539 extends lv1 implements ph0<bc3.AbstractC2861, lx5> {

            /* renamed from: ၥ */
            public final /* synthetic */ AndroidViewHolder f243;

            /* renamed from: ၦ */
            public final /* synthetic */ gx1 f244;

            /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
            public C8539(AndroidViewHolder androidViewHolder, gx1 gx1Var) {
                super(1);
                this.f243 = androidViewHolder;
                this.f244 = gx1Var;
            }

            @Override // okhttp3.internal.p042io.ph0
            public final lx5 invoke(bc3.AbstractC2861 abstractC2861) {
                fa1.m6826(abstractC2861, "$this$layout");
                C7847.m17507(this.f243, this.f244);
                return lx5.f14876;
            }
        }

        public C0202(gx1 gx1Var) {
            this.f242 = gx1Var;
        }

        @Override // okhttp3.internal.p042io.ye2
        /* renamed from: Ϳ */
        public final int mo73(@zu2 ea1 ea1Var, @zu2 List<? extends m91> list, int i) {
            fa1.m6826(ea1Var, "<this>");
            return m78(i);
        }

        @Override // okhttp3.internal.p042io.ye2
        /* renamed from: Ԩ */
        public final int mo74(@zu2 ea1 ea1Var, @zu2 List<? extends m91> list, int i) {
            fa1.m6826(ea1Var, "<this>");
            return m79(i);
        }

        @Override // okhttp3.internal.p042io.ye2
        @zu2
        /* renamed from: ԩ */
        public final ze2 mo75(@zu2 bf2 bf2Var, @zu2 List<? extends ue2> list, long j) {
            ze2 mo5129;
            fa1.m6826(bf2Var, "$this$measure");
            fa1.m6826(list, "measurables");
            if (C7409.m17005(j) != 0) {
                AndroidViewHolder.this.getChildAt(0).setMinimumWidth(C7409.m17005(j));
            }
            if (C7409.m17004(j) != 0) {
                AndroidViewHolder.this.getChildAt(0).setMinimumHeight(C7409.m17004(j));
            }
            AndroidViewHolder androidViewHolder = AndroidViewHolder.this;
            int m17005 = C7409.m17005(j);
            int m17003 = C7409.m17003(j);
            ViewGroup.LayoutParams layoutParams = AndroidViewHolder.this.getLayoutParams();
            fa1.m6823(layoutParams);
            int access$obtainMeasureSpec = AndroidViewHolder.access$obtainMeasureSpec(androidViewHolder, m17005, m17003, layoutParams.width);
            AndroidViewHolder androidViewHolder2 = AndroidViewHolder.this;
            int m17004 = C7409.m17004(j);
            int m17002 = C7409.m17002(j);
            ViewGroup.LayoutParams layoutParams2 = AndroidViewHolder.this.getLayoutParams();
            fa1.m6823(layoutParams2);
            androidViewHolder.measure(access$obtainMeasureSpec, AndroidViewHolder.access$obtainMeasureSpec(androidViewHolder2, m17004, m17002, layoutParams2.height));
            mo5129 = bf2Var.mo5129(AndroidViewHolder.this.getMeasuredWidth(), AndroidViewHolder.this.getMeasuredHeight(), C5849yq.f26859, new C8539(AndroidViewHolder.this, this.f242));
            return mo5129;
        }

        @Override // okhttp3.internal.p042io.ye2
        /* renamed from: Ԫ */
        public final int mo76(@zu2 ea1 ea1Var, @zu2 List<? extends m91> list, int i) {
            fa1.m6826(ea1Var, "<this>");
            return m78(i);
        }

        @Override // okhttp3.internal.p042io.ye2
        /* renamed from: ԫ */
        public final int mo77(@zu2 ea1 ea1Var, @zu2 List<? extends m91> list, int i) {
            fa1.m6826(ea1Var, "<this>");
            return m79(i);
        }

        /* renamed from: Ԭ */
        public final int m78(int i) {
            AndroidViewHolder androidViewHolder = AndroidViewHolder.this;
            ViewGroup.LayoutParams layoutParams = androidViewHolder.getLayoutParams();
            fa1.m6823(layoutParams);
            androidViewHolder.measure(AndroidViewHolder.access$obtainMeasureSpec(androidViewHolder, 0, i, layoutParams.width), View.MeasureSpec.makeMeasureSpec(0, 0));
            return AndroidViewHolder.this.getMeasuredHeight();
        }

        /* renamed from: ԭ */
        public final int m79(int i) {
            AndroidViewHolder androidViewHolder = AndroidViewHolder.this;
            int makeMeasureSpec = View.MeasureSpec.makeMeasureSpec(0, 0);
            AndroidViewHolder androidViewHolder2 = AndroidViewHolder.this;
            ViewGroup.LayoutParams layoutParams = androidViewHolder2.getLayoutParams();
            fa1.m6823(layoutParams);
            androidViewHolder.measure(makeMeasureSpec, AndroidViewHolder.access$obtainMeasureSpec(androidViewHolder2, 0, i, layoutParams.height));
            return AndroidViewHolder.this.getMeasuredWidth();
        }
    }

    /* renamed from: androidx.compose.ui.viewinterop.AndroidViewHolder$֏ */
    public static final class C0203 extends lv1 implements ph0<InterfaceC5066sk, lx5> {

        /* renamed from: ၥ */
        public final /* synthetic */ gx1 f245;

        /* renamed from: ၦ */
        public final /* synthetic */ AndroidViewHolder f246;

        /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
        public C0203(gx1 gx1Var, AndroidViewHolder androidViewHolder) {
            super(1);
            this.f245 = gx1Var;
            this.f246 = androidViewHolder;
        }

        @Override // okhttp3.internal.p042io.ph0
        public final lx5 invoke(InterfaceC5066sk interfaceC5066sk) {
            InterfaceC5066sk interfaceC5066sk2 = interfaceC5066sk;
            fa1.m6826(interfaceC5066sk2, "$this$drawBehind");
            gx1 gx1Var = this.f245;
            AndroidViewHolder androidViewHolder = this.f246;
            InterfaceC7598 mo9285 = interfaceC5066sk2.mo8475().mo9285();
            y33 y33Var = gx1Var.f11141;
            AndroidComposeView androidComposeView = y33Var instanceof AndroidComposeView ? (AndroidComposeView) y33Var : null;
            if (androidComposeView != null) {
                androidComposeView.drawAndroidView(androidViewHolder, C6354.m15231(mo9285));
            }
            return lx5.f14876;
        }
    }

    /* renamed from: androidx.compose.ui.viewinterop.AndroidViewHolder$ؠ */
    public static final class C0204 extends lv1 implements ph0<bw1, lx5> {

        /* renamed from: ၦ */
        public final /* synthetic */ gx1 f248;

        /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
        public C0204(gx1 gx1Var) {
            super(1);
            this.f248 = gx1Var;
        }

        @Override // okhttp3.internal.p042io.ph0
        public final lx5 invoke(bw1 bw1Var) {
            fa1.m6826(bw1Var, "it");
            C7847.m17507(AndroidViewHolder.this, this.f248);
            return lx5.f14876;
        }
    }

    /* renamed from: androidx.compose.ui.viewinterop.AndroidViewHolder$ހ */
    public static final class C0205 extends lv1 implements ph0<AndroidViewHolder, lx5> {
        public C0205() {
            super(1);
        }

        @Override // okhttp3.internal.p042io.ph0
        public final lx5 invoke(AndroidViewHolder androidViewHolder) {
            fa1.m6826(androidViewHolder, "it");
            Handler handler = AndroidViewHolder.this.getHandler();
            final nh0 nh0Var = AndroidViewHolder.this.f226;
            handler.post(new Runnable() { // from class: okhttp3.internal.io.ऽ
                @Override // java.lang.Runnable
                public final void run() {
                    nh0 nh0Var2 = nh0.this;
                    fa1.m6826(nh0Var2, "$tmp0");
                    nh0Var2.invoke();
                }
            });
            return lx5.f14876;
        }
    }

    @InterfaceC4988s2(m11868c = "androidx.compose.ui.viewinterop.AndroidViewHolder$onNestedFling$1", m11869f = "AndroidViewHolder.android.kt", m11870l = {480, Videoio.CAP_PROP_XI_CC_MATRIX_12}, m11871m = "invokeSuspend")
    /* renamed from: androidx.compose.ui.viewinterop.AndroidViewHolder$ށ */
    public static final class C0206 extends u75 implements di0<InterfaceC7881, InterfaceC7155<? super lx5>, Object> {

        /* renamed from: ၥ */
        public int f250;

        /* renamed from: ၦ */
        public final /* synthetic */ boolean f251;

        /* renamed from: ၮ */
        public final /* synthetic */ AndroidViewHolder f252;

        /* renamed from: ၯ */
        public final /* synthetic */ long f253;

        /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
        public C0206(boolean z, AndroidViewHolder androidViewHolder, long j, InterfaceC7155<? super C0206> interfaceC7155) {
            super(2, interfaceC7155);
            this.f251 = z;
            this.f252 = androidViewHolder;
            this.f253 = j;
        }

        @Override // okhttp3.internal.p042io.AbstractC7853
        @zu2
        public final InterfaceC7155<lx5> create(@wv2 Object obj, @zu2 InterfaceC7155<?> interfaceC7155) {
            return new C0206(this.f251, this.f252, this.f253, interfaceC7155);
        }

        @Override // okhttp3.internal.p042io.di0
        /* renamed from: invoke */
        public final Object mo18338invoke(InterfaceC7881 interfaceC7881, InterfaceC7155<? super lx5> interfaceC7155) {
            return ((C0206) create(interfaceC7881, interfaceC7155)).invokeSuspend(lx5.f14876);
        }

        @Override // okhttp3.internal.p042io.AbstractC7853
        @wv2
        public final Object invokeSuspend(@zu2 Object obj) {
            EnumC7329 enumC7329 = EnumC7329.COROUTINE_SUSPENDED;
            int i = this.f250;
            if (i == 0) {
                C4350o9.m10270(obj);
                if (this.f251) {
                    fr2 fr2Var = this.f252.f214;
                    long j = this.f253;
                    p56.C4470 c4470 = p56.f17164;
                    long j2 = p56.f17165;
                    this.f250 = 2;
                    if (fr2Var.m7057(j, j2, this) == enumC7329) {
                        return enumC7329;
                    }
                } else {
                    fr2 fr2Var2 = this.f252.f214;
                    p56.C4470 c44702 = p56.f17164;
                    long j3 = p56.f17165;
                    long j4 = this.f253;
                    this.f250 = 1;
                    if (fr2Var2.m7057(j3, j4, this) == enumC7329) {
                        return enumC7329;
                    }
                }
            } else {
                if (i != 1 && i != 2) {
                    throw new IllegalStateException("call to 'resume' before 'invoke' with coroutine");
                }
                C4350o9.m10270(obj);
            }
            return lx5.f14876;
        }
    }

    @InterfaceC4988s2(m11868c = "androidx.compose.ui.viewinterop.AndroidViewHolder$onNestedPreFling$1", m11869f = "AndroidViewHolder.android.kt", m11870l = {498}, m11871m = "invokeSuspend")
    /* renamed from: androidx.compose.ui.viewinterop.AndroidViewHolder$ނ */
    public static final class C0207 extends u75 implements di0<InterfaceC7881, InterfaceC7155<? super lx5>, Object> {

        /* renamed from: ၥ */
        public int f254;

        /* renamed from: ၮ */
        public final /* synthetic */ long f256;

        /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
        public C0207(long j, InterfaceC7155<? super C0207> interfaceC7155) {
            super(2, interfaceC7155);
            this.f256 = j;
        }

        @Override // okhttp3.internal.p042io.AbstractC7853
        @zu2
        public final InterfaceC7155<lx5> create(@wv2 Object obj, @zu2 InterfaceC7155<?> interfaceC7155) {
            return AndroidViewHolder.this.new C0207(this.f256, interfaceC7155);
        }

        @Override // okhttp3.internal.p042io.di0
        /* renamed from: invoke */
        public final Object mo18338invoke(InterfaceC7881 interfaceC7881, InterfaceC7155<? super lx5> interfaceC7155) {
            return ((C0207) create(interfaceC7881, interfaceC7155)).invokeSuspend(lx5.f14876);
        }

        @Override // okhttp3.internal.p042io.AbstractC7853
        @wv2
        public final Object invokeSuspend(@zu2 Object obj) {
            EnumC7329 enumC7329 = EnumC7329.COROUTINE_SUSPENDED;
            int i = this.f254;
            if (i == 0) {
                C4350o9.m10270(obj);
                fr2 fr2Var = AndroidViewHolder.this.f214;
                long j = this.f256;
                this.f254 = 1;
                if (fr2Var.m7059(j, this) == enumC7329) {
                    return enumC7329;
                }
            } else {
                if (i != 1) {
                    throw new IllegalStateException("call to 'resume' before 'invoke' with coroutine");
                }
                C4350o9.m10270(obj);
            }
            return lx5.f14876;
        }
    }

    /* renamed from: androidx.compose.ui.viewinterop.AndroidViewHolder$ރ */
    public static final class C0208 extends lv1 implements nh0<lx5> {
        public C0208() {
            super(0);
        }

        @Override // okhttp3.internal.p042io.nh0
        public final lx5 invoke() {
            if (AndroidViewHolder.this.f217) {
                xv4 xv4Var = AndroidViewHolder.this.f224;
                AndroidViewHolder androidViewHolder = AndroidViewHolder.this;
                xv4Var.m14076(androidViewHolder, androidViewHolder.f225, AndroidViewHolder.this.getUpdate());
            }
            return lx5.f14876;
        }
    }

    /* renamed from: androidx.compose.ui.viewinterop.AndroidViewHolder$ބ */
    public static final class C0209 extends lv1 implements ph0<nh0<? extends lx5>, lx5> {
        public C0209() {
            super(1);
        }

        @Override // okhttp3.internal.p042io.ph0
        public final lx5 invoke(nh0<? extends lx5> nh0Var) {
            nh0<? extends lx5> nh0Var2 = nh0Var;
            fa1.m6826(nh0Var2, "command");
            if (AndroidViewHolder.this.getHandler().getLooper() == Looper.myLooper()) {
                nh0Var2.invoke();
            } else {
                AndroidViewHolder.this.getHandler().post(new RunnableC7885(nh0Var2, 1));
            }
            return lx5.f14876;
        }
    }

    /* renamed from: androidx.compose.ui.viewinterop.AndroidViewHolder$ޅ */
    public static final class C0210 extends lv1 implements nh0<lx5> {

        /* renamed from: ၥ */
        public static final C0210 f259 = new C0210();

        public C0210() {
            super(0);
        }

        @Override // okhttp3.internal.p042io.nh0
        public final /* bridge */ /* synthetic */ lx5 invoke() {
            return lx5.f14876;
        }
    }

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public AndroidViewHolder(@zu2 Context context, @wv2 AbstractC6779 abstractC6779, @zu2 fr2 fr2Var) {
        super(context);
        fa1.m6826(context, "context");
        fa1.m6826(fr2Var, "dispatcher");
        this.f214 = fr2Var;
        if (abstractC6779 != null) {
            ec6.m6429(this, abstractC6779);
        }
        setSaveFromParentEnabled(false);
        this.f216 = C0210.f259;
        this.f218 = rk2.C4938.f20511;
        this.f220 = bz5.m5404();
        this.f224 = new xv4(new C0209());
        this.f225 = new C0205();
        this.f226 = new C0208();
        this.f228 = new int[2];
        this.f229 = Integer.MIN_VALUE;
        this.f230 = Integer.MIN_VALUE;
        this.f231 = new NestedScrollingParentHelper(this);
        gx1 gx1Var = new gx1(false, 0, 3, null);
        qf3 qf3Var = new qf3();
        qf3Var.f18585 = new rf3(this);
        y04 y04Var = new y04();
        y04 y04Var2 = qf3Var.f18586;
        if (y04Var2 != null) {
            y04Var2.f26317 = null;
        }
        qf3Var.f18586 = y04Var;
        y04Var.f26317 = qf3Var;
        setOnRequestDisallowInterceptTouchEvent$ui_release(y04Var);
        rk2 m14323 = ym2.m14323(ve4.m13222(qf3Var, new C0203(gx1Var, this)), new C0204(gx1Var));
        gx1Var.mo7499(this.f218.mo23(m14323));
        this.f219 = new C0198(gx1Var, m14323);
        gx1Var.mo7496(this.f220);
        this.f221 = new C0199(gx1Var);
        xv3 xv3Var = new xv3();
        gx1Var.f11130 = new C0200(gx1Var, xv3Var);
        gx1Var.f11120 = new C0201(xv3Var);
        gx1Var.mo7500(new C0202(gx1Var));
        this.f232 = gx1Var;
    }

    public static final int access$obtainMeasureSpec(AndroidViewHolder androidViewHolder, int i, int i2, int i3) {
        Objects.requireNonNull(androidViewHolder);
        int i4 = 1073741824;
        if (i3 >= 0 || i == i2) {
            return View.MeasureSpec.makeMeasureSpec(C6489.m15438(i3, i, i2), 1073741824);
        }
        if (i3 == -2 && i2 != Integer.MAX_VALUE) {
            i4 = Integer.MIN_VALUE;
        } else if (i3 != -1 || i2 == Integer.MAX_VALUE) {
            return View.MeasureSpec.makeMeasureSpec(0, 0);
        }
        return View.MeasureSpec.makeMeasureSpec(i2, i4);
    }

    @Override // android.view.ViewGroup, android.view.View
    public boolean gatherTransparentRegion(@wv2 Region region) {
        if (region == null) {
            return true;
        }
        getLocationInWindow(this.f228);
        int[] iArr = this.f228;
        region.op(iArr[0], iArr[1], getWidth() + iArr[0], getHeight() + this.f228[1], Region.Op.DIFFERENCE);
        return true;
    }

    @zu2
    /* renamed from: getDensity, reason: from getter */
    public final InterfaceC5282u7 getF220() {
        return this.f220;
    }

    @zu2
    /* renamed from: getLayoutNode, reason: from getter */
    public final gx1 getF232() {
        return this.f232;
    }

    @Override // android.view.View
    @wv2
    public ViewGroup.LayoutParams getLayoutParams() {
        ViewGroup.LayoutParams layoutParams;
        View view = this.view;
        return (view == null || (layoutParams = view.getLayoutParams()) == null) ? new ViewGroup.LayoutParams(-1, -1) : layoutParams;
    }

    @wv2
    public final LifecycleOwner getLifecycleOwner() {
        return this.lifecycleOwner;
    }

    @zu2
    /* renamed from: getModifier, reason: from getter */
    public final rk2 getF218() {
        return this.f218;
    }

    @Override // android.view.ViewGroup, androidx.core.view.NestedScrollingParent
    public int getNestedScrollAxes() {
        return this.f231.getNestedScrollAxes();
    }

    @wv2
    public final ph0<InterfaceC5282u7, lx5> getOnDensityChanged$ui_release() {
        return this.f221;
    }

    @wv2
    public final ph0<rk2, lx5> getOnModifierChanged$ui_release() {
        return this.f219;
    }

    @wv2
    public final ph0<Boolean, lx5> getOnRequestDisallowInterceptTouchEvent$ui_release() {
        return this.f227;
    }

    @wv2
    public final SavedStateRegistryOwner getSavedStateRegistryOwner() {
        return this.savedStateRegistryOwner;
    }

    @zu2
    public final nh0<lx5> getUpdate() {
        return this.f216;
    }

    @wv2
    public final View getView() {
        return this.view;
    }

    @Override // android.view.ViewGroup, android.view.ViewParent
    @wv2
    public ViewParent invalidateChildInParent(@wv2 int[] location, @wv2 Rect dirty) {
        super.invalidateChildInParent(location, dirty);
        this.f232.m7516();
        return null;
    }

    @Override // android.view.View
    public boolean isNestedScrollingEnabled() {
        View view = this.view;
        return view != null ? view.isNestedScrollingEnabled() : super.isNestedScrollingEnabled();
    }

    @Override // android.view.ViewGroup, android.view.View
    public final void onAttachedToWindow() {
        super.onAttachedToWindow();
        this.f224.m14077();
    }

    @Override // android.view.ViewGroup, android.view.ViewParent
    public void onDescendantInvalidated(@zu2 View view, @zu2 View view2) {
        fa1.m6826(view, "child");
        fa1.m6826(view2, TypedValues.AttributesType.S_TARGET);
        super.onDescendantInvalidated(view, view2);
        this.f232.m7516();
    }

    @Override // android.view.ViewGroup, android.view.View
    public final void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        xu4 xu4Var = this.f224.f26204;
        if (xu4Var != null) {
            xu4Var.dispose();
        }
        this.f224.m14074();
    }

    @Override // android.view.ViewGroup, android.view.View
    public final void onLayout(boolean z, int i, int i2, int i3, int i4) {
        View view = this.view;
        if (view != null) {
            view.layout(0, 0, i3 - i, i4 - i2);
        }
    }

    @Override // android.view.View
    public final void onMeasure(int i, int i2) {
        View view = this.view;
        if (view != null) {
            view.measure(i, i2);
        }
        View view2 = this.view;
        int measuredWidth = view2 != null ? view2.getMeasuredWidth() : 0;
        View view3 = this.view;
        setMeasuredDimension(measuredWidth, view3 != null ? view3.getMeasuredHeight() : 0);
        this.f229 = i;
        this.f230 = i2;
    }

    @Override // android.view.ViewGroup, android.view.ViewParent, androidx.core.view.NestedScrollingParent
    public boolean onNestedFling(@zu2 View target, float velocityX, float velocityY, boolean consumed) {
        fa1.m6826(target, TypedValues.AttributesType.S_TARGET);
        if (!isNestedScrollingEnabled()) {
            return false;
        }
        C6814.m16042(this.f214.m7060(), null, 0, new C0206(consumed, this, yg3.m14266(velocityX * (-1.0f), velocityY * (-1.0f)), null), 3);
        return false;
    }

    @Override // android.view.ViewGroup, android.view.ViewParent, androidx.core.view.NestedScrollingParent
    public boolean onNestedPreFling(@zu2 View target, float velocityX, float velocityY) {
        fa1.m6826(target, TypedValues.AttributesType.S_TARGET);
        if (!isNestedScrollingEnabled()) {
            return false;
        }
        C6814.m16042(this.f214.m7060(), null, 0, new C0207(yg3.m14266(velocityX * (-1.0f), velocityY * (-1.0f)), null), 3);
        return false;
    }

    @Override // androidx.core.view.NestedScrollingParent2
    public void onNestedPreScroll(@zu2 View view, int i, int i2, @zu2 int[] iArr, int i3) {
        long j;
        fa1.m6826(view, TypedValues.AttributesType.S_TARGET);
        fa1.m6826(iArr, "consumed");
        if (isNestedScrollingEnabled()) {
            fr2 fr2Var = this.f214;
            float f = -1;
            long m5168 = C2878bi.m5168(i * f, i2 * f);
            int i4 = i3 == 0 ? 1 : 2;
            er2 er2Var = fr2Var.f10350;
            if (er2Var != null) {
                j = er2Var.mo6542(m5168, i4);
            } else {
                g03.C3393 c3393 = g03.f10466;
                j = g03.f10467;
            }
            iArr[0] = C7024.m16368(g03.m7139(j));
            iArr[1] = C7024.m16368(g03.m7140(j));
        }
    }

    @Override // androidx.core.view.NestedScrollingParent2
    public void onNestedScroll(@zu2 View view, int i, int i2, int i3, int i4, int i5) {
        fa1.m6826(view, TypedValues.AttributesType.S_TARGET);
        if (isNestedScrollingEnabled()) {
            float f = i;
            float f2 = -1;
            this.f214.m7058(C2878bi.m5168(f * f2, i2 * f2), C2878bi.m5168(i3 * f2, i4 * f2), i5 == 0 ? 1 : 2);
        }
    }

    @Override // androidx.core.view.NestedScrollingParent3
    public void onNestedScroll(@zu2 View view, int i, int i2, int i3, int i4, int i5, @zu2 int[] iArr) {
        fa1.m6826(view, TypedValues.AttributesType.S_TARGET);
        fa1.m6826(iArr, "consumed");
        if (isNestedScrollingEnabled()) {
            float f = i;
            float f2 = -1;
            long m7058 = this.f214.m7058(C2878bi.m5168(f * f2, i2 * f2), C2878bi.m5168(i3 * f2, i4 * f2), i5 == 0 ? 1 : 2);
            iArr[0] = C7024.m16368(g03.m7139(m7058));
            iArr[1] = C7024.m16368(g03.m7140(m7058));
        }
    }

    @Override // androidx.core.view.NestedScrollingParent2
    public void onNestedScrollAccepted(@zu2 View view, @zu2 View view2, int i, int i2) {
        fa1.m6826(view, "child");
        fa1.m6826(view2, TypedValues.AttributesType.S_TARGET);
        this.f231.onNestedScrollAccepted(view, view2, i, i2);
    }

    @Override // androidx.core.view.NestedScrollingParent2
    public boolean onStartNestedScroll(@zu2 View child, @zu2 View target, int axes, int type) {
        fa1.m6826(child, "child");
        fa1.m6826(target, TypedValues.AttributesType.S_TARGET);
        return ((axes & 2) == 0 && (axes & 1) == 0) ? false : true;
    }

    @Override // androidx.core.view.NestedScrollingParent2
    public void onStopNestedScroll(@zu2 View view, int i) {
        fa1.m6826(view, TypedValues.AttributesType.S_TARGET);
        this.f231.onStopNestedScroll(view, i);
    }

    @Override // android.view.View
    public final void onWindowVisibilityChanged(int i) {
        super.onWindowVisibilityChanged(i);
        if (Build.VERSION.SDK_INT >= 23 || i != 0) {
            return;
        }
        this.f232.m7516();
    }

    public final void remeasure() {
        int i;
        int i2 = this.f229;
        if (i2 == Integer.MIN_VALUE || (i = this.f230) == Integer.MIN_VALUE) {
            return;
        }
        measure(i2, i);
    }

    @Override // android.view.ViewGroup, android.view.ViewParent
    public void requestDisallowInterceptTouchEvent(boolean z) {
        ph0<? super Boolean, lx5> ph0Var = this.f227;
        if (ph0Var != null) {
            ph0Var.invoke(Boolean.valueOf(z));
        }
        super.requestDisallowInterceptTouchEvent(z);
    }

    public final void setDensity(@zu2 InterfaceC5282u7 interfaceC5282u7) {
        fa1.m6826(interfaceC5282u7, "value");
        if (interfaceC5282u7 != this.f220) {
            this.f220 = interfaceC5282u7;
            ph0<? super InterfaceC5282u7, lx5> ph0Var = this.f221;
            if (ph0Var != null) {
                ph0Var.invoke(interfaceC5282u7);
            }
        }
    }

    public final void setLifecycleOwner(@wv2 LifecycleOwner lifecycleOwner) {
        if (lifecycleOwner != this.lifecycleOwner) {
            this.lifecycleOwner = lifecycleOwner;
            ViewTreeLifecycleOwner.set(this, lifecycleOwner);
        }
    }

    public final void setModifier(@zu2 rk2 rk2Var) {
        fa1.m6826(rk2Var, "value");
        if (rk2Var != this.f218) {
            this.f218 = rk2Var;
            ph0<? super rk2, lx5> ph0Var = this.f219;
            if (ph0Var != null) {
                ph0Var.invoke(rk2Var);
            }
        }
    }

    public final void setOnDensityChanged$ui_release(@wv2 ph0<? super InterfaceC5282u7, lx5> ph0Var) {
        this.f221 = ph0Var;
    }

    public final void setOnModifierChanged$ui_release(@wv2 ph0<? super rk2, lx5> ph0Var) {
        this.f219 = ph0Var;
    }

    public final void setOnRequestDisallowInterceptTouchEvent$ui_release(@wv2 ph0<? super Boolean, lx5> ph0Var) {
        this.f227 = ph0Var;
    }

    public final void setSavedStateRegistryOwner(@wv2 SavedStateRegistryOwner savedStateRegistryOwner) {
        if (savedStateRegistryOwner != this.savedStateRegistryOwner) {
            this.savedStateRegistryOwner = savedStateRegistryOwner;
            C10501ViewTreeSavedStateRegistryOwner.set(this, savedStateRegistryOwner);
        }
    }

    public final void setUpdate(@zu2 nh0<lx5> nh0Var) {
        fa1.m6826(nh0Var, "value");
        this.f216 = nh0Var;
        this.f217 = true;
        this.f226.invoke();
    }

    public final void setView$ui_release(@wv2 View view) {
        if (view != this.view) {
            this.view = view;
            removeAllViewsInLayout();
            if (view != null) {
                addView(view);
                this.f226.invoke();
            }
        }
    }

    @Override // android.view.ViewGroup
    public boolean shouldDelayChildPressedState() {
        return true;
    }
}
