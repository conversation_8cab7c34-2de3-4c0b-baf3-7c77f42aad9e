package androidx.compose.runtime;

import android.annotation.SuppressLint;
import android.os.Parcel;
import android.os.Parcelable;
import androidx.exifinterface.media.ExifInterface;
import kotlin.Metadata;
import okhttp3.internal.p042io.C7498;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.iw3;
import okhttp3.internal.p042io.k55;
import okhttp3.internal.p042io.mv4;
import okhttp3.internal.p042io.nv4;
import okhttp3.internal.p042io.qr2;
import okhttp3.internal.p042io.ro1;
import okhttp3.internal.p042io.wv2;
import okhttp3.internal.p042io.zu2;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000\u0010\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b\u0001\u0018\u0000*\u0004\b\u0000\u0010\u00012\b\u0012\u0004\u0012\u00028\u00000\u00022\u00020\u0003¨\u0006\u0004"}, m4115d2 = {"Landroidx/compose/runtime/ParcelableSnapshotMutableState;", ExifInterface.GPS_DIRECTION_TRUE, "Lokhttp3/internal/io/mv4;", "Landroid/os/Parcelable;", "runtime_release"}, m4116k = 1, m4117mv = {1, 7, 1})
@SuppressLint({"BanParcelableUsage"})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class ParcelableSnapshotMutableState<T> extends mv4<T> implements Parcelable {

    @ro1
    @zu2
    public static final Parcelable.Creator<ParcelableSnapshotMutableState<Object>> CREATOR = new C0163();

    /* renamed from: androidx.compose.runtime.ParcelableSnapshotMutableState$Ϳ */
    public static final class C0163 implements Parcelable.ClassLoaderCreator<ParcelableSnapshotMutableState<Object>> {
        @Override // android.os.Parcelable.Creator
        public final Object createFromParcel(Parcel parcel) {
            fa1.m6826(parcel, "parcel");
            return createFromParcel(parcel, null);
        }

        @Override // android.os.Parcelable.Creator
        public final Object[] newArray(int i) {
            return new ParcelableSnapshotMutableState[i];
        }

        @Override // android.os.Parcelable.ClassLoaderCreator
        @zu2
        /* renamed from: Ϳ, reason: merged with bridge method [inline-methods] */
        public final ParcelableSnapshotMutableState<Object> createFromParcel(@zu2 Parcel parcel, @wv2 ClassLoader classLoader) {
            nv4 nv4Var;
            fa1.m6826(parcel, "parcel");
            if (classLoader == null) {
                classLoader = C0163.class.getClassLoader();
            }
            Object readValue = parcel.readValue(classLoader);
            int readInt = parcel.readInt();
            if (readInt == 0) {
                nv4Var = qr2.f19477;
            } else if (readInt == 1) {
                nv4Var = k55.f13609;
            } else {
                if (readInt != 2) {
                    throw new IllegalStateException(C7498.m17103("Unsupported MutableState policy ", readInt, " was restored"));
                }
                nv4Var = iw3.f12730;
            }
            return new ParcelableSnapshotMutableState<>(readValue, nv4Var);
        }
    }

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public ParcelableSnapshotMutableState(T t, @zu2 nv4<T> nv4Var) {
        super(t, nv4Var);
        fa1.m6826(nv4Var, "policy");
    }

    @Override // android.os.Parcelable
    public final int describeContents() {
        return 0;
    }

    @Override // android.os.Parcelable
    public final void writeToParcel(@zu2 Parcel parcel, int i) {
        int i2;
        fa1.m6826(parcel, "parcel");
        parcel.writeValue(getValue());
        nv4<T> nv4Var = this.f15607;
        if (fa1.m6818(nv4Var, qr2.f19477)) {
            i2 = 0;
        } else if (fa1.m6818(nv4Var, k55.f13609)) {
            i2 = 1;
        } else {
            if (!fa1.m6818(nv4Var, iw3.f12730)) {
                throw new IllegalStateException("Only known types of MutableState's SnapshotMutationPolicy are supported");
            }
            i2 = 2;
        }
        parcel.writeInt(i2);
    }
}
