#!/usr/bin/env python3
"""
带GUI授权界面的AutoJS9服务器
使用tkinter创建授权确认对话框
"""

import asyncio
import tkinter as tk
from tkinter import messagebox, ttk
import threading
import queue
from autojs_server import AutoJSServer, TokenManager
import logging

logger = logging.getLogger(__name__)

class GUITokenManager(TokenManager):
    """带GUI的Token管理器"""
    
    def __init__(self, db_path: str = "auth.db"):
        super().__init__(db_path)
        self.auth_queue = queue.Queue()
        self.gui_thread = None
        self.root = None
        self._start_gui_thread()
    
    def _start_gui_thread(self):
        """启动GUI线程"""
        self.gui_thread = threading.Thread(target=self._run_gui, daemon=True)
        self.gui_thread.start()
    
    def _run_gui(self):
        """运行GUI主循环"""
        self.root = tk.Tk()
        self.root.withdraw()  # 隐藏主窗口
        self.root.title("AutoJS9 服务器")
        
        # 定期检查授权请求
        self.root.after(100, self._check_auth_requests)
        self.root.mainloop()
    
    def _check_auth_requests(self):
        """检查授权请求队列"""
        try:
            while True:
                request = self.auth_queue.get_nowait()
                self._show_auth_dialog(request)
        except queue.Empty:
            pass
        
        # 继续检查
        self.root.after(100, self._check_auth_requests)
    
    def _show_auth_dialog(self, request):
        """显示授权对话框"""
        device_id, device_name, future = request
        
        # 创建对话框
        dialog = tk.Toplevel(self.root)
        dialog.title("设备授权请求")
        dialog.geometry("400x300")
        dialog.resizable(False, False)
        dialog.grab_set()  # 模态对话框
        
        # 居中显示
        dialog.transient(self.root)
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (dialog.winfo_screenheight() // 2) - (300 // 2)
        dialog.geometry(f"400x300+{x}+{y}")
        
        # 创建界面元素
        main_frame = ttk.Frame(dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(
            main_frame, 
            text="设备授权请求", 
            font=("Arial", 16, "bold")
        )
        title_label.pack(pady=(0, 20))
        
        # 设备信息
        info_frame = ttk.LabelFrame(main_frame, text="设备信息", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 20))
        
        ttk.Label(info_frame, text="设备ID:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Label(info_frame, text=device_id, font=("Courier", 9)).grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        ttk.Label(info_frame, text="设备名称:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Label(info_frame, text=device_name or "未知设备").grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # 说明文字
        desc_label = ttk.Label(
            main_frame,
            text="此设备请求连接到AutoJS9服务器。\n是否允许此设备进行调试连接？",
            justify=tk.CENTER
        )
        desc_label.pack(pady=(0, 20))
        
        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        def on_allow():
            future.set_result(True)
            dialog.destroy()
        
        def on_deny():
            future.set_result(False)
            dialog.destroy()
        
        def on_close():
            future.set_result(False)
            dialog.destroy()
        
        ttk.Button(
            button_frame, 
            text="允许", 
            command=on_allow,
            style="Accent.TButton"
        ).pack(side=tk.RIGHT, padx=(10, 0))
        
        ttk.Button(
            button_frame, 
            text="拒绝", 
            command=on_deny
        ).pack(side=tk.RIGHT)
        
        # 处理窗口关闭
        dialog.protocol("WM_DELETE_WINDOW", on_close)
        
        # 聚焦到对话框
        dialog.focus_set()
    
    async def request_authorization(self, device_id: str, device_name: str = None) -> bool:
        """请求用户授权设备"""
        logger.info(f"请求授权设备: {device_id} ({device_name})")
        
        # 创建Future来等待GUI响应
        future = asyncio.Future()
        
        # 将请求放入队列
        self.auth_queue.put((device_id, device_name, future))
        
        try:
            # 等待用户响应
            result = await asyncio.wait_for(future, timeout=60.0)
            
            if result:
                # 授权成功，保存到数据库
                token = self.generate_token(device_id)
                self._save_authorized_device(device_id, token, device_name)
                self.authorized_tokens.add(token)
                logger.info(f"设备 {device_id} 授权成功")
            else:
                logger.info(f"设备 {device_id} 授权被拒绝")
            
            return result
            
        except asyncio.TimeoutError:
            logger.warning(f"设备 {device_id} 授权请求超时")
            return False

class GUIAutoJSServer(AutoJSServer):
    """带GUI的AutoJS服务器"""
    
    def __init__(self, host: str = "0.0.0.0", port: int = 9317):
        super().__init__(host, port)
        self.token_manager = GUITokenManager()
    
    async def start_server(self):
        """启动服务器"""
        logger.info(f"启动AutoJS服务器 (GUI版本): {self.host}:{self.port}")
        
        # 显示系统托盘通知（如果可能）
        try:
            import plyer
            plyer.notification.notify(
                title="AutoJS9 服务器",
                message=f"服务器已启动: {self.host}:{self.port}",
                timeout=3
            )
        except ImportError:
            pass
        
        await super().start_server()

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="AutoJS9 GUI服务器")
    parser.add_argument("--host", default="0.0.0.0", help="服务器主机")
    parser.add_argument("--port", "-p", type=int, default=9317, help="服务器端口")
    parser.add_argument("--debug", action="store_true", help="调试模式")
    
    args = parser.parse_args()
    
    # 设置日志
    level = logging.DEBUG if args.debug else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 创建并启动服务器
    server = GUIAutoJSServer(host=args.host, port=args.port)
    
    try:
        asyncio.run(server.start_server())
    except KeyboardInterrupt:
        logger.info("服务器已停止")

if __name__ == "__main__":
    main()
