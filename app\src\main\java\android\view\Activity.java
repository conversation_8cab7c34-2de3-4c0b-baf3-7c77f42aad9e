package android.view;

import android.graphics.Rect;
import android.view.View;
import androidx.annotation.RequiresApi;
import kotlin.Metadata;
import okhttp3.internal.p042io.C7179;
import okhttp3.internal.p042io.EnumC7329;
import okhttp3.internal.p042io.InterfaceC5527vx;
import okhttp3.internal.p042io.InterfaceC7155;
import okhttp3.internal.p042io.lx5;
import okhttp3.internal.p042io.r80;
import okhttp3.internal.p042io.wv2;
import okhttp3.internal.p042io.zu2;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\u001a\u001f\u0010\u0004\u001a\u00020\u0003*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u0001H\u0087@ø\u0001\u0000¢\u0006\u0004\b\u0004\u0010\u0005\u0082\u0002\u0004\n\u0002\b\u0019¨\u0006\u0006"}, m4115d2 = {"Landroid/app/Activity;", "Landroid/view/View;", "view", "Lokhttp3/internal/io/lx5;", "trackPipAnimationHintView", "(Landroid/app/Activity;Landroid/view/View;Lokhttp3/internal/io/ৡ;)Ljava/lang/Object;", "activity-ktx_release"}, m4116k = 2, m4117mv = {1, 6, 0})
/* renamed from: androidx.activity.PipHintTrackerKt, reason: from Kotlin metadata */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class Activity {
    @wv2
    @RequiresApi(26)
    @InterfaceC5527vx
    public static final Object trackPipAnimationHintView(@zu2 final android.app.Activity activity, @zu2 View view, @zu2 InterfaceC7155<? super lx5> interfaceC7155) {
        Object collect = new C7179(new PipHintTrackerKt$trackPipAnimationHintView$flow$1(view, null)).collect(new r80() { // from class: androidx.activity.PipHintTrackerKt$trackPipAnimationHintView$2
            @wv2
            public final Object emit(@zu2 android.graphics.Rect rect, @zu2 InterfaceC7155<? super lx5> interfaceC71552) {
                Api26Impl.INSTANCE.setPipParamsSourceRectHint(activity, rect);
                return lx5.f14876;
            }

            @Override // okhttp3.internal.p042io.r80
            public /* bridge */ /* synthetic */ Object emit(Object obj, InterfaceC7155 interfaceC71552) {
                return emit((android.graphics.Rect) obj, (InterfaceC7155<? super lx5>) interfaceC71552);
            }
        }, interfaceC7155);
        return collect == EnumC7329.COROUTINE_SUSPENDED ? collect : lx5.f14876;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final Rect trackPipAnimationHintView$positionInWindow(View view) {
        Rect rect = new Rect();
        view.getGlobalVisibleRect(rect);
        return rect;
    }
}
