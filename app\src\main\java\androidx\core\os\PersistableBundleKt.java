package androidx.core.os;

import android.os.PersistableBundle;
import androidx.annotation.RequiresApi;
import java.util.Map;
import kotlin.Metadata;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.v63;
import okhttp3.internal.p042io.zu2;

@Metadata(m4113bv = {}, m4114d1 = {"\u0000\"\n\u0002\u0010\u0011\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010$\n\u0002\b\u0002\u001a?\u0010\u0006\u001a\u00020\u00052.\u0010\u0004\u001a\u0018\u0012\u0014\b\u0001\u0012\u0010\u0012\u0004\u0012\u00020\u0002\u0012\u0006\u0012\u0004\u0018\u00010\u00030\u00010\u0000\"\u0010\u0012\u0004\u0012\u00020\u0002\u0012\u0006\u0012\u0004\u0018\u00010\u00030\u0001H\u0007¢\u0006\u0004\b\u0006\u0010\u0007\u001a\b\u0010\u0006\u001a\u00020\u0005H\u0007\u001a\u001a\u0010\t\u001a\u00020\u0005*\u0010\u0012\u0004\u0012\u00020\u0002\u0012\u0006\u0012\u0004\u0018\u00010\u00030\bH\u0007¨\u0006\n"}, m4115d2 = {"", "Lokhttp3/internal/io/v63;", "", "", "pairs", "Landroid/os/PersistableBundle;", "persistableBundleOf", "([Lokhttp3/internal/io/v63;)Landroid/os/PersistableBundle;", "", "toPersistableBundle", "core-ktx_release"}, m4116k = 2, m4117mv = {1, 6, 0})
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class PersistableBundleKt {
    @RequiresApi(21)
    @zu2
    public static final PersistableBundle persistableBundleOf() {
        return PersistableBundleApi21ImplKt.createPersistableBundle(0);
    }

    @RequiresApi(21)
    @zu2
    public static final PersistableBundle persistableBundleOf(@zu2 v63<String, ? extends Object>... v63VarArr) {
        fa1.m6826(v63VarArr, "pairs");
        PersistableBundle createPersistableBundle = PersistableBundleApi21ImplKt.createPersistableBundle(v63VarArr.length);
        for (v63<String, ? extends Object> v63Var : v63VarArr) {
            PersistableBundleApi21ImplKt.putValue(createPersistableBundle, v63Var.f23750, v63Var.f23751);
        }
        return createPersistableBundle;
    }

    @RequiresApi(21)
    @zu2
    public static final PersistableBundle toPersistableBundle(@zu2 Map<String, ? extends Object> map) {
        fa1.m6826(map, "<this>");
        PersistableBundle createPersistableBundle = PersistableBundleApi21ImplKt.createPersistableBundle(map.size());
        for (Map.Entry<String, ? extends Object> entry : map.entrySet()) {
            PersistableBundleApi21ImplKt.putValue(createPersistableBundle, entry.getKey(), entry.getValue());
        }
        return createPersistableBundle;
    }
}
