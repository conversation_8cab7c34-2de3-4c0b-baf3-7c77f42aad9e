package androidx.core.view;

import android.view.ViewParent;
import kotlin.Metadata;
import okhttp3.internal.p042io.fa1;
import okhttp3.internal.p042io.hj0;
import okhttp3.internal.p042io.ph0;
import okhttp3.internal.p042io.zu2;

@Metadata(m4116k = 3, m4117mv = {1, 6, 0}, m4119xi = 48)
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public /* synthetic */ class ViewKt$ancestors$1 extends hj0 implements ph0<ViewParent, ViewParent> {
    public static final ViewKt$ancestors$1 INSTANCE = new ViewKt$ancestors$1();

    public ViewKt$ancestors$1() {
        super(1, ViewParent.class, "getParent", "getParent()Landroid/view/ViewParent;", 0);
    }

    @Override // okhttp3.internal.p042io.ph0
    public final ViewParent invoke(@zu2 ViewParent viewParent) {
        fa1.m6826(viewParent, "p0");
        return viewParent.getParent();
    }
}
