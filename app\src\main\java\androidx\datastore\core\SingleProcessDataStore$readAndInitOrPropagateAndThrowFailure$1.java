package androidx.datastore.core;

import kotlin.Metadata;
import okhttp3.internal.p042io.AbstractC6644;
import okhttp3.internal.p042io.InterfaceC4988s2;
import okhttp3.internal.p042io.InterfaceC7155;
import okhttp3.internal.p042io.wv2;
import okhttp3.internal.p042io.zu2;

@InterfaceC4988s2(m11868c = "androidx.datastore.core.SingleProcessDataStore", m11869f = "SingleProcessDataStore.kt", m11870l = {302}, m11871m = "readAndInitOrPropagateAndThrowFailure")
@Metadata(m4116k = 3, m4117mv = {1, 5, 1}, m4119xi = 48)
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes2.dex */
public final class SingleProcessDataStore$readAndInitOrPropagateAndThrowFailure$1 extends AbstractC6644 {
    public Object L$0;
    public int label;
    public /* synthetic */ Object result;
    public final /* synthetic */ SingleProcessDataStore<T> this$0;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public SingleProcessDataStore$readAndInitOrPropagateAndThrowFailure$1(SingleProcessDataStore<T> singleProcessDataStore, InterfaceC7155<? super SingleProcessDataStore$readAndInitOrPropagateAndThrowFailure$1> interfaceC7155) {
        super(interfaceC7155);
        this.this$0 = singleProcessDataStore;
    }

    @Override // okhttp3.internal.p042io.AbstractC7853
    @wv2
    public final Object invokeSuspend(@zu2 Object obj) {
        Object readAndInitOrPropagateAndThrowFailure;
        this.result = obj;
        this.label |= Integer.MIN_VALUE;
        readAndInitOrPropagateAndThrowFailure = this.this$0.readAndInitOrPropagateAndThrowFailure(this);
        return readAndInitOrPropagateAndThrowFailure;
    }
}
