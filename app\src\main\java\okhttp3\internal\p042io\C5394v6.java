package okhttp3.internal.p042io;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.VisibleForTesting;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import okhttp3.internal.p042io.bi2;

/* renamed from: okhttp3.internal.io.v6 */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5394v6 {

    /* renamed from: Ϳ */
    @Nullable
    public InterfaceC5560w6 f23710;

    /* renamed from: Ԩ */
    @NonNull
    public Map<String, List<bi2.InterfaceC2882>> f23711;

    /* renamed from: ԩ */
    @NonNull
    @VisibleForTesting
    public final C9422 f23712;

    /* renamed from: okhttp3.internal.io.v6$Ϳ, reason: contains not printable characters */
    public class C9422 implements bi2.InterfaceC2881 {
        public C9422() {
        }

        /* JADX WARN: Type inference failed for: r0v11, types: [java.util.HashMap, java.util.Map<java.lang.String, java.util.List<okhttp3.internal.io.bi2$Ԭ>>] */
        /* JADX WARN: Type inference failed for: r0v6, types: [java.util.HashMap, java.util.Map<java.lang.String, java.util.List<okhttp3.internal.io.bi2$Ԭ>>] */
        /* JADX WARN: Type inference failed for: r0v9, types: [java.util.HashMap, java.util.Map<java.lang.String, java.util.List<okhttp3.internal.io.bi2$Ԭ>>] */
        @Override // okhttp3.internal.p042io.bi2.InterfaceC2881
        /* renamed from: Ϳ */
        public final void mo476(@NonNull sh2 sh2Var, @NonNull bi2.InterfaceC2882 interfaceC2882) {
            String str;
            String str2;
            if (C5394v6.this.f23710 == null) {
                return;
            }
            String str3 = sh2Var.f21180;
            Map map = (Map) sh2Var.f21181;
            ((Integer) map.get("loadingUnitId")).intValue();
            str = (String) map.get("componentName");
            Objects.requireNonNull(str3);
            switch (str3) {
                case "uninstallDeferredComponent":
                    C5394v6.this.f23710.m13481();
                    str2 = null;
                    break;
                case "getDeferredComponentInstallState":
                    str2 = C5394v6.this.f23710.m13483();
                    break;
                case "installDeferredComponent":
                    C5394v6.this.f23710.m13482();
                    if (!C5394v6.this.f23711.containsKey(str)) {
                        C5394v6.this.f23711.put(str, new ArrayList());
                    }
                    ((List) C5394v6.this.f23711.get(str)).add(interfaceC2882);
                    return;
                default:
                    ((bi2.C2879.C8942) interfaceC2882).mo3057();
                    return;
            }
            ((bi2.C2879.C8942) interfaceC2882).mo3056(str2);
        }
    }

    public C5394v6(@NonNull C5136t c5136t) {
        C9422 c9422 = new C9422();
        this.f23712 = c9422;
        new bi2(c5136t, "flutter/deferredcomponent", vz4.f24568).m5189(c9422);
        Objects.requireNonNull(mb0.m9655());
        this.f23710 = null;
        this.f23711 = new HashMap();
    }
}
