package okhttp3.internal.p042io;

import okhttp3.internal.p042io.C5265u2;
import p041j$.util.concurrent.ConcurrentHashMap;

/* renamed from: okhttp3.internal.io.w2 */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5546w2 extends lv1 implements ph0<cm1, lx5> {

    /* renamed from: ၥ */
    public final /* synthetic */ C5265u2 f24592;

    /* renamed from: ၦ */
    public final /* synthetic */ String f24593;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public C5546w2(C5265u2 c5265u2, String str) {
        super(1);
        this.f24592 = c5265u2;
        this.f24593 = str;
    }

    @Override // okhttp3.internal.p042io.ph0
    public final lx5 invoke(cm1 cm1Var) {
        Object putIfAbsent;
        cm1 cm1Var2 = cm1Var;
        fa1.m6826(cm1Var2, "it");
        C6528 c6528 = this.f24592.f22732;
        fa1.m6826(c6528, "scope");
        C5386v2 c5386v2 = new C5386v2(c6528, cm1Var2);
        ConcurrentHashMap<String, Object> concurrentHashMap = cm1Var2.f8096;
        String name = C5265u2.C9370.class.getName();
        Object obj = concurrentHashMap.get(name);
        if (obj == null && (putIfAbsent = concurrentHashMap.putIfAbsent(name, (obj = c5386v2.invoke()))) != null) {
            obj = putIfAbsent;
        }
        ((C5265u2.C9370) obj).f22743.mo8074(this.f24593);
        return lx5.f14876;
    }
}
