package okhttp3.internal.p042io;

import com.stardust.autojs.project.ProjectLauncher;

/* renamed from: okhttp3.internal.io.xo */
/* loaded from: C:\Users\<USER>\Downloads\35287cad784d02ccacc724f7c526ccb4.zip\35287cad784d02ccacc724f7c526ccb4\classes4.dex */
public final class C5727xo extends lv1 implements ph0<yx0, lx5> {

    /* renamed from: ၥ */
    public final /* synthetic */ String f26100;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public C5727xo(String str) {
        super(1);
        this.f26100 = str;
    }

    @Override // okhttp3.internal.p042io.ph0
    public final lx5 invoke(yx0 yx0Var) {
        yx0 yx0Var2 = yx0Var;
        fa1.m6826(yx0Var2, "it");
        ProjectLauncher.launch$default(new ProjectLauncher(this.f26100), yx0Var2, false, 2, (Object) null);
        return lx5.f14876;
    }
}
